#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[完成]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 主菜单
show_main_menu() {
    clear
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}    URL Stash Vault 项目管理工具${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo
    echo "请选择要执行的操作："
    echo
    echo -e "${GREEN}[1]${NC} 快速重启服务 (开发环境)"
    echo -e "${GREEN}[2]${NC} 停止所有服务"
    echo -e "${GREEN}[3]${NC} 完整Docker部署"
    echo -e "${GREEN}[4]${NC} 查看服务状态"
    echo -e "${GREEN}[5]${NC} 查看项目信息"
    echo -e "${RED}[0]${NC} 退出"
    echo
    read -p "请输入选项 [0-5]: " choice
    echo
}

# 快速重启服务
restart_services() {
    clear
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}        快速重启服务 (开发环境)${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo

    log_info "项目根目录: $PROJECT_ROOT"
    echo

    # 步骤 1: 停止现有服务
    echo "[步骤 1/3] 停止现有服务..."
    log_info "停止前端服务 (端口 3000, 3001)..."

    # 查找并终止占用3000和3001端口的进程
    for port in 3000 3001; do
        pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ ! -z "$pids" ]; then
            log_info "终止端口 $port 上的进程: $pids"
            echo $pids | xargs kill -9 2>/dev/null || true
        fi
    done

    log_info "停止后端服务 (端口 8080)..."
    pids=$(lsof -ti:8080 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        log_info "终止端口 8080 上的进程: $pids"
        echo $pids | xargs kill -9 2>/dev/null || true
    fi

    sleep 2
    log_success "服务停止完成"
    echo

    # 步骤 2: 启动后端服务
    echo "[步骤 2/3] 启动后端服务..."
    cd "$PROJECT_ROOT/backend"

    # 检查Go是否安装
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go语言环境"
        return 1
    fi

    log_info "正在启动后端服务器 (http://localhost:8080)..."

    # 创建日志目录
    mkdir -p ../logs

    # 使用nohup在后台启动后端服务
    nohup go run cmd/server/main.go > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid

    sleep 3
    log_success "后端服务启动完成 (PID: $BACKEND_PID)"
    echo

    # 步骤 3: 启动前端服务
    echo "[步骤 3/3] 启动前端服务..."
    cd "$PROJECT_ROOT"

    # 检查Node.js是否安装
    if ! command -v npm &> /dev/null; then
        log_error "Node.js/npm未安装，请先安装Node.js环境"
        return 1
    fi

    log_info "正在启动前端开发服务器..."

    # 使用nohup在后台启动前端服务
    nohup npm run dev > logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > logs/frontend.pid

    sleep 5

    echo
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}          重启完成！${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo "前端地址: http://localhost:3000 或 http://localhost:3001"
    echo "后端地址: http://localhost:8080"
    echo
    echo "进程信息:"
    echo "  后端进程 PID: $BACKEND_PID"
    echo "  前端进程 PID: $FRONTEND_PID"
    echo
    echo "日志文件:"
    echo "  后端日志: logs/backend.log"
    echo "  前端日志: logs/frontend.log"
    echo
    echo "停止服务命令:"
    echo "  kill \$(cat logs/backend.pid) \$(cat logs/frontend.pid)"
    echo
    echo "提示: 服务可能需要几秒钟才能完全启动"
    echo -e "${CYAN}======================================${NC}"
}

# 停止所有服务
stop_services() {
    clear
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}          停止所有服务${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo

    # 步骤 1: 停止本地服务
    echo "[步骤 1/2] 停止本地服务..."
    log_info "停止前端服务 (端口 3000, 3001)..."

    # 查找并终止占用3000和3001端口的进程
    for port in 3000 3001; do
        pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ ! -z "$pids" ]; then
            log_info "终止端口 $port 上的进程: $pids"
            echo $pids | xargs kill -9 2>/dev/null || true
        fi
    done

    log_info "停止后端服务 (端口 8080)..."
    pids=$(lsof -ti:8080 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        log_info "终止端口 8080 上的进程: $pids"
        echo $pids | xargs kill -9 2>/dev/null || true
    fi

    # 停止通过PID文件记录的进程
    if [ -f "$PROJECT_ROOT/logs/backend.pid" ]; then
        backend_pid=$(cat "$PROJECT_ROOT/logs/backend.pid")
        if kill -0 "$backend_pid" 2>/dev/null; then
            kill -9 "$backend_pid" 2>/dev/null || true
            log_info "停止后端进程: $backend_pid"
        fi
        rm -f "$PROJECT_ROOT/logs/backend.pid"
    fi

    if [ -f "$PROJECT_ROOT/logs/frontend.pid" ]; then
        frontend_pid=$(cat "$PROJECT_ROOT/logs/frontend.pid")
        if kill -0 "$frontend_pid" 2>/dev/null; then
            kill -9 "$frontend_pid" 2>/dev/null || true
            log_info "停止前端进程: $frontend_pid"
        fi
        rm -f "$PROJECT_ROOT/logs/frontend.pid"
    fi

    log_success "本地服务停止完成"
    echo

    # 步骤 2: 停止Docker容器
    echo "[步骤 2/2] 停止Docker容器..."
    if docker stop url-stash-vault-frontend url-stash-vault-backend 2>/dev/null; then
        log_success "Docker容器停止完成"
    else
        log_info "没有运行中的Docker容器"
    fi

    echo
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}          所有服务已停止${NC}"
    echo -e "${CYAN}======================================${NC}"
}

# Docker部署
docker_deploy() {
    clear
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}        完整Docker部署${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo
    log_warning "此操作将："
    echo "1. 构建Docker镜像"
    echo "2. 推送到Docker Hub (用户名: kkape)"
    echo "3. 启动容器化服务"
    echo

    read -p "确认继续？(y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        return 0
    fi

    # 错误处理
    set -e
    trap 'log_error "脚本执行失败，退出中..."; return 1' ERR

    # 检查Docker是否安装
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        return 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        return 1
    fi

    # 设置变量
    DOCKER_USERNAME="kkape"
    FRONTEND_IMAGE="${DOCKER_USERNAME}/url-stash-vault-frontend"
    BACKEND_IMAGE="${DOCKER_USERNAME}/url-stash-vault-backend"

    log_info "项目根目录: $PROJECT_ROOT"
    echo

    # 步骤 1: 停止现有服务
    echo "[步骤 1/8] 停止现有服务..."
    log_info "停止前端服务 (端口 3000, 3001)..."

    # 查找并终止占用3000和3001端口的进程
    for port in 3000 3001; do
        pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ ! -z "$pids" ]; then
            log_info "终止端口 $port 上的进程: $pids"
            echo $pids | xargs kill -9 2>/dev/null || true
        fi
    done

    log_info "停止后端服务 (端口 8080)..."
    pids=$(lsof -ti:8080 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        log_info "终止端口 8080 上的进程: $pids"
        echo $pids | xargs kill -9 2>/dev/null || true
    fi

    log_info "停止现有Docker容器..."
    docker stop url-stash-vault-frontend url-stash-vault-backend 2>/dev/null || true
    docker rm url-stash-vault-frontend url-stash-vault-backend 2>/dev/null || true

    sleep 3
    log_success "服务停止完成"
    echo

    # 步骤 2: 检查环境配置
    echo "[步骤 2/8] 检查环境配置..."
    cd "$PROJECT_ROOT"

    if [ ! -f "backend/.env" ]; then
        log_warning "backend/.env 文件不存在，从模板创建..."
        if [ -f "backend/env.example" ]; then
            cp "backend/env.example" "backend/.env"
            log_success "已创建 backend/.env，请根据需要修改配置"
        else
            log_error "backend/env.example 模板文件不存在"
            return 1
        fi
    fi

    if [ ! -f ".env" ]; then
        log_warning "前端 .env 文件不存在，创建默认配置..."
        cat > .env << 'EOF'
VITE_API_URL=http://localhost:8080
VITE_USE_SUPABASE=false
EOF
        log_success "已创建前端 .env 文件"
    fi

    log_success "环境配置检查完成"
    echo

    # 步骤 3: 构建后端Docker镜像
    echo "[步骤 3/8] 构建后端Docker镜像..."
    cd "$PROJECT_ROOT/backend"

    if [ ! -f "Dockerfile" ]; then
        log_info "创建后端Dockerfile..."
        cat > Dockerfile << 'EOF'
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=1 GOOS=linux go build -o main ./cmd/server

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/
COPY --from=builder /app/main .
EXPOSE 8080
CMD ["./main"]
EOF
    fi

    log_info "构建后端镜像: $BACKEND_IMAGE..."
    docker build -t "$BACKEND_IMAGE:latest" . --no-cache
    log_success "后端镜像构建完成"
    echo

    # 步骤 4: 构建前端Docker镜像
    echo "[步骤 4/8] 构建前端Docker镜像..."
    cd "$PROJECT_ROOT"

    if [ ! -f "Dockerfile" ]; then
        log_info "创建前端Dockerfile..."
        cat > Dockerfile << 'EOF'
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
EOF
    fi

    if [ ! -f "nginx.conf" ]; then
        log_info "创建nginx配置文件..."
        cat > nginx.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://backend:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
    fi

    log_info "构建前端镜像: $FRONTEND_IMAGE..."
    docker build -t "$FRONTEND_IMAGE:latest" . --no-cache
    log_success "前端镜像构建完成"
    echo

    # 步骤 5: Docker Hub 登录
    echo "[步骤 5/8] 登录Docker Hub..."
    echo "请输入Docker Hub密码（用户名: $DOCKER_USERNAME）:"
    if ! docker login -u "$DOCKER_USERNAME"; then
        log_error "Docker Hub登录失败"
        return 1
    fi
    log_success "Docker Hub登录成功"
    echo

    # 步骤 6: 推送镜像
    echo "[步骤 6/8] 推送镜像到Docker Hub..."
    log_info "推送后端镜像..."
    docker push "$BACKEND_IMAGE:latest"

    log_info "推送前端镜像..."
    docker push "$FRONTEND_IMAGE:latest"
    log_success "镜像推送完成"
    echo

    # 步骤 7: 创建Docker Compose文件
    echo "[步骤 7/8] 创建Docker Compose配置..."
    cd "$PROJECT_ROOT"

    cat > docker-compose.yml << EOF
version: '3.8'
services:
  backend:
    image: $BACKEND_IMAGE:latest
    container_name: url-stash-vault-backend
    ports:
      - "8080:8080"
    environment:
      - DB_PATH=/data/app.db
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
      - SMTP_ENABLED=false
    volumes:
      - ./data:/data
    restart: unless-stopped

  frontend:
    image: $FRONTEND_IMAGE:latest
    container_name: url-stash-vault-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped
EOF

    log_success "Docker Compose配置创建完成"
    echo

    # 步骤 8: 启动服务
    echo "[步骤 8/8] 启动服务..."
    log_info "创建数据目录..."
    mkdir -p data

    log_info "启动Docker容器..."
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi

    echo
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}          部署完成！${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo "前端地址: http://localhost:3000"
    echo "后端地址: http://localhost:8080"
    echo
    echo "查看日志命令:"
    if command -v docker-compose &> /dev/null; then
        echo "  docker-compose logs -f"
        echo
        echo "停止服务命令:"
        echo "  docker-compose down"
    else
        echo "  docker compose logs -f"
        echo
        echo "停止服务命令:"
        echo "  docker compose down"
    fi
    echo -e "${CYAN}======================================${NC}"

    sleep 3
    echo
    log_info "正在检查服务状态..."
    sleep 5

    echo
    log_info "Docker容器状态:"
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
}

# 检查服务状态
check_status() {
    clear
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}          服务状态检查${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo

    echo "[检查本地端口占用]"
    
    for port in 3000 3001 8080; do
        if lsof -i:$port &> /dev/null; then
            echo -e "✓ 端口 $port 正在使用中"
        else
            echo -e "✗ 端口 $port 未被占用"
        fi
    done

    echo
    echo "[检查Docker容器状态]"
    if command -v docker &> /dev/null; then
        docker ps --filter "name=url-stash-vault" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "没有运行中的相关容器"
    else
        echo "Docker未安装"
    fi

    echo
    echo "[环境检查]"
    if command -v go &> /dev/null; then
        echo -e "✓ Go 已安装"
        go version
    else
        echo -e "✗ Go 未安装"
    fi

    if command -v npm &> /dev/null; then
        echo -e "✓ Node.js/npm 已安装"
        echo "npm $(npm --version)"
    else
        echo -e "✗ Node.js/npm 未安装"
    fi

    if command -v docker &> /dev/null; then
        echo -e "✓ Docker 已安装"
        docker --version
    else
        echo -e "✗ Docker 未安装"
    fi

    echo
    echo "[日志文件状态]"
    if [ -f "$PROJECT_ROOT/logs/backend.log" ]; then
        echo -e "✓ 后端日志: logs/backend.log"
    else
        echo -e "✗ 后端日志文件不存在"
    fi

    if [ -f "$PROJECT_ROOT/logs/frontend.log" ]; then
        echo -e "✓ 前端日志: logs/frontend.log"
    else
        echo -e "✗ 前端日志文件不存在"
    fi
}

# 项目信息
show_project_info() {
    clear
    echo -e "${CYAN}======================================${NC}"
    echo -e "${CYAN}          项目信息${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo
    echo -e "${GREEN}项目名称:${NC} URL Stash Vault"
    echo -e "${GREEN}描述:${NC} 一体化导航管理平台"
    echo
    echo -e "${YELLOW}功能模块:${NC}"
    echo "- 短链接服务"
    echo "- 导航书签管理"
    echo "- 临时邮箱服务"
    echo "- 在线工具集"
    echo "- 热点新闻聚合"
    echo "- 备忘录系统"
    echo
    echo -e "${YELLOW}技术栈:${NC}"
    echo "前端: React + TypeScript + Vite + Tailwind CSS"
    echo "后端: Go + SQLite + JWT认证"
    echo "部署: Docker + Docker Compose"
    echo
    echo -e "${YELLOW}默认端口:${NC}"
    echo "- 前端开发服务器: 3000/3001"
    echo "- 后端API服务器: 8080"
    echo "- 前端Docker容器: 3000"
    echo
    echo -e "${YELLOW}Docker镜像:${NC}"
    echo "- kkape/url-stash-vault-frontend:latest"
    echo "- kkape/url-stash-vault-backend:latest"
    echo
    echo -e "${YELLOW}配置文件:${NC}"
    echo "- backend/.env (后端环境配置)"
    echo "- .env (前端环境配置)"
    echo "- docker-compose.yml (容器编排)"
    echo
    echo -e "${YELLOW}项目根目录:${NC} $PROJECT_ROOT"
}

# 主循环
main() {
    while true; do
        show_main_menu
        
        case $choice in
            1)
                restart_services
                ;;
            2)
                stop_services
                ;;
            3)
                docker_deploy
                ;;
            4)
                check_status
                ;;
            5)
                show_project_info
                ;;
            0)
                echo
                log_success "感谢使用 URL Stash Vault 项目管理工具！"
                exit 0
                ;;
            *)
                log_error "无效的选项，请重新选择"
                sleep 2
                continue
                ;;
        esac
        
        echo
        read -p "按回车键返回主菜单..."
    done
}

# 启动主程序
main 