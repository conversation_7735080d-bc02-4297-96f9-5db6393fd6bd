@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:MAIN_MENU
cls
echo ======================================
echo    URL Stash Vault 项目管理工具
echo ======================================
echo.
echo 请选择要执行的操作：
echo.
echo [1] 快速重启服务 (开发环境)
echo [2] 停止所有服务
echo [3] 完整Docker部署
echo [4] 查看服务状态
echo [5] 查看项目信息
echo [0] 退出
echo.
set /p choice="请输入选项 [0-5]: "

if "%choice%"=="1" goto RESTART_SERVICES
if "%choice%"=="2" goto STOP_SERVICES
if "%choice%"=="3" goto DOCKER_DEPLOY
if "%choice%"=="4" goto CHECK_STATUS
if "%choice%"=="5" goto PROJECT_INFO
if "%choice%"=="0" goto EXIT
goto INVALID_CHOICE

:RESTART_SERVICES
cls
echo ======================================
echo        快速重启服务 (开发环境)
echo ======================================
echo.

set PROJECT_ROOT=%~dp0..
echo [信息] 项目根目录: %PROJECT_ROOT%
echo.

:: 停止现有服务
echo [步骤 1/3] 停止现有服务...
echo 停止前端服务 (端口 3000, 3001)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3000" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3001" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)

echo 停止后端服务 (端口 8080)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8080" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)

timeout /t 2 >nul
echo [完成] 服务停止完成
echo.

:: 启动后端服务
echo [步骤 2/3] 启动后端服务...
cd /d "%PROJECT_ROOT%\backend"
echo 正在启动后端服务器 (http://localhost:8080)...
start "后端服务器" cmd /c "go run cmd/server/main.go"
timeout /t 3 >nul
echo [完成] 后端服务启动完成
echo.

:: 启动前端服务
echo [步骤 3/3] 启动前端服务...
cd /d "%PROJECT_ROOT%"
echo 正在启动前端开发服务器...
start "前端服务器" cmd /c "npm run dev"
timeout /t 5 >nul

echo.
echo ======================================
echo          重启完成！
echo ======================================
echo 前端地址: http://localhost:3000 或 http://localhost:3001
echo 后端地址: http://localhost:8080
echo.
echo 提示: 服务可能需要几秒钟才能完全启动
echo ======================================
goto CONTINUE

:STOP_SERVICES
cls
echo ======================================
echo          停止所有服务
echo ======================================
echo.

echo [步骤 1/2] 停止本地服务...
echo 停止前端服务 (端口 3000, 3001)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3000" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3001" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)

echo 停止后端服务 (端口 8080)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8080" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)

echo [完成] 本地服务停止完成
echo.

echo [步骤 2/2] 停止Docker容器...
docker stop url-stash-vault-frontend url-stash-vault-backend >nul 2>&1
if %errorlevel% equ 0 (
    echo [完成] Docker容器停止完成
) else (
    echo [信息] 没有运行中的Docker容器
)

echo.
echo ======================================
echo          所有服务已停止
echo ======================================
goto CONTINUE

:DOCKER_DEPLOY
cls
echo ======================================
echo        完整Docker部署
echo ======================================
echo.
echo 警告: 此操作将：
echo 1. 构建Docker镜像
echo 2. 推送到Docker Hub (用户名: kkape)
echo 3. 启动容器化服务
echo.
set /p confirm="确认继续？(y/N): "
if /i not "%confirm%"=="y" goto MAIN_MENU

:: 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker未安装或未启动，请先安装Docker Desktop
    goto CONTINUE
)

:: 设置变量
set DOCKER_USERNAME=kkape
set FRONTEND_IMAGE=%DOCKER_USERNAME%/url-stash-vault-frontend
set BACKEND_IMAGE=%DOCKER_USERNAME%/url-stash-vault-backend
set PROJECT_ROOT=%~dp0..

echo [信息] 项目根目录: %PROJECT_ROOT%
echo.

:: 停止现有服务
echo [步骤 1/8] 停止现有服务...
echo 停止前端服务 (端口 3000, 3001)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3000" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":3001" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)

echo 停止后端服务 (端口 8080)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8080" ^| findstr "LISTENING"') do (
    echo 终止进程 %%a
    taskkill /PID %%a /F >nul 2>&1
)

echo 停止现有Docker容器...
docker stop url-stash-vault-frontend url-stash-vault-backend >nul 2>&1
docker rm url-stash-vault-frontend url-stash-vault-backend >nul 2>&1

timeout /t 3 >nul
echo [完成] 服务停止完成
echo.

:: 检查环境文件
echo [步骤 2/8] 检查环境配置...
cd /d "%PROJECT_ROOT%"

if not exist "backend\.env" (
    echo [警告] backend\.env 文件不存在，从模板创建...
    if exist "backend\env.example" (
        copy "backend\env.example" "backend\.env" >nul
        echo [完成] 已创建 backend\.env，请根据需要修改配置
    ) else (
        echo [错误] backend\env.example 模板文件不存在
        goto CONTINUE
    )
)

if not exist ".env" (
    echo [警告] 前端 .env 文件不存在，创建默认配置...
    echo VITE_API_URL=http://localhost:8080 > .env
    echo VITE_USE_SUPABASE=false >> .env
    echo [完成] 已创建前端 .env 文件
)

echo [完成] 环境配置检查完成
echo.

:: 构建后端Docker镜像
echo [步骤 3/8] 构建后端Docker镜像...
cd /d "%PROJECT_ROOT%\backend"

if not exist "Dockerfile" (
    echo [信息] 创建后端Dockerfile...
    (
        echo FROM golang:1.21-alpine AS builder
        echo WORKDIR /app
        echo COPY go.mod go.sum ./
        echo RUN go mod download
        echo COPY . .
        echo RUN CGO_ENABLED=1 GOOS=linux go build -o main ./cmd/server
        echo.
        echo FROM alpine:latest
        echo RUN apk --no-cache add ca-certificates tzdata
        echo WORKDIR /root/
        echo COPY --from=builder /app/main .
        echo EXPOSE 8080
        echo CMD ["./main"]
    ) > Dockerfile
)

echo 构建后端镜像: %BACKEND_IMAGE%...
docker build -t %BACKEND_IMAGE%:latest . --no-cache
if %errorlevel% neq 0 (
    echo [错误] 后端镜像构建失败
    goto CONTINUE
)
echo [完成] 后端镜像构建完成
echo.

:: 构建前端Docker镜像
echo [步骤 4/8] 构建前端Docker镜像...
cd /d "%PROJECT_ROOT%"

if not exist "Dockerfile" (
    echo [信息] 创建前端Dockerfile...
    (
        echo FROM node:18-alpine AS builder
        echo WORKDIR /app
        echo COPY package*.json ./
        echo RUN npm ci --only=production
        echo COPY . .
        echo RUN npm run build
        echo.
        echo FROM nginx:alpine
        echo COPY --from=builder /app/dist /usr/share/nginx/html
        echo COPY nginx.conf /etc/nginx/conf.d/default.conf
        echo EXPOSE 80
        echo CMD ["nginx", "-g", "daemon off;"]
    ) > Dockerfile
)

if not exist "nginx.conf" (
    echo [信息] 创建nginx配置文件...
    (
        echo server {
        echo     listen 80;
        echo     server_name localhost;
        echo     root /usr/share/nginx/html;
        echo     index index.html;
        echo.
        echo     location / {
        echo         try_files $uri $uri/ /index.html;
        echo     }
        echo.
        echo     location /api/ {
        echo         proxy_pass http://backend:8080/;
        echo         proxy_set_header Host $host;
        echo         proxy_set_header X-Real-IP $remote_addr;
        echo         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        echo         proxy_set_header X-Forwarded-Proto $scheme;
        echo     }
        echo }
    ) > nginx.conf
)

echo 构建前端镜像: %FRONTEND_IMAGE%...
docker build -t %FRONTEND_IMAGE%:latest . --no-cache
if %errorlevel% neq 0 (
    echo [错误] 前端镜像构建失败
    goto CONTINUE
)
echo [完成] 前端镜像构建完成
echo.

:: Docker Hub 登录
echo [步骤 5/8] 登录Docker Hub...
echo 请输入Docker Hub密码（用户名: %DOCKER_USERNAME%）:
docker login -u %DOCKER_USERNAME%
if %errorlevel% neq 0 (
    echo [错误] Docker Hub登录失败
    goto CONTINUE
)
echo [完成] Docker Hub登录成功
echo.

:: 推送镜像
echo [步骤 6/8] 推送镜像到Docker Hub...
echo 推送后端镜像...
docker push %BACKEND_IMAGE%:latest
if %errorlevel% neq 0 (
    echo [错误] 后端镜像推送失败
    goto CONTINUE
)

echo 推送前端镜像...
docker push %FRONTEND_IMAGE%:latest
if %errorlevel% neq 0 (
    echo [错误] 前端镜像推送失败
    goto CONTINUE
)
echo [完成] 镜像推送完成
echo.

:: 创建Docker Compose文件
echo [步骤 7/8] 创建Docker Compose配置...
cd /d "%PROJECT_ROOT%"

(
    echo version: '3.8'
    echo services:
    echo   backend:
    echo     image: %BACKEND_IMAGE%:latest
    echo     container_name: url-stash-vault-backend
    echo     ports:
    echo       - "8080:8080"
    echo     environment:
    echo       - DB_PATH=/data/app.db
    echo       - JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
    echo       - SMTP_ENABLED=false
    echo     volumes:
    echo       - ./data:/data
    echo     restart: unless-stopped
    echo.
    echo   frontend:
    echo     image: %FRONTEND_IMAGE%:latest
    echo     container_name: url-stash-vault-frontend
    echo     ports:
    echo       - "3000:80"
    echo     depends_on:
    echo       - backend
    echo     restart: unless-stopped
) > docker-compose.yml

echo [完成] Docker Compose配置创建完成
echo.

:: 启动服务
echo [步骤 8/8] 启动服务...
echo 创建数据目录...
if not exist "data" mkdir data

echo 启动Docker容器...
docker-compose up -d
if %errorlevel% neq 0 (
    echo [错误] 服务启动失败
    goto CONTINUE
)

echo.
echo ======================================
echo          部署完成！
echo ======================================
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:8080
echo.
echo 查看日志命令:
echo   docker-compose logs -f
echo.
echo 停止服务命令:
echo   docker-compose down
echo ======================================

timeout /t 3 >nul
echo.
echo 正在检查服务状态...
timeout /t 5 >nul

echo.
echo Docker容器状态:
docker-compose ps
goto CONTINUE

:CHECK_STATUS
cls
echo ======================================
echo          服务状态检查
echo ======================================
echo.

echo [检查本地端口占用]
echo 端口 3000:
netstat -ano | findstr ":3000" | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 端口 3000 正在使用中
) else (
    echo ✗ 端口 3000 未被占用
)

echo 端口 3001:
netstat -ano | findstr ":3001" | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 端口 3001 正在使用中
) else (
    echo ✗ 端口 3001 未被占用
)

echo 端口 8080:
netstat -ano | findstr ":8080" | findstr "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 端口 8080 正在使用中
) else (
    echo ✗ 端口 8080 未被占用
)

echo.
echo [检查Docker容器状态]
docker ps --filter "name=url-stash-vault" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>nul
if %errorlevel% neq 0 (
    echo Docker未安装或未启动
)

echo.
echo [环境检查]
go version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Go 已安装
    go version
) else (
    echo ✗ Go 未安装
)

npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Node.js/npm 已安装
    npm --version
) else (
    echo ✗ Node.js/npm 未安装
)

docker --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Docker 已安装
    docker --version
) else (
    echo ✗ Docker 未安装
)
goto CONTINUE

:PROJECT_INFO
cls
echo ======================================
echo          项目信息
echo ======================================
echo.
echo 项目名称: URL Stash Vault
echo 描述: 一体化导航管理平台
echo.
echo 功能模块:
echo - 短链接服务
echo - 导航书签管理
echo - 临时邮箱服务
echo - 在线工具集
echo - 热点新闻聚合
echo - 备忘录系统
echo.
echo 技术栈:
echo 前端: React + TypeScript + Vite + Tailwind CSS
echo 后端: Go + SQLite + JWT认证
echo 部署: Docker + Docker Compose
echo.
echo 默认端口:
echo - 前端开发服务器: 3000/3001
echo - 后端API服务器: 8080
echo - 前端Docker容器: 3000
echo.
echo Docker镜像:
echo - kkape/url-stash-vault-frontend:latest
echo - kkape/url-stash-vault-backend:latest
echo.
echo 配置文件:
echo - backend/.env (后端环境配置)
echo - .env (前端环境配置)
echo - docker-compose.yml (容器编排)
goto CONTINUE

:INVALID_CHOICE
echo.
echo [错误] 无效的选项，请重新选择
timeout /t 2 >nul
goto MAIN_MENU

:CONTINUE
echo.
echo 按任意键返回主菜单...
pause >nul
goto MAIN_MENU

:EXIT
echo.
echo 感谢使用 URL Stash Vault 项目管理工具！
timeout /t 2 >nul
exit /b 0 