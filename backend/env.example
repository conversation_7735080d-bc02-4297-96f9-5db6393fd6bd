# 服务器配置
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
SERVER_READ_TIMEOUT=15s
SERVER_WRITE_TIMEOUT=15s
SERVER_IDLE_TIMEOUT=60s

# 数据库配置
# 支持的驱动: sqlite, mysql, postgres
DB_DRIVER=sqlite
# 如果不设置 DB_SOURCE，将根据驱动类型自动生成默认连接字符串

# SQLite 配置 (当 DB_DRIVER=sqlite 时)
DB_SOURCE=./data/app.db

# MySQL 配置 (当 DB_DRIVER=mysql 时)
# DB_SOURCE=user:password@tcp(localhost:3306)/url_stash_vault?charset=utf8mb4&parseTime=True&loc=Local
# 或者分别配置各个参数:
# DB_HOST=localhost
# DB_PORT=3306
# DB_USER=root
# DB_PASSWORD=your_password
# DB_NAME=url_stash_vault

# PostgreSQL 配置 (当 DB_DRIVER=postgres 时)
# DB_SOURCE=host=localhost port=5432 user=postgres password=your_password dbname=url_stash_vault sslmode=disable TimeZone=Asia/Shanghai
# 或者分别配置各个参数:
# DB_HOST=localhost
# DB_PORT=5432
# DB_USER=postgres
# DB_PASSWORD=your_password
# DB_NAME=url_stash_vault
# DB_SSLMODE=disable

# 数据库连接池配置
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=5m
DB_AUTO_MIGRATE=true
DB_LOG_LEVEL=warn

# JWT 配置
# 生产环境必须设置一个安全的随机密钥，长度至少 32 个字符
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production-32-chars-minimum
JWT_EXPIRATION=24h
JWT_ISSUER=url-stash-vault

# Redis 配置 (可选)
REDIS_ENABLED=false
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# CORS 配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization
CORS_ALLOW_CREDENTIALS=true

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# SMTP 邮件配置
SMTP_ENABLED=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>

# 应用配置
APP_ENV=development
APP_DEBUG=true 