package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// 模拟的模型结构（简化版，实际应该导入真实的模型）
type User struct {
	ID           uint   `gorm:"primarykey"`
	Email        string `gorm:"uniqueIndex;not null"`
	Password     string `gorm:"not null"`
	IsSuperAdmin bool   `gorm:"default:false"`
	CreatedAt    string
	UpdatedAt    string
	DeletedAt    string `gorm:"index"`
}

type UserRole struct {
	ID        string `gorm:"primarykey;type:varchar(255)"`
	UserID    uint   `gorm:"index;not null"`
	Role      string `gorm:"type:varchar(50);not null"`
	CreatedAt string
}

type Memo struct {
	ID           string `gorm:"primarykey;type:varchar(255)"`
	UserID       uint   `gorm:"index;not null"`
	Content      string `gorm:"type:text;not null"`
	Tags         string `gorm:"type:json"`
	ReminderDate string
	CreatedAt    string
	UpdatedAt    string
}

type ShortURL struct {
	ID          string `gorm:"primarykey;type:varchar(255)"`
	UserID      *uint  `gorm:"index"`
	OriginalURL string `gorm:"type:text;not null"`
	ShortCode   string `gorm:"type:varchar(50);uniqueIndex;not null"`
	Clicks      int64  `gorm:"default:0"`
	ExpiresAt   string
	CreatedAt   string
}

type Todo struct {
	ID        string `gorm:"primarykey;type:varchar(255)"`
	UserID    uint   `gorm:"index;not null"`
	Title     string `gorm:"type:varchar(512);not null"`
	Completed bool   `gorm:"default:false"`
	DueDate   string
	Priority  string `gorm:"type:varchar(20)"`
	CreatedAt string
	UpdatedAt string
}

type TempEmail struct {
	ID           string `gorm:"primarykey;type:varchar(255)"`
	UserID       uint   `gorm:"index;not null"`
	EmailAddress string `gorm:"type:varchar(255);uniqueIndex;not null"`
	ExpiresAt    string
	CreatedAt    string
}

type ReceivedEmail struct {
	ID          string `gorm:"primarykey;type:varchar(255)"`
	TempEmailID string `gorm:"index;not null"`
	FromAddress string `gorm:"type:varchar(255);not null"`
	Subject     string `gorm:"type:varchar(512)"`
	Body        string `gorm:"type:text"`
	ReceivedAt  string
}

type BannerConfig struct {
	ID             string `gorm:"primarykey;type:varchar(255)"`
	FirstText      string `gorm:"type:varchar(255);not null"`
	SecondText     string `gorm:"type:varchar(255);not null"`
	FirstGradient  string `gorm:"type:varchar(100);not null"`
	SecondGradient string `gorm:"type:varchar(100);not null"`
	Height         *int
	CreatedAt      string
	UpdatedAt      string
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run generate_sql.go <database_type>")
		fmt.Println("支持的数据库类型: sqlite, mysql, postgres")
		os.Exit(1)
	}

	dbType := strings.ToLower(os.Args[1])
	
	// 创建输出目录
	outputDir := "sql_scripts"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("创建输出目录失败: %v", err)
	}

	switch dbType {
	case "sqlite":
		generateSQLiteSQL(outputDir)
	case "mysql":
		generateMySQLSQL(outputDir)
	case "postgres", "postgresql":
		generatePostgreSQLSQL(outputDir)
	default:
		log.Fatalf("不支持的数据库类型: %s", dbType)
	}

	fmt.Printf("SQL 脚本已生成到 %s 目录\n", outputDir)
}

func generateSQLiteSQL(outputDir string) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		DryRun: true,
	})
	if err != nil {
		log.Fatalf("创建 SQLite 连接失败: %v", err)
	}

	generateSQL(db, "sqlite", outputDir)
}

func generateMySQLSQL(outputDir string) {
	// 使用虚拟连接字符串，因为我们只需要生成 SQL
	db, err := gorm.Open(mysql.New(mysql.Config{
		DSN:                       "user:pass@tcp(localhost:3306)/dbname?charset=utf8mb4&parseTime=True&loc=Local",
		DisableDatetimePrecision:  true,
		DontSupportRenameIndex:    true,
		DontSupportRenameColumn:   true,
		SkipInitializeWithVersion: false,
	}), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		DryRun: true,
	})
	if err != nil {
		log.Fatalf("创建 MySQL 连接失败: %v", err)
	}

	generateSQL(db, "mysql", outputDir)
}

func generatePostgreSQLSQL(outputDir string) {
	db, err := gorm.Open(postgres.New(postgres.Config{
		DSN:                  "host=localhost user=user password=pass dbname=dbname port=5432 sslmode=disable",
		PreferSimpleProtocol: true,
	}), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		DryRun: true,
	})
	if err != nil {
		log.Fatalf("创建 PostgreSQL 连接失败: %v", err)
	}

	generateSQL(db, "postgres", outputDir)
}

func generateSQL(db *gorm.DB, dbType, outputDir string) {
	models := []interface{}{
		&User{},
		&UserRole{},
		&Memo{},
		&ShortURL{},
		&Todo{},
		&TempEmail{},
		&ReceivedEmail{},
		&BannerConfig{},
	}

	var allSQL []string
	var createTableSQL []string
	var indexSQL []string

	// 生成建表语句
	for _, model := range models {
		stmt := &gorm.Statement{DB: db}
		stmt.Parse(model)
		
		// 生成 CREATE TABLE 语句
		sql := db.Migrator().CreateTableSQL(model)
		if sql != "" {
			createTableSQL = append(createTableSQL, sql)
			allSQL = append(allSQL, sql)
		}
	}

	// 生成索引语句
	indexes := getIndexStatements(dbType)
	indexSQL = append(indexSQL, indexes...)
	allSQL = append(allSQL, indexes...)

	// 生成初始数据插入语句
	insertSQL := getInsertStatements(dbType)
	allSQL = append(allSQL, insertSQL...)

	// 写入完整的 SQL 文件
	writeToFile(filepath.Join(outputDir, fmt.Sprintf("complete_%s.sql", dbType)), allSQL)
	
	// 写入分类的 SQL 文件
	writeToFile(filepath.Join(outputDir, fmt.Sprintf("tables_%s.sql", dbType)), createTableSQL)
	writeToFile(filepath.Join(outputDir, fmt.Sprintf("indexes_%s.sql", dbType)), indexSQL)
	writeToFile(filepath.Join(outputDir, fmt.Sprintf("data_%s.sql", dbType)), insertSQL)

	fmt.Printf("已生成 %s SQL 脚本\n", dbType)
}

func getIndexStatements(dbType string) []string {
	var indexes []string

	switch dbType {
	case "sqlite":
		indexes = []string{
			"CREATE INDEX IF NOT EXISTS idx_memos_user_created ON memos(user_id, created_at DESC);",
			"CREATE INDEX IF NOT EXISTS idx_short_urls_user_created ON short_urls(user_id, created_at DESC);",
			"CREATE INDEX IF NOT EXISTS idx_todos_user_completed ON todos(user_id, completed, created_at DESC);",
			"CREATE INDEX IF NOT EXISTS idx_temp_emails_expires ON temp_emails(expires_at);",
			"CREATE INDEX IF NOT EXISTS idx_received_emails_temp_received ON received_emails(temp_email_id, received_at DESC);",
			"CREATE INDEX IF NOT EXISTS idx_short_urls_expires ON short_urls(expires_at);",
		}
	case "mysql":
		indexes = []string{
			"CREATE INDEX idx_memos_user_created ON memos(user_id, created_at DESC);",
			"CREATE INDEX idx_short_urls_user_created ON short_urls(user_id, created_at DESC);",
			"CREATE INDEX idx_todos_user_completed ON todos(user_id, completed, created_at DESC);",
			"CREATE INDEX idx_temp_emails_expires ON temp_emails(expires_at);",
			"CREATE INDEX idx_received_emails_temp_received ON received_emails(temp_email_id, received_at DESC);",
			"CREATE INDEX idx_short_urls_expires ON short_urls(expires_at);",
		}
	case "postgres":
		indexes = []string{
			"CREATE INDEX IF NOT EXISTS idx_memos_user_created ON memos(user_id, created_at DESC);",
			"CREATE INDEX IF NOT EXISTS idx_short_urls_user_created ON short_urls(user_id, created_at DESC);",
			"CREATE INDEX IF NOT EXISTS idx_todos_user_completed ON todos(user_id, completed, created_at DESC);",
			"CREATE INDEX IF NOT EXISTS idx_temp_emails_expires ON temp_emails(expires_at);",
			"CREATE INDEX IF NOT EXISTS idx_received_emails_temp_received ON received_emails(temp_email_id, received_at DESC);",
			"CREATE INDEX IF NOT EXISTS idx_short_urls_expires ON short_urls(expires_at);",
		}
	}

	return indexes
}

func getInsertStatements(dbType string) []string {
	var inserts []string

	// 默认横幅配置
	switch dbType {
	case "sqlite", "postgres":
		inserts = append(inserts, `
INSERT OR IGNORE INTO banner_configs (id, first_text, second_text, first_gradient, second_gradient, height, created_at, updated_at)
VALUES ('default', '欢迎使用', 'URL Stash Vault', 'from-blue-600 to-purple-600', 'from-purple-600 to-pink-600', 200, datetime('now'), datetime('now'));`)
	case "mysql":
		inserts = append(inserts, `
INSERT IGNORE INTO banner_configs (id, first_text, second_text, first_gradient, second_gradient, height, created_at, updated_at)
VALUES ('default', '欢迎使用', 'URL Stash Vault', 'from-blue-600 to-purple-600', 'from-purple-600 to-pink-600', 200, NOW(), NOW());`)
	}

	return inserts
}

func writeToFile(filename string, lines []string) {
	file, err := os.Create(filename)
	if err != nil {
		log.Fatalf("创建文件失败 %s: %v", filename, err)
	}
	defer file.Close()

	// 写入文件头注释
	fmt.Fprintf(file, "-- URL Stash Vault 数据库脚本\n")
	fmt.Fprintf(file, "-- 生成时间: %s\n", "自动生成")
	fmt.Fprintf(file, "-- 数据库类型: %s\n\n", filepath.Base(filename))

	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			fmt.Fprintf(file, "%s\n\n", line)
		}
	}

	fmt.Printf("已写入文件: %s\n", filename)
} 