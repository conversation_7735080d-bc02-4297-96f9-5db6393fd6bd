# Stage 1: Build the Go application
FROM golang:1.21-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum first to leverage Docker cache for dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy the rest of the backend application code
COPY . .

# Build the application. 
# CGO_ENABLED=0 for a static binary (good for scratch, but alpine needs some libs if not fully static)
# -ldflags "-w -s" to strip debug information and reduce binary size
RUN CGO_ENABLED=0 GOOS=linux go build -a -ldflags '-w -s' -o /app/server ./cmd/server/main.go

# Stage 2: Create a minimal production image
FROM alpine:latest

WORKDIR /app

# Copy the built binary from the builder stage
COPY --from=builder /app/server /app/server

# Copy the .env file if you want to package it (not always recommended for production secrets)
# For production, it's better to manage .env via Docker volumes or environment variables at runtime.
# COPY .env .env 

# Expose the port the app runs on (must match SERVER_PORT in .env)
EXPOSE 8080 

# Command to run the application
# The .env file needs to be present in the WORKDIR or configuration passed as environment variables.
CMD ["/app/server"] 