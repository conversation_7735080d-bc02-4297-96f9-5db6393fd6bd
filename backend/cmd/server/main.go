package main

import (
	"log"
	"url-stash-vault/internal/api"
	"url-stash-vault/internal/auth"
	"url-stash-vault/internal/cache"
	"url-stash-vault/internal/config"
	"url-stash-vault/internal/database"
	"url-stash-vault/internal/email"
	"url-stash-vault/internal/shorturl"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("无法加载配置: %v", err)
	}

	// 初始化认证模块
	auth.InitAuth(cfg)

	// 初始化数据库连接
	err = database.Initialize(cfg)
	if err != nil {
		log.Fatalf("无法初始化数据库: %v", err)
	}
	defer database.Close()

	// 初始化 Redis 缓存
	cache.InitRedis(*cfg)

	// 初始化邮件服务
	emailService := email.NewEmailService(cfg)

	// 初始化服务
	authService := auth.NewAuthService(cfg, database.GetDB(), emailService)
	shortURLService := shorturl.NewService(database.GetDB())

	// 设置 Gin 引擎
	router := api.SetupRouter(cfg, authService, shortURLService)

	// 启动服务器
	serverAddr := cfg.Server.Host + ":" + cfg.Server.Port
	log.Printf("服务器正在启动，监听地址 %s", serverAddr)
	if err := router.Run(serverAddr); err != nil {
		log.Fatalf("无法启动服务器: %v", err)
	}
}
