#!/bin/bash

APP_NAME="url-stash-vault-server"
APP_MAIN="cmd/server/main.go"
DB_SOURCE="./data/app.db"
REDIS_ENABLED="false"
LOG_FILE="server.log"
PID_FILE="server.pid"
LISTEN_PORT="8080"

# Function to get absolute path of the script directory
get_script_dir() {
  SOURCE="${BASH_SOURCE[0]}"
  while [ -h "$SOURCE" ]; do # resolve $SOURCE until the file is no longer a symlink
    DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
    SOURCE="$(readlink "$SOURCE")"
    [[ $SOURCE != /* ]] && SOURCE="$DIR/$SOURCE" # if $SOURCE was a relative symlink, we need to resolve it relative to the path where the symlink file was located
  done
  DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
  echo "$DIR"
}

SCRIPT_DIR=$(get_script_dir)

# Ensure paths are relative to the script directory
DB_SOURCE_ABS="$SCRIPT_DIR/$DB_SOURCE"
LOG_FILE_ABS="$SCRIPT_DIR/$LOG_FILE"
PID_FILE_ABS="$SCRIPT_DIR/$PID_FILE"
APP_MAIN_ABS="$SCRIPT_DIR/$APP_MAIN"

# Function to kill process using the port
kill_process_on_port() {
  local port=$1
  echo "检查端口 $port 是否被占用..."
  # macOS and Linux compatible way to find PID on port
  local pids_on_port=$(lsof -t -i:$port -sTCP:LISTEN)

  if [ -n "$pids_on_port" ]; then
    echo "端口 $port 被以下 PID 占用: $pids_on_port"
    for pid_to_kill in $pids_on_port; do
      # 避免杀死脚本自身或父进程 (如果脚本恰好以某种方式占用了端口)
      if [ "$pid_to_kill" -eq "$$" ] || [ "$pid_to_kill" -eq "$PPID" ]; then
        echo "跳过杀死脚本自身/父进程 PID: $pid_to_kill"
        continue
      fi
      echo "尝试停止占用端口 $port 的进程 PID: $pid_to_kill ..."
      kill $pid_to_kill
      sleep 1
      if ps -p $pid_to_kill > /dev/null; then
        echo "未能通过 kill 停止进程 $pid_to_kill。尝试 kill -9 ..."
        kill -9 $pid_to_kill
        sleep 1
      fi
      if ps -p $pid_to_kill > /dev/null; then
        echo "错误: 无法停止占用端口 $port 的进程 PID: $pid_to_kill。请手动处理。"
        return 1 # Indicate failure
      else
        echo "进程 PID: $pid_to_kill 已成功停止。"
      fi
    done
  else
    echo "端口 $port 未被占用。"
  fi
  return 0 # Indicate success
}

start() {
  if [ -f "$PID_FILE_ABS" ]; then
    PID=$(cat "$PID_FILE_ABS")
    if ps -p $PID > /dev/null; then
      echo "服务已经在运行，PID: $PID"
      return
    else
      echo "警告: PID 文件存在但进程未运行。删除旧的 PID 文件。"
      rm -f "$PID_FILE_ABS"
    fi
  fi

  # 检查并停止占用端口的进程
  kill_process_on_port $LISTEN_PORT
  if [ $? -ne 0 ]; then
      echo "端口 $LISTEN_PORT 清理失败，无法启动服务。"
      return 1
  fi

  echo "启动 Go 后端 ($APP_NAME) ..."
  cd "$SCRIPT_DIR" # Ensure we are in the backend directory
  nohup env REDIS_ENABLED=$REDIS_ENABLED DB_SOURCE=$DB_SOURCE_ABS go run "$APP_MAIN_ABS" >> "$LOG_FILE_ABS" 2>&1 &
  echo $! > "$PID_FILE_ABS"
  sleep 1 # Give it a moment to start
  if [ -f "$PID_FILE_ABS" ]; then
      PID=$(cat "$PID_FILE_ABS")
      if ps -p $PID > /dev/null; then
        echo "服务已启动，PID: $PID。日志文件: $LOG_FILE_ABS"
      else
        echo "服务启动失败。请检查日志: $LOG_FILE_ABS"
        rm -f "$PID_FILE_ABS"
      fi
  else
      echo "服务启动失败，未能创建 PID 文件。请检查日志: $LOG_FILE_ABS"
  fi
}

stop() {
  if [ -f "$PID_FILE_ABS" ]; then
    PID=$(cat "$PID_FILE_ABS")
    echo "停止进程 $PID ..."
    if ps -p $PID > /dev/null; then
      kill $PID
      # Wait for process to terminate
      for i in {1..10}; do # Wait up to 10 seconds
        if ! ps -p $PID > /dev/null; then
          rm -f "$PID_FILE_ABS"
          echo "服务已停止。"
          return
        fi
        sleep 1
      done
      echo "警告: 进程 $PID 可能未能完全停止。尝试强制终止..."
      kill -9 $PID
      sleep 1 # Give SIGKILL a moment
      if ! ps -p $PID > /dev/null; then # Check again after SIGKILL
         rm -f "$PID_FILE_ABS"
         echo "服务已强制停止。"
      else
         echo "错误: 无法停止进程 $PID。请手动检查。"
      fi
    else
      echo "警告: PID 文件存在但进程未运行。删除旧的 PID 文件。"
      rm -f "$PID_FILE_ABS"
    fi
  else
    echo "服务未运行 (未找到 PID 文件)。"
  fi
}

restart() {
  echo "重启服务 ..."
  stop
  sleep 2 # Give it a moment to fully stop
  start
}

status() {
  if [ -f "$PID_FILE_ABS" ]; then
    PID=$(cat "$PID_FILE_ABS")
    if ps -p $PID > /dev/null; then
      echo "服务 ($APP_NAME) 正在运行，PID: $PID"
      echo "监听端口: $LISTEN_PORT"
      echo "日志文件: $LOG_FILE_ABS"
    else
      echo "服务 ($APP_NAME) 未运行 (PID 文件存在但进程不存在)。"
    fi
  else
    echo "服务 ($APP_NAME) 未运行 (未找到 PID 文件)。"
  fi
}

logs() {
    echo "显示最近 20 行日志 ($LOG_FILE_ABS):"
    if [ -f "$LOG_FILE_ABS" ]; then
        tail -n 20 "$LOG_FILE_ABS"
    else
        echo "错误: 日志文件未找到。"
    fi
}

show_menu() {
    echo "------------------------------------"
    echo " 后端服务管理脚本 ($APP_NAME)"
    echo "------------------------------------"
    echo "脚本目录: $SCRIPT_DIR"
    echo "PID 文件: $PID_FILE_ABS"
    echo "日志文件: $LOG_FILE_ABS"
    echo "监听端口: $LISTEN_PORT"
    echo "------------------------------------"
    echo "1. 启动服务 (Start)"
    echo "2. 停止服务 (Stop)"
    echo "3. 重启服务 (Restart)"
    echo "4. 查看状态 (Status)"
    echo "5. 查看日志 (View Logs)"
    echo "0. 退出 (Exit)"
    echo "------------------------------------"
}

if [ "$1" == "" ]; then
    while true; do
        show_menu
        read -p "请输入选项 [0-5]: " choice
        echo ""
        case $choice in
            1) start ;;
            2) stop ;;
            3) restart ;;
            4) status ;;
            5) logs ;;
            0) echo "退出脚本。"; exit 0 ;;
            *) echo "无效选项，请输入 0 到 5 之间的数字。" ;;
        esac
        echo ""
        read -n 1 -s -r -p "按任意键返回菜单..."
        clear #或者使用 echo -e "\033c" 清屏
    done
else
    case "$1" in
      start) start ;;
      stop) stop ;;
      restart) restart ;;
      status) status ;;
      logs) logs ;;
      *) echo "用法: $0 {start|stop|restart|status|logs} 或运行不带参数的 $0 进入交互模式" ; exit 1;;
    esac
fi

