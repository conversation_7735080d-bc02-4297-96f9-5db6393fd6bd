package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"url-stash-vault/internal/config"

	"github.com/go-redis/redis/v8"
)

var Rdb *redis.Client

// InitRedis 初始化 Redis 连接
func InitRedis(cfg config.Config) *redis.Client {
	if !cfg.Redis.Enabled || cfg.Redis.Addr == "" {
		log.Println("Redis 未启用或未配置地址，跳过 Redis 初始化。")
		return nil
	}

	Rdb = redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Addr,
		Password: cfg.Redis.Password, // no password set
		DB:       cfg.Redis.DB,       // use default DB
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := Rdb.Ping(ctx).Result()
	if err != nil {
		log.Printf("无法连接到 Redis: %v. 缓存将不可用。", err)
		return nil // 或者根据策略 panic
	}

	log.Println("成功连接到 Redis.")
	return Rdb
}

// JWT Token 缓存相关
const (
	JWTTokenPrefix = "jwt_token:"
	ShortURLPrefix = "short_url:"
	DefaultShortURLExpiry = 30 * 24 * time.Hour // 30天
)

// SetJWTTokenByUsername 在 Redis 中根据用户名存储 JWT token（每个用户最多一个token）
func SetJWTTokenByUsername(ctx context.Context, username string, token string, expiration time.Duration) error {
	if Rdb == nil {
		return nil // Redis 未启用，直接返回
	}

	key := fmt.Sprintf("%s%s", JWTTokenPrefix, username)
	return Rdb.Set(ctx, key, token, expiration).Err()
}

// GetJWTTokenByUsername 从 Redis 中根据用户名获取 JWT token
func GetJWTTokenByUsername(ctx context.Context, username string) (string, error) {
	if Rdb == nil {
		return "", nil // Redis 未启用，返回空字符串
	}

	key := fmt.Sprintf("%s%s", JWTTokenPrefix, username)
	val, err := Rdb.Get(ctx, key).Result()
	if err == redis.Nil {
		return "", nil // Key 不存在
	}
	return val, err
}

// DeleteJWTTokenByUsername 从 Redis 中根据用户名删除 JWT token
func DeleteJWTTokenByUsername(ctx context.Context, username string) error {
	if Rdb == nil {
		return nil // Redis 未启用，直接返回
	}

	key := fmt.Sprintf("%s%s", JWTTokenPrefix, username)
	return Rdb.Del(ctx, key).Err()
}

// 保留旧的方法以兼容现有代码，但标记为废弃
// SetJWTToken 在 Redis 中存储 JWT token（废弃：请使用 SetJWTTokenByUsername）
func SetJWTToken(ctx context.Context, userID uint, token string, expiration time.Duration) error {
	if Rdb == nil {
		return nil // Redis 未启用，直接返回
	}

	key := fmt.Sprintf("%s%d", JWTTokenPrefix, userID)
	return Rdb.Set(ctx, key, token, expiration).Err()
}

// GetJWTToken 从 Redis 中获取 JWT token（废弃：请使用 GetJWTTokenByUsername）
func GetJWTToken(ctx context.Context, userID uint) (string, error) {
	if Rdb == nil {
		return "", nil // Redis 未启用，返回空字符串
	}

	key := fmt.Sprintf("%s%d", JWTTokenPrefix, userID)
	val, err := Rdb.Get(ctx, key).Result()
	if err == redis.Nil {
		return "", nil // Key 不存在
	}
	return val, err
}

// DeleteJWTToken 从 Redis 中删除 JWT token（废弃：请使用 DeleteJWTTokenByUsername）
func DeleteJWTToken(ctx context.Context, userID uint) error {
	if Rdb == nil {
		return nil // Redis 未启用，直接返回
	}

	key := fmt.Sprintf("%s%d", JWTTokenPrefix, userID)
	return Rdb.Del(ctx, key).Err()
}

// ShortURLData 短链在Redis中存储的数据结构
type ShortURLData struct {
	OriginalURL string    `json:"original_url"`
	UserID      *uint     `json:"user_id,omitempty"`
	Clicks      int64     `json:"clicks"`
	CreatedAt   time.Time `json:"created_at"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// SetShortURL 在 Redis 中存储短链数据
func SetShortURL(ctx context.Context, shortCode string, data ShortURLData, customExpiry *time.Time) error {
	if Rdb == nil {
		return nil // Redis 未启用，直接返回
	}

	key := fmt.Sprintf("%s%s", ShortURLPrefix, shortCode)

	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化短链数据失败: %w", err)
	}

	// 计算过期时间
	var expiration time.Duration
	if customExpiry != nil && !customExpiry.IsZero() {
		// 如果有自定义过期时间，计算距离现在的时间差
		expiration = time.Until(*customExpiry)
		if expiration <= 0 {
			// 如果已经过期，设置为1秒后过期
			expiration = time.Second
		}
	} else {
		// 如果是永久链接，设置为30天过期
		expiration = DefaultShortURLExpiry
	}

	return Rdb.Set(ctx, key, jsonData, expiration).Err()
}

// GetShortURL 从 Redis 中获取短链数据
func GetShortURL(ctx context.Context, shortCode string) (*ShortURLData, error) {
	if Rdb == nil {
		return nil, nil // Redis 未启用，返回 nil
	}

	key := fmt.Sprintf("%s%s", ShortURLPrefix, shortCode)
	val, err := Rdb.Get(ctx, key).Result()
	if err == redis.Nil {
		return nil, nil // Key 不存在
	}
	if err != nil {
		return nil, err
	}

	// 反序列化数据
	var data ShortURLData
	if err := json.Unmarshal([]byte(val), &data); err != nil {
		return nil, fmt.Errorf("反序列化短链数据失败: %w", err)
	}

	return &data, nil
}

// IncrementShortURLClicks 增加短链点击次数
func IncrementShortURLClicks(ctx context.Context, shortCode string) error {
	if Rdb == nil {
		return nil // Redis 未启用，直接返回
	}

	// 获取当前数据
	data, err := GetShortURL(ctx, shortCode)
	if err != nil || data == nil {
		return err // 如果获取失败或数据不存在，返回错误
	}

	// 增加点击次数
	data.Clicks++

	// 重新设置数据，保持原有的过期时间
	return SetShortURL(ctx, shortCode, *data, data.ExpiresAt)
}

// RefreshShortURLExpiry 刷新短链在Redis中的过期时间（用于永久链接）
func RefreshShortURLExpiry(ctx context.Context, shortCode string) error {
	if Rdb == nil {
		return nil // Redis 未启用，直接返回
	}

	// 获取当前数据
	data, err := GetShortURL(ctx, shortCode)
	if err != nil || data == nil {
		return err // 如果获取失败或数据不存在，返回错误
	}

	// 如果是永久链接（ExpiresAt 为 nil），刷新 Redis 过期时间为30天
	if data.ExpiresAt == nil {
		key := fmt.Sprintf("%s%s", ShortURLPrefix, shortCode)
		return Rdb.Expire(ctx, key, DefaultShortURLExpiry).Err()
	}

	return nil
}

// DeleteShortURL 从 Redis 中删除短链数据
func DeleteShortURL(ctx context.Context, shortCode string) error {
	if Rdb == nil {
		return nil // Redis 未启用，直接返回
	}

	key := fmt.Sprintf("%s%s", ShortURLPrefix, shortCode)
	return Rdb.Del(ctx, key).Err()
}

// IsRedisEnabled 检查 Redis 是否启用
func IsRedisEnabled() bool {
	return Rdb != nil
}

// Example usage:
// func SetValue(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
// 	 if Rdb == nil {
// 		 return errors.New("redis client not initialized")
// 	 }
// 	 return Rdb.Set(ctx, key, value, expiration).Err()
// }

// func GetValue(ctx context.Context, key string) (string, error) {
// 	 if Rdb == nil {
// 		 return "", errors.New("redis client not initialized")
// 	 }
// 	 val, err := Rdb.Get(ctx, key).Result()
// 	 if err == redis.Nil {
// 		 return "", nil // Key does not exist
// 	 }
// 	 return val, err
// }