package middleware

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	
	"url-stash-vault/internal/auth"
)

const (
	AuthorizationHeader = "Authorization"
	BearerPrefix       = "Bearer "
	UserIDKey          = "user_id"
	UserEmailKey       = "user_email"
	UserClaimsKey      = "user_claims"
)

type AuthMiddleware struct {
	authService *auth.AuthService
}

func NewAuthMiddleware(authService *auth.AuthService) *AuthMiddleware {
	return &AuthMiddleware{
		authService: authService,
	}
}

// RequireAuth 要求用户认证的中间件
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "缺少认证令牌",
				"code":  "MISSING_TOKEN",
			})
			c.Abort()
			return
		}

		claims, err := m.authService.ValidateToken(token)
		if err != nil {
			var statusCode int
			var errorCode string

			switch err {
			case auth.ErrTokenExpired:
				statusCode = http.StatusUnauthorized
				errorCode = "TOKEN_EXPIRED"
			case auth.ErrInvalidToken:
				statusCode = http.StatusUnauthorized
				errorCode = "INVALID_TOKEN"
			case auth.ErrUserNotFound:
				statusCode = http.StatusUnauthorized
				errorCode = "USER_NOT_FOUND"
			default:
				statusCode = http.StatusInternalServerError
				errorCode = "AUTH_ERROR"
			}

			c.JSON(statusCode, gin.H{
				"error": err.Error(),
				"code":  errorCode,
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set(UserIDKey, claims.UserID)
		c.Set(UserEmailKey, claims.Email)
		c.Set(UserClaimsKey, claims)

		c.Next()
	}
}

// RequirePermission 要求特定权限的中间件
func (m *AuthMiddleware) RequirePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get(UserIDKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "用户未认证",
				"code":  "NOT_AUTHENTICATED",
			})
			c.Abort()
			return
		}

		if err := m.authService.CheckPermission(userID.(uint), resource, action); err != nil {
			if err == auth.ErrInsufficientPermissions {
				c.JSON(http.StatusForbidden, gin.H{
					"error": "权限不足",
					"code":  "INSUFFICIENT_PERMISSIONS",
				})
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "权限检查失败",
					"code":  "PERMISSION_CHECK_ERROR",
				})
			}
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireResourceOwnership 要求资源所有权的中间件
// resourceUserIDParam 是 URL 参数中包含资源所有者 ID 的参数名
func (m *AuthMiddleware) RequireResourceOwnership(resourceUserIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get(UserIDKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "用户未认证",
				"code":  "NOT_AUTHENTICATED",
			})
			c.Abort()
			return
		}

		// 从 URL 参数或请求体中获取资源所有者 ID
		var resourceUserID uint

		if resourceUserIDParam != "" {
			// 从 URL 参数获取
			resourceUserIDStr := c.Param(resourceUserIDParam)
			if resourceUserIDStr == "" {
				resourceUserIDStr = c.Query(resourceUserIDParam)
			}
			
			if resourceUserIDStr != "" {
				resourceUserIDInt, parseErr := strconv.ParseUint(resourceUserIDStr, 10, 32)
				if parseErr != nil {
					c.JSON(http.StatusBadRequest, gin.H{
						"error": "无效的用户 ID",
						"code":  "INVALID_USER_ID",
					})
					c.Abort()
					return
				}
				resourceUserID = uint(resourceUserIDInt)
			} else {
				// 如果没有指定资源所有者，默认为当前用户
				resourceUserID = userID.(uint)
			}
		} else {
			// 默认检查当前用户
			resourceUserID = userID.(uint)
		}

		if err := m.authService.CheckResourceOwnership(userID.(uint), resourceUserID); err != nil {
			if err == auth.ErrInsufficientPermissions {
				c.JSON(http.StatusForbidden, gin.H{
					"error": "无权访问此资源",
					"code":  "RESOURCE_ACCESS_DENIED",
				})
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "资源所有权检查失败",
					"code":  "OWNERSHIP_CHECK_ERROR",
				})
			}
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin 要求管理员权限的中间件
func (m *AuthMiddleware) RequireAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get(UserClaimsKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "用户未认证",
				"code":  "NOT_AUTHENTICATED",
			})
			c.Abort()
			return
		}

		userClaims := claims.(*auth.Claims)
		
		// 检查是否是超级管理员或拥有管理员角色
		isAdmin := userClaims.IsSuperAdmin
		if !isAdmin {
			for _, role := range userClaims.Roles {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}

		if !isAdmin {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "需要管理员权限",
				"code":  "ADMIN_REQUIRED",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuth 可选认证中间件（不强制要求认证）
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token == "" {
			c.Next()
			return
		}

		claims, err := m.authService.ValidateToken(token)
		if err != nil {
			// 令牌无效时不阻止请求，但不设置用户信息
			c.Next()
			return
		}

		// 设置用户信息
		c.Set(UserIDKey, claims.UserID)
		c.Set(UserEmailKey, claims.Email)
		c.Set(UserClaimsKey, claims)

		c.Next()
	}
}

// RateLimitByUser 按用户限流中间件
func (m *AuthMiddleware) RateLimitByUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以实现基于用户的限流逻辑
		// 例如使用 Redis 存储用户请求计数
		
		userID, exists := c.Get(UserIDKey)
		if exists {
			// 实现用户级别的限流
			_ = userID // 避免未使用变量警告
			// TODO: 实现限流逻辑
		}

		c.Next()
	}
}

// CORS 中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 允许的源列表（应该从配置中读取）
		allowedOrigins := []string{
			"http://localhost:3000",
			"http://localhost:8080",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:8080",
		}

		// 检查是否是允许的源
		isAllowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				isAllowed = true
				break
			}
		}

		if isAllowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// SecurityHeaders 安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		
		c.Next()
	}
}

// RequestLogger 请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
		)
	})
}

// 工具函数

// extractToken 从请求头中提取 JWT 令牌
func extractToken(c *gin.Context) string {
	authHeader := c.GetHeader(AuthorizationHeader)
	if authHeader == "" {
		return ""
	}

	if !strings.HasPrefix(authHeader, BearerPrefix) {
		return ""
	}

	return strings.TrimPrefix(authHeader, BearerPrefix)
}

// GetCurrentUserID 从上下文中获取当前用户 ID
func GetCurrentUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		return 0, false
	}
	return userID.(uint), true
}

// GetCurrentUserEmail 从上下文中获取当前用户邮箱
func GetCurrentUserEmail(c *gin.Context) (string, bool) {
	email, exists := c.Get(UserEmailKey)
	if !exists {
		return "", false
	}
	return email.(string), true
}

// GetCurrentUserClaims 从上下文中获取当前用户的完整声明
func GetCurrentUserClaims(c *gin.Context) (*auth.Claims, bool) {
	claims, exists := c.Get(UserClaimsKey)
	if !exists {
		return nil, false
	}
	return claims.(*auth.Claims), true
}

// IsCurrentUserAdmin 检查当前用户是否是管理员
func IsCurrentUserAdmin(c *gin.Context) bool {
	claims, exists := GetCurrentUserClaims(c)
	if !exists {
		return false
	}

	if claims.IsSuperAdmin {
		return true
	}

	for _, role := range claims.Roles {
		if role == "admin" {
			return true
		}
	}

	return false
} 