package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"

	"url-stash-vault/internal/config"
)

var (
	Client *redis.Client
	ctx    = context.Background()
)

// RedisService Redis 缓存服务
type RedisService struct {
	client  *redis.Client
	enabled bool
}

// CachedShortURL 缓存的短链数据结构
type CachedShortURL struct {
	ID          string     `json:"id"`
	UserID      *uint      `json:"user_id"`
	OriginalURL string     `json:"original_url"`
	ShortCode   string     `json:"short_code"`
	Clicks      int64      `json:"clicks"`
	ExpiresAt   *time.Time `json:"expires_at"`
	CreatedAt   time.Time  `json:"created_at"`
}

// CachedJWT 缓存的 JWT 信息
type CachedJWT struct {
	UserID       uint      `json:"user_id"`
	Email        string    `json:"email"`
	IsSuperAdmin bool      `json:"is_super_admin"`
	Roles        []string  `json:"roles"`
	ExpiresAt    time.Time `json:"expires_at"`
}

const (
	// Redis 键前缀
	JWTPrefix      = "jwt:"
	ShortURLPrefix = "shorturl:"
	UserPrefix     = "user:"

	// 默认过期时间
	DefaultShortURLTTL = 30 * 24 * time.Hour // 30天
	DefaultJWTTTL      = 24 * time.Hour      // 24小时
	MaxShortURLTTL     = 30 * 24 * time.Hour // 最大30天
)

// Initialize 初始化 Redis 连接
func Initialize(cfg *config.Config) (*RedisService, error) {
	if !cfg.Redis.Enabled {
		log.Println("Redis 未启用，缓存功能将不可用")
		return &RedisService{enabled: false}, nil
	}

	// 创建 Redis 客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Addr,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 测试连接
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Printf("Redis 连接失败: %v", err)
		return &RedisService{enabled: false}, nil
	}

	Client = rdb
	log.Printf("Redis 连接成功: %s", cfg.Redis.Addr)

	return &RedisService{
		client:  rdb,
		enabled: true,
	}, nil
}

// NewRedisService 创建新的 Redis 服务实例
func NewRedisService(client *redis.Client, enabled bool) *RedisService {
	return &RedisService{
		client:  client,
		enabled: enabled,
	}
}

// IsEnabled 检查 Redis 是否启用
func (r *RedisService) IsEnabled() bool {
	return r.enabled && r.client != nil
}

// --- JWT 缓存方法 ---

// SetJWT 缓存 JWT 信息
func (r *RedisService) SetJWT(token string, jwtData CachedJWT) error {
	if !r.IsEnabled() {
		return nil // Redis 未启用，静默跳过
	}

	key := JWTPrefix + token
	data, err := json.Marshal(jwtData)
	if err != nil {
		return fmt.Errorf("序列化 JWT 数据失败: %w", err)
	}

	// 计算过期时间
	ttl := time.Until(jwtData.ExpiresAt)
	if ttl <= 0 {
		return nil // 已过期，不缓存
	}

	err = r.client.Set(ctx, key, data, ttl).Err()
	if err != nil {
		log.Printf("缓存 JWT 失败: %v", err)
		return err
	}

	log.Printf("JWT 已缓存: %s, TTL: %v", token[:10]+"...", ttl)
	return nil
}

// GetJWT 获取缓存的 JWT 信息
func (r *RedisService) GetJWT(token string) (*CachedJWT, error) {
	if !r.IsEnabled() {
		return nil, nil // Redis 未启用，返回 nil
	}

	key := JWTPrefix + token
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		log.Printf("获取 JWT 缓存失败: %v", err)
		return nil, err
	}

	var jwtData CachedJWT
	if err := json.Unmarshal([]byte(data), &jwtData); err != nil {
		log.Printf("反序列化 JWT 数据失败: %v", err)
		// 删除损坏的缓存
		r.client.Del(ctx, key)
		return nil, err
	}

	// 检查是否过期
	if time.Now().After(jwtData.ExpiresAt) {
		r.client.Del(ctx, key)
		return nil, nil
	}

	return &jwtData, nil
}

// DeleteJWT 删除 JWT 缓存（用于登出）
func (r *RedisService) DeleteJWT(token string) error {
	if !r.IsEnabled() {
		return nil
	}

	key := JWTPrefix + token
	return r.client.Del(ctx, key).Err()
}

// --- 短链缓存方法 ---

// SetShortURL 缓存短链信息
func (r *RedisService) SetShortURL(shortCode string, urlData CachedShortURL) error {
	if !r.IsEnabled() {
		return nil
	}

	key := ShortURLPrefix + shortCode
	data, err := json.Marshal(urlData)
	if err != nil {
		return fmt.Errorf("序列化短链数据失败: %w", err)
	}

	// 计算过期时间
	var ttl time.Duration
	if urlData.ExpiresAt != nil {
		// 有设置过期时间的短链
		ttl = time.Until(*urlData.ExpiresAt)
		if ttl <= 0 {
			return nil // 已过期，不缓存
		}
		// 限制最大缓存时间为30天
		if ttl > MaxShortURLTTL {
			ttl = MaxShortURLTTL
		}
	} else {
		// 永久链接，缓存30天
		ttl = DefaultShortURLTTL
	}

	err = r.client.Set(ctx, key, data, ttl).Err()
	if err != nil {
		log.Printf("缓存短链失败: %v", err)
		return err
	}

	log.Printf("短链已缓存: %s -> %s, TTL: %v", shortCode, urlData.OriginalURL, ttl)
	return nil
}

// GetShortURL 获取缓存的短链信息
func (r *RedisService) GetShortURL(shortCode string) (*CachedShortURL, error) {
	if !r.IsEnabled() {
		return nil, nil
	}

	key := ShortURLPrefix + shortCode
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		log.Printf("获取短链缓存失败: %v", err)
		return nil, err
	}

	var urlData CachedShortURL
	if err := json.Unmarshal([]byte(data), &urlData); err != nil {
		log.Printf("反序列化短链数据失败: %v", err)
		// 删除损坏的缓存
		r.client.Del(ctx, key)
		return nil, err
	}

	// 检查是否过期（数据库层面的过期）
	if urlData.ExpiresAt != nil && time.Now().After(*urlData.ExpiresAt) {
		r.client.Del(ctx, key)
		return nil, nil
	}

	return &urlData, nil
}

// RefreshShortURL 刷新短链缓存（用于永久链接的自动续期）
func (r *RedisService) RefreshShortURL(shortCode string, urlData CachedShortURL) error {
	if !r.IsEnabled() {
		return nil
	}

	// 对于永久链接，重新缓存30天
	if urlData.ExpiresAt == nil {
		return r.SetShortURL(shortCode, urlData)
	}

	return nil
}

// IncrementShortURLClicks 增加短链点击次数
func (r *RedisService) IncrementShortURLClicks(shortCode string) error {
	if !r.IsEnabled() {
		return nil
	}

	key := ShortURLPrefix + shortCode

	// 获取当前数据
	urlData, err := r.GetShortURL(shortCode)
	if err != nil || urlData == nil {
		return nil // 缓存中没有，跳过
	}

	// 增加点击次数
	urlData.Clicks++

	// 更新缓存，保持原有的 TTL
	ttl := r.client.TTL(ctx, key).Val()
	if ttl > 0 {
		data, err := json.Marshal(urlData)
		if err != nil {
			return err
		}
		return r.client.Set(ctx, key, data, ttl).Err()
	}

	return nil
}

// DeleteShortURL 删除短链缓存
func (r *RedisService) DeleteShortURL(shortCode string) error {
	if !r.IsEnabled() {
		return nil
	}

	key := ShortURLPrefix + shortCode
	return r.client.Del(ctx, key).Err()
}

// --- 用户缓存方法 ---

// SetUserInfo 缓存用户信息
func (r *RedisService) SetUserInfo(userID uint, userData interface{}) error {
	if !r.IsEnabled() {
		return nil
	}

	key := fmt.Sprintf("%s%d", UserPrefix, userID)
	data, err := json.Marshal(userData)
	if err != nil {
		return fmt.Errorf("序列化用户数据失败: %w", err)
	}

	// 用户信息缓存1小时
	return r.client.Set(ctx, key, data, time.Hour).Err()
}

// GetUserInfo 获取缓存的用户信息
func (r *RedisService) GetUserInfo(userID uint, result interface{}) error {
	if !r.IsEnabled() {
		return redis.Nil // 返回未找到错误
	}

	key := fmt.Sprintf("%s%d", UserPrefix, userID)
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}

	return json.Unmarshal([]byte(data), result)
}

// DeleteUserInfo 删除用户信息缓存
func (r *RedisService) DeleteUserInfo(userID uint) error {
	if !r.IsEnabled() {
		return nil
	}

	key := fmt.Sprintf("%s%d", UserPrefix, userID)
	return r.client.Del(ctx, key).Err()
}

// --- 通用方法 ---

// FlushAll 清空所有缓存（谨慎使用）
func (r *RedisService) FlushAll() error {
	if !r.IsEnabled() {
		return nil
	}

	return r.client.FlushAll(ctx).Err()
}

// GetStats 获取 Redis 统计信息
func (r *RedisService) GetStats() map[string]interface{} {
	if !r.IsEnabled() {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	info := r.client.Info(ctx, "memory", "stats").Val()

	return map[string]interface{}{
		"enabled": true,
		"info":    info,
	}
}

// Close 关闭 Redis 连接
func (r *RedisService) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// GetGlobalRedisService 获取全局 Redis 服务实例
func GetGlobalRedisService() *RedisService {
	if Client == nil {
		return &RedisService{enabled: false}
	}
	return &RedisService{
		client:  Client,
		enabled: true,
	}
}
