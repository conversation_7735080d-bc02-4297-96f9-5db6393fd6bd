package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"url-stash-vault/internal/database"
	"url-stash-vault/internal/middleware"
)

// S3ConfigRequest S3配置请求结构
type S3ConfigRequest struct {
	Name            string  `json:"name" binding:"required"`
	AccessKeyID     string  `json:"access_key_id" binding:"required"`
	SecretAccessKey string  `json:"secret_access_key" binding:"required"`
	Region          string  `json:"region" binding:"required"`
	Bucket          string  `json:"bucket" binding:"required"`
	Endpoint        *string `json:"endpoint"`
	UseSSL          *bool   `json:"use_ssl"`
	IsDefault       *bool   `json:"is_default"`
	IsActive        *bool   `json:"is_active"`
}

// S3ConfigResponse S3配置响应结构
type S3ConfigResponse struct {
	ID              string  `json:"id"`
	Name            string  `json:"name"`
	AccessKeyID     string  `json:"access_key_id"`
	SecretAccessKey string  `json:"secret_access_key,omitempty"` // 敏感信息，通常不返回
	Region          string  `json:"region"`
	Bucket          string  `json:"bucket"`
	Endpoint        *string `json:"endpoint"`
	UseSSL          bool    `json:"use_ssl"`
	IsDefault       bool    `json:"is_default"`
	IsActive        bool    `json:"is_active"`
	CreatedAt       string  `json:"created_at"`
	UpdatedAt       string  `json:"updated_at"`
}

// isAdmin 检查用户是否为管理员
func isAdmin(c *gin.Context) bool {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		return false
	}

	var user database.User
	if err := database.DB.First(&user, userID).Error; err != nil {
		return false
	}

	return user.IsSuperAdmin
}

// GetS3Configs 获取S3配置列表
func GetS3Configs(c *gin.Context) {
	// 检查管理员权限
	if !isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	var configs []database.S3Config
	if err := database.DB.Find(&configs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取S3配置失败"})
		return
	}

	var response []S3ConfigResponse
	for _, config := range configs {
		response = append(response, S3ConfigResponse{
			ID:        config.ID,
			Name:      config.Name,
			Region:    config.Region,
			Bucket:    config.Bucket,
			Endpoint:  config.Endpoint,
			UseSSL:    config.UseSSL,
			IsDefault: config.IsDefault,
			IsActive:  config.IsActive,
			CreatedAt: config.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: config.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	c.JSON(http.StatusOK, response)
}

// CreateS3Config 创建S3配置
func CreateS3Config(c *gin.Context) {
	// 检查管理员权限
	if !isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	var req S3ConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	// 如果设置为默认配置，先取消其他默认配置
	if req.IsDefault != nil && *req.IsDefault {
		database.DB.Model(&database.S3Config{}).Where("is_default = ?", true).Update("is_default", false)
	}

	config := database.S3Config{
		ID:              uuid.New().String(),
		Name:            req.Name,
		AccessKeyID:     req.AccessKeyID,
		SecretAccessKey: req.SecretAccessKey,
		Region:          req.Region,
		Bucket:          req.Bucket,
		Endpoint:        req.Endpoint,
		UseSSL:          req.UseSSL != nil && *req.UseSSL,
		IsDefault:       req.IsDefault != nil && *req.IsDefault,
		IsActive:        req.IsActive == nil || *req.IsActive, // 默认启用
	}

	if err := database.DB.Create(&config).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建S3配置失败"})
		return
	}

	response := S3ConfigResponse{
		ID:        config.ID,
		Name:      config.Name,
		Region:    config.Region,
		Bucket:    config.Bucket,
		Endpoint:  config.Endpoint,
		UseSSL:    config.UseSSL,
		IsDefault: config.IsDefault,
		IsActive:  config.IsActive,
		CreatedAt: config.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: config.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateS3Config 更新S3配置
func UpdateS3Config(c *gin.Context) {
	// 检查管理员权限
	if !isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	id := c.Param("id")
	var req S3ConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	var config database.S3Config
	if err := database.DB.First(&config, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "S3配置不存在"})
		return
	}

	// 如果设置为默认配置，先取消其他默认配置
	if req.IsDefault != nil && *req.IsDefault && !config.IsDefault {
		database.DB.Model(&database.S3Config{}).Where("is_default = ? AND id != ?", true, id).Update("is_default", false)
	}

	// 更新配置
	config.Name = req.Name
	config.AccessKeyID = req.AccessKeyID
	config.SecretAccessKey = req.SecretAccessKey
	config.Region = req.Region
	config.Bucket = req.Bucket
	config.Endpoint = req.Endpoint
	if req.UseSSL != nil {
		config.UseSSL = *req.UseSSL
	}
	if req.IsDefault != nil {
		config.IsDefault = *req.IsDefault
	}
	if req.IsActive != nil {
		config.IsActive = *req.IsActive
	}

	if err := database.DB.Save(&config).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新S3配置失败"})
		return
	}

	response := S3ConfigResponse{
		ID:        config.ID,
		Name:      config.Name,
		Region:    config.Region,
		Bucket:    config.Bucket,
		Endpoint:  config.Endpoint,
		UseSSL:    config.UseSSL,
		IsDefault: config.IsDefault,
		IsActive:  config.IsActive,
		CreatedAt: config.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: config.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	c.JSON(http.StatusOK, response)
}

// DeleteS3Config 删除S3配置
func DeleteS3Config(c *gin.Context) {
	// 检查管理员权限
	if !isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	id := c.Param("id")

	// 检查是否有文件使用此配置
	var fileCount int64
	database.DB.Model(&database.FileUpload{}).Where("s3_config_id = ?", id).Count(&fileCount)
	if fileCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无法删除：仍有文件使用此S3配置"})
		return
	}

	if err := database.DB.Delete(&database.S3Config{}, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除S3配置失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "S3配置删除成功"})
}

// SetDefaultS3Config 设置默认S3配置
func SetDefaultS3Config(c *gin.Context) {
	// 检查管理员权限
	if !isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	id := c.Param("id")

	// 先取消所有默认配置
	database.DB.Model(&database.S3Config{}).Where("is_default = ?", true).Update("is_default", false)

	// 设置新的默认配置
	if err := database.DB.Model(&database.S3Config{}).Where("id = ?", id).Update("is_default", true).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "设置默认S3配置失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "默认S3配置设置成功"})
}

// TestS3Config 测试S3配置连接
func TestS3Config(c *gin.Context) {
	// 检查管理员权限
	if !isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "需要管理员权限"})
		return
	}

	id := c.Param("id")
	var config database.S3Config
	if err := database.DB.First(&config, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "S3配置不存在"})
		return
	}

	// TODO: 实现S3连接测试逻辑
	// 这里应该使用AWS SDK测试连接

	c.JSON(http.StatusOK, gin.H{
		"message": "S3配置测试成功",
		"status":  "connected",
	})
}
