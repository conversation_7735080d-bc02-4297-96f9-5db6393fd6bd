package api

import (
	"fmt"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"url-stash-vault/internal/database"
	"url-stash-vault/internal/middleware"
	"url-stash-vault/internal/services"
)

// FileUploadResponse 文件上传响应
type FileUploadResponse struct {
	ID           string `json:"id"`
	OriginalName string `json:"original_name"`
	FileName     string `json:"file_name"`
	FileSize     int64  `json:"file_size"`
	MimeType     string `json:"mime_type"`
	S3URL        string `json:"s3_url"`
	UploadType   string `json:"upload_type"`
	CreatedAt    string `json:"created_at"`
}

// getUserID 获取当前用户ID
func getUserID(c *gin.Context) uint {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		return 0
	}
	return userID
}

// UploadFile 通用文件上传接口
func UploadFile(c *gin.Context) {
	// 获取当前用户
	userID := getUserID(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	// 获取上传类型
	uploadType := c.DefaultPostForm("upload_type", "general")

	// 获取文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "获取文件失败: " + err.Error()})
		return
	}
	defer file.Close()

	// 验证文件类型和大小
	if err := validateFile(header, uploadType); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取默认S3配置
	var s3Config database.S3Config
	if err := database.DB.Where("is_default = ? AND is_active = ?", true, true).First(&s3Config).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "未找到可用的S3配置，请联系管理员在系统设置中配置S3存储",
			"code":  "S3_CONFIG_NOT_FOUND",
		})
		return
	}

	// 生成文件名
	fileName := generateFileName(header.Filename, uploadType)
	s3Key := fmt.Sprintf("%s/%s", uploadType, fileName)

	// 上传到S3
	s3URL, err := services.UploadToS3(file, s3Key, &s3Config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "文件上传失败: " + err.Error()})
		return
	}

	// 保存文件记录到数据库
	fileUpload := database.FileUpload{
		ID:           uuid.New().String(),
		UserID:       &userID,
		OriginalName: header.Filename,
		FileName:     fileName,
		FilePath:     s3Key,
		FileSize:     header.Size,
		MimeType:     header.Header.Get("Content-Type"),
		S3ConfigID:   s3Config.ID,
		S3Key:        s3Key,
		S3URL:        s3URL,
		UploadType:   uploadType,
	}

	if err := database.DB.Create(&fileUpload).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "保存文件记录失败"})
		return
	}

	response := FileUploadResponse{
		ID:           fileUpload.ID,
		OriginalName: fileUpload.OriginalName,
		FileName:     fileUpload.FileName,
		FileSize:     fileUpload.FileSize,
		MimeType:     fileUpload.MimeType,
		S3URL:        fileUpload.S3URL,
		UploadType:   fileUpload.UploadType,
		CreatedAt:    fileUpload.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	c.JSON(http.StatusOK, response)
}

// UploadAvatar 上传头像
func UploadAvatar(c *gin.Context) {
	// 获取当前用户
	userID := getUserID(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	// 获取文件
	file, header, err := c.Request.FormFile("avatar")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "获取头像文件失败: " + err.Error()})
		return
	}
	defer file.Close()

	// 验证头像文件
	if err := validateAvatarFile(header); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取默认S3配置
	var s3Config database.S3Config
	if err := database.DB.Where("is_default = ? AND is_active = ?", true, true).First(&s3Config).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "未找到可用的S3配置，请联系管理员在系统设置中配置S3存储",
			"code":  "S3_CONFIG_NOT_FOUND",
		})
		return
	}

	// 生成头像文件名
	fileName := generateFileName(header.Filename, "avatar")
	s3Key := fmt.Sprintf("avatars/%d/%s", userID, fileName)

	// 上传到S3
	s3URL, err := services.UploadToS3(file, s3Key, &s3Config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "头像上传失败: " + err.Error()})
		return
	}

	// 保存文件记录到数据库
	fileUpload := database.FileUpload{
		ID:           uuid.New().String(),
		UserID:       &userID,
		OriginalName: header.Filename,
		FileName:     fileName,
		FilePath:     s3Key,
		FileSize:     header.Size,
		MimeType:     header.Header.Get("Content-Type"),
		S3ConfigID:   s3Config.ID,
		S3Key:        s3Key,
		S3URL:        s3URL,
		UploadType:   "avatar",
	}

	if err := database.DB.Create(&fileUpload).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "保存头像记录失败"})
		return
	}

	// 更新用户头像URL
	if err := database.DB.Model(&database.User{}).Where("id = ?", userID).Update("avatar_url", s3URL).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新用户头像失败"})
		return
	}

	response := FileUploadResponse{
		ID:           fileUpload.ID,
		OriginalName: fileUpload.OriginalName,
		FileName:     fileUpload.FileName,
		FileSize:     fileUpload.FileSize,
		MimeType:     fileUpload.MimeType,
		S3URL:        fileUpload.S3URL,
		UploadType:   fileUpload.UploadType,
		CreatedAt:    fileUpload.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	c.JSON(http.StatusOK, response)
}

// GetUserFiles 获取用户文件列表
func GetUserFiles(c *gin.Context) {
	userID := getUserID(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	uploadType := c.Query("upload_type")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset := (page - 1) * limit

	query := database.DB.Where("user_id = ?", userID)
	if uploadType != "" {
		query = query.Where("upload_type = ?", uploadType)
	}

	var files []database.FileUpload
	var total int64

	query.Model(&database.FileUpload{}).Count(&total)
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&files).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取文件列表失败"})
		return
	}

	var response []FileUploadResponse
	for _, file := range files {
		response = append(response, FileUploadResponse{
			ID:           file.ID,
			OriginalName: file.OriginalName,
			FileName:     file.FileName,
			FileSize:     file.FileSize,
			MimeType:     file.MimeType,
			S3URL:        file.S3URL,
			UploadType:   file.UploadType,
			CreatedAt:    file.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"files": response,
		"total": total,
		"page":  page,
		"limit": limit,
	})
}

// DeleteFile 删除文件
func DeleteFile(c *gin.Context) {
	userID := getUserID(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	fileID := c.Param("id")
	var file database.FileUpload

	// 查找文件记录
	if err := database.DB.Where("id = ? AND user_id = ?", fileID, userID).First(&file).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "文件不存在"})
		return
	}

	// 从S3删除文件
	var s3Config database.S3Config
	if err := database.DB.First(&s3Config, "id = ?", file.S3ConfigID).Error; err == nil {
		if err := services.DeleteFromS3(file.S3Key, &s3Config); err != nil {
			// 记录错误但不阻止删除数据库记录
			fmt.Printf("删除S3文件失败: %v\n", err)
		}
	}

	// 删除数据库记录
	if err := database.DB.Delete(&file).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除文件记录失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "文件删除成功"})
}

// validateFile 验证文件
func validateFile(header *multipart.FileHeader, uploadType string) error {
	// 检查文件大小 (10MB)
	maxSize := int64(10 * 1024 * 1024)
	if header.Size > maxSize {
		return fmt.Errorf("文件大小不能超过10MB")
	}

	// 根据上传类型验证文件类型
	switch uploadType {
	case "avatar":
		return validateAvatarFile(header)
	case "image":
		return validateImageFile(header)
	case "document":
		return validateDocumentFile(header)
	default:
		return nil // 通用文件类型不限制
	}
}

// validateAvatarFile 验证头像文件
func validateAvatarFile(header *multipart.FileHeader) error {
	// 检查文件大小 (2MB)
	maxSize := int64(2 * 1024 * 1024)
	if header.Size > maxSize {
		return fmt.Errorf("头像文件大小不能超过2MB")
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(header.Filename))
	allowedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			return nil
		}
	}

	return fmt.Errorf("头像文件格式不支持，仅支持: %s", strings.Join(allowedExts, ", "))
}

// validateImageFile 验证图片文件
func validateImageFile(header *multipart.FileHeader) error {
	ext := strings.ToLower(filepath.Ext(header.Filename))
	allowedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".svg"}
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			return nil
		}
	}
	return fmt.Errorf("图片文件格式不支持")
}

// validateDocumentFile 验证文档文件
func validateDocumentFile(header *multipart.FileHeader) error {
	ext := strings.ToLower(filepath.Ext(header.Filename))
	allowedExts := []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt"}
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			return nil
		}
	}
	return fmt.Errorf("文档文件格式不支持")
}

// generateFileName 生成文件名
func generateFileName(originalName, uploadType string) string {
	ext := filepath.Ext(originalName)
	timestamp := time.Now().Unix()
	uuid := uuid.New().String()[:8]
	return fmt.Sprintf("%s_%d_%s%s", uploadType, timestamp, uuid, ext)
}
