package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"url-stash-vault/internal/auth"
	"url-stash-vault/internal/database"
	"url-stash-vault/internal/middleware"
	"url-stash-vault/internal/shorturl"
)

// --- User Handlers ---

type UserHandler struct {
	authService *auth.AuthService
}

func NewUserHandler(authService *auth.AuthService) *UserHandler {
	return &UserHandler{authService: authService}
}

func (h *UserHandler) RegisterUser(c *gin.Context) {
	var req auth.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	response, err := h.authService.Register(req)
	if err != nil {
		var statusCode int
		switch err {
		case auth.ErrUserAlreadyExists:
			statusCode = http.StatusConflict
		case auth.ErrWeakPassword:
			statusCode = http.StatusBadRequest
		case auth.ErrInvalidEmail:
			statusCode = http.StatusBadRequest
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

func (h *UserHandler) LoginUser(c *gin.Context) {
	var req auth.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	response, err := h.authService.Login(req)
	if err != nil {
		var statusCode int
		switch err {
		case auth.ErrInvalidCredentials:
			statusCode = http.StatusUnauthorized
		case auth.ErrEmailNotVerified:
			statusCode = http.StatusUnauthorized
			c.JSON(statusCode, gin.H{"error": "邮箱未验证，请先验证邮箱后再登录"})
			return
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *UserHandler) GetMe(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	userInfo, err := h.authService.GetUserByID(userID)
	if err != nil {
		if err == auth.ErrUserNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户信息失败"})
		}
		return
	}

	c.JSON(http.StatusOK, userInfo)
}

func (h *UserHandler) LogoutUser(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 调用auth服务的登出方法
	if err := h.authService.Logout(userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "登出失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "登出成功"})
}

// SendVerificationEmail 发送邮箱验证邮件
func (h *UserHandler) SendVerificationEmail(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	if err := h.authService.SendVerificationEmail(userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "发送验证邮件失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "验证邮件已发送"})
}

// VerifyEmail 验证邮箱
func (h *UserHandler) VerifyEmail(c *gin.Context) {
	var req auth.VerifyEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if err := h.authService.VerifyEmail(req); err != nil {
		var statusCode int
		switch err {
		case auth.ErrInvalidToken:
			statusCode = http.StatusBadRequest
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "邮箱验证成功"})
}

// ResendVerificationEmail 重新发送验证邮件（不需要认证）
func (h *UserHandler) ResendVerificationEmail(c *gin.Context) {
	var req auth.ResendVerificationEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if err := h.authService.ResendVerificationEmail(req.Email); err != nil {
		var statusCode int
		switch {
		case err == auth.ErrUserNotFound:
			statusCode = http.StatusNotFound
		case strings.Contains(err.Error(), "请等待60秒"):
			statusCode = http.StatusTooManyRequests
		case strings.Contains(err.Error(), "邮箱已验证"):
			statusCode = http.StatusBadRequest
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "验证邮件已重新发送"})
}

// ForgotPassword 忘记密码
func (h *UserHandler) ForgotPassword(c *gin.Context) {
	var req auth.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if err := h.authService.ForgotPassword(req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "处理忘记密码请求失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "如果邮箱存在，重置链接已发送"})
}

// ResetPassword 重置密码
func (h *UserHandler) ResetPassword(c *gin.Context) {
	var req auth.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	if err := h.authService.ResetPassword(req); err != nil {
		var statusCode int
		switch err {
		case auth.ErrInvalidToken, auth.ErrWeakPassword:
			statusCode = http.StatusBadRequest
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "密码重置成功"})
}

// GetOAuthProviders 获取OAuth提供商列表
func (h *UserHandler) GetOAuthProviders(c *gin.Context) {
	providers, err := h.authService.GetOAuthProviders()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取OAuth提供商失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"providers": providers})
}

// GetOAuthAuthURL 获取OAuth认证URL
func (h *UserHandler) GetOAuthAuthURL(c *gin.Context) {
	providerName := c.Param("provider")
	state := c.Query("state")

	if state == "" {
		state = "default_state" // 应该生成随机状态
	}

	authURL, err := h.authService.GetOAuthAuthURL(providerName, state)
	if err != nil {
		var statusCode int
		switch err {
		case auth.ErrOAuthProviderNotFound:
			statusCode = http.StatusNotFound
		case auth.ErrOAuthProviderDisabled:
			statusCode = http.StatusForbidden
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"auth_url": authURL})
}

// HandleOAuthCallback 处理OAuth回调
func (h *UserHandler) HandleOAuthCallback(c *gin.Context) {
	providerName := c.Param("provider")
	code := c.Query("code")
	state := c.Query("state")

	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少授权码"})
		return
	}

	response, err := h.authService.HandleOAuthCallback(providerName, code, state)
	if err != nil {
		var statusCode int
		switch err {
		case auth.ErrOAuthProviderNotFound:
			statusCode = http.StatusNotFound
		case auth.ErrOAuthProviderDisabled:
			statusCode = http.StatusForbidden
		case auth.ErrOAuthCodeInvalid:
			statusCode = http.StatusBadRequest
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// LinkOAuthAccount 关联OAuth账户
func (h *UserHandler) LinkOAuthAccount(c *gin.Context) {
	var req auth.LinkAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	response, err := h.authService.LinkOAuthAccount(req)
	if err != nil {
		var statusCode int
		switch err {
		case auth.ErrAccountLinkTokenInvalid:
			statusCode = http.StatusBadRequest
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetUserOAuthAccounts 获取用户的OAuth账户列表
func (h *UserHandler) GetUserOAuthAccounts(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	accounts, err := h.authService.GetUserOAuthAccounts(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取OAuth账户失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"accounts": accounts})
}

// UnlinkOAuthAccount 解绑OAuth账户
func (h *UserHandler) UnlinkOAuthAccount(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	providerName := c.Param("provider")
	if providerName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少提供商名称"})
		return
	}

	err := h.authService.UnlinkOAuthAccount(userID, providerName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "解绑OAuth账户失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "OAuth账户解绑成功"})
}

// ConfirmRegistration 确认注册（验证邮箱后创建用户）
func (h *UserHandler) ConfirmRegistration(c *gin.Context) {
	var req struct {
		Token string `json:"token" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	response, err := h.authService.ConfirmRegistration(req.Token)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 返回成功响应，包含token和用户信息
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// --- Memo Handlers ---

type MemoHandler struct {
	authService *auth.AuthService
}

func NewMemoHandler(authService *auth.AuthService) *MemoHandler {
	return &MemoHandler{authService: authService}
}

type CreateMemoRequest struct {
	Content      string    `json:"content" binding:"required"`
	Tags         []string  `json:"tags"`
	ReminderDate *string   `json:"reminder_date"`
}

type UpdateMemoRequest struct {
	Content      *string   `json:"content"`
	Tags         []string  `json:"tags"`
	ReminderDate *string   `json:"reminder_date"`
}

func (h *MemoHandler) CreateMemo(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var req CreateMemoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	var reminderDate *time.Time
	if req.ReminderDate != nil {
		if parsedTime, err := time.Parse("2006-01-02T15:04:05Z", *req.ReminderDate); err == nil {
			reminderDate = &parsedTime
		}
	}

	tagsJSON, _ := json.Marshal(req.Tags)

	memo := database.Memo{
		UserID:       userID,
		Content:      req.Content,
		Tags:         datatypes.JSON(tagsJSON),
		ReminderDate: reminderDate,
	}

	db := database.GetDB()
	if err := db.Create(&memo).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建备忘录失败"})
		return
	}

	c.JSON(http.StatusCreated, memo)
}

func (h *MemoHandler) ListMemos(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var memos []database.Memo
	db := database.GetDB()
	if err := db.Where("user_id = ?", userID).Order("created_at DESC").Find(&memos).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取备忘录列表失败"})
		return
	}

	c.JSON(http.StatusOK, memos)
}

func (h *MemoHandler) GetMemo(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	memoID := c.Param("id")
	var memo database.Memo
	db := database.GetDB()
	if err := db.Where("id = ? AND user_id = ?", memoID, userID).First(&memo).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "备忘录不存在"})
		return
	}

	c.JSON(http.StatusOK, memo)
}

func (h *MemoHandler) UpdateMemo(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	memoID := c.Param("id")
	var req UpdateMemoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	var memo database.Memo
	db := database.GetDB()
	if err := db.Where("id = ? AND user_id = ?", memoID, userID).First(&memo).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "备忘录不存在"})
		return
	}

	// 更新字段
	if req.Content != nil {
		memo.Content = *req.Content
	}
	if req.Tags != nil {
		tagsJSON, _ := json.Marshal(req.Tags)
		memo.Tags = datatypes.JSON(tagsJSON)
	}
	if req.ReminderDate != nil {
		if parsedTime, err := time.Parse("2006-01-02T15:04:05Z", *req.ReminderDate); err == nil {
			memo.ReminderDate = &parsedTime
		}
	}

	if err := db.Save(&memo).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新备忘录失败"})
		return
	}

	c.JSON(http.StatusOK, memo)
}

func (h *MemoHandler) DeleteMemo(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	memoID := c.Param("id")
	db := database.GetDB()
	result := db.Where("id = ? AND user_id = ?", memoID, userID).Delete(&database.Memo{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除备忘录失败"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "备忘录不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "备忘录删除成功"})
}

// --- Todo Handlers ---

type TodoHandler struct {
	authService *auth.AuthService
}

func NewTodoHandler(authService *auth.AuthService) *TodoHandler {
	return &TodoHandler{authService: authService}
}

type CreateTodoRequest struct {
	Title    string  `json:"title" binding:"required"`
	DueDate  *string `json:"due_date"`
	Priority *string `json:"priority"`
}

type UpdateTodoRequest struct {
	Title     *string `json:"title"`
	Completed *bool   `json:"completed"`
	DueDate   *string `json:"due_date"`
	Priority  *string `json:"priority"`
}

func (h *TodoHandler) CreateTodo(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var req CreateTodoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	var dueDate *time.Time
	if req.DueDate != nil {
		if parsedTime, err := time.Parse("2006-01-02T15:04:05Z", *req.DueDate); err == nil {
			dueDate = &parsedTime
		}
	}

	priority := ""
	if req.Priority != nil {
		priority = *req.Priority
	}

	todo := database.Todo{
		UserID:   userID,
		Title:    req.Title,
		DueDate:  dueDate,
		Priority: priority,
	}

	db := database.GetDB()
	if err := db.Create(&todo).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建待办事项失败"})
		return
	}

	c.JSON(http.StatusCreated, todo)
}

func (h *TodoHandler) ListTodos(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var todos []database.Todo
	db := database.GetDB()
	if err := db.Where("user_id = ?", userID).Order("created_at DESC").Find(&todos).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取待办事项列表失败"})
		return
	}

	c.JSON(http.StatusOK, todos)
}

func (h *TodoHandler) GetTodo(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	todoID := c.Param("id")
	var todo database.Todo
	db := database.GetDB()
	if err := db.Where("id = ? AND user_id = ?", todoID, userID).First(&todo).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "待办事项不存在"})
		return
	}

	c.JSON(http.StatusOK, todo)
}

func (h *TodoHandler) UpdateTodo(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	todoID := c.Param("id")
	var req UpdateTodoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	var todo database.Todo
	db := database.GetDB()
	if err := db.Where("id = ? AND user_id = ?", todoID, userID).First(&todo).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "待办事项不存在"})
		return
	}

	// 更新字段
	if req.Title != nil {
		todo.Title = *req.Title
	}
	if req.Completed != nil {
		todo.Completed = *req.Completed
	}
	if req.DueDate != nil {
		if parsedTime, err := time.Parse("2006-01-02T15:04:05Z", *req.DueDate); err == nil {
			todo.DueDate = &parsedTime
		}
	}
	if req.Priority != nil {
		todo.Priority = *req.Priority
	}

	if err := db.Save(&todo).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新待办事项失败"})
		return
	}

	c.JSON(http.StatusOK, todo)
}

func (h *TodoHandler) DeleteTodo(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	todoID := c.Param("id")
	db := database.GetDB()
	result := db.Where("id = ? AND user_id = ?", todoID, userID).Delete(&database.Todo{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除待办事项失败"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "待办事项不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "待办事项删除成功"})
}

// --- ShortURL Handlers ---

type ShortURLHandler struct {
	authService     *auth.AuthService
	shortURLService *shorturl.Service
}

func NewShortURLHandler(authService *auth.AuthService, shortURLService *shorturl.Service) *ShortURLHandler {
	return &ShortURLHandler{
		authService:     authService,
		shortURLService: shortURLService,
	}
}

func (h *ShortURLHandler) CreateShortURL(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var req shorturl.CreateShortURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	// 构建基础URL用于生成完整的短链
	baseURL := c.GetHeader("X-Forwarded-Proto")
	if baseURL == "" {
		if c.Request.TLS != nil {
			baseURL = "https"
		} else {
			baseURL = "http"
		}
	}
	baseURL += "://" + c.Request.Host

	response, err := h.shortURLService.CreateShortURL(c.Request.Context(), req, &userID, baseURL)
	if err != nil {
		var statusCode int
		switch err {
		case shorturl.ErrInvalidURL:
			statusCode = http.StatusBadRequest
		case shorturl.ErrShortCodeExists:
			statusCode = http.StatusConflict
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

func (h *ShortURLHandler) ListShortURLs(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var req shorturl.ListShortURLsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	response, err := h.shortURLService.ListShortURLs(c.Request.Context(), req, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *ShortURLHandler) GetShortURL(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少短链ID"})
		return
	}

	_, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// TODO: 实现获取单个短链的功能
	// 这里需要在shorturl服务中添加GetShortURLByID方法
	c.JSON(http.StatusNotImplemented, gin.H{"message": "获取短链详情功能正在开发中"})
}

func (h *ShortURLHandler) UpdateShortURL(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少短链ID"})
		return
	}

	_, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// TODO: 实现更新短链的功能
	// 这里需要在shorturl服务中添加UpdateShortURL方法
	c.JSON(http.StatusNotImplemented, gin.H{"message": "更新短链功能正在开发中"})
}

func (h *ShortURLHandler) DeleteShortURL(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少短链ID"})
		return
	}

	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	err := h.shortURLService.DeleteShortURL(c.Request.Context(), id, userID)
	if err != nil {
		var statusCode int
		switch err {
		case shorturl.ErrShortURLNotFound:
			statusCode = http.StatusNotFound
		case shorturl.ErrPermissionDenied:
			statusCode = http.StatusForbidden
		default:
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "短链删除成功"})
}

func (h *ShortURLHandler) RedirectShortURL(c *gin.Context) {
	shortCode := c.Param("code")
	if shortCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少短链代码"})
		return
	}

	originalURL, err := h.shortURLService.GetShortURL(c.Request.Context(), shortCode)
	if err != nil {
		if err == shorturl.ErrShortURLNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "短链不存在或已过期"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取短链失败"})
		}
		return
	}

	c.Redirect(http.StatusMovedPermanently, originalURL)
}

func (h *ShortURLHandler) GetDailyCount(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	count, err := h.shortURLService.GetDailyCount(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取每日计数失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"count": count})
}

// --- Admin Handlers ---

type AdminHandler struct {
	authService *auth.AuthService
}

func NewAdminHandler(authService *auth.AuthService) *AdminHandler {
	return &AdminHandler{authService: authService}
}

// ListUsers 获取用户列表（管理员功能）
func (h *AdminHandler) ListUsers(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败"})
		return
	}

	// 检查是否是超级管理员
	if err := h.authService.CheckPermission(userID, "admin", "read"); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	// 查询参数
	page := c.DefaultQuery("page", "1")
	limit := c.DefaultQuery("limit", "10")
	search := c.Query("search")

	pageInt, _ := strconv.Atoi(page)
	limitInt, _ := strconv.Atoi(limit)
	if pageInt < 1 {
		pageInt = 1
	}
	if limitInt < 1 || limitInt > 100 {
		limitInt = 10
	}

	users, total, err := h.authService.ListUsers(pageInt, limitInt, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"users": users,
		"total": total,
		"page":  pageInt,
		"limit": limitInt,
	})
}

// UpdateUser 更新用户信息（管理员功能）
func (h *AdminHandler) UpdateUser(c *gin.Context) {
	// 获取当前用户ID
	currentUserID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败"})
		return
	}

	// 检查是否是超级管理员
	if err := h.authService.CheckPermission(currentUserID, "admin", "write"); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID"})
		return
	}

	var req struct {
		Username      *string `json:"username"`
		Email         *string `json:"email"`
		EmailVerified *bool   `json:"email_verified"`
		IsSuperAdmin  *bool   `json:"is_super_admin"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据"})
		return
	}

	if err := h.authService.UpdateUser(uint(userID), req.Username, req.Email, req.EmailVerified, req.IsSuperAdmin); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "用户信息更新成功"})
}

// DeleteUser 删除用户（管理员功能）
func (h *AdminHandler) DeleteUser(c *gin.Context) {
	// 获取当前用户ID
	currentUserID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败"})
		return
	}

	// 检查是否是超级管理员
	if err := h.authService.CheckPermission(currentUserID, "admin", "delete"); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID"})
		return
	}

	// 不能删除自己
	if uint(userID) == currentUserID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "不能删除自己的账户"})
		return
	}

	if err := h.authService.DeleteUser(uint(userID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "用户删除成功"})
}

// GetUserStats 获取用户统计信息（管理员功能）
func (h *AdminHandler) GetUserStats(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败"})
		return
	}

	// 检查是否是超级管理员
	if err := h.authService.CheckPermission(userID, "admin", "read"); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	stats, err := h.authService.GetUserStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户统计失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// --- Navigation Handlers ---

type NavigationHandler struct {
	authService *auth.AuthService
}

func NewNavigationHandler(authService *auth.AuthService) *NavigationHandler {
	return &NavigationHandler{authService: authService}
}

// Category request/response types
type CreateCategoryRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description"`
	Icon        *string `json:"icon"`
	ParentID    *string `json:"parent_id"`
	SortOrder   *int    `json:"sort_order"`
	IsPublic    *bool   `json:"is_public"`
}

type UpdateCategoryRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	Icon        *string `json:"icon"`
	ParentID    *string `json:"parent_id"`
	SortOrder   *int    `json:"sort_order"`
	IsPublic    *bool   `json:"is_public"`
}

// Link request/response types
type CreateLinkRequest struct {
	CategoryID  string  `json:"category_id" binding:"required"`
	Name        string  `json:"name" binding:"required"`
	URL         string  `json:"url" binding:"required"`
	Description *string `json:"description"`
	Icon        *string `json:"icon"`
	SortOrder   *int    `json:"sort_order"`
	IsInternal  *bool   `json:"is_internal"`
	IsPublic    *bool   `json:"is_public"`
}

type UpdateLinkRequest struct {
	CategoryID  *string `json:"category_id"`
	Name        *string `json:"name"`
	URL         *string `json:"url"`
	Description *string `json:"description"`
	Icon        *string `json:"icon"`
	SortOrder   *int    `json:"sort_order"`
	IsInternal  *bool   `json:"is_internal"`
	IsPublic    *bool   `json:"is_public"`
	Status      *string `json:"status"`
}

// Category handlers
func (h *NavigationHandler) CreateCategory(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var req CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	category := database.NavCategory{
		ID:          uuid.New().String(),
		UserID:      &userID,
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		ParentID:    req.ParentID,
		IsPublic:    req.IsPublic != nil && *req.IsPublic,
	}

	if req.SortOrder != nil {
		category.SortOrder = *req.SortOrder
	}

	db := database.GetDB()
	if err := db.Create(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建分类失败"})
		return
	}

	c.JSON(http.StatusCreated, category)
}

func (h *NavigationHandler) ListCategories(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var categories []database.NavCategory
	db := database.GetDB()

	// 获取用户的分类和公共分类
	if err := db.Where("user_id = ? OR (user_id IS NULL AND is_public = ?)", userID, true).
		Order("sort_order ASC, created_at ASC").
		Find(&categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取分类列表失败"})
		return
	}

	c.JSON(http.StatusOK, categories)
}

func (h *NavigationHandler) GetCategory(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	categoryID := c.Param("id")
	var category database.NavCategory
	db := database.GetDB()

	if err := db.Where("id = ? AND (user_id = ? OR (user_id IS NULL AND is_public = ?))",
		categoryID, userID, true).First(&category).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "分类不存在"})
		return
	}

	c.JSON(http.StatusOK, category)
}

func (h *NavigationHandler) UpdateCategory(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	categoryID := c.Param("id")
	var req UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	var category database.NavCategory
	db := database.GetDB()

	// 只能修改自己的分类
	if err := db.Where("id = ? AND user_id = ?", categoryID, userID).First(&category).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "分类不存在或无权限"})
		return
	}

	// 更新字段
	if req.Name != nil {
		category.Name = *req.Name
	}
	if req.Description != nil {
		category.Description = req.Description
	}
	if req.Icon != nil {
		category.Icon = req.Icon
	}
	if req.ParentID != nil {
		category.ParentID = req.ParentID
	}
	if req.SortOrder != nil {
		category.SortOrder = *req.SortOrder
	}
	if req.IsPublic != nil {
		category.IsPublic = *req.IsPublic
	}

	if err := db.Save(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新分类失败"})
		return
	}

	c.JSON(http.StatusOK, category)
}

func (h *NavigationHandler) DeleteCategory(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	categoryID := c.Param("id")
	db := database.GetDB()

	// 只能删除自己的分类
	result := db.Where("id = ? AND user_id = ?", categoryID, userID).Delete(&database.NavCategory{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除分类失败"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "分类不存在或无权限"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "分类删除成功"})
}

// Link handlers
func (h *NavigationHandler) CreateLink(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	var req CreateLinkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	link := database.NavLink{
		ID:          uuid.New().String(),
		UserID:      &userID,
		CategoryID:  req.CategoryID,
		Name:        req.Name,
		URL:         req.URL,
		Description: req.Description,
		Icon:        req.Icon,
		IsInternal:  req.IsInternal != nil && *req.IsInternal,
		IsPublic:    req.IsPublic != nil && *req.IsPublic,
		Status:      "active",
	}

	if req.SortOrder != nil {
		link.SortOrder = *req.SortOrder
	}

	db := database.GetDB()
	if err := db.Create(&link).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建链接失败"})
		return
	}

	c.JSON(http.StatusCreated, link)
}

func (h *NavigationHandler) ListLinks(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	categoryID := c.Query("category_id")
	var links []database.NavLink
	db := database.GetDB()

	query := db.Where("user_id = ? OR (user_id IS NULL AND is_public = ?)", userID, true)

	if categoryID != "" {
		query = query.Where("category_id = ?", categoryID)
	}

	if err := query.Order("sort_order ASC, created_at ASC").Find(&links).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取链接列表失败"})
		return
	}

	c.JSON(http.StatusOK, links)
}

// --- Domain Whitelist Handlers ---

type CreateDomainWhitelistRequest struct {
	Domain   string `json:"domain" binding:"required"`
	Approved *bool  `json:"approved"`
}

type UpdateDomainWhitelistRequest struct {
	Domain   *string `json:"domain"`
	Approved *bool   `json:"approved"`
}

func (h *AdminHandler) ListDomainWhitelist(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	// 获取查询参数
	approved := c.Query("approved")

	var domains []database.DomainWhitelist
	db := database.GetDB()
	query := db.Order("created_at DESC")

	if approved != "" {
		if approved == "true" {
			query = query.Where("approved = ?", true)
		} else if approved == "false" {
			query = query.Where("approved = ?", false)
		}
	}

	if err := query.Find(&domains).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取域名白名单失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"domains": domains})
}

func (h *AdminHandler) CreateDomainWhitelist(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var req CreateDomainWhitelistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	approved := false // Default to false for new domains
	if req.Approved != nil {
		approved = *req.Approved
	}

	domain := database.DomainWhitelist{
		ID:       uuid.New().String(),
		Domain:   req.Domain,
		Approved: approved,
	}

	db := database.GetDB()
	if err := db.Create(&domain).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建域名白名单失败"})
		return
	}

	c.JSON(http.StatusCreated, domain)
}

func (h *AdminHandler) UpdateDomainWhitelist(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	domainID := c.Param("id")
	var req UpdateDomainWhitelistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	var domain database.DomainWhitelist
	db := database.GetDB()
	if err := db.Where("id = ?", domainID).First(&domain).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "域名不存在"})
		return
	}

	// 更新字段
	if req.Domain != nil {
		domain.Domain = *req.Domain
	}
	if req.Approved != nil {
		domain.Approved = *req.Approved
	}

	if err := db.Save(&domain).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新域名白名单失败"})
		return
	}

	c.JSON(http.StatusOK, domain)
}

func (h *AdminHandler) DeleteDomainWhitelist(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	domainID := c.Param("id")
	db := database.GetDB()
	result := db.Where("id = ?", domainID).Delete(&database.DomainWhitelist{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除域名白名单失败"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "域名不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "域名删除成功"})
}

func (h *AdminHandler) SeedDefaultDomains(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	defaultDomains := []string{
		"google.com", "github.com", "microsoft.com",
		"apple.com", "amazon.com", "facebook.com",
		"twitter.com", "linkedin.com", "youtube.com",
	}

	db := database.GetDB()
	var createdCount int

	for _, domainName := range defaultDomains {
		// 检查域名是否已存在
		var existingDomain database.DomainWhitelist
		if err := db.Where("domain = ?", domainName).First(&existingDomain).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				// 域名不存在，创建新的
				domain := database.DomainWhitelist{
					ID:       uuid.New().String(),
					Domain:   domainName,
					Approved: true,
				}
				if err := db.Create(&domain).Error; err == nil {
					createdCount++
				}
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "默认域名种子数据创建完成",
		"created": createdCount,
		"total":   len(defaultDomains),
	})
}

// --- URL Visits Handlers ---

func (h *AdminHandler) ListURLVisits(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var visits []database.URLVisit
	db := database.GetDB()
	if err := db.Order("visited_at DESC").Find(&visits).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取访问记录失败"})
		return
	}

	c.JSON(http.StatusOK, visits)
}

func (h *AdminHandler) GetVisitStats(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	db := database.GetDB()

	// 获取总访问数
	var totalVisits int64
	db.Model(&database.URLVisit{}).Count(&totalVisits)

	// 获取今日访问数
	var todayVisits int64
	today := time.Now().Format("2006-01-02")
	db.Model(&database.URLVisit{}).Where("DATE(visited_at) = ?", today).Count(&todayVisits)

	// 获取按国家分组的访问统计
	var countryStats []struct {
		Country string `json:"country"`
		Count   int64  `json:"count"`
	}
	db.Model(&database.URLVisit{}).
		Select("country, COUNT(*) as count").
		Where("country IS NOT NULL AND country != ''").
		Group("country").
		Order("count DESC").
		Limit(10).
		Find(&countryStats)

	c.JSON(http.StatusOK, gin.H{
		"total_visits":   totalVisits,
		"today_visits":   todayVisits,
		"country_stats":  countryStats,
	})
}

// --- Banner Config Handlers ---

type UpdateBannerConfigRequest struct {
	MainTitle       map[string]string `json:"main_title"`
	MainDescription map[string]string `json:"main_description"`
	GridRows        *int              `json:"grid_rows"`
	GridColumns     *int              `json:"grid_columns"`
}

func (h *AdminHandler) GetBannerConfig(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var config database.BannerConfig
	db := database.GetDB()
	if err := db.First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 返回默认配置
			defaultConfig := map[string]interface{}{
				"main_title": map[string]string{
					"en": "All-in-One Productivity Platform",
					"zh": "一站式生产力平台",
				},
				"main_description": map[string]string{
					"en": "Streamline your workflow with powerful tools designed for modern productivity",
					"zh": "使用为现代生产力设计的强大工具简化您的工作流程",
				},
				"grid_rows":    1,
				"grid_columns": 6,
			}
			c.JSON(http.StatusOK, defaultConfig)
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取横幅配置失败"})
		return
	}

	c.JSON(http.StatusOK, config)
}

func (h *AdminHandler) UpdateBannerConfig(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var req UpdateBannerConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	db := database.GetDB()
	var config database.BannerConfig

	// 尝试获取现有配置
	if err := db.First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新配置
			config = database.BannerConfig{
				ID: "default",
			}
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取横幅配置失败"})
			return
		}
	}

	// 更新字段
	if req.MainTitle != nil {
		titleJSON, _ := json.Marshal(req.MainTitle)
		config.MainTitle = datatypes.JSON(titleJSON)
	}
	if req.MainDescription != nil {
		descJSON, _ := json.Marshal(req.MainDescription)
		config.MainDescription = datatypes.JSON(descJSON)
	}

	// 保存配置
	if err := db.Save(&config).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新横幅配置失败"})
		return
	}

	c.JSON(http.StatusOK, config)
}

// --- Feature Ordering Handlers ---

type UpdateFeatureOrderingRequest struct {
	ComponentType    string                 `json:"component_type" binding:"required"`
	ComponentsConfig map[string]interface{} `json:"components_config" binding:"required"`
}

func (h *AdminHandler) GetFeatureOrdering(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var ordering []database.FeatureOrdering
	db := database.GetDB()
	if err := db.Order("created_at DESC").Find(&ordering).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取功能排序失败"})
		return
	}

	c.JSON(http.StatusOK, ordering)
}

func (h *AdminHandler) UpdateFeatureOrdering(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var req UpdateFeatureOrderingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	db := database.GetDB()
	var ordering database.FeatureOrdering

	// 尝试获取现有配置
	if err := db.Where("component_type = ?", req.ComponentType).First(&ordering).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新配置
			configJSON, _ := json.Marshal(req.ComponentsConfig)
			ordering = database.FeatureOrdering{
				ID:               uuid.New().String(),
				ComponentType:    req.ComponentType,
				ComponentsConfig: datatypes.JSON(configJSON),
			}
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取功能排序失败"})
			return
		}
	} else {
		// 更新现有配置
		configJSON, _ := json.Marshal(req.ComponentsConfig)
		ordering.ComponentsConfig = datatypes.JSON(configJSON)
	}

	// 保存配置
	if err := db.Save(&ordering).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新功能排序失败"})
		return
	}

	c.JSON(http.StatusOK, ordering)
}

// --- Email Domains Handlers ---

type CreateEmailDomainRequest struct {
	Domain string `json:"domain" binding:"required"`
}

func (h *AdminHandler) ListEmailDomains(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var domains []database.EmailDomain
	db := database.GetDB()
	if err := db.Order("created_at DESC").Find(&domains).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取邮箱域名失败"})
		return
	}

	c.JSON(http.StatusOK, domains)
}

func (h *AdminHandler) CreateEmailDomain(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	var req CreateEmailDomainRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	// 检查域名是否已存在
	var existingDomain database.EmailDomain
	db := database.GetDB()
	if err := db.Where("domain = ?", req.Domain).First(&existingDomain).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "域名已存在"})
		return
	}

	domain := database.EmailDomain{
		ID:     uuid.New().String(),
		Domain: req.Domain,
	}

	if err := db.Create(&domain).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建邮箱域名失败"})
		return
	}

	c.JSON(http.StatusCreated, domain)
}

func (h *AdminHandler) DeleteEmailDomain(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	// 检查是否为管理员
	user, err := h.authService.GetUserByID(userID)
	if err != nil || !user.IsSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}

	domainID := c.Param("id")
	db := database.GetDB()
	result := db.Where("id = ?", domainID).Delete(&database.EmailDomain{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除邮箱域名失败"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "邮箱域名不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "邮箱域名删除成功"})
}

func (h *NavigationHandler) GetLink(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	linkID := c.Param("id")
	var link database.NavLink
	db := database.GetDB()

	if err := db.Where("id = ? AND (user_id = ? OR (user_id IS NULL AND is_public = ?))",
		linkID, userID, true).First(&link).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "链接不存在"})
		return
	}

	c.JSON(http.StatusOK, link)
}

func (h *NavigationHandler) UpdateLink(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	linkID := c.Param("id")
	var req UpdateLinkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数无效: " + err.Error()})
		return
	}

	var link database.NavLink
	db := database.GetDB()

	// 只能修改自己的链接
	if err := db.Where("id = ? AND user_id = ?", linkID, userID).First(&link).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "链接不存在或无权限"})
		return
	}

	// 更新字段
	if req.CategoryID != nil {
		link.CategoryID = *req.CategoryID
	}
	if req.Name != nil {
		link.Name = *req.Name
	}
	if req.URL != nil {
		link.URL = *req.URL
	}
	if req.Description != nil {
		link.Description = req.Description
	}
	if req.Icon != nil {
		link.Icon = req.Icon
	}
	if req.SortOrder != nil {
		link.SortOrder = *req.SortOrder
	}
	if req.IsInternal != nil {
		link.IsInternal = *req.IsInternal
	}
	if req.IsPublic != nil {
		link.IsPublic = *req.IsPublic
	}
	if req.Status != nil {
		link.Status = *req.Status
	}

	if err := db.Save(&link).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新链接失败"})
		return
	}

	c.JSON(http.StatusOK, link)
}

func (h *NavigationHandler) DeleteLink(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
		return
	}

	linkID := c.Param("id")
	db := database.GetDB()

	// 只能删除自己的链接
	result := db.Where("id = ? AND user_id = ?", linkID, userID).Delete(&database.NavLink{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除链接失败"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "链接不存在或无权限"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "链接删除成功"})
}
