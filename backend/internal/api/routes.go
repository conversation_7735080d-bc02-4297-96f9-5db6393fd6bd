package api

import (
	"github.com/gin-gonic/gin"

	"url-stash-vault/internal/auth"
	"url-stash-vault/internal/config"
	"url-stash-vault/internal/middleware"
	"url-stash-vault/internal/shorturl"
)

// SetupRouter 配置和返回 Gin 引擎
func SetupRouter(cfg *config.Config, authService *auth.AuthService, shortURLService *shorturl.Service) *gin.Engine {
	router := gin.Default()

	// 添加中间件
	router.Use(middleware.CORS())
	router.Use(middleware.SecurityHeaders())
	router.Use(middleware.RequestLogger())

	// 创建认证中间件
	authMiddleware := middleware.NewAuthMiddleware(authService)

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "UP", "backend": "go"})
	})

	// 数据库健康检查
	router.GET("/health/db", func(c *gin.Context) {
		// TODO: 实现数据库健康检查
		c.JSON(200, gin.H{"status": "UP", "database": "connected"})
	})

	// 实例化处理器
	userHandler := NewUserHandler(authService)
	shortURLHandler := NewShortURLHandler(authService, shortURLService)
	memoHandler := NewMemoHandler(authService)
	todoHandler := NewTodoHandler(authService)
	adminHandler := NewAdminHandler(authService)
	navigationHandler := NewNavigationHandler(authService)

	// 重定向路由（公开，需要放在API组之前）
	router.GET("/s/:code", shortURLHandler.RedirectShortURL)

	// API v1 路由组
	apiV1 := router.Group("/api/v1")
	{
		// --- 认证路由 ---
		authGroup := apiV1.Group("/auth")
		{
			authGroup.POST("/register", userHandler.RegisterUser)
			authGroup.POST("/confirm-registration", userHandler.ConfirmRegistration)
			authGroup.POST("/login", userHandler.LoginUser)
			authGroup.POST("/logout", authMiddleware.RequireAuth(), userHandler.LogoutUser)
			authGroup.GET("/me", authMiddleware.RequireAuth(), userHandler.GetMe)

			// 邮箱验证
			authGroup.POST("/send-verification", authMiddleware.RequireAuth(), userHandler.SendVerificationEmail)
			authGroup.POST("/resend-verification", userHandler.ResendVerificationEmail) // 不需要认证
			authGroup.POST("/verify-email", userHandler.VerifyEmail)

			// 密码重置
			authGroup.POST("/forgot-password", userHandler.ForgotPassword)
			authGroup.POST("/reset-password", userHandler.ResetPassword)

			// OAuth路由
			authGroup.GET("/oauth/providers", userHandler.GetOAuthProviders)
			authGroup.GET("/oauth/:provider/auth", userHandler.GetOAuthAuthURL)
			authGroup.GET("/oauth/:provider/callback", userHandler.HandleOAuthCallback)
			authGroup.POST("/oauth/link", userHandler.LinkOAuthAccount)
			authGroup.GET("/oauth/accounts", authMiddleware.RequireAuth(), userHandler.GetUserOAuthAccounts)
			authGroup.DELETE("/oauth/:provider", authMiddleware.RequireAuth(), userHandler.UnlinkOAuthAccount)
		}

		// --- ShortURL 路由 ---
		shortUrlGroup := apiV1.Group("/shorturls")
		{
			// 需要认证的路由
			shortUrlGroup.Use(authMiddleware.RequireAuth())
			shortUrlGroup.POST("", shortURLHandler.CreateShortURL)
			shortUrlGroup.GET("", shortURLHandler.ListShortURLs)
			shortUrlGroup.GET("/daily-count", shortURLHandler.GetDailyCount)
			shortUrlGroup.GET("/:id", shortURLHandler.GetShortURL)
			shortUrlGroup.PUT("/:id", shortURLHandler.UpdateShortURL)
			shortUrlGroup.DELETE("/:id", shortURLHandler.DeleteShortURL)
		}

		// --- Memo 路由（需要认证）---
		memosGroup := apiV1.Group("/memos")
		memosGroup.Use(authMiddleware.RequireAuth())
		{
			memosGroup.POST("", memoHandler.CreateMemo)
			memosGroup.GET("", memoHandler.ListMemos)
			memosGroup.GET("/:id", memoHandler.GetMemo)
			memosGroup.PUT("/:id", memoHandler.UpdateMemo)
			memosGroup.DELETE("/:id", memoHandler.DeleteMemo)
		}

		// --- Todo 路由（需要认证）---
		todosGroup := apiV1.Group("/todos")
		todosGroup.Use(authMiddleware.RequireAuth())
		{
			todosGroup.POST("", todoHandler.CreateTodo)
			todosGroup.GET("", todoHandler.ListTodos)
			todosGroup.GET("/:id", todoHandler.GetTodo)
			todosGroup.PUT("/:id", todoHandler.UpdateTodo)
			todosGroup.DELETE("/:id", todoHandler.DeleteTodo)
		}

		// --- Navigation 路由（需要认证）---
		navigationGroup := apiV1.Group("/navigation")
		navigationGroup.Use(authMiddleware.RequireAuth())
		{
			// 分类路由
			navigationGroup.POST("/categories", navigationHandler.CreateCategory)
			navigationGroup.GET("/categories", navigationHandler.ListCategories)
			navigationGroup.GET("/categories/:id", navigationHandler.GetCategory)
			navigationGroup.PUT("/categories/:id", navigationHandler.UpdateCategory)
			navigationGroup.DELETE("/categories/:id", navigationHandler.DeleteCategory)

			// 链接路由
			navigationGroup.POST("/links", navigationHandler.CreateLink)
			navigationGroup.GET("/links", navigationHandler.ListLinks)
			navigationGroup.GET("/links/:id", navigationHandler.GetLink)
			navigationGroup.PUT("/links/:id", navigationHandler.UpdateLink)
			navigationGroup.DELETE("/links/:id", navigationHandler.DeleteLink)
		}

		// --- 域名白名单路由 ---
		domainsGroup := apiV1.Group("/domains")
		domainsGroup.Use(authMiddleware.RequireAuth())
		{
			domainsGroup.GET("/whitelist", adminHandler.ListDomainWhitelist)
			domainsGroup.POST("/whitelist", adminHandler.CreateDomainWhitelist)
			domainsGroup.PUT("/whitelist/:id", adminHandler.UpdateDomainWhitelist)
			domainsGroup.DELETE("/whitelist/:id", adminHandler.DeleteDomainWhitelist)
			domainsGroup.POST("/whitelist/seed", adminHandler.SeedDefaultDomains)
		}

		// --- URL 访问统计路由 ---
		visitsGroup := apiV1.Group("/visits")
		visitsGroup.Use(authMiddleware.RequireAuth())
		{
			visitsGroup.GET("", adminHandler.ListURLVisits)
			visitsGroup.GET("/stats", adminHandler.GetVisitStats)
		}

		// --- Banner 配置路由 ---
		bannerGroup := apiV1.Group("/banner")
		bannerGroup.Use(authMiddleware.RequireAuth())
		{
			bannerGroup.GET("/config", adminHandler.GetBannerConfig)
			bannerGroup.PUT("/config", adminHandler.UpdateBannerConfig)
		}

		// --- 功能排序路由 ---
		featuresGroup := apiV1.Group("/features")
		featuresGroup.Use(authMiddleware.RequireAuth())
		{
			featuresGroup.GET("/ordering", adminHandler.GetFeatureOrdering)
			featuresGroup.PUT("/ordering", adminHandler.UpdateFeatureOrdering)
		}

		// --- 邮箱域名路由 ---
		emailDomainsGroup := apiV1.Group("/email-domains")
		emailDomainsGroup.Use(authMiddleware.RequireAuth())
		{
			emailDomainsGroup.GET("", adminHandler.ListEmailDomains)
			emailDomainsGroup.POST("", adminHandler.CreateEmailDomain)
			emailDomainsGroup.DELETE("/:id", adminHandler.DeleteEmailDomain)
		}

		// --- S3配置路由 ---
		s3ConfigGroup := apiV1.Group("/s3-configs")
		s3ConfigGroup.Use(authMiddleware.RequireAuth())
		{
			s3ConfigGroup.GET("", GetS3Configs)
			s3ConfigGroup.POST("", CreateS3Config)
			s3ConfigGroup.PUT("/:id", UpdateS3Config)
			s3ConfigGroup.DELETE("/:id", DeleteS3Config)
			s3ConfigGroup.POST("/:id/set-default", SetDefaultS3Config)
			s3ConfigGroup.POST("/:id/test", TestS3Config)
		}

		// --- 文件上传路由 ---
		uploadGroup := apiV1.Group("/upload")
		uploadGroup.Use(authMiddleware.RequireAuth())
		{
			uploadGroup.POST("", UploadFile)
			uploadGroup.POST("/avatar", UploadAvatar)
		}

		// --- 文件管理路由 ---
		filesGroup := apiV1.Group("/files")
		filesGroup.Use(authMiddleware.RequireAuth())
		{
			filesGroup.GET("", GetUserFiles)
			filesGroup.DELETE("/:id", DeleteFile)
		}
	}

	// --- 管理员路由 ---
	adminGroup := apiV1.Group("/admin")
	adminGroup.Use(authMiddleware.RequireAuth())
	{
		// 用户管理路由
		adminGroup.GET("/users", adminHandler.ListUsers)
		adminGroup.PUT("/users/:id", adminHandler.UpdateUser)
		adminGroup.DELETE("/users/:id", adminHandler.DeleteUser)
		adminGroup.GET("/users/stats", adminHandler.GetUserStats)
	}

	return router
}
