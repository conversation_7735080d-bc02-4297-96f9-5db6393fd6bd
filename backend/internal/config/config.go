package config

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config 存储应用的所有配置
// 通过 viper 从环境变量或配置文件中读取
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	JWT      JWTConfig
	Redis    RedisConfig
	CORS     CORSConfig
	SMTP     SMTPConfig
	App      AppConfig
}

type ServerConfig struct {
	Port         string
	Host         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

type DatabaseConfig struct {
	Driver          string // sqlite, mysql, postgres
	Source          string // 连接字符串
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
	AutoMigrate     bool
	LogLevel        string // silent, error, warn, info
}

type JWTConfig struct {
	SecretKey      string
	ExpirationTime time.Duration
	Issuer         string
}

type RedisConfig struct {
	Addr     string
	Password string
	DB       int
	Enabled  bool
}

type CORSConfig struct {
	AllowedOrigins   []string
	AllowedMethods   []string
	AllowedHeaders   []string
	AllowCredentials bool
}

type SMTPConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	From     string
	Enabled  bool
}

type AppConfig struct {
	Environment string // development, production
	Debug       bool
}

func Load() (*Config, error) {
	// 尝试加载 .env 文件
	if err := godotenv.Load(); err != nil {
		log.Println("未找到 .env 文件，使用环境变量")
	}

	config := &Config{
		Server: ServerConfig{
			Port:         getEnv("SERVER_PORT", "8080"),
			Host:         getEnv("SERVER_HOST", "0.0.0.0"),
			ReadTimeout:  getDurationEnv("SERVER_READ_TIMEOUT", 15*time.Second),
			WriteTimeout: getDurationEnv("SERVER_WRITE_TIMEOUT", 15*time.Second),
			IdleTimeout:  getDurationEnv("SERVER_IDLE_TIMEOUT", 60*time.Second),
		},
		Database: DatabaseConfig{
			Driver:          getEnv("DB_DRIVER", "sqlite"),
			Source:          getDatabaseSource(),
			MaxOpenConns:    getIntEnv("DB_MAX_OPEN_CONNS", 25),
			MaxIdleConns:    getIntEnv("DB_MAX_IDLE_CONNS", 5),
			ConnMaxLifetime: getDurationEnv("DB_CONN_MAX_LIFETIME", 5*time.Minute),
			AutoMigrate:     getBoolEnv("DB_AUTO_MIGRATE", true),
			LogLevel:        getEnv("DB_LOG_LEVEL", "warn"),
		},
		JWT: JWTConfig{
			SecretKey:      getEnv("JWT_SECRET_KEY", generateDefaultJWTSecret()),
			ExpirationTime: getDurationEnv("JWT_EXPIRATION", 24*time.Hour),
			Issuer:         getEnv("JWT_ISSUER", "url-stash-vault"),
		},
		Redis: RedisConfig{
			Addr:     getEnv("REDIS_ADDR", "localhost:6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getIntEnv("REDIS_DB", 0),
			Enabled:  getBoolEnv("REDIS_ENABLED", false),
		},
		CORS: CORSConfig{
			AllowedOrigins:   getSliceEnv("CORS_ALLOWED_ORIGINS", []string{"http://localhost:3000", "http://localhost:8080"}),
			AllowedMethods:   getSliceEnv("CORS_ALLOWED_METHODS", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
			AllowedHeaders:   getSliceEnv("CORS_ALLOWED_HEADERS", []string{"Content-Type", "Authorization"}),
			AllowCredentials: getBoolEnv("CORS_ALLOW_CREDENTIALS", true),
		},
		SMTP: SMTPConfig{
			Host:     getEnv("SMTP_HOST", ""),
			Port:     getIntEnv("SMTP_PORT", 587),
			Username: getEnv("SMTP_USERNAME", ""),
			Password: getEnv("SMTP_PASSWORD", ""),
			From:     getEnv("SMTP_FROM", "<EMAIL>"),
			Enabled:  getBoolEnv("SMTP_ENABLED", false),
		},
		App: AppConfig{
			Environment: getEnv("APP_ENV", "development"),
			Debug:       getBoolEnv("APP_DEBUG", true),
		},
	}

	// 验证必要配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return config, nil
}

func (c *Config) Validate() error {
	if c.JWT.SecretKey == "" {
		return fmt.Errorf("JWT_SECRET_KEY 不能为空")
	}

	if len(c.JWT.SecretKey) < 32 {
		return fmt.Errorf("JWT_SECRET_KEY 长度至少需要 32 个字符")
	}

	if c.Database.Driver == "" {
		return fmt.Errorf("DB_DRIVER 不能为空")
	}

	validDrivers := map[string]bool{
		"sqlite":   true,
		"mysql":    true,
		"postgres": true,
	}

	if !validDrivers[c.Database.Driver] {
		return fmt.Errorf("不支持的数据库驱动: %s，支持的驱动: sqlite, mysql, postgres", c.Database.Driver)
	}

	return nil
}

func getDatabaseSource() string {
	driver := getEnv("DB_DRIVER", "sqlite")
	source := os.Getenv("DB_SOURCE")

	if source != "" {
		return source
	}

	// 根据驱动类型生成默认连接字符串
	switch driver {
	case "sqlite":
		return "database.db" // 使用文件数据库而不是内存数据库
	case "mysql":
		host := getEnv("DB_HOST", "localhost")
		port := getEnv("DB_PORT", "3306")
		user := getEnv("DB_USER", "root")
		password := getEnv("DB_PASSWORD", "")
		dbname := getEnv("DB_NAME", "url_stash_vault")
		return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			user, password, host, port, dbname)
	case "postgres":
		host := getEnv("DB_HOST", "localhost")
		port := getEnv("DB_PORT", "5432")
		user := getEnv("DB_USER", "postgres")
		password := getEnv("DB_PASSWORD", "")
		dbname := getEnv("DB_NAME", "url_stash_vault")
		sslmode := getEnv("DB_SSLMODE", "disable")
		return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=Asia/Shanghai",
			host, port, user, password, dbname, sslmode)
	default:
		return "./data/app.db"
	}
}

func generateDefaultJWTSecret() string {
	log.Println("警告: 未设置 JWT_SECRET_KEY，使用默认密钥。生产环境请设置安全的随机密钥！")
	return "default-jwt-secret-key-change-in-production-environment-32-chars-minimum"
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getSliceEnv(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		// 简单的逗号分割，实际项目中可能需要更复杂的解析
		return []string{value}
	}
	return defaultValue
}
