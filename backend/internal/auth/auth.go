package auth

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"url-stash-vault/internal/cache"
	"url-stash-vault/internal/config"
	"url-stash-vault/internal/database"
)

var (
	ErrInvalidCredentials      = errors.New("邮箱或密码错误")
	ErrUserNotFound            = errors.New("用户不存在")
	ErrUserAlreadyExists       = errors.New("用户已存在")
	ErrWeakPassword            = errors.New("密码强度不足")
	ErrInvalidEmail            = errors.New("邮箱格式无效")
	ErrTokenExpired            = errors.New("令牌已过期")
	ErrInvalidToken            = errors.New("无效令牌")
	ErrInsufficientPermissions = errors.New("权限不足")
	ErrEmailNotVerified        = errors.New("邮箱未验证")
	ErrTokenAlreadyUsed        = errors.New("令牌已使用")
)

// jwtKey will be populated by InitAuth
var jwtKey []byte

const TokenTTL = time.Hour * 24 // Token Time To Live

// InitAuth initializes the auth package with the JWT secret key from config.
// This should be called once during application startup.
func InitAuth(cfg *config.Config) {
	if cfg.JWT.SecretKey == "" || cfg.JWT.SecretKey == "a_very_secure_default_secret_key_for_dev_only_change_it" {
		panic("JWT_SECRET_KEY is not set or is using the insecure default. Please set a strong secret key in your .env file.")
	}
	jwtKey = []byte(cfg.JWT.SecretKey)
}

type AuthService struct {
	cfg          *config.Config
	db           *gorm.DB
	emailService EmailService
}

// EmailService 定义邮件服务接口
type EmailService interface {
	SendVerificationEmail(to, token string) error
	SendPasswordResetEmail(to, token string) error
}

type Claims struct {
	UserID       uint     `json:"user_id"`
	Email        string   `json:"email"`
	IsSuperAdmin bool     `json:"is_super_admin"`
	Roles        []string `json:"roles"`
	jwt.RegisteredClaims
}

type UserInfo struct {
	ID            uint      `json:"id"`
	Username      string    `json:"username"`
	Email         string    `json:"email"`
	EmailVerified bool      `json:"email_verified"`
	IsSuperAdmin  bool      `json:"is_super_admin"`
	Roles         []string  `json:"roles"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
}

type LoginResponse struct {
	Token string   `json:"token"`
	User  UserInfo `json:"user"`
}

type ForgotPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type ResetPasswordRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

type VerifyEmailRequest struct {
	Token string `json:"token" binding:"required"`
}

type ResendVerificationEmailRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// PendingRegistration 待注册用户信息
type PendingRegistration struct {
	Email     string `gorm:"primaryKey"`
	Username  string `gorm:"uniqueIndex"`
	Password  string
	Token     string `gorm:"uniqueIndex"`
	ExpiresAt time.Time
	CreatedAt time.Time
}

func NewAuthService(cfg *config.Config, db *gorm.DB, emailService EmailService) *AuthService {
	return &AuthService{
		cfg:          cfg,
		db:           db,
		emailService: emailService,
	}
}

// Register 用户注册 - 修改为先发送验证邮件
func (s *AuthService) Register(req RegisterRequest) (*LoginResponse, error) {
	// 验证邮箱格式
	if !isValidEmail(req.Email) {
		return nil, ErrInvalidEmail
	}

	// 验证用户名格式
	if err := validateUsername(req.Username); err != nil {
		return nil, err
	}

	// 验证密码强度
	if err := validatePasswordStrength(req.Password); err != nil {
		return nil, err
	}

	// 检查邮箱是否已存在
	var existingUser database.User
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, fmt.Errorf("邮箱已被注册")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查邮箱存在性失败: %w", err)
	}

	// 检查用户名是否已存在
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, fmt.Errorf("用户名已被占用")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查用户名存在性失败: %w", err)
	}

	// 检查是否有待验证的注册
	var pendingReg PendingRegistration
	if err := s.db.Where("email = ? OR username = ?", req.Email, req.Username).First(&pendingReg).Error; err == nil {
		// 如果有待验证的注册且未过期，删除旧的
		s.db.Delete(&pendingReg)
	}

	// 哈希密码
	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码哈希失败: %w", err)
	}

	// 生成验证令牌
	token, err := generateRandomToken()
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	// 保存待注册信息
	pendingReg = PendingRegistration{
		Email:     req.Email,
		Username:  req.Username,
		Password:  hashedPassword,
		Token:     token,
		ExpiresAt: time.Now().Add(24 * time.Hour), // 24小时过期
		CreatedAt: time.Now(),
	}

	if err := s.db.Create(&pendingReg).Error; err != nil {
		return nil, fmt.Errorf("保存注册信息失败: %w", err)
	}

	// 检查SMTP是否启用
	if s.cfg.SMTP.Enabled && s.emailService != nil {
		// SMTP启用时，发送验证邮件
		if err := s.emailService.SendVerificationEmail(req.Email, token); err != nil {
			// 邮件发送失败，删除待注册信息
			s.db.Delete(&pendingReg)
			return nil, fmt.Errorf("发送验证邮件失败: %w", err)
		}

		// 返回成功但不创建用户，也不返回token
		return &LoginResponse{
			Token: "", // 不返回token
			User: UserInfo{
				Email:    req.Email,
				Username: req.Username,
			},
		}, nil
	} else {
		// SMTP未启用时，直接创建用户（跳过邮件验证）
		s.db.Delete(&pendingReg) // 删除待注册信息

		// 检查是否是第一个用户（超级管理员）
		var userCount int64
		if err := s.db.Model(&database.User{}).Count(&userCount).Error; err != nil {
			return nil, fmt.Errorf("检查用户数量失败: %w", err)
		}

		// 创建用户
		user := database.User{
			Username:      req.Username,
			Email:         req.Email,
			Password:      hashedPassword,
			EmailVerified: true,           // 直接设为已验证
			IsSuperAdmin:  userCount == 0, // 第一个用户是超级管理员
		}

		if err := s.db.Create(&user).Error; err != nil {
			// 检查是否是唯一约束错误
			if isDuplicateError(err) {
				return nil, fmt.Errorf("用户名或邮箱已存在")
			}
			return nil, fmt.Errorf("创建用户失败: %w", err)
		}

		// 分配默认角色
		if err := s.assignDefaultRole(user.ID); err != nil {
			return nil, fmt.Errorf("分配默认角色失败: %w", err)
		}

		// 生成JWT令牌
		token, err := s.generateToken(user.ID, user.Username)
		if err != nil {
			return nil, fmt.Errorf("生成令牌失败: %w", err)
		}

		// 获取用户信息
		userInfo, err := s.getUserInfo(user.ID)
		if err != nil {
			return nil, fmt.Errorf("获取用户信息失败: %w", err)
		}

		return &LoginResponse{
			Token: token,
			User:  *userInfo,
		}, nil
	}
}

// ConfirmRegistration 确认注册（验证邮箱后创建用户）
func (s *AuthService) ConfirmRegistration(token string) (*LoginResponse, error) {
	// 查找待注册信息
	var pendingReg PendingRegistration
	if err := s.db.Where("token = ? AND expires_at > ?", token, time.Now()).First(&pendingReg).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("验证链接无效或已过期")
		}
		return nil, fmt.Errorf("查找注册信息失败: %w", err)
	}

	// 检查是否是第一个用户（超级管理员）
	var userCount int64
	if err := s.db.Model(&database.User{}).Count(&userCount).Error; err != nil {
		return nil, fmt.Errorf("检查用户数量失败: %w", err)
	}

	// 创建用户
	user := database.User{
		Username:      pendingReg.Username,
		Email:         pendingReg.Email,
		Password:      pendingReg.Password,
		EmailVerified: true,           // 邮箱已验证
		IsSuperAdmin:  userCount == 0, // 第一个用户是超级管理员
	}

	if err := s.db.Create(&user).Error; err != nil {
		// 检查是否是唯一约束错误
		if isDuplicateError(err) {
			return nil, fmt.Errorf("用户名或邮箱已存在")
		}
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 删除待注册信息
	s.db.Delete(&pendingReg)

	// 分配默认角色
	if err := s.assignDefaultRole(user.ID); err != nil {
		// 记录错误但不阻止注册流程
		fmt.Printf("分配默认角色失败: %v\n", err)
	}

	// 生成 JWT 令牌
	tokenStr, err := s.generateToken(user.ID, user.Username)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	// 获取用户信息（包括角色）
	userInfo, err := s.getUserInfo(user.ID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	return &LoginResponse{
		Token: tokenStr,
		User:  *userInfo,
	}, nil
}

// Login 用户登录
func (s *AuthService) Login(req LoginRequest) (*LoginResponse, error) {
	// 查找用户
	var user database.User
	if err := s.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrInvalidCredentials
		}
		return nil, fmt.Errorf("查找用户失败: %w", err)
	}

	// 验证密码
	if !checkPasswordHash(req.Password, user.Password) {
		return nil, ErrInvalidCredentials
	}

	// 检查邮箱是否已验证
	if !user.EmailVerified {
		return nil, ErrEmailNotVerified
	}

	// 检查是否已有有效的JWT token（多端登录使用相同token）
	var tokenStr string
	if cache.IsRedisEnabled() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		existingToken, err := cache.GetJWTTokenByUsername(ctx, user.Username)
		if err == nil && existingToken != "" {
			// 验证现有token是否仍然有效
			if _, err := s.ValidateToken(existingToken); err == nil {
				// 现有token有效，直接使用
				tokenStr = existingToken
				fmt.Printf("用户 %s 使用现有有效token登录\n", user.Username)
			} else {
				// 现有token无效，删除并生成新的
				cache.DeleteJWTTokenByUsername(ctx, user.Username)
				tokenStr, err = s.generateToken(user.ID, user.Username)
				if err != nil {
					return nil, fmt.Errorf("生成令牌失败: %w", err)
				}
			}
		} else {
			// 没有现有token，生成新的
			tokenStr, err = s.generateToken(user.ID, user.Username)
			if err != nil {
				return nil, fmt.Errorf("生成令牌失败: %w", err)
			}
		}
	} else {
		// Redis未启用，直接生成新token
		var err error
		tokenStr, err = s.generateToken(user.ID, user.Username)
		if err != nil {
			return nil, fmt.Errorf("生成令牌失败: %w", err)
		}
	}

	// 获取用户信息（包括角色）
	userInfo, err := s.getUserInfo(user.ID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	return &LoginResponse{
		Token: tokenStr,
		User:  *userInfo,
	}, nil
}

// Logout 用户登出，删除Redis中的JWT token
func (s *AuthService) Logout(userID uint) error {
	// 获取用户信息以获取用户名
	userInfo, err := s.getUserInfo(userID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 如果Redis启用，删除缓存的JWT token
	if cache.IsRedisEnabled() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := cache.DeleteJWTTokenByUsername(ctx, userInfo.Username); err != nil {
			return fmt.Errorf("删除Redis中的JWT token失败: %w", err)
		}
	}

	return nil
}

// ValidateToken 验证 JWT 令牌
func (s *AuthService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return []byte(s.cfg.JWT.SecretKey), nil
	})

	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, ErrTokenExpired
		}
		return nil, ErrInvalidToken
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		// 验证用户是否仍然存在且活跃
		var user database.User
		if err := s.db.Where("id = ?", claims.UserID).First(&user).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, ErrUserNotFound
			}
			return nil, fmt.Errorf("验证用户状态失败: %w", err)
		}

		// 如果Redis启用，验证token是否在Redis中存在（用于登出后的token失效）
		if cache.IsRedisEnabled() {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			cachedToken, err := cache.GetJWTTokenByUsername(ctx, user.Username)
			if err != nil {
				fmt.Printf("从Redis获取JWT token失败: %v\n", err)
			} else if cachedToken != tokenString {
				// Redis中的token与当前token不匹配，可能已登出或被替换
				return nil, ErrInvalidToken
			}
		}

		return claims, nil
	}

	return nil, ErrInvalidToken
}

// GetUserByID 根据 ID 获取用户信息
func (s *AuthService) GetUserByID(userID uint) (*UserInfo, error) {
	return s.getUserInfo(userID)
}

// CheckPermission 检查用户权限
func (s *AuthService) CheckPermission(userID uint, resource, action string) error {
	userInfo, err := s.getUserInfo(userID)
	if err != nil {
		return err
	}

	// 超级管理员拥有所有权限
	if userInfo.IsSuperAdmin {
		return nil
	}

	// 检查角色权限
	hasPermission := s.checkRolePermissions(userInfo.Roles, resource, action)
	if !hasPermission {
		return ErrInsufficientPermissions
	}

	return nil
}

// CheckResourceOwnership 检查资源所有权
func (s *AuthService) CheckResourceOwnership(userID uint, resourceUserID uint) error {
	// 用户只能访问自己的资源
	if userID != resourceUserID {
		// 检查是否是超级管理员
		userInfo, err := s.getUserInfo(userID)
		if err != nil {
			return err
		}

		if !userInfo.IsSuperAdmin {
			return ErrInsufficientPermissions
		}
	}

	return nil
}

// 内部方法

func (s *AuthService) generateToken(userID uint, username string) (string, error) {
	userInfo, err := s.getUserInfo(userID)
	if err != nil {
		return "", err
	}

	claims := Claims{
		UserID:       userInfo.ID,
		Email:        userInfo.Email,
		IsSuperAdmin: userInfo.IsSuperAdmin,
		Roles:        userInfo.Roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.cfg.JWT.ExpirationTime)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    s.cfg.JWT.Issuer,
			Subject:   fmt.Sprintf("%d", userID),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.cfg.JWT.SecretKey))
	if err != nil {
		return "", err
	}

	// 如果Redis启用，将JWT token缓存到Redis（根据用户名存储）
	if cache.IsRedisEnabled() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := cache.SetJWTTokenByUsername(ctx, username, tokenString, s.cfg.JWT.ExpirationTime); err != nil {
			// 记录错误但不影响主流程
			fmt.Printf("缓存JWT token到Redis失败: %v\n", err)
		}
	}

	return tokenString, nil
}

func (s *AuthService) getUserInfo(userID uint) (*UserInfo, error) {
	var user database.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("查找用户失败: %w", err)
	}

	// 获取用户角色
	var userRoles []database.UserRole
	if err := s.db.Where("user_id = ?", userID).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	roles := make([]string, len(userRoles))
	for i, role := range userRoles {
		roles[i] = role.Role
	}

	return &UserInfo{
		ID:            user.ID,
		Username:      user.Username,
		Email:         user.Email,
		EmailVerified: user.EmailVerified,
		IsSuperAdmin:  user.IsSuperAdmin,
		Roles:         roles,
		CreatedAt:     user.CreatedAt,
		UpdatedAt:     user.UpdatedAt,
	}, nil
}

func (s *AuthService) assignDefaultRole(userID uint) error {
	defaultRole := database.UserRole{
		ID:     fmt.Sprintf("user_%d_default", userID),
		UserID: userID,
		Role:   "user", // 默认角色
	}

	return s.db.Create(&defaultRole).Error
}

func (s *AuthService) checkRolePermissions(roles []string, resource, action string) bool {
	// 简单的权限检查逻辑
	// 实际项目中可能需要更复杂的 RBAC 系统

	for _, role := range roles {
		switch role {
		case "admin":
			return true // 管理员拥有所有权限
		case "user":
			// 普通用户的权限
			switch resource {
			case "memo", "shorturl", "todo":
				return action == "read" || action == "write" || action == "delete"
			case "temp_email":
				return action == "read" || action == "write"
			}
		}
	}

	return false
}

// 工具函数

func hashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

func checkPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func validatePasswordStrength(password string) error {
	if len(password) < 8 {
		return fmt.Errorf("%w: 密码长度至少需要 8 个字符", ErrWeakPassword)
	}

	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	if !hasUpper {
		return fmt.Errorf("%w: 密码必须包含至少一个大写字母", ErrWeakPassword)
	}
	if !hasLower {
		return fmt.Errorf("%w: 密码必须包含至少一个小写字母", ErrWeakPassword)
	}
	if !hasNumber {
		return fmt.Errorf("%w: 密码必须包含至少一个数字", ErrWeakPassword)
	}
	if !hasSpecial {
		return fmt.Errorf("%w: 密码必须包含至少一个特殊字符", ErrWeakPassword)
	}

	return nil
}

// GetUserIDFromContext retrieves the user ID from the Gin context (set by AuthMiddleware).
func GetUserIDFromContext(c *gin.Context) (uint, error) {
	userIDUntyped, exists := c.Get("userID")
	if !exists {
		return 0, errors.New("userID not found in context, ensure AuthMiddleware is used")
	}
	userID, ok := userIDUntyped.(uint)
	if !ok {
		return 0, errors.New("userID in context is not of type uint")
	}
	return userID, nil
}

// SendVerificationEmail 发送邮箱验证邮件
func (s *AuthService) SendVerificationEmail(userID uint) error {
	// 查找用户
	var user database.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrUserNotFound
		}
		return fmt.Errorf("查找用户失败: %w", err)
	}

	// 如果已经验证，不需要再发送
	if user.EmailVerified {
		return nil
	}

	// 生成验证令牌
	token, err := generateRandomToken()
	if err != nil {
		return fmt.Errorf("生成令牌失败: %w", err)
	}

	// 保存验证令牌到数据库
	verificationToken := database.EmailVerificationToken{
		UserID:    userID,
		Token:     token,
		ExpiresAt: time.Now().Add(24 * time.Hour), // 24小时过期
		Used:      false,
	}

	if err := s.db.Create(&verificationToken).Error; err != nil {
		return fmt.Errorf("保存验证令牌失败: %w", err)
	}

	// 发送验证邮件
	return s.emailService.SendVerificationEmail(user.Email, token)
}

// VerifyEmail 验证邮箱
func (s *AuthService) VerifyEmail(req VerifyEmailRequest) error {
	// 首先检查是否是待注册用户的验证令牌
	var pendingReg PendingRegistration
	if err := s.db.Where("token = ? AND expires_at > ?", req.Token, time.Now()).First(&pendingReg).Error; err == nil {
		// 这是待注册用户的验证，需要创建用户
		return s.ConfirmRegistrationByToken(req.Token)
	}

	// 如果不是待注册用户，检查是否是已注册用户的验证令牌
	var verificationToken database.EmailVerificationToken
	if err := s.db.Where("token = ? AND used = false AND expires_at > ?", req.Token, time.Now()).First(&verificationToken).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrInvalidToken
		}
		return fmt.Errorf("查找验证令牌失败: %w", err)
	}

	// 标记令牌为已使用
	if err := s.db.Model(&verificationToken).Update("used", true).Error; err != nil {
		return fmt.Errorf("更新令牌状态失败: %w", err)
	}

	// 更新用户邮箱验证状态
	if err := s.db.Model(&database.User{}).Where("id = ?", verificationToken.UserID).Update("email_verified", true).Error; err != nil {
		return fmt.Errorf("更新用户验证状态失败: %w", err)
	}

	return nil
}

// ConfirmRegistrationByToken 通过令牌确认注册（内部方法）
func (s *AuthService) ConfirmRegistrationByToken(token string) error {
	// 查找待注册信息
	var pendingReg PendingRegistration
	if err := s.db.Where("token = ? AND expires_at > ?", token, time.Now()).First(&pendingReg).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("验证链接无效或已过期")
		}
		return fmt.Errorf("查找注册信息失败: %w", err)
	}

	// 检查是否是第一个用户（超级管理员）
	var userCount int64
	if err := s.db.Model(&database.User{}).Count(&userCount).Error; err != nil {
		return fmt.Errorf("检查用户数量失败: %w", err)
	}

	// 创建用户
	user := database.User{
		Username:      pendingReg.Username,
		Email:         pendingReg.Email,
		Password:      pendingReg.Password,
		EmailVerified: true, // 邮箱已验证
		IsSuperAdmin:  userCount == 0, // 第一个用户是超级管理员
	}

	if err := s.db.Create(&user).Error; err != nil {
		if isDuplicateError(err) {
			return fmt.Errorf("用户名或邮箱已存在")
		}
		return fmt.Errorf("创建用户失败: %w", err)
	}

	// 删除待注册信息
	if err := s.db.Delete(&pendingReg).Error; err != nil {
		log.Printf("删除待注册信息失败: %v", err)
		// 不返回错误，因为用户已经创建成功
	}

	return nil
}

// ForgotPassword 忘记密码
func (s *AuthService) ForgotPassword(req ForgotPasswordRequest) error {
	// 查找用户
	var user database.User
	if err := s.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 为了安全，即使用户不存在也返回成功
			return nil
		}
		return fmt.Errorf("查找用户失败: %w", err)
	}

	// 生成重置令牌
	token, err := generateRandomToken()
	if err != nil {
		return fmt.Errorf("生成令牌失败: %w", err)
	}

	// 保存重置令牌到数据库
	resetToken := database.PasswordResetToken{
		UserID:    user.ID,
		Token:     token,
		ExpiresAt: time.Now().Add(1 * time.Hour), // 1小时过期
		Used:      false,
	}

	if err := s.db.Create(&resetToken).Error; err != nil {
		return fmt.Errorf("保存重置令牌失败: %w", err)
	}

	// 发送密码重置邮件
	return s.emailService.SendPasswordResetEmail(user.Email, token)
}

// ResetPassword 重置密码
func (s *AuthService) ResetPassword(req ResetPasswordRequest) error {
	// 验证密码强度
	if err := validatePasswordStrength(req.NewPassword); err != nil {
		return err
	}

	// 查找重置令牌
	var resetToken database.PasswordResetToken
	if err := s.db.Where("token = ? AND used = false AND expires_at > ?", req.Token, time.Now()).First(&resetToken).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrInvalidToken
		}
		return fmt.Errorf("查找重置令牌失败: %w", err)
	}

	// 哈希新密码
	hashedPassword, err := hashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	// 标记令牌为已使用
	if err := s.db.Model(&resetToken).Update("used", true).Error; err != nil {
		return fmt.Errorf("更新令牌状态失败: %w", err)
	}

	// 更新用户密码
	if err := s.db.Model(&database.User{}).Where("id = ?", resetToken.UserID).Update("password", hashedPassword).Error; err != nil {
		return fmt.Errorf("更新用户密码失败: %w", err)
	}

	return nil
}

// ResendVerificationEmail 重新发送验证邮件（通过邮箱地址）
func (s *AuthService) ResendVerificationEmail(email string) error {
	// 首先检查是否是待注册用户
	var pendingReg PendingRegistration
	if err := s.db.Where("email = ?", email).First(&pendingReg).Error; err == nil {
		// 检查是否在短时间内重复发送（防止滥用）
		if pendingReg.CreatedAt.After(time.Now().Add(-1 * time.Minute)) {
			return fmt.Errorf("请等待60秒后再重新发送")
		}

		// 生成新的验证令牌
		token, err := generateRandomToken()
		if err != nil {
			return fmt.Errorf("生成令牌失败: %w", err)
		}

		// 更新待注册信息的令牌和过期时间
		pendingReg.Token = token
		pendingReg.ExpiresAt = time.Now().Add(24 * time.Hour)
		pendingReg.CreatedAt = time.Now() // 更新创建时间用于冷却检查

		if err := s.db.Save(&pendingReg).Error; err != nil {
			return fmt.Errorf("更新待注册信息失败: %w", err)
		}

		// 发送验证邮件
		return s.emailService.SendVerificationEmail(email, token)
	}

	// 如果不是待注册用户，检查是否是已注册但未验证的用户
	var user database.User
	if err := s.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrUserNotFound
		}
		return fmt.Errorf("查找用户失败: %w", err)
	}

	// 如果已经验证，不需要再发送
	if user.EmailVerified {
		return fmt.Errorf("邮箱已验证")
	}

	// 检查是否在短时间内重复发送（防止滥用）
	var lastToken database.EmailVerificationToken
	if err := s.db.Where("user_id = ? AND created_at > ?", user.ID, time.Now().Add(-1*time.Minute)).
		Order("created_at DESC").First(&lastToken).Error; err == nil {
		return fmt.Errorf("请等待60秒后再重新发送")
	}

	// 生成验证令牌
	token, err := generateRandomToken()
	if err != nil {
		return fmt.Errorf("生成令牌失败: %w", err)
	}

	// 保存验证令牌到数据库
	verificationToken := database.EmailVerificationToken{
		UserID:    user.ID,
		Token:     token,
		ExpiresAt: time.Now().Add(24 * time.Hour), // 24小时过期
		Used:      false,
	}

	if err := s.db.Create(&verificationToken).Error; err != nil {
		return fmt.Errorf("保存验证令牌失败: %w", err)
	}

	// 发送验证邮件
	return s.emailService.SendVerificationEmail(user.Email, token)
}

// generateRandomToken 生成随机令牌
func generateRandomToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func validateUsername(username string) error {
	if len(username) < 3 {
		return fmt.Errorf("用户名长度至少需要 3 个字符")
	}
	if len(username) > 50 {
		return fmt.Errorf("用户名长度不能超过 50 个字符")
	}

	// 允许字母、数字、下划线和连字符
	validUsernameRegex := regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
	if !validUsernameRegex.MatchString(username) {
		return fmt.Errorf("用户名只能包含字母、数字、下划线和连字符")
	}

	return nil
}

func isDuplicateError(err error) bool {
	if err == nil {
		return false
	}

	// SQLite duplicate entry error
	errorStr := err.Error()
	return strings.Contains(errorStr, "UNIQUE constraint failed") ||
		strings.Contains(errorStr, "duplicate key value") ||
		strings.Contains(errorStr, "already exists")
}

// ListUsers 获取用户列表（管理员功能）
func (s *AuthService) ListUsers(page, limit int, search string) ([]UserInfo, int64, error) {
	var users []database.User
	var total int64

	query := s.db.Model(&database.User{})

	// 如果有搜索条件
	if search != "" {
		query = query.Where("username ILIKE ? OR email ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * limit
	if err := query.Preload("Roles").Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("查询用户列表失败: %w", err)
	}

	// 转换为UserInfo格式
	userInfos := make([]UserInfo, len(users))
	for i, user := range users {
		roles := make([]string, len(user.Roles))
		for j, role := range user.Roles {
			roles[j] = role.Role
		}

		userInfos[i] = UserInfo{
			ID:            user.ID,
			Username:      user.Username,
			Email:         user.Email,
			EmailVerified: user.EmailVerified,
			IsSuperAdmin:  user.IsSuperAdmin,
			Roles:         roles,
			CreatedAt:     user.CreatedAt,
			UpdatedAt:     user.UpdatedAt,
		}
	}

	return userInfos, total, nil
}

// UpdateUser 更新用户信息（管理员功能）
func (s *AuthService) UpdateUser(userID uint, username, email *string, emailVerified, isSuperAdmin *bool) error {
	// 查找用户
	var user database.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrUserNotFound
		}
		return fmt.Errorf("查找用户失败: %w", err)
	}

	// 准备更新数据
	updates := make(map[string]interface{})

	if username != nil {
		// 检查用户名是否被其他用户占用
		var existingUser database.User
		err := s.db.Where("username = ? AND id != ?", *username, userID).First(&existingUser).Error
		if err == nil {
			return fmt.Errorf("用户名已被其他用户占用")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("检查用户名唯一性失败: %w", err)
		}
		updates["username"] = *username
	}

	if email != nil {
		// 检查邮箱是否被其他用户占用
		var existingUser database.User
		err := s.db.Where("email = ? AND id != ?", *email, userID).First(&existingUser).Error
		if err == nil {
			return fmt.Errorf("邮箱已被其他用户占用")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("检查邮箱唯一性失败: %w", err)
		}
		updates["email"] = *email
	}

	if emailVerified != nil {
		updates["email_verified"] = *emailVerified
	}

	if isSuperAdmin != nil {
		updates["is_super_admin"] = *isSuperAdmin
	}

	// 执行更新
	if len(updates) > 0 {
		if err := s.db.Model(&user).Updates(updates).Error; err != nil {
			return fmt.Errorf("更新用户信息失败: %w", err)
		}
	}

	return nil
}

// DeleteUser 删除用户（管理员功能）
func (s *AuthService) DeleteUser(userID uint) error {
	// 查找用户
	var user database.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrUserNotFound
		}
		return fmt.Errorf("查找用户失败: %w", err)
	}

	// 软删除用户（使用GORM的软删除功能）
	if err := s.db.Delete(&user).Error; err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	return nil
}

// GetUserStats 获取用户统计信息（管理员功能）
func (s *AuthService) GetUserStats() (map[string]interface{}, error) {
	var totalUsers int64
	var superAdmins int64
	var verifiedUsers int64
	var newUsersThisMonth int64

	// 总用户数
	if err := s.db.Model(&database.User{}).Count(&totalUsers).Error; err != nil {
		return nil, fmt.Errorf("获取总用户数失败: %w", err)
	}

	// 超级管理员数量
	if err := s.db.Model(&database.User{}).Where("is_super_admin = ?", true).Count(&superAdmins).Error; err != nil {
		return nil, fmt.Errorf("获取超级管理员数量失败: %w", err)
	}

	// 已验证邮箱的用户数量
	if err := s.db.Model(&database.User{}).Where("email_verified = ?", true).Count(&verifiedUsers).Error; err != nil {
		return nil, fmt.Errorf("获取已验证用户数量失败: %w", err)
	}

	// 本月新增用户数
	startOfMonth := time.Now().AddDate(0, 0, -time.Now().Day()+1)
	if err := s.db.Model(&database.User{}).Where("created_at >= ?", startOfMonth).Count(&newUsersThisMonth).Error; err != nil {
		return nil, fmt.Errorf("获取本月新增用户数失败: %w", err)
	}

	// 获取角色统计
	var roleStats []struct {
		Role  string `json:"role"`
		Count int64  `json:"count"`
	}

	if err := s.db.Model(&database.UserRole{}).Select("role, COUNT(*) as count").Group("role").Scan(&roleStats).Error; err != nil {
		return nil, fmt.Errorf("获取角色统计失败: %w", err)
	}

	return map[string]interface{}{
		"total_users":          totalUsers,
		"super_admins":         superAdmins,
		"verified_users":       verifiedUsers,
		"new_users_this_month": newUsersThisMonth,
		"verification_rate":    float64(verifiedUsers) / float64(totalUsers) * 100,
		"role_distribution":    roleStats,
	}, nil
}
