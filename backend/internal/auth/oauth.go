package auth

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"gorm.io/gorm"

	"url-stash-vault/internal/cache"
	"url-stash-vault/internal/database"
)

var (
	ErrOAuthProviderNotFound   = errors.New("OAuth提供商未找到")
	ErrOAuthProviderDisabled   = errors.New("OAuth提供商已禁用")
	ErrOAuthStateInvalid       = errors.New("OAuth状态无效")
	ErrOAuthCodeInvalid        = errors.New("OAuth授权码无效")
	ErrAccountLinkRequired     = errors.New("需要账户关联")
	ErrAccountLinkTokenInvalid = errors.New("账户关联令牌无效")
)

// OAuth提供商信息
type OAuthProviderInfo struct {
	ID           string   `json:"id"`
	ProviderName string   `json:"provider_name"`
	ClientID     string   `json:"client_id"`
	RedirectURL  string   `json:"redirect_url"`
	Scopes       []string `json:"scopes"`
	Enabled      bool     `json:"enabled"`
}

// OAuth用户信息
type OAuthUserInfo struct {
	ProviderUserID string `json:"provider_user_id"`
	Email          string `json:"email"`
	Username       string `json:"username"`
	AvatarURL      string `json:"avatar_url,omitempty"`
	Name           string `json:"name,omitempty"`
}

// OAuth认证响应
type OAuthAuthResponse struct {
	Token             string         `json:"token,omitempty"`
	User              *UserInfo      `json:"user,omitempty"`
	NeedAccountLink   bool           `json:"need_account_link"`
	AccountLinkToken  string         `json:"account_link_token,omitempty"`
	ExistingUserEmail string         `json:"existing_user_email,omitempty"`
	ProviderUserInfo  *OAuthUserInfo `json:"provider_user_info,omitempty"`
}

// 账户关联请求
type LinkAccountRequest struct {
	Token        string `json:"token" binding:"required"`
	Username     string `json:"username,omitempty"`
	LinkExisting bool   `json:"link_existing"`
}

// GetOAuthProviders 获取所有启用的OAuth提供商
func (s *AuthService) GetOAuthProviders() ([]OAuthProviderInfo, error) {
	var providers []database.OAuthProvider
	if err := s.db.Where("enabled = ?", true).Find(&providers).Error; err != nil {
		return nil, fmt.Errorf("获取OAuth提供商失败: %w", err)
	}

	result := make([]OAuthProviderInfo, len(providers))
	for i, provider := range providers {
		var scopes []string
		if err := json.Unmarshal(provider.Scopes, &scopes); err != nil {
			scopes = []string{}
		}

		result[i] = OAuthProviderInfo{
			ID:           provider.ID,
			ProviderName: provider.ProviderName,
			ClientID:     provider.ClientID,
			RedirectURL:  provider.RedirectURL,
			Scopes:       scopes,
			Enabled:      provider.Enabled,
		}
	}

	return result, nil
}

// GetOAuthAuthURL 获取OAuth认证URL
func (s *AuthService) GetOAuthAuthURL(providerName, state string) (string, error) {
	provider, err := s.getOAuthProvider(providerName)
	if err != nil {
		return "", err
	}

	if !provider.Enabled {
		return "", ErrOAuthProviderDisabled
	}

	var scopes []string
	if err := json.Unmarshal(provider.Scopes, &scopes); err != nil {
		return "", fmt.Errorf("解析OAuth作用域失败: %w", err)
	}

	switch providerName {
	case "github":
		return s.buildGitHubAuthURL(provider.ClientID, provider.RedirectURL, state, scopes), nil
	case "google":
		return s.buildGoogleAuthURL(provider.ClientID, provider.RedirectURL, state, scopes), nil
	default:
		return "", fmt.Errorf("不支持的OAuth提供商: %s", providerName)
	}
}

// HandleOAuthCallback 处理OAuth回调
func (s *AuthService) HandleOAuthCallback(providerName, code, state string) (*OAuthAuthResponse, error) {
	provider, err := s.getOAuthProvider(providerName)
	if err != nil {
		return nil, err
	}

	if !provider.Enabled {
		return nil, ErrOAuthProviderDisabled
	}

	// 获取访问令牌
	accessToken, err := s.getOAuthAccessToken(providerName, code, provider)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 获取用户信息
	userInfo, err := s.getOAuthUserInfo(providerName, accessToken)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 检查是否已有关联账户
	var oauthAccount database.UserOAuthAccount
	err = s.db.Where("provider_name = ? AND provider_user_id = ?",
		providerName, userInfo.ProviderUserID).First(&oauthAccount).Error

	if err == nil {
		// 已有关联账户，直接登录
		user, err := s.getUserInfo(oauthAccount.UserID)
		if err != nil {
			return nil, fmt.Errorf("获取用户信息失败: %w", err)
		}

		// 检查是否已有有效的JWT token（多端登录使用相同token）
		var token string
		if cache.IsRedisEnabled() {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			existingToken, err := cache.GetJWTTokenByUsername(ctx, user.Username)
			if err == nil && existingToken != "" {
				// 验证现有token是否仍然有效
				if _, err := s.ValidateToken(existingToken); err == nil {
					// 现有token有效，直接使用
					token = existingToken
					fmt.Printf("OAuth用户 %s 使用现有有效token登录\n", user.Username)
				} else {
					// 现有token无效，删除并生成新的
					cache.DeleteJWTTokenByUsername(ctx, user.Username)
					token, err = s.generateToken(oauthAccount.UserID, user.Username)
					if err != nil {
						return nil, fmt.Errorf("生成令牌失败: %w", err)
					}
				}
			} else {
				// 没有现有token，生成新的
				token, err = s.generateToken(oauthAccount.UserID, user.Username)
				if err != nil {
					return nil, fmt.Errorf("生成令牌失败: %w", err)
				}
			}
		} else {
			// Redis未启用，直接生成新token
			var err error
			token, err = s.generateToken(oauthAccount.UserID, user.Username)
			if err != nil {
				return nil, fmt.Errorf("生成令牌失败: %w", err)
			}
		}

		return &OAuthAuthResponse{
			Token: token,
			User:  user,
		}, nil
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询OAuth账户失败: %w", err)
	}

	// 检查是否已有相同邮箱的用户
	var existingUser database.User
	err = s.db.Where("email = ?", userInfo.Email).First(&existingUser).Error

	if err == nil {
		// 有相同邮箱的用户，需要关联
		linkToken, err := s.createAccountLinkRequest(userInfo, providerName)
		if err != nil {
			return nil, fmt.Errorf("创建账户关联请求失败: %w", err)
		}

		return &OAuthAuthResponse{
			NeedAccountLink:   true,
			AccountLinkToken:  linkToken,
			ExistingUserEmail: existingUser.Email,
			ProviderUserInfo:  userInfo,
		}, nil
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 创建新用户，但需要设置用户名
	linkToken, err := s.createAccountLinkRequest(userInfo, providerName)
	if err != nil {
		return nil, fmt.Errorf("创建账户关联请求失败: %w", err)
	}

	return &OAuthAuthResponse{
		NeedAccountLink:  true,
		AccountLinkToken: linkToken,
		ProviderUserInfo: userInfo,
	}, nil
}

// LinkOAuthAccount 关联OAuth账户
func (s *AuthService) LinkOAuthAccount(req LinkAccountRequest) (*OAuthAuthResponse, error) {
	// 查找账户关联请求
	var linkRequest database.AccountLinkRequest
	err := s.db.Where("token = ? AND used = false AND expires_at > ?",
		req.Token, time.Now()).First(&linkRequest).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAccountLinkTokenInvalid
		}
		return nil, fmt.Errorf("查找账户关联请求失败: %w", err)
	}

	// 标记为已使用
	if err := s.db.Model(&linkRequest).Update("used", true).Error; err != nil {
		return nil, fmt.Errorf("更新关联请求状态失败: %w", err)
	}

	var user database.User
	var userID uint

	if req.LinkExisting {
		// 关联到现有用户
		if err := s.db.Where("email = ?", linkRequest.Email).First(&user).Error; err != nil {
			return nil, fmt.Errorf("查找现有用户失败: %w", err)
		}
		userID = user.ID
	} else {
		// 创建新用户
		if req.Username == "" {
			return nil, errors.New("创建新用户时用户名不能为空")
		}

		// 检查用户名是否唯一
		var existingUser database.User
		if err := s.db.Where("email = ?", req.Username+"@oauth.local").First(&existingUser).Error; err == nil {
			return nil, errors.New("用户名已存在")
		}

		// 生成随机密码（OAuth用户不使用密码登录）
		randomPassword, err := generateRandomToken()
		if err != nil {
			return nil, fmt.Errorf("生成随机密码失败: %w", err)
		}

		hashedPassword, err := hashPassword(randomPassword)
		if err != nil {
			return nil, fmt.Errorf("哈希密码失败: %w", err)
		}

		user = database.User{
			Email:         linkRequest.Email,
			Password:      hashedPassword,
			EmailVerified: true, // OAuth用户邮箱已验证
			IsSuperAdmin:  false,
		}

		if err := s.db.Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建用户失败: %w", err)
		}

		userID = user.ID

		// 分配默认角色
		if err := s.assignDefaultRole(userID); err != nil {
			fmt.Printf("分配默认角色失败: %v\n", err)
		}
	}

	// 创建OAuth账户关联
	var providerData map[string]interface{}
	if err := json.Unmarshal(linkRequest.ProviderData, &providerData); err != nil {
		return nil, fmt.Errorf("解析提供商数据失败: %w", err)
	}

	oauthAccount := database.UserOAuthAccount{
		ID:               fmt.Sprintf("%s_%s_%d", linkRequest.ProviderName, linkRequest.ProviderUserID, userID),
		UserID:           userID,
		ProviderName:     linkRequest.ProviderName,
		ProviderUserID:   linkRequest.ProviderUserID,
		ProviderEmail:    linkRequest.Email,
		ProviderUsername: providerData["username"].(string),
		ProviderData:     linkRequest.ProviderData,
	}

	if err := s.db.Create(&oauthAccount).Error; err != nil {
		return nil, fmt.Errorf("创建OAuth账户关联失败: %w", err)
	}

	// 获取用户信息
	userInfo, err := s.getUserInfo(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 检查是否已有有效的JWT token（多端登录使用相同token）
	var token string
	if cache.IsRedisEnabled() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		existingToken, err := cache.GetJWTTokenByUsername(ctx, userInfo.Username)
		if err == nil && existingToken != "" {
			// 验证现有token是否仍然有效
			if _, err := s.ValidateToken(existingToken); err == nil {
				// 现有token有效，直接使用
				token = existingToken
				fmt.Printf("OAuth关联用户 %s 使用现有有效token登录\n", userInfo.Username)
			} else {
				// 现有token无效，删除并生成新的
				cache.DeleteJWTTokenByUsername(ctx, userInfo.Username)
				token, err = s.generateToken(userID, userInfo.Username)
				if err != nil {
					return nil, fmt.Errorf("生成令牌失败: %w", err)
				}
			}
		} else {
			// 没有现有token，生成新的
			token, err = s.generateToken(userID, userInfo.Username)
			if err != nil {
				return nil, fmt.Errorf("生成令牌失败: %w", err)
			}
		}
	} else {
		// Redis未启用，直接生成新token
		var err error
		token, err = s.generateToken(userID, userInfo.Username)
		if err != nil {
			return nil, fmt.Errorf("生成令牌失败: %w", err)
		}
	}

	return &OAuthAuthResponse{
		Token: token,
		User:  userInfo,
	}, nil
}

// GetUserOAuthAccounts 获取用户的OAuth账户列表
func (s *AuthService) GetUserOAuthAccounts(userID uint) ([]database.UserOAuthAccount, error) {
	var accounts []database.UserOAuthAccount
	if err := s.db.Where("user_id = ?", userID).Find(&accounts).Error; err != nil {
		return nil, fmt.Errorf("查询OAuth账户失败: %w", err)
	}
	return accounts, nil
}

// UnlinkOAuthAccount 解绑OAuth账户
func (s *AuthService) UnlinkOAuthAccount(userID uint, providerName string) error {
	result := s.db.Where("user_id = ? AND provider_name = ?", userID, providerName).
		Delete(&database.UserOAuthAccount{})

	if result.Error != nil {
		return fmt.Errorf("解绑OAuth账户失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("OAuth账户不存在")
	}

	return nil
}

// 内部方法

func (s *AuthService) getOAuthProvider(providerName string) (*database.OAuthProvider, error) {
	var provider database.OAuthProvider
	if err := s.db.Where("provider_name = ?", providerName).First(&provider).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrOAuthProviderNotFound
		}
		return nil, fmt.Errorf("查询OAuth提供商失败: %w", err)
	}
	return &provider, nil
}

func (s *AuthService) createAccountLinkRequest(userInfo *OAuthUserInfo, providerName string) (string, error) {
	token, err := generateRandomToken()
	if err != nil {
		return "", err
	}

	providerData, err := json.Marshal(map[string]interface{}{
		"email":      userInfo.Email,
		"username":   userInfo.Username,
		"avatar_url": userInfo.AvatarURL,
		"name":       userInfo.Name,
	})
	if err != nil {
		return "", fmt.Errorf("序列化提供商数据失败: %w", err)
	}

	linkRequest := database.AccountLinkRequest{
		ID:             fmt.Sprintf("%s_%s_%d", providerName, userInfo.ProviderUserID, time.Now().Unix()),
		Email:          userInfo.Email,
		Token:          token,
		ExpiresAt:      time.Now().Add(30 * time.Minute), // 30分钟过期
		Used:           false,
		ProviderName:   providerName,
		ProviderUserID: userInfo.ProviderUserID,
		ProviderData:   providerData,
	}

	if err := s.db.Create(&linkRequest).Error; err != nil {
		return "", fmt.Errorf("创建账户关联请求失败: %w", err)
	}

	return token, nil
}

// OAuth提供商特定方法

func (s *AuthService) buildGitHubAuthURL(clientID, redirectURL, state string, scopes []string) string {
	params := url.Values{}
	params.Add("client_id", clientID)
	params.Add("redirect_uri", redirectURL)
	params.Add("scope", strings.Join(scopes, " "))
	params.Add("state", state)

	return "https://github.com/login/oauth/authorize?" + params.Encode()
}

func (s *AuthService) buildGoogleAuthURL(clientID, redirectURL, state string, scopes []string) string {
	params := url.Values{}
	params.Add("client_id", clientID)
	params.Add("redirect_uri", redirectURL)
	params.Add("scope", strings.Join(scopes, " "))
	params.Add("state", state)
	params.Add("response_type", "code")
	params.Add("access_type", "offline")

	return "https://accounts.google.com/o/oauth2/v2/auth?" + params.Encode()
}

func (s *AuthService) getOAuthAccessToken(providerName, code string, provider *database.OAuthProvider) (string, error) {
	switch providerName {
	case "github":
		return s.getGitHubAccessToken(code, provider)
	case "google":
		return s.getGoogleAccessToken(code, provider)
	default:
		return "", fmt.Errorf("不支持的OAuth提供商: %s", providerName)
	}
}

func (s *AuthService) getGitHubAccessToken(code string, provider *database.OAuthProvider) (string, error) {
	data := url.Values{}
	data.Set("client_id", provider.ClientID)
	data.Set("client_secret", provider.ClientSecret)
	data.Set("code", code)

	req, err := http.NewRequest("POST", "https://github.com/login/oauth/access_token", strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", err
	}

	if accessToken, ok := result["access_token"].(string); ok {
		return accessToken, nil
	}

	return "", ErrOAuthCodeInvalid
}

func (s *AuthService) getGoogleAccessToken(code string, provider *database.OAuthProvider) (string, error) {
	data := url.Values{}
	data.Set("client_id", provider.ClientID)
	data.Set("client_secret", provider.ClientSecret)
	data.Set("code", code)
	data.Set("grant_type", "authorization_code")
	data.Set("redirect_uri", provider.RedirectURL)

	req, err := http.NewRequest("POST", "https://oauth2.googleapis.com/token", strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", err
	}

	if accessToken, ok := result["access_token"].(string); ok {
		return accessToken, nil
	}

	return "", ErrOAuthCodeInvalid
}

func (s *AuthService) getOAuthUserInfo(providerName, accessToken string) (*OAuthUserInfo, error) {
	switch providerName {
	case "github":
		return s.getGitHubUserInfo(accessToken)
	case "google":
		return s.getGoogleUserInfo(accessToken)
	default:
		return nil, fmt.Errorf("不支持的OAuth提供商: %s", providerName)
	}
}

func (s *AuthService) getGitHubUserInfo(accessToken string) (*OAuthUserInfo, error) {
	req, err := http.NewRequest("GET", "https://api.github.com/user", nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "token "+accessToken)
	req.Header.Set("Accept", "application/vnd.github.v3+json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	// 获取邮箱（可能需要额外请求）
	email, _ := result["email"].(string)
	if email == "" {
		email, _ = s.getGitHubPrimaryEmail(accessToken)
	}

	return &OAuthUserInfo{
		ProviderUserID: fmt.Sprintf("%.0f", result["id"].(float64)),
		Email:          email,
		Username:       result["login"].(string),
		AvatarURL:      result["avatar_url"].(string),
		Name:           result["name"].(string),
	}, nil
}

func (s *AuthService) getGitHubPrimaryEmail(accessToken string) (string, error) {
	req, err := http.NewRequest("GET", "https://api.github.com/user/emails", nil)
	if err != nil {
		return "", err
	}

	req.Header.Set("Authorization", "token "+accessToken)
	req.Header.Set("Accept", "application/vnd.github.v3+json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var emails []map[string]interface{}
	if err := json.Unmarshal(body, &emails); err != nil {
		return "", err
	}

	for _, email := range emails {
		if primary, ok := email["primary"].(bool); ok && primary {
			if emailAddr, ok := email["email"].(string); ok {
				return emailAddr, nil
			}
		}
	}

	return "", errors.New("未找到主要邮箱")
}

func (s *AuthService) getGoogleUserInfo(accessToken string) (*OAuthUserInfo, error) {
	req, err := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v2/userinfo", nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	return &OAuthUserInfo{
		ProviderUserID: result["id"].(string),
		Email:          result["email"].(string),
		Username:       result["email"].(string), // Google没有username，使用email
		AvatarURL:      result["picture"].(string),
		Name:           result["name"].(string),
	}, nil
}


