package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/glebarez/sqlite"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"url-stash-vault/internal/config"
)

var DB *gorm.DB

// Initialize 初始化数据库连接
func Initialize(cfg *config.Config) error {
	var err error

	log.Printf("数据库初始化开始。驱动: %s, 数据源: %s", cfg.Database.Driver, cfg.Database.Source)

	// 配置 GORM 日志级别
	logLevel := getLogLevel(cfg.Database.LogLevel)

	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
	}

	// 根据驱动类型连接数据库
	switch cfg.Database.Driver {
	case "sqlite":
		DB, err = connectSQLite(cfg.Database.Source, gormConfig)
	case "mysql":
		DB, err = connectMySQL(cfg.Database.Source, gormConfig)
	case "postgres":
		DB, err = connectPostgreSQL(cfg.Database.Source, gormConfig)
	default:
		return fmt.Errorf("不支持的数据库驱动: %s", cfg.Database.Driver)
	}

	if err != nil {
		return fmt.Errorf("数据库连接失败: %w", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("获取数据库实例失败: %w", err)
	}

	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.Database.ConnMaxLifetime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	log.Printf("数据库连接成功: %s", cfg.Database.Driver)

	// 自动迁移
	if cfg.Database.AutoMigrate {
		if err := AutoMigrate(); err != nil {
			return fmt.Errorf("数据库迁移失败: %w", err)
		}
		log.Println("数据库迁移完成")
	}

	return nil
}

// connectSQLite 连接 SQLite 数据库
func connectSQLite(source string, config *gorm.Config) (*gorm.DB, error) {
	// 如果是内存数据库，直接连接
	if source == ":memory:" {
		return gorm.Open(sqlite.Open(source), config)
	}

	// 确保目录存在
	dir := filepath.Dir(source)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建数据库目录失败: %w", err)
	}

	return gorm.Open(sqlite.Open(source), config)
}

// connectMySQL 连接 MySQL 数据库
func connectMySQL(source string, config *gorm.Config) (*gorm.DB, error) {
	return gorm.Open(mysql.Open(source), config)
}

// connectPostgreSQL 连接 PostgreSQL 数据库
func connectPostgreSQL(source string, config *gorm.Config) (*gorm.DB, error) {
	return gorm.Open(postgres.Open(source), config)
}

// AutoMigrate 自动迁移所有表
func AutoMigrate() error {
	// 按依赖顺序迁移表
	models := []interface{}{
		// 基础表
		&User{},
		&OAuthProvider{},
		&EmailDomain{},
		&PendingRegistration{},
		&S3Config{},

		// 依赖用户的表
		&UserRole{},
		&UserOAuthAccount{},
		&AccountLinkRequest{},
		&DomainWhitelist{},
		&TempEmail{},
		&ShortURL{},
		&Memo{},
		&Todo{},
		&FeatureOrdering{},
		&EmailVerificationToken{},
		&PasswordResetToken{},
		&NavCategory{},
		&NavLink{},
		&FileUpload{},

		// 依赖其他表的表
		&ReceivedEmail{},
		&URLVisit{},

		// 配置表
		&BannerConfig{},

		// 日志表
		&DatabaseChangeLog{},
	}

	for _, model := range models {
		if err := DB.AutoMigrate(model); err != nil {
			return fmt.Errorf("迁移表 %T 失败: %w", model, err)
		}
	}

	// 创建索引
	if err := createIndexes(); err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	// 插入初始数据
	if err := seedInitialData(); err != nil {
		return fmt.Errorf("插入初始数据失败: %w", err)
	}

	return nil
}

// createIndexes 创建额外的索引
func createIndexes() error {
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_memos_user_created ON memos(user_id, created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_short_urls_user_created ON short_urls(user_id, created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_todos_user_completed ON todos(user_id, completed, created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_temp_emails_expires ON temp_emails(expires_at)",
		"CREATE INDEX IF NOT EXISTS idx_received_emails_temp_received ON received_emails(temp_email_id, received_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_short_urls_expires ON short_urls(expires_at)",
		"CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires ON email_verification_tokens(expires_at)",
		"CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires ON password_reset_tokens(expires_at)",
		"CREATE INDEX IF NOT EXISTS idx_pending_registrations_expires ON pending_registrations(expires_at)",
	}

	for _, indexSQL := range indexes {
		if err := DB.Exec(indexSQL).Error; err != nil {
			log.Printf("创建索引失败 (可能已存在): %s, 错误: %v", indexSQL, err)
		}
	}

	return nil
}

// seedInitialData 插入初始数据
func seedInitialData() error {
	log.Println("seedInitialData: 开始执行初始数据填充检查")
	// 检查是否已有数据
	var userCount int64
	if err := DB.Model(&User{}).Count(&userCount).Error; err != nil {
		log.Printf("seedInitialData: 查询用户数量失败: %v", err)
		return err
	}

	log.Printf("seedInitialData: 当前用户数量: %d", userCount)

	// 如果已有用户，跳过初始化
	if userCount > 0 {
		log.Println("seedInitialData: 已存在用户，跳过初始数据填充。")
		return nil
	}

	log.Println("seedInitialData: 没有现有用户，开始填充默认数据...")

	// 创建默认的 OAuth 提供商配置（如果需要）
	defaultProviders := []OAuthProvider{
		{
			ID:           "github",
			ProviderName: "github",
			ClientID:     "your_github_client_id",
			ClientSecret: "your_github_client_secret",
			RedirectURL:  "http://localhost:8080/auth/github/callback",
			Scopes:       []byte(`["user:email"]`),
			Enabled:      false, // 默认禁用，需要配置后启用
		},
		{
			ID:           "google",
			ProviderName: "google",
			ClientID:     "your_google_client_id",
			ClientSecret: "your_google_client_secret",
			RedirectURL:  "http://localhost:8080/auth/google/callback",
			Scopes:       []byte(`["openid", "profile", "email"]`),
			Enabled:      false,
		},
	}

	for _, provider := range defaultProviders {
		var existingProvider OAuthProvider
		if err := DB.Where("provider_name = ?", provider.ProviderName).First(&existingProvider).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := DB.Create(&provider).Error; err != nil {
					log.Printf("创建默认 OAuth 提供商失败: %v", err)
				}
			}
		}
	}

	// 创建默认的横幅配置
	defaultBanner := BannerConfig{
		ID:             "default",
		FirstText:      "欢迎使用",
		SecondText:     "URL Stash Vault",
		FirstGradient:  "from-blue-600 to-purple-600",
		SecondGradient: "from-purple-600 to-pink-600",
		Height:         intPtr(200),
		UseThirdLine:   boolPtr(false),
	}

	var existingBanner BannerConfig
	if err := DB.Where("id = ?", "default").First(&existingBanner).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			if err := DB.Create(&defaultBanner).Error; err != nil {
				log.Printf("创建默认横幅配置失败: %v", err)
			}
		}
	}

	// 创建默认的S3配置（如果不存在）
	var existingS3Config S3Config
	if err := DB.Where("is_default = ?", true).First(&existingS3Config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建一个示例S3配置（需要用户后续配置）
			defaultS3Config := S3Config{
				ID:              "default-s3",
				Name:            "Default S3 Configuration",
				AccessKeyID:     "your-access-key-id",
				SecretAccessKey: "your-secret-access-key",
				Region:          "us-east-1",
				Bucket:          "your-bucket-name",
				UseSSL:          true,
				IsDefault:       true,
				IsActive:        false, // 默认禁用，需要用户配置后启用
			}

			if err := DB.Create(&defaultS3Config).Error; err != nil {
				log.Printf("创建默认S3配置失败: %v", err)
			} else {
				log.Println("已创建默认S3配置，请在管理员面板中配置正确的S3参数")
			}
		}
	}

	log.Println("初始数据插入完成")
	return nil
}

// getLogLevel 转换日志级别
func getLogLevel(level string) logger.LogLevel {
	switch level {
	case "silent":
		return logger.Silent
	case "error":
		return logger.Error
	case "warn":
		return logger.Warn
	case "info":
		return logger.Info
	default:
		return logger.Warn
	}
}

// Close 关闭数据库连接
func Close() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// 辅助函数
func intPtr(i int) *int {
	return &i
}

func boolPtr(b bool) *bool {
	return &b
}

func stringPtr(s string) *string {
	return &s
}
