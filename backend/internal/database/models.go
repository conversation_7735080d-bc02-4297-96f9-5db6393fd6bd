package database

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// --- Base Models for common fields ---
// Model base model definition, including fields `ID`, `CreatedAt`, `UpdatedAt`, `DeletedAt`
// type Model struct {
// 	ID        string `gorm:"primarykey;type:uuid;default:gen_random_uuid()"` // Assuming UUIDs if string IDs
// 	CreatedAt time.Time
// 	UpdatedAt time.Time
// 	DeletedAt gorm.DeletedAt `gorm:"index"`
// }

// --- Auth & Users ---

// User represents a user in the system.
// We need to define a basic User model, as Supabase likely handled this.
// This is a placeholder and needs to be adapted to your auth requirements.
type User struct {
	gorm.Model           // Includes ID, CreatedAt, UpdatedAt, DeletedAt
	Username      string `gorm:"uniqueIndex;not null"`
	Email         string `gorm:"uniqueIndex;not null"`
	Password      string `gorm:"not null"` // Store hashed passwords only!
	EmailVerified bool   `gorm:"default:false"`
	IsSuperAdmin  bool   `gorm:"default:false"`

	Roles           []UserRole         `gorm:"foreignKey:UserID"`
	OAuthAccounts   []UserOAuthAccount `gorm:"foreignKey:UserID"`
	ShortURLs       []ShortURL         `gorm:"foreignKey:UserID"`
	Memos           []Memo             `gorm:"foreignKey:UserID"`
	Todos           []Todo             `gorm:"foreignKey:UserID"`
	TempEmails      []TempEmail        `gorm:"foreignKey:UserID"`
	DomainWhitelist []DomainWhitelist  `gorm:"foreignKey:UserID"`
	FeatureOrdering []FeatureOrdering  `gorm:"foreignKey:UserID"` // If ordering is per user
	NavCategories   []NavCategory      `gorm:"foreignKey:UserID"`
	NavLinks        []NavLink          `gorm:"foreignKey:UserID"`
}

type UserRole struct {
	ID        string    `gorm:"primarykey;type:varchar(255)"` // Matching Supabase type. Consider UUID if creating new.
	UserID    uint      `gorm:"index;not null"`               // Foreign key to User table. Adjust if User.ID is string/UUID.
	Role      string    `gorm:"type:varchar(50);not null"`
	CreatedAt time.Time `gorm:"autoCreateTime"`

	User User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

type UserOAuthAccount struct {
	ID               string         `gorm:"primarykey;type:varchar(255)"`
	UserID           uint           `gorm:"index;not null"`
	ProviderName     string         `gorm:"type:varchar(50);not null;index:idx_user_provider,unique"`
	ProviderUserID   string         `gorm:"type:varchar(255);not null;index:idx_user_provider,unique"`
	ProviderEmail    string         `gorm:"type:varchar(255)"`
	ProviderUsername string         `gorm:"type:varchar(255)"`
	ProviderData     datatypes.JSON // Or string if simpler
	CreatedAt        time.Time      `gorm:"autoCreateTime"`
	UpdatedAt        time.Time      `gorm:"autoUpdateTime"`

	User User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

type AccountLinkRequest struct {
	ID             string         `gorm:"primarykey;type:varchar(255)"`
	Email          string         `gorm:"type:varchar(255);not null"`
	Token          string         `gorm:"type:varchar(255);uniqueIndex;not null"`
	ExpiresAt      time.Time      `gorm:"not null"`
	Used           bool           `gorm:"default:false"`
	ProviderName   string         `gorm:"type:varchar(50);not null"`
	ProviderUserID string         `gorm:"type:varchar(255);not null"`
	ProviderData   datatypes.JSON `gorm:"serializer:json"` // Or string
	CreatedAt      time.Time      `gorm:"autoCreateTime"`
}

type OAuthProvider struct {
	ID           string         `gorm:"primarykey;type:varchar(255)"`
	ProviderName string         `gorm:"type:varchar(50);uniqueIndex;not null"`
	ClientID     string         `gorm:"type:varchar(255);not null"`
	ClientSecret string         `gorm:"type:varchar(255);not null"` // Encrypt this at rest!
	RedirectURL  string         `gorm:"type:varchar(512)"`
	Scopes       datatypes.JSON `gorm:"serializer:json"` // Storing as JSON array of strings
	Enabled      bool           `gorm:"default:true"`
	CreatedAt    time.Time      `gorm:"autoCreateTime"`
	UpdatedAt    time.Time      `gorm:"autoUpdateTime"`
}

// --- Core Features ---

type ShortURL struct {
	ID          string `gorm:"primarykey;type:varchar(255)"`
	UserID      *uint  `gorm:"index"` // Nullable foreign key
	OriginalURL string `gorm:"type:text;not null"`
	ShortCode   string `gorm:"type:varchar(50);uniqueIndex;not null"`
	Clicks      int64  `gorm:"default:0"`
	ExpiresAt   *time.Time
	CreatedAt   time.Time `gorm:"autoCreateTime"`

	User *User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;"`
}

type Memo struct {
	ID           string         `gorm:"primarykey;type:varchar(255)"`
	UserID       uint           `gorm:"index;not null"`
	Content      string         `gorm:"type:text;not null"`
	Tags         datatypes.JSON `gorm:"serializer:json"` // Storing as JSON array of strings, e.g., ["work", "project"]
	ReminderDate *time.Time
	CreatedAt    time.Time `gorm:"autoCreateTime"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime"`

	User User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

type Todo struct {
	ID        string `gorm:"primarykey;type:varchar(255)"`
	UserID    uint   `gorm:"index;not null"`
	Title     string `gorm:"type:varchar(512);not null"`
	Completed bool   `gorm:"default:false"`
	DueDate   *time.Time
	Priority  string    `gorm:"type:varchar(20)"` // e.g., "high", "medium", "low"
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`

	User User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

// BannerConfig from types.ts (simplified, adjust as needed)
type BannerConfig struct {
	ID              string  `gorm:"primarykey;type:varchar(255)"`
	FirstText       string  `gorm:"type:varchar(255);not null"`
	SecondText      string  `gorm:"type:varchar(255);not null"`
	ThirdText       *string `gorm:"type:varchar(255)"`
	FourthText      *string `gorm:"type:varchar(255)"`
	FirstGradient   string  `gorm:"type:varchar(100);not null"`
	SecondGradient  string  `gorm:"type:varchar(100);not null"`
	ThirdGradient   *string `gorm:"type:varchar(100)"`
	FourthGradient  *string `gorm:"type:varchar(100)"`
	AnimationStyle  *string `gorm:"type:varchar(50)"` // Renamed from animation_speed for clarity, or use speed directly
	CustomClass     *string `gorm:"type:varchar(100)"`
	DisplayStyle    *string `gorm:"type:varchar(50)"`
	Height          *int
	Lines           datatypes.JSON `gorm:"serializer:json"`
	MainDescription datatypes.JSON `gorm:"serializer:json"`
	MainTitle       datatypes.JSON `gorm:"serializer:json"`
	Spacing         *int
	UseThirdLine    *bool
	CreatedAt       time.Time `gorm:"autoCreateTime"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime"`
}

// FeatureOrdering from types.ts
type FeatureOrdering struct {
	ID               string         `gorm:"primarykey;type:varchar(255)"`
	UserID           *uint          `gorm:"index"` // If ordering can be global (UserID is null) or per-user
	ComponentType    string         `gorm:"type:varchar(100);not null"`
	ComponentsConfig datatypes.JSON `gorm:"serializer:json;not null"`
	CreatedAt        time.Time      `gorm:"autoCreateTime"`
	UpdatedAt        time.Time      `gorm:"autoUpdateTime"`

	User *User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

// --- Email related ---

type TempEmail struct {
	ID           string    `gorm:"primarykey;type:varchar(255)"`
	UserID       uint      `gorm:"index;not null"`
	EmailAddress string    `gorm:"type:varchar(255);uniqueIndex;not null"`
	ExpiresAt    time.Time `gorm:"not null"`
	CreatedAt    time.Time `gorm:"autoCreateTime"`

	User           User            `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	ReceivedEmails []ReceivedEmail `gorm:"foreignKey:TempEmailID"`
}

type ReceivedEmail struct {
	ID          string    `gorm:"primarykey;type:varchar(255)"`
	TempEmailID string    `gorm:"index;not null"` // Foreign key to TempEmail
	FromAddress string    `gorm:"type:varchar(255);not null"`
	Subject     *string   `gorm:"type:varchar(512)"`
	Body        *string   `gorm:"type:text"`
	ReceivedAt  time.Time `gorm:"autoCreateTime"`

	TempEmail TempEmail `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

type EmailDomain struct {
	ID        string    `gorm:"primarykey;type:varchar(255)"`
	Domain    string    `gorm:"type:varchar(255);uniqueIndex;not null"`
	CreatedAt time.Time `gorm:"autoCreateTime"`
	// Maybe a UserID if domains are user-specific, or a flag for globally approved domains
}

// S3Config S3配置
type S3Config struct {
	ID              string    `gorm:"primarykey;type:varchar(255)"`
	Name            string    `gorm:"type:varchar(255);not null"`        // 配置名称
	AccessKeyID     string    `gorm:"type:varchar(255);not null"`        // AWS Access Key ID
	SecretAccessKey string    `gorm:"type:varchar(255);not null"`        // AWS Secret Access Key
	Region          string    `gorm:"type:varchar(100);not null"`        // AWS Region
	Bucket          string    `gorm:"type:varchar(255);not null"`        // S3 Bucket名称
	Endpoint        *string   `gorm:"type:varchar(255)"`                 // 自定义端点（用于兼容S3的服务）
	UseSSL          bool      `gorm:"default:true"`                      // 是否使用SSL
	IsDefault       bool      `gorm:"default:false"`                     // 是否为默认配置
	IsActive        bool      `gorm:"default:true"`                      // 是否启用
	CreatedAt       time.Time `gorm:"autoCreateTime"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime"`
}

// FileUpload 文件上传记录
type FileUpload struct {
	ID           string    `gorm:"primarykey;type:varchar(255)"`
	UserID       *uint     `gorm:"index"`                           // 上传用户ID，可为空（匿名上传）
	OriginalName string    `gorm:"type:varchar(255);not null"`      // 原始文件名
	FileName     string    `gorm:"type:varchar(255);not null"`      // 存储的文件名
	FilePath     string    `gorm:"type:varchar(500);not null"`      // 文件路径
	FileSize     int64     `gorm:"not null"`                        // 文件大小（字节）
	MimeType     string    `gorm:"type:varchar(100);not null"`      // MIME类型
	S3ConfigID   string    `gorm:"type:varchar(255);not null"`      // 关联的S3配置ID
	S3Key        string    `gorm:"type:varchar(500);not null"`      // S3中的Key
	S3URL        string    `gorm:"type:varchar(1000);not null"`     // 文件访问URL
	UploadType   string    `gorm:"type:varchar(50);not null"`       // 上传类型（avatar, document, image等）
	CreatedAt    time.Time `gorm:"autoCreateTime"`

	User     *User     `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;"`
	S3Config S3Config  `gorm:"constraint:OnUpdate:CASCADE,OnDelete:RESTRICT;"`
}

// URLVisit 记录URL访问统计
type URLVisit struct {
	ID          string    `gorm:"primarykey;type:varchar(255)"`
	ShortURLID  *string   `gorm:"type:varchar(255);index"` // 关联的短链ID，可为空
	OriginalURL string    `gorm:"type:text;not null"`      // 原始URL
	ShortCode   *string   `gorm:"type:varchar(50);index"`  // 短链代码，可为空
	IPAddress   string    `gorm:"type:varchar(45)"`        // 访问者IP地址
	UserAgent   *string   `gorm:"type:text"`               // 用户代理字符串
	Referer     *string   `gorm:"type:text"`               // 来源页面
	Country     *string   `gorm:"type:varchar(100)"`       // 国家
	City        *string   `gorm:"type:varchar(100)"`       // 城市
	Latitude    *float64  `gorm:"type:decimal(10,8)"`      // 纬度
	Longitude   *float64  `gorm:"type:decimal(11,8)"`      // 经度
	VisitedAt   time.Time `gorm:"autoCreateTime"`          // 访问时间

	ShortURL *ShortURL `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;"`
}

type DomainWhitelist struct {
	ID        string    `gorm:"primarykey;type:varchar(255)" json:"id"`
	UserID    *uint     `gorm:"index" json:"user_id"` // If whitelisting is per user, or global if null
	Domain    string    `gorm:"type:varchar(255);not null;index:idx_user_domain_whitelist,unique" json:"domain"`
	Approved  bool      `gorm:"default:false" json:"approved"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`

	User *User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"user,omitempty"`
}

// EmailVerificationToken 邮箱验证令牌
type PendingRegistration struct {
	Email     string    `gorm:"primaryKey;type:varchar(255)"`
	Username  string    `gorm:"uniqueIndex;type:varchar(255);not null"`
	Password  string    `gorm:"type:varchar(255);not null"`
	Token     string    `gorm:"uniqueIndex;type:varchar(255);not null"`
	ExpiresAt time.Time `gorm:"not null"`
	CreatedAt time.Time `gorm:"autoCreateTime"`
}

type EmailVerificationToken struct {
	ID        uint      `gorm:"primarykey"`
	UserID    uint      `gorm:"index;not null"`
	Token     string    `gorm:"type:varchar(255);uniqueIndex;not null"`
	ExpiresAt time.Time `gorm:"not null"`
	Used      bool      `gorm:"default:false"`
	CreatedAt time.Time `gorm:"autoCreateTime"`

	User User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

// PasswordResetToken 密码重置令牌
type PasswordResetToken struct {
	ID        uint      `gorm:"primarykey"`
	UserID    uint      `gorm:"index;not null"`
	Token     string    `gorm:"type:varchar(255);uniqueIndex;not null"`
	ExpiresAt time.Time `gorm:"not null"`
	Used      bool      `gorm:"default:false"`
	CreatedAt time.Time `gorm:"autoCreateTime"`

	User User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

// DatabaseChangeLog to record schema or significant data changes
// This is a basic example. For full audit trails, more sophisticated solutions exist.
type DatabaseChangeLog struct {
	ID          uint           `gorm:"primarykey"`
	TableName   string         `gorm:"type:varchar(255)"`
	RecordID    string         `gorm:"type:varchar(255)"` // ID of the record that was changed
	Operation   string         `gorm:"type:varchar(50)"`  // e.g., CREATE, UPDATE, DELETE, MIGRATE_UP, MIGRATE_DOWN
	Description string         `gorm:"type:text"`
	ChangedBy   string         `gorm:"type:varchar(255)"` // User ID or system process
	ChangedAt   time.Time      `gorm:"autoCreateTime"`
	OldValues   datatypes.JSON `gorm:"serializer:json"`
	NewValues   datatypes.JSON `gorm:"serializer:json"`
}

// --- Navigation Models ---

// NavCategory 导航分类
type NavCategory struct {
	ID          string    `gorm:"primarykey;type:varchar(255)"`
	UserID      *uint     `gorm:"index"` // Nullable - null for admin/global categories
	Name        string    `gorm:"type:varchar(255);not null"`
	Description *string   `gorm:"type:text"`
	Icon        *string   `gorm:"type:varchar(100)"`
	ParentID    *string   `gorm:"type:varchar(255);index"`
	SortOrder   int       `gorm:"default:0"`
	IsPublic    bool      `gorm:"default:false"` // Whether visible to all users
	CreatedAt   time.Time `gorm:"autoCreateTime"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime"`

	User     *User         `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Parent   *NavCategory  `gorm:"foreignKey:ParentID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL;"`
	Children []NavCategory `gorm:"foreignKey:ParentID"`
	Links    []NavLink     `gorm:"foreignKey:CategoryID"`
}

// NavLink 导航链接
type NavLink struct {
	ID          string    `gorm:"primarykey;type:varchar(255)"`
	UserID      *uint     `gorm:"index"` // Nullable - null for admin/global links
	CategoryID  string    `gorm:"type:varchar(255);not null;index"`
	Name        string    `gorm:"type:varchar(255);not null"`
	URL         string    `gorm:"type:text;not null"`
	Description *string   `gorm:"type:text"`
	Icon        *string   `gorm:"type:varchar(100)"`
	SortOrder   int       `gorm:"default:0"`
	IsInternal  bool      `gorm:"default:false"` // Whether it's an internal route
	IsPublic    bool      `gorm:"default:false"` // Whether visible to all users
	Status      string    `gorm:"type:varchar(50);default:'active'"` // active, pending, disabled
	CreatedAt   time.Time `gorm:"autoCreateTime"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime"`

	User     *User       `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Category NavCategory `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

// Helper function to add a change log entry (example)
// func AddChangeLog(db *gorm.DB, tableName, recordID, operation, description, changedBy string, oldValues, newValues interface{}) {
// 	 logEntry := DatabaseChangeLog{
// 		 TableName: tableName,
// 		 RecordID: recordID,
// 		 Operation: operation,
// 		 Description: description,
// 		 ChangedBy: changedBy,
// 		 OldValues: datatypes.JSONMap(oldValues.(map[string]interface{})),
// 		 NewValues: datatypes.JSONMap(newValues.(map[string]interface{})),
// 	 }
// 	 if err := db.Create(&logEntry).Error; err != nil {
// 		 log.Printf("Failed to add change log: %v", err)
// 	 }
// }
