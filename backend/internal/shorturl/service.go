package shorturl

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"net/url"
	"strings"
	"time"

	"gorm.io/gorm"

	"url-stash-vault/internal/cache"
	"url-stash-vault/internal/database"
)

var (
	ErrShortURLNotFound    = errors.New("短链不存在")
	ErrInvalidURL         = errors.New("无效的URL")
	ErrShortCodeExists    = errors.New("短链代码已存在")
	ErrPermissionDenied   = errors.New("权限不足")
)

const (
	ShortCodeLength = 6
	ShortCodeChars  = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	MaxRetries     = 10
)

type Service struct {
	db *gorm.DB
}

type CreateShortURLRequest struct {
	OriginalURL string     `json:"original_url" binding:"required"`
	CustomCode  string     `json:"custom_code,omitempty"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

type ShortURLResponse struct {
	ID          string     `json:"id"`
	OriginalURL string     `json:"original_url"`
	ShortCode   string     `json:"short_code"`
	ShortURL    string     `json:"short_url"`
	Clicks      int64      `json:"clicks"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UserID      *uint      `json:"user_id,omitempty"`
}

type ListShortURLsRequest struct {
	Page     int `form:"page,default=1"`
	PageSize int `form:"page_size,default=20"`
}

type ListShortURLsResponse struct {
	Data       []ShortURLResponse `json:"data"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}

func NewService(db *gorm.DB) *Service {
	return &Service{db: db}
}

// CreateShortURL 创建短链
func (s *Service) CreateShortURL(ctx context.Context, req CreateShortURLRequest, userID *uint, baseURL string) (*ShortURLResponse, error) {
	// 验证URL
	if !isValidURL(req.OriginalURL) {
		return nil, ErrInvalidURL
	}

	// 生成或验证短链代码
	var shortCode string
	var err error

	if req.CustomCode != "" {
		// 使用自定义代码
		shortCode = req.CustomCode
		if err := s.validateCustomCode(ctx, shortCode); err != nil {
			return nil, err
		}
	} else {
		// 生成随机代码
		shortCode, err = s.generateUniqueShortCode(ctx)
		if err != nil {
			return nil, fmt.Errorf("生成短链代码失败: %w", err)
		}
	}

	// 创建短链记录
	shortURL := database.ShortURL{
		ID:          generateID(),
		UserID:      userID,
		OriginalURL: req.OriginalURL,
		ShortCode:   shortCode,
		Clicks:      0,
		ExpiresAt:   req.ExpiresAt,
		CreatedAt:   time.Now(),
	}

	// 保存到数据库
	if err := s.db.Create(&shortURL).Error; err != nil {
		return nil, fmt.Errorf("保存短链到数据库失败: %w", err)
	}

	// 如果Redis启用，同时缓存到Redis
	if cache.IsRedisEnabled() {
		cacheData := cache.ShortURLData{
			OriginalURL: shortURL.OriginalURL,
			UserID:      shortURL.UserID,
			Clicks:      shortURL.Clicks,
			CreatedAt:   shortURL.CreatedAt,
			ExpiresAt:   shortURL.ExpiresAt,
		}

		if err := cache.SetShortURL(ctx, shortCode, cacheData, req.ExpiresAt); err != nil {
			// 记录错误但不影响主流程
			fmt.Printf("缓存短链到Redis失败: %v\n", err)
		}
	}

	return s.toResponse(shortURL, baseURL), nil
}

// GetShortURL 根据短链代码获取原始URL（用于重定向）
func (s *Service) GetShortURL(ctx context.Context, shortCode string) (string, error) {
	// 首先尝试从Redis获取
	if cache.IsRedisEnabled() {
		cacheData, err := cache.GetShortURL(ctx, shortCode)
		if err != nil {
			fmt.Printf("从Redis获取短链失败: %v\n", err)
		} else if cacheData != nil {
			// 检查是否过期
			if cacheData.ExpiresAt != nil && time.Now().After(*cacheData.ExpiresAt) {
				// 已过期，从Redis中删除
				cache.DeleteShortURL(ctx, shortCode)
				return "", ErrShortURLNotFound
			}

			// 增加点击次数
			if err := cache.IncrementShortURLClicks(ctx, shortCode); err != nil {
				fmt.Printf("增加Redis中短链点击次数失败: %v\n", err)
			}

			// 如果是永久链接，刷新Redis过期时间
			if cacheData.ExpiresAt == nil {
				if err := cache.RefreshShortURLExpiry(ctx, shortCode); err != nil {
					fmt.Printf("刷新Redis中短链过期时间失败: %v\n", err)
				}
			}

			// 异步更新数据库中的点击次数
			go s.updateClicksInDB(shortCode, cacheData.Clicks)

			return cacheData.OriginalURL, nil
		}
	}

	// Redis中没有，从数据库获取
	var shortURL database.ShortURL
	if err := s.db.Where("short_code = ?", shortCode).First(&shortURL).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", ErrShortURLNotFound
		}
		return "", fmt.Errorf("查询短链失败: %w", err)
	}

	// 检查是否过期
	if shortURL.ExpiresAt != nil && time.Now().After(*shortURL.ExpiresAt) {
		return "", ErrShortURLNotFound
	}

	// 增加点击次数
	shortURL.Clicks++
	s.db.Save(&shortURL)

	// 如果Redis启用，将数据加载到Redis
	if cache.IsRedisEnabled() {
		cacheData := cache.ShortURLData{
			OriginalURL: shortURL.OriginalURL,
			UserID:      shortURL.UserID,
			Clicks:      shortURL.Clicks,
			CreatedAt:   shortURL.CreatedAt,
			ExpiresAt:   shortURL.ExpiresAt,
		}

		if err := cache.SetShortURL(ctx, shortCode, cacheData, shortURL.ExpiresAt); err != nil {
			fmt.Printf("将短链加载到Redis失败: %v\n", err)
		}
	}

	return shortURL.OriginalURL, nil
}

// ListShortURLs 获取用户的短链列表
func (s *Service) ListShortURLs(ctx context.Context, req ListShortURLsRequest, userID uint) (*ListShortURLsResponse, error) {
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	var shortURLs []database.ShortURL
	var total int64

	// 查询总数
	if err := s.db.Model(&database.ShortURL{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("查询短链总数失败: %w", err)
	}

	// 查询分页数据
	if err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(req.PageSize).
		Offset(offset).
		Find(&shortURLs).Error; err != nil {
		return nil, fmt.Errorf("查询短链列表失败: %w", err)
	}

	// 转换为响应格式
	responses := make([]ShortURLResponse, len(shortURLs))
	for i, shortURL := range shortURLs {
		responses[i] = *s.toResponse(shortURL, "")
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &ListShortURLsResponse{
		Data:       responses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// DeleteShortURL 删除短链
func (s *Service) DeleteShortURL(ctx context.Context, id string, userID uint) error {
	var shortURL database.ShortURL
	if err := s.db.Where("id = ? AND user_id = ?", id, userID).First(&shortURL).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrShortURLNotFound
		}
		return fmt.Errorf("查询短链失败: %w", err)
	}

	// 从数据库删除
	if err := s.db.Delete(&shortURL).Error; err != nil {
		return fmt.Errorf("删除短链失败: %w", err)
	}

	// 从Redis删除
	if cache.IsRedisEnabled() {
		if err := cache.DeleteShortURL(ctx, shortURL.ShortCode); err != nil {
			fmt.Printf("从Redis删除短链失败: %v\n", err)
		}
	}

	return nil
}

// GetDailyCount 获取用户今日创建的短链数量
func (s *Service) GetDailyCount(ctx context.Context, userID uint) (int64, error) {
	// 获取今天的开始时间
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	var count int64
	err := s.db.Model(&database.ShortURL{}).
		Where("user_id = ? AND created_at >= ?", userID, today).
		Count(&count).Error

	if err != nil {
		return 0, fmt.Errorf("查询每日短链数量失败: %w", err)
	}

	return count, nil
}

// generateUniqueShortCode 生成唯一的短链代码
func (s *Service) generateUniqueShortCode(ctx context.Context) (string, error) {
	for i := 0; i < MaxRetries; i++ {
		code := generateRandomCode(ShortCodeLength)

		// 检查数据库中是否存在
		var count int64
		if err := s.db.Model(&database.ShortURL{}).Where("short_code = ?", code).Count(&count).Error; err != nil {
			return "", err
		}

		if count == 0 {
			// 检查Redis中是否存在
			if cache.IsRedisEnabled() {
				data, err := cache.GetShortURL(ctx, code)
				if err != nil {
					return "", err
				}
				if data == nil {
					return code, nil
				}
			} else {
				return code, nil
			}
		}
	}

	return "", errors.New("无法生成唯一的短链代码")
}

// validateCustomCode 验证自定义短链代码
func (s *Service) validateCustomCode(ctx context.Context, code string) error {
	if len(code) < 3 || len(code) > 20 {
		return errors.New("自定义代码长度必须在3-20字符之间")
	}

	// 检查字符是否有效
	for _, char := range code {
		if !strings.ContainsRune(ShortCodeChars, char) {
			return errors.New("自定义代码只能包含字母和数字")
		}
	}

	// 检查数据库中是否存在
	var count int64
	if err := s.db.Model(&database.ShortURL{}).Where("short_code = ?", code).Count(&count).Error; err != nil {
		return fmt.Errorf("检查代码唯一性失败: %w", err)
	}

	if count > 0 {
		return ErrShortCodeExists
	}

	// 检查Redis中是否存在
	if cache.IsRedisEnabled() {
		data, err := cache.GetShortURL(ctx, code)
		if err != nil {
			return fmt.Errorf("检查Redis中代码唯一性失败: %w", err)
		}
		if data != nil {
			return ErrShortCodeExists
		}
	}

	return nil
}

// updateClicksInDB 异步更新数据库中的点击次数
func (s *Service) updateClicksInDB(shortCode string, clicks int64) {
	if err := s.db.Model(&database.ShortURL{}).
		Where("short_code = ?", shortCode).
		Update("clicks", clicks).Error; err != nil {
		fmt.Printf("更新数据库中短链点击次数失败: %v\n", err)
	}
}

// toResponse 转换为响应格式
func (s *Service) toResponse(shortURL database.ShortURL, baseURL string) *ShortURLResponse {
	response := &ShortURLResponse{
		ID:          shortURL.ID,
		OriginalURL: shortURL.OriginalURL,
		ShortCode:   shortURL.ShortCode,
		Clicks:      shortURL.Clicks,
		ExpiresAt:   shortURL.ExpiresAt,
		CreatedAt:   shortURL.CreatedAt,
		UserID:      shortURL.UserID,
	}

	if baseURL != "" {
		response.ShortURL = fmt.Sprintf("%s/%s", strings.TrimRight(baseURL, "/"), shortURL.ShortCode)
	}

	return response
}

// 辅助函数

func isValidURL(str string) bool {
	u, err := url.Parse(str)
	return err == nil && u.Scheme != "" && u.Host != ""
}

func generateRandomCode(length int) string {
	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(ShortCodeChars)))

	for i := 0; i < length; i++ {
		randomIndex, _ := rand.Int(rand.Reader, charsetLen)
		result[i] = ShortCodeChars[randomIndex.Int64()]
	}

	return string(result)
}

func generateID() string {
	// 生成一个简单的时间戳+随机数ID
	timestamp := time.Now().UnixNano()
	randomPart := generateRandomCode(8)
	return fmt.Sprintf("%d_%s", timestamp, randomPart)
}