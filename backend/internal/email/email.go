package email

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"html/template"
	"log"
	"time"

	"gopkg.in/gomail.v2"

	"url-stash-vault/internal/config"
)

type EmailService struct {
	config *config.Config
	dialer *gomail.Dialer
}

type EmailTemplate struct {
	Subject string
	Body    string
}

// NewEmailService 创建新的邮件服务
func NewEmailService(cfg *config.Config) *EmailService {
	if !cfg.SMTP.Enabled {
		log.Println("SMTP 未启用，邮件服务将不可用")
		return &EmailService{config: cfg}
	}

	dialer := gomail.NewDialer(
		cfg.SMTP.Host,
		cfg.SMTP.Port,
		cfg.SMTP.Username,
		cfg.SMTP.Password,
	)

	// 设置TLS配置
	dialer.TLSConfig = &tls.Config{
		ServerName: cfg.SMTP.Host,
		InsecureSkipVerify: false,
	}

	return &EmailService{
		config: cfg,
		dialer: dialer,
	}
}

// SendEmail 发送邮件
func (s *EmailService) SendEmail(to, subject, body string) error {
	if !s.config.SMTP.Enabled {
		log.Printf("SMTP 未启用，跳过发送邮件到: %s", to)
		return nil
	}

	if s.dialer == nil {
		return fmt.Errorf("邮件服务未正确初始化")
	}

	m := gomail.NewMessage()
	m.SetHeader("From", s.config.SMTP.From)
	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body)

	// 添加重试机制
	var lastErr error
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		log.Printf("尝试发送邮件到: %s (第%d次尝试)", to, i+1)

		if err := s.dialer.DialAndSend(m); err != nil {
			lastErr = err
			log.Printf("发送邮件失败 (第%d次尝试): %v", i+1, err)

			if i < maxRetries-1 {
				// 等待一段时间后重试
				time.Sleep(time.Duration(i+1) * 2 * time.Second)
				continue
			}
		} else {
			log.Printf("邮件发送成功到: %s", to)
			return nil
		}
	}

	return fmt.Errorf("发送邮件失败 (已重试%d次): %w", maxRetries, lastErr)
}

// SendVerificationEmail 发送邮箱验证邮件
func (s *EmailService) SendVerificationEmail(to, token string) error {
	subject := "验证您的邮箱地址 - URL Stash Vault"

	// 构建验证链接 - 使用配置中的前端URL或默认值
	frontendURL := s.config.Server.Host
	if frontendURL == "0.0.0.0" || frontendURL == "" {
		frontendURL = "http://localhost:3000"
	} else {
		frontendURL = fmt.Sprintf("http://%s:3000", frontendURL)
	}
	verifyURL := fmt.Sprintf("%s/verify-email?token=%s", frontendURL, token)

	body := s.generateVerificationEmailBody(verifyURL)

	log.Printf("发送验证邮件到: %s, 验证链接: %s", to, verifyURL)
	return s.SendEmail(to, subject, body)
}

// SendPasswordResetEmail 发送密码重置邮件
func (s *EmailService) SendPasswordResetEmail(to, token string) error {
	subject := "重置您的密码 - URL Stash Vault"

	// 构建重置链接 - 使用配置中的前端URL或默认值
	frontendURL := s.config.Server.Host
	if frontendURL == "0.0.0.0" || frontendURL == "" {
		frontendURL = "http://localhost:3000"
	} else {
		frontendURL = fmt.Sprintf("http://%s:3000", frontendURL)
	}
	resetURL := fmt.Sprintf("%s/reset-password?token=%s", frontendURL, token)

	body := s.generatePasswordResetEmailBody(resetURL)

	log.Printf("发送密码重置邮件到: %s, 重置链接: %s", to, resetURL)
	return s.SendEmail(to, subject, body)
}

// generateVerificationEmailBody 生成验证邮件内容
func (s *EmailService) generateVerificationEmailBody(verifyURL string) string {
	tmpl := `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>邮箱验证</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>欢迎使用 URL Stash Vault</h1>
        </div>
        <div class="content">
            <h2>验证您的邮箱地址</h2>
            <p>感谢您注册 URL Stash Vault！请点击下面的按钮验证您的邮箱地址：</p>
            <a href="{{.VerifyURL}}" class="button">验证邮箱</a>
            <p>如果按钮无法点击，请复制以下链接到浏览器中打开：</p>
            <p><a href="{{.VerifyURL}}">{{.VerifyURL}}</a></p>
            <p>此链接将在 24 小时后过期。</p>
        </div>
        <div class="footer">
            <p>如果您没有注册 URL Stash Vault，请忽略此邮件。</p>
        </div>
    </div>
</body>
</html>`

	t, err := template.New("verification").Parse(tmpl)
	if err != nil {
		log.Printf("解析邮件模板失败: %v", err)
		return fmt.Sprintf("请点击以下链接验证您的邮箱：%s", verifyURL)
	}

	var buf bytes.Buffer
	data := struct {
		VerifyURL string
	}{
		VerifyURL: verifyURL,
	}

	if err := t.Execute(&buf, data); err != nil {
		log.Printf("生成邮件内容失败: %v", err)
		return fmt.Sprintf("请点击以下链接验证您的邮箱：%s", verifyURL)
	}

	return buf.String()
}

// generatePasswordResetEmailBody 生成密码重置邮件内容
func (s *EmailService) generatePasswordResetEmailBody(resetURL string) string {
	tmpl := `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>密码重置</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #e74c3c; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>URL Stash Vault</h1>
        </div>
        <div class="content">
            <h2>重置您的密码</h2>
            <p>我们收到了重置您密码的请求。请点击下面的按钮重置密码：</p>
            <a href="{{.ResetURL}}" class="button">重置密码</a>
            <p>如果按钮无法点击，请复制以下链接到浏览器中打开：</p>
            <p><a href="{{.ResetURL}}">{{.ResetURL}}</a></p>
            <p>此链接将在 1 小时后过期。</p>
        </div>
        <div class="footer">
            <p>如果您没有请求重置密码，请忽略此邮件。</p>
        </div>
    </div>
</body>
</html>`

	t, err := template.New("reset").Parse(tmpl)
	if err != nil {
		log.Printf("解析邮件模板失败: %v", err)
		return fmt.Sprintf("请点击以下链接重置您的密码：%s", resetURL)
	}

	var buf bytes.Buffer
	data := struct {
		ResetURL string
	}{
		ResetURL: resetURL,
	}

	if err := t.Execute(&buf, data); err != nil {
		log.Printf("生成邮件内容失败: %v", err)
		return fmt.Sprintf("请点击以下链接重置您的密码：%s", resetURL)
	}

	return buf.String()
}