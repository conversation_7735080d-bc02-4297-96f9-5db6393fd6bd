package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"url-stash-vault/internal/database"
)

// UploadToS3 上传文件到S3
func UploadToS3(file multipart.File, key string, config *database.S3Config) (string, error) {
	// 创建AWS会话
	sess, err := createS3Session(config)
	if err != nil {
		return "", fmt.Errorf("创建S3会话失败: %w", err)
	}

	// 创建S3服务客户端
	svc := s3.New(sess)

	// 重置文件指针到开始位置
	if _, err := file.Seek(0, io.SeekStart); err != nil {
		return "", fmt.Errorf("重置文件指针失败: %w", err)
	}

	// 上传文件
	_, err = svc.PutObject(&s3.PutObjectInput{
		Bucket: aws.String(config.Bucket),
		Key:    aws.String(key),
		Body:   file,
		ACL:    aws.String("public-read"), // 设置为公开读取
	})

	if err != nil {
		return "", fmt.Errorf("上传文件到S3失败: %w", err)
	}

	// 生成文件URL
	url := generateS3URL(config, key)
	return url, nil
}

// DeleteFromS3 从S3删除文件
func DeleteFromS3(key string, config *database.S3Config) error {
	// 创建AWS会话
	sess, err := createS3Session(config)
	if err != nil {
		return fmt.Errorf("创建S3会话失败: %w", err)
	}

	// 创建S3服务客户端
	svc := s3.New(sess)

	// 删除文件
	_, err = svc.DeleteObject(&s3.DeleteObjectInput{
		Bucket: aws.String(config.Bucket),
		Key:    aws.String(key),
	})

	if err != nil {
		return fmt.Errorf("从S3删除文件失败: %w", err)
	}

	return nil
}

// TestS3Connection 测试S3连接
func TestS3Connection(config *database.S3Config) error {
	// 创建AWS会话
	sess, err := createS3Session(config)
	if err != nil {
		return fmt.Errorf("创建S3会话失败: %w", err)
	}

	// 创建S3服务客户端
	svc := s3.New(sess)

	// 尝试列出bucket来测试连接
	_, err = svc.HeadBucket(&s3.HeadBucketInput{
		Bucket: aws.String(config.Bucket),
	})

	if err != nil {
		return fmt.Errorf("S3连接测试失败: %w", err)
	}

	return nil
}

// createS3Session 创建S3会话
func createS3Session(config *database.S3Config) (*session.Session, error) {
	// 创建AWS配置
	awsConfig := &aws.Config{
		Region: aws.String(config.Region),
		Credentials: credentials.NewStaticCredentials(
			config.AccessKeyID,
			config.SecretAccessKey,
			"", // token
		),
		DisableSSL: aws.Bool(!config.UseSSL),
	}

	// 如果有自定义端点，设置它
	if config.Endpoint != nil && *config.Endpoint != "" {
		awsConfig.Endpoint = aws.String(*config.Endpoint)
		awsConfig.S3ForcePathStyle = aws.Bool(true) // 对于自定义端点，通常需要路径样式
	}

	// 创建会话
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}

	return sess, nil
}

// generateS3URL 生成S3文件URL
func generateS3URL(config *database.S3Config, key string) string {
	if config.Endpoint != nil && *config.Endpoint != "" {
		// 自定义端点
		protocol := "https"
		if !config.UseSSL {
			protocol = "http"
		}
		return fmt.Sprintf("%s://%s/%s/%s", protocol, *config.Endpoint, config.Bucket, key)
	} else {
		// AWS S3标准端点
		protocol := "https"
		if !config.UseSSL {
			protocol = "http"
		}
		return fmt.Sprintf("%s://%s.s3.%s.amazonaws.com/%s", protocol, config.Bucket, config.Region, key)
	}
}

// GetDefaultS3Config 获取默认S3配置
func GetDefaultS3Config() (*database.S3Config, error) {
	var config database.S3Config
	if err := database.DB.Where("is_default = ? AND is_active = ?", true, true).First(&config).Error; err != nil {
		return nil, fmt.Errorf("未找到默认S3配置: %w", err)
	}
	return &config, nil
}

// ListS3Objects 列出S3对象
func ListS3Objects(config *database.S3Config, prefix string) ([]*s3.Object, error) {
	// 创建AWS会话
	sess, err := createS3Session(config)
	if err != nil {
		return nil, fmt.Errorf("创建S3会话失败: %w", err)
	}

	// 创建S3服务客户端
	svc := s3.New(sess)

	// 列出对象
	result, err := svc.ListObjects(&s3.ListObjectsInput{
		Bucket: aws.String(config.Bucket),
		Prefix: aws.String(prefix),
	})

	if err != nil {
		return nil, fmt.Errorf("列出S3对象失败: %w", err)
	}

	return result.Contents, nil
}

// GetS3ObjectInfo 获取S3对象信息
func GetS3ObjectInfo(config *database.S3Config, key string) (*s3.HeadObjectOutput, error) {
	// 创建AWS会话
	sess, err := createS3Session(config)
	if err != nil {
		return nil, fmt.Errorf("创建S3会话失败: %w", err)
	}

	// 创建S3服务客户端
	svc := s3.New(sess)

	// 获取对象信息
	result, err := svc.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(config.Bucket),
		Key:    aws.String(key),
	})

	if err != nil {
		return nil, fmt.Errorf("获取S3对象信息失败: %w", err)
	}

	return result, nil
}

// GeneratePresignedURL 生成预签名URL
func GeneratePresignedURL(config *database.S3Config, key string, expiration int64) (string, error) {
	// 创建AWS会话
	sess, err := createS3Session(config)
	if err != nil {
		return "", fmt.Errorf("创建S3会话失败: %w", err)
	}

	// 创建S3服务客户端
	svc := s3.New(sess)

	// 生成预签名URL
	req, _ := svc.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(config.Bucket),
		Key:    aws.String(key),
	})

	url, err := req.Presign(time.Duration(expiration) * time.Second)
	if err != nil {
		return "", fmt.Errorf("生成预签名URL失败: %w", err)
	}

	return url, nil
}
