[English Documentation](README.md)

# G2.AL

G2.AL 是一个多功能网络应用，提供短链接服务、临时邮箱、导航中心和实时热门内容聚合。

## 项目概述

G2.AL 将多种实用的网络工具集成到一个平台中。该应用程序帮助用户管理短链接、使用临时邮箱地址、组织书签，并随时了解各平台的最新热门话题。

### 主要功能

- **短链接服务**：创建和管理带有统计分析的短网址
- **临时邮箱**：生成一次性邮箱地址以保护您的隐私
- **导航中心**：通过现代、用户友好的界面组织和访问您的书签
- **今日热榜**：来自多个平台的实时热门内容聚合
- **链接状态检测**：自动检查书签链接是否仍然活跃
- **分类内容**：按类别组织资源，便于访问
- **响应式设计**：在桌面和移动设备上无缝工作

## 截图

![短链接](docs/screenshots/url-shortener.png)
![临时邮箱](docs/screenshots/temp-email.png)
![导航中心](docs/screenshots/navigation-hub.png)
![热门新闻](docs/screenshots/hot-news.png)

## 开发

本项目使用以下技术构建：

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

### 快速开始

唯一的要求是安装 Node.js 和 npm - [使用 nvm 安装](https://github.com/nvm-sh/nvm#installing-and-updating)

```sh
# 克隆仓库
git clone <YOUR_GIT_URL>

# 进入项目目录
cd g2al

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 部署指南

URL Stash Vault 支持两种后端架构：
1. **Supabase 后端** - 使用 Supabase 作为后端服务
2. **Go 自定义后端** - 使用 Go + Gin 框架的自定义后端

### 前端配置

#### 环境变量配置

复制 `env.example` 为 `.env` 并配置以下变量：

```bash
# 后端类型选择
VITE_BACKEND_TYPE=go_backend  # 或 supabase

# Supabase 配置 (当使用 Supabase 时)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Go 后端配置 (当使用 Go 后端时)
VITE_GO_BACKEND_URL=http://localhost:8080/api/v1
```

#### 前端启动

```bash
# 安装依赖
npm install
# 或
pnpm install

# 启动开发服务器
npm run dev
# 或
pnpm dev

# 构建生产版本
npm run build
# 或
pnpm build
```

### Go 后端部署

#### 1. 环境准备

确保已安装：
- Go 1.19+
- 数据库（SQLite/MySQL/PostgreSQL 之一）

#### 2. 配置环境变量

复制 `backend/env.example` 为 `backend/.env` 并根据需要修改：

##### SQLite 配置（推荐用于开发和小型部署）
```bash
DB_DRIVER=sqlite
DB_SOURCE=./data/app.db
```

##### MySQL 配置
```bash
DB_DRIVER=mysql
DB_SOURCE=user:password@tcp(localhost:3306)/url_stash_vault?charset=utf8mb4&parseTime=True&loc=Local
```

##### PostgreSQL 配置
```bash
DB_DRIVER=postgres
DB_SOURCE=host=localhost port=5432 user=postgres password=password dbname=url_stash_vault sslmode=disable TimeZone=Asia/Shanghai
```

#### 3. 数据库初始化

##### 自动迁移（推荐）
设置环境变量：
```bash
DB_AUTO_MIGRATE=true
```
后端启动时会自动创建表和索引。

##### 手动建表
如果需要手动建表，可以使用提供的 SQL 脚本生成器：

```bash
cd backend
go run scripts/generate_sql.go sqlite    # 生成 SQLite 脚本
go run scripts/generate_sql.go mysql     # 生成 MySQL 脚本
go run scripts/generate_sql.go postgres  # 生成 PostgreSQL 脚本
```
生成的 SQL 文件位于 `backend/sql_scripts/` 目录。

#### 4. 启动后端

```bash
cd backend

# 初始化 Go 模块（首次运行）
go mod init url-stash-vault
go mod tidy

# 启动开发服务器
go run cmd/server/main.go

# 或构建并运行
go build -o bin/server cmd/server/main.go
./bin/server
```

#### 5. 健康检查

访问 `http://localhost:8080/api/v1/health` 检查服务状态。

#### 6. 开发模式数据持久化

**重要说明**: 在开发模式下，后端使用 SQLite 数据库，数据会持久化保存。

- **数据库位置**: `./backend/data/app.db`
- **数据持久化**: 重启后端后数据会保留
- **重置数据库**: 如果需要清除所有数据进行测试：

  **Linux/macOS**:
  ```bash
  cd backend
  ./reset-db.sh
  ```

  **Windows**:
  ```cmd
  cd backend
  reset-db.bat
  ```

  **手动重置**:
  ```bash
  # 直接删除数据库文件
  rm backend/data/app.db
  ```

下次启动时会自动重新创建数据库并使用默认配置。

### 数据库配置详解

#### SQLite
- **优点**: 无需额外安装，适合开发和小型部署
- **缺点**: 不支持并发写入，不适合高并发场景
- **配置**:
  ```bash
  DB_DRIVER=sqlite
  DB_SOURCE=./data/app.db
  ```

#### MySQL
- **优点**: 成熟稳定，支持高并发
- **缺点**: 需要额外安装和配置
- **配置**:
  ```bash
  DB_DRIVER=mysql
  DB_HOST=localhost
  DB_PORT=3306
  DB_USER=root
  DB_PASSWORD=your_password
  DB_NAME=url_stash_vault
  ```

#### PostgreSQL
- **优点**: 功能强大，支持复杂查询
- **缺点**: 相对复杂，资源占用较高
- **配置**:
  ```bash
  DB_DRIVER=postgres
  DB_HOST=localhost
  DB_PORT=5432
  DB_USER=postgres
  DB_PASSWORD=your_password
  DB_NAME=url_stash_vault
  DB_SSLMODE=disable
  ```

### 安全配置

#### JWT 密钥
**重要**: 生产环境必须设置安全的 JWT 密钥：

```bash
# 生成随机密钥
openssl rand -base64 32

# 设置到环境变量
JWT_SECRET_KEY=your_generated_secure_key_here
```

#### 密码策略
系统默认要求密码：
- 至少 8 个字符
- 包含大写字母
- 包含小写字母
- 包含数字
- 包含特殊字符

#### CORS 配置
根据前端域名配置 CORS：

```bash
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

### 生产环境部署

#### 1. 使用 Docker

创建 `Dockerfile`:

```dockerfile
# 构建阶段
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY backend/ .
RUN go mod download
RUN go build -o server cmd/server/main.go

# 运行阶段
# (进一步说明取决于具体的 Docker 设置)
```

## Redis 缓存集成说明

本项目已成功集成 Redis 缓存功能，主要用于提升 JWT 令牌验证和短链访问的性能。

### 功能特性

#### 1. JWT 令牌缓存
- **存储位置**: Redis 中以 `jwt_token:{user_id}` 为键
- **自动过期**: 根据 JWT 配置的过期时间自动删除
- **登出支持**: 用户登出时自动从 Redis 中删除令牌
- **安全验证**: Token 验证时会检查 Redis 中的缓存，确保登出后的令牌失效

#### 2. 短链缓存
- **存储位置**: Redis 中以 `short_url:{short_code}` 为键
- **数据结构**: 包含原始URL、用户ID、点击次数、创建时间和过期时间
- **自动过期**:
  - 有过期时间的短链：按设置的过期时间自动删除
  - 永久短链：在 Redis 中设置 30 天过期，访问时自动刷新
- **性能优化**:
  - 优先从 Redis 读取，未命中时从数据库加载
  - 点击次数在 Redis 中实时更新，异步同步到数据库
  - 永久短链访问时自动刷新 Redis 过期时间

### 配置方法

#### 环境变量配置
在 `.env` 文件中设置以下配置：

```env
# Redis 配置
REDIS_ENABLED=true                # 启用 Redis 缓存
REDIS_ADDR=localhost:6379        # Redis 服务器地址
REDIS_PASSWORD=                  # Redis 密码（如有）
REDIS_DB=0                      # Redis 数据库编号
```

### 缓存策略

#### 1. 读取策略
1. 优先从 Redis 缓存读取数据
2. 缓存未命中时从数据库读取
3. 从数据库读取后自动加载到 Redis

#### 2. 写入策略
1. 数据同时写入数据库和 Redis
2. 点击次数等高频更新操作优先更新 Redis，异步同步数据库

#### 3. 过期策略
1. **有期限短链**: 按实际过期时间设置 Redis TTL
2. **永久短链**: Redis 中设置 30 天 TTL，访问时自动刷新
3. **JWT 令牌**: 按 JWT 配置的过期时间设置 TTL

### 监控和调试

#### 1. 日志信息
- Redis 连接状态日志
- 缓存命中/未命中日志
- 异步操作错误日志

#### 2. 健康检查
```
GET /health      # 基础健康检查
GET /health/db   # 数据库健康检查
```

#### 3. Redis 状态查看
可以通过 Redis CLI 查看缓存状态：
```bash
# 查看所有 JWT 令牌
redis-cli KEYS "jwt_token:*"

# 查看所有短链缓存
redis-cli KEYS "short_url:*"

# 查看特定短链的数据
redis-cli GET "short_url:abc123"
```

### 注意事项

1. **生产环境配置**: 确保在生产环境中设置正确的 Redis 连接参数和密码
2. **内存管理**: 监控 Redis 内存使用情况，适当设置最大内存限制
3. **数据一致性**: 缓存和数据库可能存在短暂的数据不一致，这是正常现象
4. **容量规划**: 根据业务量合理配置 Redis 实例规格
5. **备份策略**: 虽然缓存数据可以重建，但建议配置 Redis 持久化

## API 集成

应用程序与 api.allbs.cn API 集成，以获取各种平台的热门内容。集成在 `src/hooks/hot-news/api.ts` 文件中处理。

## 贡献

欢迎贡献！请随时提交 Pull Request。

## 许可证

本项目基于 MIT 许可证 - 详情请参阅 LICENSE 文件。

---

[English Documentation](README.md) | [报告问题](https://github.com/yourusername/g2al/issues)

## 最新更新与迁移状态

### ✅ 完成 Supabase 到 Go 后端迁移

项目已成功从 Supabase 迁移到自定义 Go 后端，提供更好的性能和控制：

#### 迁移成果
- **100% Supabase 独立**: 所有 Supabase 依赖已完全移除
- **双后端支持**: 保持与 Supabase 和 Go 后端的兼容性
- **性能提升**: API 响应时间提升至 1-10ms
- **功能完整**: 所有原有功能得到维护和增强

#### 已迁移的关键组件
- 管理员统计和用户管理
- 导航分类和链接管理
- 邮箱域名管理
- 功能排序服务
- 仪表板数据和访客统计
- 临时邮箱功能（API 就绪）

### 🚀 新增 S3 文件上传系统

实现了完整的 S3 集成文件上传系统：

#### 功能特性
- **S3 配置管理**: 管理员面板管理多个 S3 配置
- **文件上传 API**: 支持头像上传和通用文件上传
- **文件类型验证**: 自动验证不同文件类型
- **大小限制**: 可配置文件大小限制（头像 2MB，通用文件 10MB）
- **AWS S3 兼容**: 支持 AWS S3 和兼容 S3 的服务（MinIO、阿里云 OSS）

#### S3 配置
管理员可通过管理面板配置 S3 存储：
1. 导航至系统设置 → S3 配置管理
2. 添加 S3 凭据（Access Key、Secret Key、Region、Bucket）
3. 设为默认配置并启用
4. 激活前测试连接

### 🔐 增强路由保护

实现了全面的路由保护系统：

#### 受保护路由
- **公开页面**: 首页、导航、热榜、在线工具、关于
- **用户页面**: 控制台、个人中心、备忘录与待办（需要登录）
- **管理页面**: 管理员面板（需要管理员权限）

#### 功能特性
- **403 禁止页面**: 用户友好的访问拒绝页面，提供登录选项
- **认证对话框**: 基于模态框的登录/注册，替代独立页面
- **自动重定向**: 成功认证后的智能重定向

### 🎨 UI/UX 改进

#### 认证体验
- **模态框认证**: 在便捷对话框中进行登录、注册和密码重置
- **OAuth 集成**: 支持第三方认证提供商
- **邮箱验证**: 自动邮箱验证，支持重发功能
- **多语言支持**: 完整的中英文本地化

#### 导航增强
- **响应式设计**: 针对桌面和移动设备优化
- **改进的可访问性**: 更好的键盘导航和屏幕阅读器支持
- **视觉反馈**: 整个应用程序的加载状态和错误处理

### 📊 API 端点

#### 认证与用户管理
```
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/register       # 用户注册
POST /api/v1/auth/logout         # 用户登出
GET  /api/v1/auth/me            # 获取当前用户信息
POST /api/v1/auth/forgot-password # 密码重置
```

#### 文件上传与 S3 管理
```
GET  /api/v1/s3-configs         # 列出 S3 配置
POST /api/v1/s3-configs         # 创建 S3 配置
PUT  /api/v1/s3-configs/:id     # 更新 S3 配置
DELETE /api/v1/s3-configs/:id   # 删除 S3 配置
POST /api/v1/upload/avatar      # 上传用户头像
POST /api/v1/upload             # 通用文件上传
GET  /api/v1/files              # 列出用户文件
DELETE /api/v1/files/:id        # 删除文件
```

#### 导航与内容管理
```
GET  /api/v1/navigation/categories    # 列出导航分类
POST /api/v1/navigation/categories    # 创建分类
GET  /api/v1/navigation/links         # 列出导航链接
POST /api/v1/navigation/links         # 创建链接
GET  /api/v1/email-domains           # 列出邮箱域名
POST /api/v1/email-domains           # 添加邮箱域名
```

### 🔧 配置要求

#### S3 文件上传设置
启用文件上传功能需要：
1. 在管理面板中配置至少一个活跃的 S3 配置
2. 确保 S3 存储桶具有适当的读写权限
3. 为 Web 访问设置适当的 CORS 策略

#### 环境变量
```bash
# 后端配置
DB_DRIVER=sqlite                    # 数据库驱动（sqlite/mysql/postgres）
DB_SOURCE=./data/app.db            # 数据库连接字符串
JWT_SECRET_KEY=your_secure_key     # JWT 签名密钥
REDIS_ENABLED=true                 # 启用 Redis 缓存
REDIS_ADDR=localhost:6379          # Redis 服务器地址

# 前端配置
VITE_BACKEND_TYPE=go_backend       # 后端类型选择
VITE_GO_BACKEND_URL=http://localhost:8080/api/v1  # Go 后端 URL
```

### 🚨 破坏性变更

#### 移除的页面
以下独立页面已替换为模态对话框：
- `/login` → 登录模态框
- `/register` → 注册模态框
- `/reset-password` → 密码重置模态框

#### 更新的 API 路径
- 邮箱域名: `/email-domains` → `/api/v1/email-domains`
- 头像上传: `/auth/upload-avatar` → `/api/v1/upload/avatar`

### 📈 性能改进

- **数据库优化**: 适当的索引和查询优化
- **Redis 缓存**: JWT 令牌和频繁访问的数据缓存
- **API 响应时间**: 从 50-200ms 提升至 1-10ms
- **文件上传**: 高效的 S3 集成，支持进度跟踪

### 🔍 测试与验证

所有核心功能已测试和验证：
- ✅ 用户认证和授权
- ✅ 管理面板功能
- ✅ 导航管理
- ✅ 文件上传系统
- ✅ 路由保护
- ✅ 多语言支持
- ✅ 响应式设计

### React Hook 调用错误修复

之前修复了在使用 Radix UI 组件时 memo-todo 页面中的"Invalid hook call"错误：
1. 将 Radix UI Popover 替换为自定义下拉菜单实现
2. 优化项目依赖，确保单一 React 实例
3. 添加依赖修复脚本 `npm run fix-deps`

---

## 🚀 部署脚本

### 概述

`scripts` 目录包含了用于一键部署和管理 URL Stash Vault 应用的脚本文件，提供一键部署和服务管理功能。

### 快速开始

#### Windows 环境
```bash
# 运行交互式管理工具
scripts\manage.bat
```

#### Linux/Mac 环境
```bash
# 首次使用需要添加执行权限
chmod +x scripts/manage.sh

# 运行交互式管理工具
./scripts/manage.sh
```

### 交互式管理工具

#### 主菜单选项

```
======================================
    URL Stash Vault 项目管理工具
======================================

请选择要执行的操作：

[1] 快速重启服务 (开发环境)
[2] 停止所有服务
[3] 完整Docker部署
[4] 查看服务状态
[5] 查看项目信息
[0] 退出
```

#### 功能详解

##### [1] 快速重启服务 (开发环境)
- 🔄 停止现有服务（端口 3000, 3001, 8080）
- 🚀 启动后端Go服务器
- 🌐 启动前端Vite开发服务器
- 📊 显示进程信息和日志位置
- ⚡ 适用于日常开发调试

##### [2] 停止所有服务
- 🛑 终止本地开发服务器
- 🐳 停止Docker容器
- 🧹 清理PID文件
- 🔒 完全释放相关端口

##### [3] 完整Docker部署
- ⚠️ 确认提示，防误操作
- 🏗️ 构建前后端Docker镜像
- 📤 推送到Docker Hub (kkape仓库)
- 🚀 启动容器化服务
- 📊 显示部署状态

##### [4] 查看服务状态
- 🔍 检查端口占用情况
- 🐳 显示Docker容器状态
- 🛠️ 环境依赖检查 (Go, Node.js, Docker)
- 📄 日志文件状态检查

##### [5] 查看项目信息
- 📖 项目概述和功能模块
- 🛠️ 技术栈信息
- 🌐 端口配置说明
- 🐳 Docker镜像信息
- 📁 配置文件位置

### 使用方法

#### 交互式管理（推荐）

##### Windows
```bash
# 直接运行
scripts\manage.bat

# 或者双击 scripts\manage.bat 文件
```

##### Linux/Mac
```bash
# 添加执行权限（首次）
chmod +x scripts/manage.sh

# 运行管理工具
./scripts/manage.sh
```

### 界面特色

#### Windows 界面
- 📚 清晰的菜单选项
- 🔄 循环菜单设计
- ⚠️ 错误提示和确认对话
- 📊 实时状态显示

#### Linux/Mac 界面
- 🌈 丰富的颜色输出
- 📊 带颜色的状态指示
- 💬 友好的日志消息
- 🎯 清晰的进度提示

### 前置要求

#### 开发环境
- **Node.js** (18+) 和 npm
- **Go** (1.21+)
- 项目依赖已安装

#### 生产环境 (Docker)
- **Docker** 和 **Docker Compose**
- **Docker Hub** 账户 (用户名: kkape)

### 配置文件

#### 自动创建的配置文件

1. **后端环境配置** (`backend/.env`)
   - 如不存在，从 `backend/env.example` 复制

2. **前端环境配置** (`.env`)
   - 如不存在，自动创建默认配置

3. **Docker 配置**
   - `Dockerfile` (前端)
   - `backend/Dockerfile` (后端)
   - `nginx.conf` (Nginx配置)
   - `docker-compose.yml` (容器编排)

#### 默认端口配置

- **前端开发服务器**: http://localhost:3000 或 http://localhost:3001
- **后端API服务器**: http://localhost:8080
- **前端Docker容器**: http://localhost:3000 (映射到容器80端口)
- **后端Docker容器**: http://localhost:8080

### Docker镜像仓库

#### 镜像名称
- **前端**: `kkape/url-stash-vault-frontend:latest`
- **后端**: `kkape/url-stash-vault-backend:latest`

#### 推送要求
- 需要Docker Hub账户 (用户名: kkape)
- 执行部署脚本时会提示输入密码

### 日志文件

#### 位置
- `logs/backend.log` - 后端服务日志
- `logs/frontend.log` - 前端服务日志
- `logs/backend.pid` - 后端进程ID (Linux/Mac)
- `logs/frontend.pid` - 前端进程ID (Linux/Mac)

#### 查看日志
```bash
# 查看后端日志
tail -f logs/backend.log

# 查看前端日志
tail -f logs/frontend.log

# 查看Docker日志
docker-compose logs -f
```

### 故障排除

#### 常见问题

1. **端口占用**
   - 脚本会自动终止占用端口的进程
   - 手动检查: `netstat -ano | findstr :8080` (Windows) 或 `lsof -i :8080` (Linux/Mac)

2. **Docker权限问题**
   - 确保Docker服务正在运行
   - Linux环境可能需要sudo权限或将用户加入docker组

3. **构建失败**
   - 检查网络连接
   - 确保所有依赖已正确安装
   - 查看错误日志定位问题

4. **推送失败**
   - 确认Docker Hub账户信息正确
   - 检查网络连接
   - 验证镜像构建是否成功

#### 手动操作

如果脚本执行失败，可以手动执行以下命令：

##### 手动构建镜像
```bash
# 后端
cd backend
docker build -t kkape/url-stash-vault-backend:latest .

# 前端
cd ..
docker build -t kkape/url-stash-vault-frontend:latest .
```

##### 手动推送镜像
```bash
docker login -u kkape
docker push kkape/url-stash-vault-backend:latest
docker push kkape/url-stash-vault-frontend:latest
```

##### 手动启动容器
```bash
docker-compose up -d
```

### 注意事项

1. **生产环境**：修改 `docker-compose.yml` 中的 `JWT_SECRET_KEY` 为更安全的密钥
2. **HTTPS**：生产环境建议配置HTTPS和反向代理
3. **数据备份**：`./data` 目录包含数据库文件，请定期备份
4. **环境变量**：根据实际需求修改 `.env` 配置文件
5. **防火墙**：确保相关端口已正确开放

### 脚本特色

#### 智能化
- 🧠 自动检测环境依赖
- 🔄 智能端口管理
- 📁 自动创建配置文件

#### 用户友好
- 🎨 美观的界面设计
- 💬 详细的进度提示
- ⚠️ 完善的错误处理

#### 功能完整
- 🛠️ 开发环境管理
- 🐳 生产环境部署
- 📊 状态监控
- 📖 信息查看