
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Auth context of the function
    const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string
    const supabase = createClient(supabaseUrl, supabaseKey)

    console.log('Running cleanup job for expired URLs')

    // Get current time
    const now = new Date().toISOString()

    // Find all expired URLs
    const { data: expiredUrls, error: findError } = await supabase
      .from('short_urls')
      .select('id, short_code')
      .lt('expires_at', now)
      .not('expires_at', 'is', null)

    if (findError) {
      throw findError
    }

    console.log(`Found ${expiredUrls?.length || 0} expired URLs to clean up`)

    if (expiredUrls && expiredUrls.length > 0) {
      // Delete the expired URLs
      const { error: deleteError } = await supabase
        .from('short_urls')
        .delete()
        .in('id', expiredUrls.map(url => url.id))

      if (deleteError) {
        throw deleteError
      }

      console.log(`Successfully deleted ${expiredUrls.length} expired URLs`)
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: `Cleaned up ${expiredUrls?.length || 0} expired URLs`,
        cleaned: expiredUrls?.length || 0
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error cleaning up expired URLs:', error)
    
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
