[中文文档](README_CN.md)

# G2.AL

G2.AL is a multi-purpose web application that provides short URL services, temporary email, navigation hub, and real-time trending content aggregation.

## Project Overview

G2.AL combines multiple useful web tools into one platform. The application helps users manage short links, use temporary email addresses, organize bookmarks, and stay updated on trending topics across various platforms.

### Key Features

- **URL Shortener**: Create and manage shortened URLs with analytics
- **Temporary Email**: Generate disposable email addresses to protect your privacy
- **Navigation Hub**: Organize and access your bookmarks with a modern, user-friendly interface
- **Today's Hot News**: Real-time aggregation of trending content from multiple platforms
- **Link Status Detection**: Automatically checks if bookmarked links are still active
- **Categorized Content**: Organize resources by categories for easy access
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Screenshots

![URL Shortener](docs/screenshots/url-shortener.png)
![Temp Email](docs/screenshots/temp-email.png)
![Navigation Hub](docs/screenshots/navigation-hub.png)
![Hot News](docs/screenshots/hot-news.png)

## Development

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

### Getting Started

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

```sh
# Clone the repository
git clone <YOUR_GIT_URL>

# Navigate to the project directory
cd g2al

# Install dependencies
npm install

# Start the development server
npm run dev
```

## Deployment Guide

URL Stash Vault supports two backend architectures:
1. **Supabase Backend** - Uses Supabase as the backend service.
2. **Go Custom Backend** - Uses a custom backend built with Go + Gin framework.

### Frontend Configuration

#### Environment Variables

Copy `env.example` to `.env` and configure the following variables:

```bash
# Backend type selection
VITE_BACKEND_TYPE=go_backend  # or supabase

# Supabase Configuration (when using Supabase)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Go Backend Configuration (when using Go backend)
VITE_GO_BACKEND_URL=http://localhost:8080/api/v1
```

#### Frontend Startup

```bash
# Install dependencies
npm install
# or
pnpm install

# Start development server
npm run dev
# or
pnpm dev

# Build for production
npm run build
# or
pnpm build
```

### Go Backend Deployment

#### 1. Prerequisites

Ensure you have installed:
- Go 1.19+
- A database (SQLite/MySQL/PostgreSQL)

#### 2. Configure Environment Variables

Copy `backend/env.example` to `backend/.env` and modify as needed:

##### SQLite Configuration (recommended for development and small deployments)
```bash
DB_DRIVER=sqlite
DB_SOURCE=./data/app.db
```

##### MySQL Configuration
```bash
DB_DRIVER=mysql
DB_SOURCE=user:password@tcp(localhost:3306)/url_stash_vault?charset=utf8mb4&parseTime=True&loc=Local
```

##### PostgreSQL Configuration
```bash
DB_DRIVER=postgres
DB_SOURCE=host=localhost port=5432 user=postgres password=password dbname=url_stash_vault sslmode=disable TimeZone=Asia/Shanghai
```

#### 3. Database Initialization

##### Automatic Migration (Recommended)
Set the environment variable:
```bash
DB_AUTO_MIGRATE=true
```
The backend will automatically create tables and indexes on startup.

##### Manual Table Creation
If manual table creation is needed, use the provided SQL script generator:

```bash
cd backend
go run scripts/generate_sql.go sqlite    # Generate SQLite script
go run scripts/generate_sql.go mysql     # Generate MySQL script
go run scripts/generate_sql.go postgres  # Generate PostgreSQL script
```
Generated SQL files are located in the `backend/sql_scripts/` directory.

#### 4. Start Backend

```bash
cd backend

# Initialize Go modules (first time)
go mod init url-stash-vault
go mod tidy

# Start development server
go run cmd/server/main.go

# Or build and run
go build -o bin/server cmd/server/main.go
./bin/server
```

#### 5. Health Check

Access `http://localhost:8080/api/v1/health` to check service status.

#### 6. Development Mode Data Persistence

**Important**: In development mode, the backend uses SQLite database with persistent storage.

- **Database Location**: `./backend/data/app.db`
- **Data Persistence**: Data is preserved between backend restarts
- **Reset Database**: If you need to clear all data for testing:

  **Linux/macOS**:
  ```bash
  cd backend
  ./reset-db.sh
  ```

  **Windows**:
  ```cmd
  cd backend
  reset-db.bat
  ```

  **Manual Reset**:
  ```bash
  # Simply delete the database file
  rm backend/data/app.db
  ```

The database will be automatically recreated with default configuration on the next startup.

### Database Configuration Details

#### SQLite
- **Pros**: No extra installation required, suitable for development and small deployments.
- **Cons**: Does not support concurrent writes, not suitable for high-concurrency scenarios.
- **Configuration**:
  ```bash
  DB_DRIVER=sqlite
  DB_SOURCE=./data/app.db
  ```

#### MySQL
- **Pros**: Mature and stable, supports high concurrency.
- **Cons**: Requires additional installation and configuration.
- **Configuration**:
  ```bash
  DB_DRIVER=mysql
  DB_HOST=localhost
  DB_PORT=3306
  DB_USER=root
  DB_PASSWORD=your_password
  DB_NAME=url_stash_vault
  ```

#### PostgreSQL
- **Pros**: Powerful features, supports complex queries.
- **Cons**: Relatively complex, higher resource consumption.
- **Configuration**:
  ```bash
  DB_DRIVER=postgres
  DB_HOST=localhost
  DB_PORT=5432
  DB_USER=postgres
  DB_PASSWORD=your_password
  DB_NAME=url_stash_vault
  DB_SSLMODE=disable
  ```

### Security Configuration

#### JWT Secret Key
**Important**: A secure JWT key must be set for production environments:

```bash
# Generate a random key
openssl rand -base64 32

# Set it in environment variables
JWT_SECRET_KEY=your_generated_secure_key_here
```

#### Password Policy
The system defaults require passwords to be:
- At least 8 characters long
- Contain uppercase letters
- Contain lowercase letters
- Contain numbers
- Contain special characters

#### CORS Configuration
Configure CORS according to your frontend domain:

```bash
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

### Production Deployment

#### 1. Using Docker

Create a `Dockerfile`:

```dockerfile
# Build stage
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY backend/ .
RUN go mod download
RUN go build -o server cmd/server/main.go

# Run stage
# (Further instructions depend on your specific Docker setup)
```

## Redis Cache Integration

This project has successfully integrated Redis caching functionality, primarily to enhance the performance of JWT token validation and short URL access.

### Features

#### 1. JWT Token Cache
- **Storage**: In Redis with `jwt_token:{user_id}` as the key.
- **Auto-Expiration**: Automatically deleted based on JWT configured expiration time.
- **Logout Support**: Token automatically deleted from Redis on user logout.
- **Security Validation**: Token validation checks Redis cache to ensure logged-out tokens are invalidated.

#### 2. Short URL Cache
- **Storage**: In Redis with `short_url:{short_code}` as the key.
- **Data Structure**: Contains original URL, user ID, click count, creation time, and expiration time.
- **Auto-Expiration**:
  - For short URLs with an expiration time: automatically deleted according to the set expiration.
  - For permanent short URLs: set to expire in Redis after 30 days, refreshed on access.
- **Performance Optimization**:
  - Prioritizes reading from Redis; loads from database on cache miss.
  - Click counts are updated in Redis in real-time and synced asynchronously to the database.
  - Redis expiration time for permanent short URLs is refreshed on access.

### Configuration

#### Environment Variables
Set the following in your `.env` file:

```env
# Redis Configuration
REDIS_ENABLED=true                # Enable Redis cache
REDIS_ADDR=localhost:6379        # Redis server address
REDIS_PASSWORD=                  # Redis password (if any)
REDIS_DB=0                      # Redis database number
```

### Cache Strategy

#### 1. Read Strategy
1. Prioritize reading data from Redis cache.
2. On cache miss, read from the database.
3. Automatically load data into Redis after reading from the database.

#### 2. Write Strategy
1. Data is written to both the database and Redis.
2. High-frequency updates (like click counts) are prioritized for Redis, with asynchronous database synchronization.

#### 3. Expiration Strategy
1. **Time-limited Short URLs**: Redis TTL set according to actual expiration time.
2. **Permanent Short URLs**: Redis TTL set to 30 days, refreshed on access.
3. **JWT Tokens**: TTL set according to JWT configured expiration time.

### Monitoring and Debugging

#### 1. Log Information
- Redis connection status logs.
- Cache hit/miss logs.
- Asynchronous operation error logs.

#### 2. Health Check
```
GET /health      # Basic health check
GET /health/db   # Database health check
```

#### 3. Redis Status Check
Check cache status via Redis CLI:
```bash
# View all JWT tokens
redis-cli KEYS "jwt_token:*"

# View all short URL caches
redis-cli KEYS "short_url:*"

# View data for a specific short URL
redis-cli GET "short_url:abc123"
```

### Important Notes

1. **Production Configuration**: Ensure correct Redis connection parameters and password are set in production.
2. **Memory Management**: Monitor Redis memory usage and set appropriate maximum memory limits.
3. **Data Consistency**: Brief inconsistencies between cache and database are normal.
4. **Capacity Planning**: Configure Redis instance specifications according to business volume.
5. **Backup Strategy**: Although cache data can be rebuilt, configuring Redis persistence is recommended.

## API Integration

The application integrates with the api.allbs.cn API to fetch trending content across various platforms. The integration is handled in the `src/hooks/hot-news/api.ts` file.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

---

[中文文档](README_CN.md) | [Report Issues](https://github.com/yourusername/g2al/issues)

## Recent Updates & Migration Status

### ✅ Complete Supabase to Go Backend Migration

The project has successfully migrated from Supabase to a custom Go backend, providing better performance and control:

#### Migration Achievements
- **100% Supabase Independence**: All Supabase dependencies have been removed
- **Dual Backend Support**: Maintains compatibility with both Supabase and Go backend
- **Enhanced Performance**: API response times improved to 1-10ms
- **Complete Feature Parity**: All original features maintained and enhanced

#### Key Components Migrated
- Admin statistics and user management
- Navigation categories and links management
- Email domain management
- Feature ordering service
- Dashboard data and visitor statistics
- Temporary email functionality (API ready)

### 🚀 New S3 File Upload System

A complete file upload system has been implemented with S3 integration:

#### Features
- **S3 Configuration Management**: Admin panel for managing multiple S3 configurations
- **File Upload APIs**: Support for avatar uploads and general file uploads
- **File Type Validation**: Automatic validation for different file types
- **Size Limits**: Configurable file size limits (2MB for avatars, 10MB for general files)
- **AWS S3 Compatibility**: Supports AWS S3 and S3-compatible services (MinIO, Alibaba Cloud OSS)

#### S3 Configuration
Administrators can configure S3 storage through the admin panel:
1. Navigate to System Settings → S3 Configuration Management
2. Add S3 credentials (Access Key, Secret Key, Region, Bucket)
3. Set as default configuration and enable
4. Test connection before activation

### 🔐 Enhanced Route Protection

Implemented comprehensive route protection system:

#### Protected Routes
- **Public Pages**: Home, navigation, hot news, online tools, about
- **User Pages**: Dashboard, profile, memo & todo (requires login)
- **Admin Pages**: Admin panel (requires admin privileges)

#### Features
- **403 Forbidden Page**: User-friendly access denied page with login options
- **Authentication Dialogs**: Modal-based login/register instead of separate pages
- **Automatic Redirection**: Smart redirection after successful authentication

### 🎨 UI/UX Improvements

#### Authentication Experience
- **Modal-Based Auth**: Login, register, and password reset in convenient dialogs
- **OAuth Integration**: Support for third-party authentication providers
- **Email Verification**: Automated email verification with resend functionality
- **Multi-language Support**: Complete Chinese and English localization

#### Navigation Enhancements
- **Responsive Design**: Optimized for both desktop and mobile devices
- **Improved Accessibility**: Better keyboard navigation and screen reader support
- **Visual Feedback**: Loading states and error handling throughout the application

### 📊 API Endpoints

#### Authentication & User Management
```
POST /api/v1/auth/login          # User login
POST /api/v1/auth/register       # User registration
POST /api/v1/auth/logout         # User logout
GET  /api/v1/auth/me            # Get current user info
POST /api/v1/auth/forgot-password # Password reset
```

#### File Upload & S3 Management
```
GET  /api/v1/s3-configs         # List S3 configurations
POST /api/v1/s3-configs         # Create S3 configuration
PUT  /api/v1/s3-configs/:id     # Update S3 configuration
DELETE /api/v1/s3-configs/:id   # Delete S3 configuration
POST /api/v1/upload/avatar      # Upload user avatar
POST /api/v1/upload             # General file upload
GET  /api/v1/files              # List user files
DELETE /api/v1/files/:id        # Delete file
```

#### Navigation & Content Management
```
GET  /api/v1/navigation/categories    # List navigation categories
POST /api/v1/navigation/categories    # Create category
GET  /api/v1/navigation/links         # List navigation links
POST /api/v1/navigation/links         # Create link
GET  /api/v1/email-domains           # List email domains
POST /api/v1/email-domains           # Add email domain
```

### 🔧 Configuration Requirements

#### S3 File Upload Setup
To enable file upload functionality:
1. Configure at least one active S3 configuration in the admin panel
2. Ensure proper S3 bucket permissions for read/write operations
3. Set appropriate CORS policies for web access

#### Environment Variables
```bash
# Backend Configuration
DB_DRIVER=sqlite                    # Database driver (sqlite/mysql/postgres)
DB_SOURCE=./data/app.db            # Database connection string
JWT_SECRET_KEY=your_secure_key     # JWT signing key
REDIS_ENABLED=true                 # Enable Redis caching
REDIS_ADDR=localhost:6379          # Redis server address

# Frontend Configuration
VITE_BACKEND_TYPE=go_backend       # Backend type selection
VITE_GO_BACKEND_URL=http://localhost:8080/api/v1  # Go backend URL
```

### 🚨 Breaking Changes

#### Removed Pages
The following standalone pages have been replaced with modal dialogs:
- `/login` → Login modal
- `/register` → Register modal
- `/reset-password` → Password reset modal

#### Updated API Paths
- Email domains: `/email-domains` → `/api/v1/email-domains`
- Avatar upload: `/auth/upload-avatar` → `/api/v1/upload/avatar`

### 📈 Performance Improvements

- **Database Optimization**: Proper indexing and query optimization
- **Redis Caching**: JWT tokens and frequently accessed data cached
- **API Response Times**: Improved from 50-200ms to 1-10ms
- **File Upload**: Efficient S3 integration with progress tracking

### 🔍 Testing & Validation

All core functionality has been tested and validated:
- ✅ User authentication and authorization
- ✅ Admin panel functionality
- ✅ Navigation management
- ✅ File upload system
- ✅ Route protection
- ✅ Multi-language support
- ✅ Responsive design

### React Hook Call Error Fix

Previously fixed "Invalid hook call" error in memo-todo page when using Radix UI components:
1. Replaced Radix UI Popover with custom dropdown implementation
2. Optimized project dependencies to ensure single React instance
3. Added dependency fix script `npm run fix-deps`

# React Hook Call Error Fix

Previously fixed "Invalid hook call" error in memo-todo page when using Radix UI components:
1. Replaced Radix UI Popover with custom dropdown implementation
2. Optimized project dependencies to ensure single React instance
3. Added dependency fix script `npm run fix-deps`

---

## 🚀 Deployment Scripts

### Overview

The `scripts` directory contains deployment and management scripts for URL Stash Vault application, providing one-click deployment and service management capabilities.

### Quick Start

#### Windows Environment
```bash
# Run interactive management tool
scripts\manage.bat
```

#### Linux/Mac Environment
```bash
# Add execution permission (first time)
chmod +x scripts/manage.sh

# Run interactive management tool
./scripts/manage.sh
```

### Interactive Management Tool

#### Main Menu Options

```
======================================
    URL Stash Vault Project Management
======================================

Please select an operation:

[1] Quick Restart Services (Development)
[2] Stop All Services
[3] Complete Docker Deployment
[4] Check Service Status
[5] View Project Information
[0] Exit
```

#### Feature Details

##### [1] Quick Restart Services (Development)
- 🔄 Stop existing services (ports 3000, 3001, 8080)
- 🚀 Start backend Go server
- 🌐 Start frontend Vite development server
- 📊 Display process information and log locations
- ⚡ Perfect for daily development and debugging

##### [2] Stop All Services
- 🛑 Terminate local development servers
- 🐳 Stop Docker containers
- 🧹 Clean up PID files
- 🔒 Completely release related ports

##### [3] Complete Docker Deployment
- ⚠️ Confirmation prompt to prevent misoperation
- 🏗️ Build frontend and backend Docker images
- 📤 Push to Docker Hub (kkape repository)
- 🚀 Start containerized services
- 📊 Display deployment status

##### [4] Check Service Status
- 🔍 Check port usage
- 🐳 Display Docker container status
- 🛠️ Environment dependency check (Go, Node.js, Docker)
- 📄 Log file status check

##### [5] View Project Information
- 📖 Project overview and feature modules
- 🛠️ Technology stack information
- 🌐 Port configuration details
- 🐳 Docker image information
- 📁 Configuration file locations

### Usage Methods

#### Interactive Management (Recommended)

##### Windows
```bash
# Direct execution
scripts\manage.bat

# Or double-click scripts\manage.bat file
```

##### Linux/Mac
```bash
# Add execution permission (first time)
chmod +x scripts/manage.sh

# Run management tool
./scripts/manage.sh
```

### Interface Features

#### Windows Interface
- 📚 Clear menu options
- 🔄 Loop menu design
- ⚠️ Error prompts and confirmation dialogs
- 📊 Real-time status display

#### Linux/Mac Interface
- 🌈 Rich color output
- 📊 Colored status indicators
- 💬 Friendly log messages
- 🎯 Clear progress indicators

### Prerequisites

#### Development Environment
- **Node.js** (18+) and npm
- **Go** (1.21+)
- Project dependencies installed

#### Production Environment (Docker)
- **Docker** and **Docker Compose**
- **Docker Hub** account (username: kkape)

### Configuration Files

#### Auto-created Configuration Files

1. **Backend Environment Configuration** (`backend/.env`)
   - Created from `backend/env.example` if not exists

2. **Frontend Environment Configuration** (`.env`)
   - Auto-created with default configuration if not exists

3. **Docker Configuration**
   - `Dockerfile` (frontend)
   - `backend/Dockerfile` (backend)
   - `nginx.conf` (Nginx configuration)
   - `docker-compose.yml` (container orchestration)

#### Default Port Configuration

- **Frontend Development Server**: http://localhost:3000 or http://localhost:3001
- **Backend API Server**: http://localhost:8080
- **Frontend Docker Container**: http://localhost:3000 (mapped to container port 80)
- **Backend Docker Container**: http://localhost:8080

### Docker Image Repository

#### Image Names
- **Frontend**: `kkape/url-stash-vault-frontend:latest`
- **Backend**: `kkape/url-stash-vault-backend:latest`

#### Push Requirements
- Requires Docker Hub account (username: kkape)
- Password prompt during deployment script execution

### Log Files

#### Locations
- `logs/backend.log` - Backend service logs
- `logs/frontend.log` - Frontend service logs
- `logs/backend.pid` - Backend process ID (Linux/Mac)
- `logs/frontend.pid` - Frontend process ID (Linux/Mac)

#### Viewing Logs
```bash
# View backend logs
tail -f logs/backend.log

# View frontend logs
tail -f logs/frontend.log

# View Docker logs
docker-compose logs -f
```

### Troubleshooting

#### Common Issues

1. **Port Occupied**
   - Scripts automatically terminate processes occupying ports
   - Manual check: `netstat -ano | findstr :8080` (Windows) or `lsof -i :8080` (Linux/Mac)

2. **Docker Permission Issues**
   - Ensure Docker service is running
   - Linux environments may require sudo permissions or adding user to docker group

3. **Build Failures**
   - Check network connection
   - Ensure all dependencies are properly installed
   - Review error logs to locate issues

4. **Push Failures**
   - Confirm Docker Hub account information is correct
   - Check network connection
   - Verify image build was successful

#### Manual Operations

If script execution fails, you can manually execute the following commands:

##### Manual Image Building
```bash
# Backend
cd backend
docker build -t kkape/url-stash-vault-backend:latest .

# Frontend
cd ..
docker build -t kkape/url-stash-vault-frontend:latest .
```

##### Manual Image Pushing
```bash
docker login -u kkape
docker push kkape/url-stash-vault-backend:latest
docker push kkape/url-stash-vault-frontend:latest
```

##### Manual Container Startup
```bash
docker-compose up -d
```

### Important Notes

1. **Production Environment**: Modify `JWT_SECRET_KEY` in `docker-compose.yml` to a more secure key
2. **HTTPS**: Configure HTTPS and reverse proxy for production environments
3. **Data Backup**: The `./data` directory contains database files, please backup regularly
4. **Environment Variables**: Modify `.env` configuration files according to actual needs
5. **Firewall**: Ensure related ports are properly opened

### Script Features

#### Intelligence
- 🧠 Automatic environment dependency detection
- 🔄 Smart port management
- 📁 Automatic configuration file creation

#### User-Friendly
- 🎨 Beautiful interface design
- 💬 Detailed progress indicators
- ⚠️ Comprehensive error handling

#### Complete Functionality
- 🛠️ Development environment management
- 🐳 Production environment deployment
- 📊 Status monitoring
- 📖 Information viewing
