# Stage 1: Build the frontend assets
FROM node:20-alpine AS frontend-builder

WORKDIR /app

# Copy package.json and lock files
COPY package.json ./
# Choose your lock file based on your package manager
COPY pnpm-lock.yaml ./
# COPY yarn.lock ./
# COPY package-lock.json ./

# Install dependencies (using pnpm as an example from your project files)
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# Copy the rest of the frontend application code
COPY . .

# Set the API URL build argument
# This ARG can be overridden during the docker build command if needed
ARG VITE_GO_BACKEND_URL=/api/v1
ENV VITE_GO_BACKEND_URL=${VITE_GO_BACKEND_URL}

# Build the frontend application
RUN pnpm run build

# Stage 2: Serve the frontend assets with Nginx
FROM nginx:stable-alpine

# Copy built assets from the builder stage
COPY --from=frontend-builder /app/dist /usr/share/nginx/html

# Copy a custom Nginx configuration (optional, but good for SPA routing)
# Create an nginx.conf file in your project root if you need this.
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# Default Nginx config usually works for SPAs if you handle 404s correctly or use hash routing.
# If you use browser routing (HTML5 history mode), you'll need a custom nginx.conf like this:
# server {
#    listen       80;
#    server_name  localhost;
#
#    location / {
#        root   /usr/share/nginx/html;
#        index  index.html index.htm;
#        try_files $uri $uri/ /index.html;
#    }
#
#    # Optional: Proxy API requests to the backend if running in the same Docker network
#    # location /api/v1/ {
#    #    proxy_pass http://backend-service-name:8080; # Replace with your backend service name and port
#    #    proxy_set_header Host $host;
#    #    proxy_set_header X-Real-IP $remote_addr;
#    #    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#    #    proxy_set_header X-Forwarded-Proto $scheme;
#    # }
# }

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"] 