
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ap, ArrowR<PERSON> } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';

const WhyChooseUs = () => {
  const { language } = useAppContext();
  
  return (
    <div className="mt-16 mb-16">
      <div className="text-center mb-10">
        <h2 className="text-2xl sm:text-3xl font-bold inline-block bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent1-600">
          {language === 'en' ? 'Why Choose Us?' : '为什么选择我们？'}
        </h2>
        <div className="w-20 h-1 bg-gradient-to-r from-primary to-accent1-600 mx-auto mt-3"></div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
        <Card className="bg-card/50 border shadow-sm hover:shadow-md transition-all overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-primary/5 rounded-full translate-x-1/2 -translate-y-1/2 blur-xl"></div>
          <CardHeader className="pb-2">
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
              <ShieldCheck className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-lg">{language === 'en' ? 'Privacy Focused' : '注重隐私'}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-sm">
              {language === 'en' 
                ? 'We respect your data and privacy. No unnecessary tracking or data collection.' 
                : '我们尊重您的数据和隐私。没有不必要的跟踪或数据收集。'}
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-card/50 border shadow-sm hover:shadow-md transition-all overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-accent1-500/5 rounded-full translate-x-1/2 -translate-y-1/2 blur-xl"></div>
          <CardHeader className="pb-2">
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
              <Zap className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-lg">{language === 'en' ? 'Fast & Reliable' : '快速可靠'}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-sm">
              {language === 'en' 
                ? 'Our services are optimized for speed and reliability. Available 24/7.' 
                : '我们的服务经过优化，以确保速度和可靠性。全天候可用。'}
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-card/50 border shadow-sm hover:shadow-md transition-all overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-primary/5 rounded-full translate-x-1/2 -translate-y-1/2 blur-xl"></div>
          <CardHeader className="pb-2">
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
              <ArrowRight className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-lg">{language === 'en' ? 'Constantly Evolving' : '持续发展'}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-sm">
              {language === 'en' 
                ? 'We constantly add new features and improve existing ones based on user feedback.' 
                : '我们不断添加新功能并根据用户反馈改进现有功能。'}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default WhyChooseUs;
