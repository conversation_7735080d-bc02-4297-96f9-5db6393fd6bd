
import React from 'react';
import { Unlink, Mail, Compass, TrendingUp, ArrowRight } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useAppContext } from '@/context/AppContext';

const FeaturesOverview = () => {
  const { language } = useAppContext();
  
  return (
    <div className="flex items-center justify-center">
      <div className="relative h-full w-full overflow-hidden rounded-xl bg-gradient-to-br from-background to-muted/50 border p-6 shadow-sm">
        <div className="space-y-5">
          <h3 className="text-xl sm:text-2xl font-bold">{language === 'en' ? 'Our Features' : '我们的功能'}</h3>
          <p className="text-muted-foreground">
            {language === 'en' 
              ? 'All the tools you need in one place, designed with privacy and simplicity in mind.'
              : '您需要的所有工具都集中在一处，注重隐私和简洁设计。'}
          </p>
          
          <div className="grid gap-4 mt-6">
            <div className="flex items-start gap-3 p-3 hover:bg-muted/50 rounded-lg transition-colors">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Unlink className="h-5 w-5 text-primary flex-shrink-0" />
              </div>
              <div>
                <h4 className="font-medium">{language === 'en' ? 'URL Shortening' : 'URL短链'}</h4>
                <p className="text-sm text-muted-foreground">
                  {language === 'en' 
                    ? 'Easily create and manage shortened URLs with advanced analytics.'
                    : '轻松创建和管理短链接，并获取高级分析数据。'}
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 hover:bg-muted/50 rounded-lg transition-colors">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Mail className="h-5 w-5 text-primary flex-shrink-0" />
              </div>
              <div>
                <h4 className="font-medium">{language === 'en' ? 'Temporary Email' : '临时邮箱'}</h4>
                <p className="text-sm text-muted-foreground">
                  {language === 'en' 
                    ? 'Disposable email addresses that protect your privacy.'
                    : '一次性电子邮件地址，保护您的隐私。'}
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 hover:bg-muted/50 rounded-lg transition-colors">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Compass className="h-5 w-5 text-primary flex-shrink-0" />
              </div>
              <div>
                <h4 className="font-medium">{language === 'en' ? 'Navigation Directory' : '导航站'}</h4>
                <p className="text-sm text-muted-foreground">
                  {language === 'en' 
                    ? 'Discover and organize useful websites in a beautiful directory.'
                    : '在精美的目录中发现和整理有用的网站。'}
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 hover:bg-muted/50 rounded-lg transition-colors">
              <div className="p-2 bg-primary/10 rounded-lg">
                <TrendingUp className="h-5 w-5 text-primary flex-shrink-0" />
              </div>
              <div>
                <h4 className="font-medium">{language === 'en' ? "Today's Hot List" : '今日热榜'}</h4>
                <p className="text-sm text-muted-foreground">
                  {language === 'en' 
                    ? 'Stay updated with trending topics from various platforms.'
                    : '了解各大平台的热门话题。'}
                </p>
              </div>
            </div>
          </div>
          
          <Button asChild size="sm" className="mt-6 bg-gradient-to-r from-primary to-accent1-600 hover:from-primary/90 hover:to-accent1-700 text-primary-foreground border-none shadow-md">
            <Link to="/register">
              {language === 'en' ? 'Sign Up' : '注册'} <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>

        {/* Decorative Circles */}
        <div className="absolute -bottom-16 -right-16 w-32 h-32 bg-primary/5 rounded-full blur-xl"></div>
        <div className="absolute -top-12 -left-12 w-24 h-24 bg-accent1-500/5 rounded-full blur-xl"></div>
      </div>
    </div>
  );
};

export default FeaturesOverview;
