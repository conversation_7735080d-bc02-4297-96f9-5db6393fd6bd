
import React from 'react';
import { Container } from "@/components/ui/container";
import { Card, CardContent } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useAppContext } from '@/context/AppContext';

const AboutFAQ = () => {
  const { language } = useAppContext();
  
  return (
    <div className="bg-muted/30 py-16">
      <Container className="max-w-5xl">
        <div className="text-center mb-10">
          <h2 className="text-2xl sm:text-3xl font-bold inline-block bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent1-600">
            {language === 'en' ? 'Frequently Asked Questions' : '常见问题'}
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-primary to-accent1-600 mx-auto mt-3"></div>
        </div>
        
        <Card className="bg-card/50 border shadow-sm">
          <CardContent className="p-6">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1" className="border-b">
                <AccordionTrigger className="hover:bg-muted/50 px-4 py-3 rounded-md transition-colors">
                  {language === 'en' ? 'Are these services free to use?' : '这些服务是免费使用的吗？'}
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4 pt-1 text-muted-foreground">
                  {language === 'en' 
                    ? 'Yes, most of our services are free to use. Some advanced features may require a premium subscription in the future, but core functionality will always remain free.' 
                    : '是的，我们的大多数服务都是免费使用的。将来某些高级功能可能需要付费订阅，但核心功能将始终保持免费。'}
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-2" className="border-b">
                <AccordionTrigger className="hover:bg-muted/50 px-4 py-3 rounded-md transition-colors">
                  {language === 'en' ? 'How do you ensure my data remains private?' : '你们如何确保我的数据保持私密？'}
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4 pt-1 text-muted-foreground">
                  {language === 'en' 
                    ? 'We prioritize your privacy. Online tools process data directly in your browser without sending information to our servers. For other services, we use strong encryption and only store what is absolutely necessary.' 
                    : '我们优先考虑您的隐私。在线工具直接在您的浏览器中处理数据，而不会将信息发送到我们的服务器。对于其他服务，我们使用强加密并且只存储绝对必要的内容。'}
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-3" className="border-b">
                <AccordionTrigger className="hover:bg-muted/50 px-4 py-3 rounded-md transition-colors">
                  {language === 'en' ? 'Can I contribute to this project?' : '我可以为这个项目做贡献吗？'}
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4 pt-1 text-muted-foreground">
                  {language === 'en' 
                    ? 'Absolutely! We are open source and welcome contributions. You can find our code on GitHub, submit issues, or contribute code directly.' 
                    : '当然可以！我们是开源的，欢迎贡献。您可以在GitHub上查看我们的代码，提交问题或直接贡献代码。'}
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-4" className="border-b">
                <AccordionTrigger className="hover:bg-muted/50 px-4 py-3 rounded-md transition-colors">
                  {language === 'en' ? 'How often are new features added?' : '新功能多久添加一次？'}
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4 pt-1 text-muted-foreground">
                  {language === 'en' 
                    ? 'We are constantly working on improvements and new features. Updates are typically released every few weeks, with major features announced on our blog and social media channels.' 
                    : '我们不断致力于改进和添加新功能。更新通常每隔几周发布一次，重大功能会在我们的博客和社交媒体渠道上宣布。'}
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="item-5" className="border-b">
                <AccordionTrigger className="hover:bg-muted/50 px-4 py-3 rounded-md transition-colors">
                  {language === 'en' ? 'What happens to my shortened URLs if the service shuts down?' : '如果服务关闭，我的短链接会怎么样？'}
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4 pt-1 text-muted-foreground">
                  {language === 'en' 
                    ? 'We are committed to maintaining this service long-term. However, if changes ever become necessary, we would provide ample notice and tools to export your data and links before any shutdown.' 
                    : '我们致力于长期维护此服务。但是，如果需要进行更改，我们会提前通知并提供工具，让您在停止服务前导出数据和链接。'}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </CardContent>
        </Card>
      </Container>
    </div>
  );
};

export default AboutFAQ;
