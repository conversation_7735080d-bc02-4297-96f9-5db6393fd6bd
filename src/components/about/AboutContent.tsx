
import React from 'react';
import { Container } from "@/components/ui/container";
import { TabsContent } from "@/components/ui/tabs";
import AboutMission from './AboutMission';
import FeaturesOverview from './FeaturesOverview';
import WhyChooseUs from './WhyChooseUs';
import FeaturesList from './FeaturesList';
import { useAppContext } from '@/context/AppContext';

interface AboutContentProps {
  activeTab: string;
}

const AboutContent = ({ activeTab }: AboutContentProps) => {
  const { language } = useAppContext();
  
  return (
    <Container className="max-w-7xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
      <TabsContent value="about" className="animate-in fade-in-50 slide-in-from-bottom-3">
        {/* Mission & Values Section */}
        <div className="grid gap-8 md:gap-12 lg:grid-cols-2 mb-16">
          {/* Left Column - Mission & Values */}
          <AboutMission />
          
          {/* Right Column - Features Overview */}
          <FeaturesOverview />
        </div>
        
        {/* Why Choose Us Section */}
        <WhyChooseUs />
      </TabsContent>
      
      {/* Features Tab Content */}
      <TabsContent value="features" className="animate-in fade-in-50 slide-in-from-bottom-3">
        <div className="mb-12">
          <div className="text-center mb-10">
            <h2 className="text-2xl sm:text-3xl font-bold inline-block bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent1-600">
              {language === 'en' ? 'Our Features' : '我们的功能'}
            </h2>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto mt-3">
              {language === 'en' 
                ? 'Discover all the ways we can help simplify your digital experience.'
                : '探索我们如何简化您的数字体验。'}
            </p>
            <div className="w-20 h-1 bg-gradient-to-r from-primary to-accent1-600 mx-auto mt-4"></div>
          </div>
          
          <FeaturesList />
          
        </div>
      </TabsContent>
    </Container>
  );
};

export default AboutContent;
