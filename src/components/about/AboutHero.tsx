
import React from 'react';
import { Container } from "@/components/ui/container";
import { Shield } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAppContext } from '@/context/AppContext';

interface AboutHeroProps {
  activeTab: string;
  setActiveTab: (value: string) => void;
}

const AboutHero = ({ activeTab, setActiveTab }: AboutHeroProps) => {
  const { t, language } = useAppContext();
  
  return (
    <div className="relative bg-gradient-to-r from-primary/10 to-accent1-500/10 py-16 overflow-hidden">
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <Container className="relative z-10 max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <div className="inline-block p-2 bg-primary/10 rounded-full mb-4">
            <Shield className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold tracking-tight mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent1-600">
            {t('aboutUs')}
          </h1>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
            {language === 'en' 
              ? 'An all-in-one platform offering tools to simplify your digital life while putting privacy and security first.'
              : '一站式平台，提供多种工具简化您的数字生活，同时将隐私和安全放在首位。'}
          </p>
        </div>
        
        {/* Navigation Tabs */}
        <div className="mt-10 sm:mt-12">
          <Tabs value={activeTab} className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 p-1 bg-muted/80 backdrop-blur-sm">
              <TabsTrigger 
                value="about"
                className="data-[state=active]:bg-background data-[state=active]:shadow-sm rounded-md transition-all"
              >
                {language === 'en' ? 'About Us' : '关于我们'}
              </TabsTrigger>
              <TabsTrigger 
                value="features"
                className="data-[state=active]:bg-background data-[state=active]:shadow-sm rounded-md transition-all"
              >
                {language === 'en' ? 'Features' : '功能介绍'}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </Container>
      
      {/* Decorative Elements */}
      <div className="absolute -top-24 -right-24 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-32 -left-32 w-80 h-80 bg-accent1-500/5 rounded-full blur-3xl"></div>
    </div>
  );
};

export default AboutHero;
