
import React from 'react';
import { Shield, Heart, Check, Code, GitFork, Mail } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useAppContext } from '@/context/AppContext';

const AboutMission = () => {
  const { t, language } = useAppContext();
  
  return (
    <div className="flex flex-col justify-center space-y-8">
      <div className="space-y-4 bg-card/50 border rounded-xl p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-full bg-primary/10">
            <Shield className="h-5 w-5 text-primary" />
          </div>
          <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">{t('ourMission')}</h2>
        </div>
        <p className="text-base sm:text-lg text-muted-foreground">
          {language === 'en' 
            ? 'To provide efficient, privacy-focused tools that simplify your online experience while respecting your data and privacy.'
            : '提供高效、注重隐私的工具，在尊重您的数据和隐私的同时简化您的在线体验。'}
        </p>
      </div>
      
      <div className="space-y-4 bg-card/50 border rounded-xl p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-full bg-primary/10">
            <Heart className="h-5 w-5 text-primary" />
          </div>
          <h3 className="text-xl font-bold">{t('ourValues')}</h3>
        </div>
        <ul className="space-y-3 text-muted-foreground">
          <li className="flex items-start gap-3 p-2 hover:bg-muted/50 rounded-lg transition-colors">
            <div className="mt-1 bg-primary/10 p-1 rounded-full">
              <Check className="h-4 w-4 text-primary" />
            </div>
            <span>{language === 'en' ? 'Privacy-first approach in everything we build' : '在我们构建的所有产品中采取隐私优先的方法'}</span>
          </li>
          <li className="flex items-start gap-3 p-2 hover:bg-muted/50 rounded-lg transition-colors">
            <div className="mt-1 bg-primary/10 p-1 rounded-full">
              <Check className="h-4 w-4 text-primary" />
            </div>
            <span>{language === 'en' ? 'Simplicity and ease of use' : '简单易用'}</span>
          </li>
          <li className="flex items-start gap-3 p-2 hover:bg-muted/50 rounded-lg transition-colors">
            <div className="mt-1 bg-primary/10 p-1 rounded-full">
              <Check className="h-4 w-4 text-primary" />
            </div>
            <span>{language === 'en' ? 'Transparency in our operations' : '运营透明'}</span>
          </li>
          <li className="flex items-start gap-3 p-2 hover:bg-muted/50 rounded-lg transition-colors">
            <div className="mt-1 bg-primary/10 p-1 rounded-full">
              <Check className="h-4 w-4 text-primary" />
            </div>
            <span>{language === 'en' ? 'Community-oriented development' : '面向社区的开发'}</span>
          </li>
        </ul>
      </div>
      
      <div className="flex flex-wrap gap-3">
        <Button asChild variant="outline" className="gap-2 rounded-full shadow-sm border border-primary/20 hover:bg-primary/5">
          <a href="https://github.com/chenqi92/url-stash-vault" target="_blank" rel="noopener noreferrer">
            <Code className="h-4 w-4" />
            {language === 'en' ? 'Source Code' : '源代码'}
          </a>
        </Button>
        <Button asChild variant="outline" className="gap-2 rounded-full shadow-sm border border-primary/20 hover:bg-primary/5">
          <a href="https://github.com/chenqi92/url-stash-vault/issues" target="_blank" rel="noopener noreferrer">
            <GitFork className="h-4 w-4" />
            {language === 'en' ? 'Contribute' : '参与贡献'}
          </a>
        </Button>
        <Button asChild variant="outline" className="gap-2 rounded-full shadow-sm border border-primary/20 hover:bg-primary/5">
          <a href="mailto:<EMAIL>">
            <Mail className="h-4 w-4" />
            {t('contactUs')}
          </a>
        </Button>
      </div>
    </div>
  );
};

export default AboutMission;
