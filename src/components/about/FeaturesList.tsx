
import React from 'react';
import { Unlink, Mail, Compass, TrendingUp, <PERSON>Tod<PERSON>, <PERSON>yNote, Check, ArrowRight } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useAppContext } from '@/context/AppContext';

const FeaturesList = () => {
  const { language } = useAppContext();
  
  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6 sm:gap-8 mb-12">
        {/* URL Shortening Feature Card */}
        <Card className="relative group overflow-hidden border bg-card/50 hover:bg-card/80 transition-colors">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent1-500/5 opacity-0 group-hover:opacity-100 transition-opacity"></div>
          <CardHeader className="pb-2">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-lg bg-primary/10 mr-3">
                <Unlink className="h-5 w-5 text-primary" />
              </div>
              <CardTitle>{language === 'en' ? 'URL Shortening' : 'URL短链'}</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              {language === 'en' 
                ? 'Create short, memorable links for your long URLs. Track clicks, locations, and devices for all your shortened links.' 
                : '为您的长URL创建简短、易记的链接。跟踪所有缩短链接的点击量、位置和设备信息。'}
            </p>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Custom short codes' : '自定义短码'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Click analytics' : '点击分析'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'QR code generation' : '二维码生成'}
              </li>
            </ul>
            <Button asChild variant="outline" size="sm" className="mt-2 border-primary/20 hover:bg-primary/5">
              <Link to="/" className="inline-flex items-center">
                {language === 'en' ? 'Try now' : '立即体验'} 
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
        
        {/* Temporary Email Feature Card */}
        <Card className="relative group overflow-hidden border bg-card/50 hover:bg-card/80 transition-colors">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent1-500/5 opacity-0 group-hover:opacity-100 transition-opacity"></div>
          <CardHeader className="pb-2">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-lg bg-primary/10 mr-3">
                <Mail className="h-5 w-5 text-primary" />
              </div>
              <CardTitle>{language === 'en' ? 'Temporary Email' : '临时邮箱'}</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              {language === 'en' 
                ? 'Create disposable email addresses to protect your privacy. Receive messages without revealing your personal email.' 
                : '创建一次性电子邮件地址以保护您的隐私。在不透露个人邮箱的情况下接收消息。'}
            </p>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Instant creation' : '即时创建'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Auto expiration' : '自动过期'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'No registration required' : '无需注册即可使用'}
              </li>
            </ul>
            <Button asChild variant="outline" size="sm" className="mt-2 border-primary/20 hover:bg-primary/5">
              <Link to="/dashboard" className="inline-flex items-center">
                {language === 'en' ? 'Try now' : '立即体验'} 
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
        
        {/* More feature cards (continuing from the original file) */}
        <Card className="relative group overflow-hidden border bg-card/50 hover:bg-card/80 transition-colors">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent1-500/5 opacity-0 group-hover:opacity-100 transition-opacity"></div>
          <CardHeader className="pb-2">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-lg bg-primary/10 mr-3">
                <Compass className="h-5 w-5 text-primary" />
              </div>
              <CardTitle>{language === 'en' ? 'Navigation Directory' : '导航站'}</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              {language === 'en' 
                ? 'Organize and share your favorite websites in a beautiful directory. Create personalized collections and share with others.' 
                : '在精美的目录中整理和分享您喜爱的网站。创建个性化的收藏并与他人分享。'}
            </p>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Categorized websites' : '分类网站'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Public & private collections' : '公共和私人收藏'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Submit new resources' : '提交新资源'}
              </li>
            </ul>
            <Button asChild variant="outline" size="sm" className="mt-2 border-primary/20 hover:bg-primary/5">
              <Link to="/navigation" className="inline-flex items-center">
                {language === 'en' ? 'Explore now' : '立即探索'} 
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
        
        {/* Today's Hot List Feature Card */}
        <Card className="relative group overflow-hidden border bg-card/50 hover:bg-card/80 transition-colors">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent1-500/5 opacity-0 group-hover:opacity-100 transition-opacity"></div>
          <CardHeader className="pb-2">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-lg bg-primary/10 mr-3">
                <TrendingUp className="h-5 w-5 text-primary" />
              </div>
              <CardTitle>{language === 'en' ? "Today's Hot List" : '今日热榜'}</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              {language === 'en' 
                ? 'Browse trending topics from various platforms all in one place. Stay updated with the latest hot topics.' 
                : '在一处浏览各大平台的热门话题。随时了解最新热点。'}
            </p>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Multiple platforms' : '多平台聚合'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Real-time updates' : '实时更新'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Categorized content' : '分类内容'}
              </li>
            </ul>
            <Button asChild variant="outline" size="sm" className="mt-2 border-primary/20 hover:bg-primary/5">
              <Link to="/hot-news" className="inline-flex items-center">
                {language === 'en' ? 'Check now' : '立即查看'} 
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
        
        {/* Todo List Feature Card */}
        <Card className="relative group overflow-hidden border bg-card/50 hover:bg-card/80 transition-colors">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent1-500/5 opacity-0 group-hover:opacity-100 transition-opacity"></div>
          <CardHeader className="pb-2">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-lg bg-primary/10 mr-3">
                <ListTodo className="h-5 w-5 text-primary" />
              </div>
              <CardTitle>{language === 'en' ? 'Todo List' : '待办事项'}</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              {language === 'en' 
                ? 'Organize your tasks with our intuitive todo list. Set priorities, deadlines, and track your progress.' 
                : '使用我们直观的待办事项列表整理您的任务。设置优先级、截止日期并跟踪您的进度。'}
            </p>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Task prioritization' : '任务优先级'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Due date reminders' : '到期日提醒'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Progress tracking' : '进度跟踪'}
              </li>
            </ul>
            <Button asChild variant="outline" size="sm" className="mt-2 border-primary/20 hover:bg-primary/5">
              <Link to="/memo-todo" className="inline-flex items-center">
                {language === 'en' ? 'Try now' : '立即体验'} 
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
        
        {/* Notes Feature Card */}
        <Card className="relative group overflow-hidden border bg-card/50 hover:bg-card/80 transition-colors">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent1-500/5 opacity-0 group-hover:opacity-100 transition-opacity"></div>
          <CardHeader className="pb-2">
            <div className="flex items-center mb-2">
              <div className="p-2 rounded-lg bg-primary/10 mr-3">
                <StickyNote className="h-5 w-5 text-primary" />
              </div>
              <CardTitle>{language === 'en' ? 'Notes' : '备忘录'}</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              {language === 'en' 
                ? 'Capture your ideas and important information with our simple note-taking tool. Access your notes anytime, anywhere.' 
                : '使用我们简单的笔记工具捕捉您的想法和重要信息。随时随地访问您的笔记。'}
            </p>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Quick capture' : '快速记录'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Organize by categories' : '按类别整理'}
              </li>
              <li className="flex items-center gap-2">
                <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <Check className="h-3 w-3 text-primary" />
                </div>
                {language === 'en' ? 'Search functionality' : '搜索功能'}
              </li>
            </ul>
            <Button asChild variant="outline" size="sm" className="mt-2 border-primary/20 hover:bg-primary/5">
              <Link to="/memo-todo" className="inline-flex items-center">
                {language === 'en' ? 'Try now' : '立即体验'} 
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
      
      <div className="flex justify-center mt-12">
        <Button asChild size="lg" className="bg-gradient-to-r from-primary to-accent1-600 hover:from-primary/90 hover:to-accent1-700 text-primary-foreground border-none shadow-md px-8">
          <Link to="/register" className="inline-flex items-center">
            {language === 'en' ? 'Get Started Now' : '立即开始'} 
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </Button>
      </div>
    </>
  );
};

export default FeaturesList;
