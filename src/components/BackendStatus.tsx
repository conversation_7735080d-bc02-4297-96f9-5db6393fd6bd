import React, { useState, useEffect } from 'react';
import { getCurrentBackendName, validateBackendConnection, isUsingSupabase } from '@/config/backend';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface BackendStatusProps {
  className?: string;
}

export const BackendStatus: React.FC<BackendStatusProps> = ({ className }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkConnection = async () => {
    setIsChecking(true);
    try {
      const connected = await validateBackendConnection();
      setIsConnected(connected);
      setLastChecked(new Date());
    } catch (error) {
      console.error('连接检查失败:', error);
      setIsConnected(false);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkConnection();
  }, []);

  const getStatusIcon = () => {
    if (isChecking) {
      return <RefreshCw className="h-4 w-4 animate-spin" />;
    }
    if (isConnected === null) {
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
    return isConnected ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusText = () => {
    if (isChecking) return '检查中...';
    if (isConnected === null) return '未知';
    return isConnected ? '已连接' : '连接失败';
  };

  const getStatusVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    if (isChecking || isConnected === null) return 'outline';
    return isConnected ? 'default' : 'destructive';
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex items-center gap-1">
        <span className="text-sm text-muted-foreground">后端:</span>
        <Badge variant={isUsingSupabase() ? 'secondary' : 'default'}>
          {getCurrentBackendName()}
        </Badge>
      </div>
      
      <div className="flex items-center gap-1">
        <span className="text-sm text-muted-foreground">状态:</span>
        <Badge variant={getStatusVariant()} className="flex items-center gap-1">
          {getStatusIcon()}
          {getStatusText()}
        </Badge>
      </div>

      <Button
        variant="ghost"
        size="sm"
        onClick={checkConnection}
        disabled={isChecking}
        className="h-6 px-2"
      >
        <RefreshCw className={`h-3 w-3 ${isChecking ? 'animate-spin' : ''}`} />
      </Button>

      {lastChecked && (
        <span className="text-xs text-muted-foreground">
          {lastChecked.toLocaleTimeString()}
        </span>
      )}
    </div>
  );
}; 