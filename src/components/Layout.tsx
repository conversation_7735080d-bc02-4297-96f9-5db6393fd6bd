
import React, { useState, useEffect } from 'react';
import Navbar from './Navbar';
import { useLocation, useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { isUsingSupabase } from '@/config/backend';
import NavigationPanel from './navigation/NavigationPanel';
import FloatingWidgets from './common/FloatingWidgets';
import { getBackendConfig } from '@/config/backend';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isNavOpen, setIsNavOpen] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    // 检查当前用户认证状态
    const checkAuthStatus = async () => {
      try {
        const token = localStorage.getItem('authToken') || localStorage.getItem('auth_token');
        if (!token) {
          setIsAuthenticated(false);
          setIsAdmin(false);
          setUser(null);
          setIsLoading(false);
          return;
        }

        const config = getBackendConfig();
        const baseURL = config.goBackend?.baseUrl;

        const response = await fetch(`${baseURL}/auth/me`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          // Token无效，清除本地存储
          localStorage.removeItem('authToken');
          localStorage.removeItem('auth_token');
          setIsAuthenticated(false);
          setIsAdmin(false);
          setUser(null);
          setIsLoading(false);
          return;
        }

        const userData = await response.json();
        setUser(userData);
        setIsAuthenticated(true);

        // 检查管理员权限
        const isUserAdmin = userData.is_super_admin ||
                           (userData.roles && userData.roles.includes('admin'));
        setIsAdmin(isUserAdmin || false);

      } catch (error) {
        console.error('Error checking auth status:', error);
        setIsAuthenticated(false);
        setIsAdmin(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();

    // 监听认证状态变化
    const handleAuthStateChange = () => {
      checkAuthStatus();
    };

    window.addEventListener('authStateChanged', handleAuthStateChange);
    window.addEventListener('storage', handleAuthStateChange);

    return () => {
      window.removeEventListener('authStateChanged', handleAuthStateChange);
      window.removeEventListener('storage', handleAuthStateChange);
    };
  }, []);

  const handleLogout = async () => {
    try {
      // 调用后端登出API
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken') || localStorage.getItem('auth_token');

      if (token) {
        try {
          await fetch(`${baseURL}/auth/logout`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
        } catch (error) {
          console.error('Backend logout failed:', error);
          // 即使后端登出失败，也要清除本地token
        }
      }

      // 清除本地存储的认证信息
      localStorage.removeItem('authToken');
      localStorage.removeItem('auth_token');

      setIsAuthenticated(false);
      setIsAdmin(false);
      setUser(null);

      // 触发认证状态变化事件
      window.dispatchEvent(new Event('authStateChanged'));

      toast({
        description: language === 'zh' ? "登出成功" : "You have been logged out successfully.",
      });

      // 获取当前路径
      const currentPath = window.location.pathname;

      // 如果当前在首页，刷新页面以确保首页内容更新
      if (currentPath === '/') {
        window.location.reload();
      } else {
        // 如果不在首页，则导航回首页
        navigate('/');
      }
    } catch (error) {
      console.error('Error during logout:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? "登出失败，请重试" : "Failed to log out. Please try again.",
      });
    }
  };

  const toggleNavPanel = () => {
    setIsNavOpen(!isNavOpen);
  };

  if (isLoading) {
    return <div className="flex h-screen items-center justify-center">Loading...</div>;
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar
        isAuthenticated={isAuthenticated}
        isAdmin={isAdmin}
        onLogout={handleLogout}
        user={user}
        onNavToggle={toggleNavPanel}
      />
      {isNavOpen && (
        <NavigationPanel
          isAuthenticated={isAuthenticated}
          isAdmin={isAdmin}
          onClose={() => setIsNavOpen(false)}
        />
      )}
      <main className="flex-1">
        {children}
      </main>
      <footer className="border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-16 md:flex-row">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} g2.al. All rights reserved.
          </p>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <a href="#" className="hover:text-foreground">Terms</a>
            <a href="#" className="hover:text-foreground">Privacy</a>
            <a href="#" className="hover:text-foreground">Contact</a>
          </div>
        </div>
      </footer>

      {/* Use the new floating widgets component, displayed even when not logged in */}
      <FloatingWidgets />
    </div>
  );
};

export default Layout;
