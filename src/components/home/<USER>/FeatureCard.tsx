
import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  link: string;
  linkText: string;
  isNew?: boolean;
  newLabel?: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  icon,
  title,
  description,
  link,
  linkText,
  isNew = false,
  newLabel = 'NEW'
}) => {
  return (
    <motion.div 
      className="p-4 bg-background rounded-lg border border-border hover:border-primary/50 hover:shadow-sm transition-all"
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <div className="flex items-center gap-3 mb-2">
        <div className="w-8 h-8 rounded-full bg-accent1-500/10 flex items-center justify-center">
          {icon}
        </div>
        {isNew && (
          <Badge variant="outline" className="h-5 text-[10px] ml-auto">
            {newLabel}
          </Badge>
        )}
        <h3 className="font-medium">
          {title}
        </h3>
      </div>
      <p className="text-sm text-muted-foreground">
        {description}
      </p>
      <Button asChild variant="link" size="sm" className="mt-2 h-6 pl-0">
        <Link to={link} className="group">
          {linkText}
          <ArrowRight className="ml-1 h-3 w-3 transition-transform group-hover:translate-x-1" />
        </Link>
      </Button>
    </motion.div>
  );
};

export default FeatureCard;
