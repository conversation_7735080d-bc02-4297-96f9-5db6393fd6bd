
import React from 'react';
import { Unlink, Mail, Compass, TrendingUp } from 'lucide-react';
import FeatureCard from './FeatureCard';

interface FeatureGridProps {
  language: string;
}

const FeatureGrid: React.FC<FeatureGridProps> = ({ language }) => {
  const features = [
    {
      icon: <Unlink className="h-4 w-4 text-accent1-500" />,
      title: language === 'en' ? 'URL Shortening' : 'URL短链',
      description: language === 'en' 
        ? 'Create short, memorable links for easy sharing and tracking.' 
        : '创建简短易记的链接，便于分享和跟踪。',
      link: '/',
      linkText: language === 'en' ? 'Try now' : '立即体验'
    },
    {
      icon: <Mail className="h-4 w-4 text-brand-500" />,
      title: language === 'en' ? 'Temporary Email' : '临时邮箱',
      description: language === 'en' 
        ? 'Protect your privacy with disposable email addresses.' 
        : '使用一次性邮箱地址保护您的隐私。',
      link: '/dashboard',
      linkText: language === 'en' ? 'Try now' : '立即体验'
    },
    {
      icon: <Compass className="h-4 w-4 text-accent1-500" />,
      title: language === 'en' ? 'Navigation Directory' : '导航站',
      description: language === 'en' 
        ? 'Discover and organize useful websites in categories.' 
        : '发现并按类别整理有用的网站资源。',
      link: '/navigation',
      linkText: language === 'en' ? 'Explore' : '去探索'
    },
    {
      icon: <TrendingUp className="h-4 w-4 text-brand-500" />,
      title: language === 'en' ? "Today's Hot List" : '今日热榜',
      description: language === 'en' 
        ? 'Track trending topics from multiple platforms in one place.' 
        : '在一处追踪各大平台热门话题。',
      link: '/hot-news',
      linkText: language === 'en' ? 'Check now' : '去看看',
      isNew: true,
      newLabel: language === 'en' ? 'NEW' : '新功能'
    }
  ];

  return (
    <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
      {features.map((feature, index) => (
        <FeatureCard 
          key={index}
          icon={feature.icon}
          title={feature.title}
          description={feature.description}
          link={feature.link}
          linkText={feature.linkText}
          isNew={feature.isNew}
          newLabel={feature.newLabel}
        />
      ))}
    </div>
  );
};

export default FeatureGrid;
