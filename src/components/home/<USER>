
import React from 'react';
import { motion } from 'framer-motion';

const HeroImage: React.FC = () => {
  return (
    <motion.div 
      className="relative"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ 
        duration: 0.7, 
        delay: 0.2,
        ease: [0.17, 0.67, 0.48, 0.99] 
      }}
    >
      {/* Decorative elements */}
      <div className="absolute -top-10 -left-10 w-40 h-40 bg-primary/5 rounded-full" />
      <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-accent1-400/5 rounded-full" />
      
      {/* Image with enhanced styling */}
      <motion.div
        className="relative z-10"
        whileHover={{ 
          boxShadow: "0 20px 40px rgba(0,0,0,0.1)", 
          transform: "translateY(-5px)" 
        }}
        transition={{ duration: 0.3 }}
      >
        <img
          src="/placeholder.svg"
          alt="Hero Image"
          className="mx-auto aspect-video overflow-hidden rounded-xl object-cover object-center sm:w-full shadow-lg border border-muted/20"
        />
        
        {/* Floating gradient accent */}
        <motion.div 
          className="absolute -top-5 -right-5 w-20 h-20 bg-gradient-to-br from-primary/30 to-accent1-400/30 rounded-full blur-xl"
          animate={{ 
            opacity: [0.5, 0.8, 0.5],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 6,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      </motion.div>
    </motion.div>
  );
};

export default HeroImage;
