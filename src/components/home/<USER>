import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAppContext } from '@/context/AppContext';
import { useBannerConfig, DEFAULT_BANNER_CONFIG, BannerTextLine } from '@/hooks/useBannerConfig';
import BannerContent from './banner/BannerContent';
import FeatureGrid from './features/FeatureGrid';

const HeroContent: React.FC = () => {
  const { t, language } = useAppContext();
  const { bannerConfig, loading } = useBannerConfig();

  // Helper function to generate title config from dynamic lines or fallback to traditional fields
  const generateTitleConfig = () => {
    // If we have dynamic lines, use them
    if (bannerConfig?.lines && bannerConfig.lines.length > 0) {
      return {
        first: bannerConfig.lines[0] || { 
          text: DEFAULT_BANNER_CONFIG.first_text, 
          gradient: DEFAULT_BANNER_CONFIG.first_gradient 
        },
        second: bannerConfig.lines[1] || { 
          text: DEFAULT_BANNER_CONFIG.second_text, 
          gradient: DEFAULT_BANNER_CONFIG.second_gradient 
        },
        third: bannerConfig.lines.length > 2 ? bannerConfig.lines[2] : undefined,
        fourth: bannerConfig.lines.length > 3 ? bannerConfig.lines[3] : undefined,
      };
    }
    
    // Otherwise use the traditional fields
    return {
      first: {
        text: bannerConfig?.first_text || DEFAULT_BANNER_CONFIG.first_text,
        gradient: bannerConfig?.first_gradient || DEFAULT_BANNER_CONFIG.first_gradient,
      },
      second: {
        text: bannerConfig?.second_text || DEFAULT_BANNER_CONFIG.second_text,
        gradient: bannerConfig?.second_gradient || DEFAULT_BANNER_CONFIG.second_gradient,
      },
      third: bannerConfig?.use_third_line && bannerConfig?.third_text ? {
        text: bannerConfig.third_text,
        gradient: bannerConfig.third_gradient || '',
      } : undefined,
      fourth: bannerConfig?.use_third_line && bannerConfig?.fourth_text ? {
        text: bannerConfig.fourth_text,
        gradient: bannerConfig.fourth_gradient || '',
      } : undefined,
    };
  };

  const getBannerLines = (): BannerTextLine[] => {
    if (bannerConfig?.lines && bannerConfig.lines.length > 0) {
      return bannerConfig.lines;
    }
    
    const lines: BannerTextLine[] = [];
    
    if (bannerConfig?.first_text) {
      lines.push({
        text: bannerConfig.first_text,
        gradient: bannerConfig.first_gradient || DEFAULT_BANNER_CONFIG.first_gradient
      });
    } else {
      lines.push({
        text: DEFAULT_BANNER_CONFIG.first_text,
        gradient: DEFAULT_BANNER_CONFIG.first_gradient
      });
    }
    
    if (bannerConfig?.second_text) {
      lines.push({
        text: bannerConfig.second_text,
        gradient: bannerConfig.second_gradient || DEFAULT_BANNER_CONFIG.second_gradient
      });
    } else {
      lines.push({
        text: DEFAULT_BANNER_CONFIG.second_text,
        gradient: DEFAULT_BANNER_CONFIG.second_gradient
      });
    }
    
    if (bannerConfig?.use_third_line && bannerConfig?.third_text) {
      lines.push({
        text: bannerConfig.third_text,
        gradient: bannerConfig.third_gradient || DEFAULT_BANNER_CONFIG.third_gradient || ''
      });
    }
    
    if (bannerConfig?.use_third_line && bannerConfig?.fourth_text) {
      lines.push({
        text: bannerConfig.fourth_text,
        gradient: bannerConfig.fourth_gradient || DEFAULT_BANNER_CONFIG.fourth_gradient || ''
      });
    }
    
    return lines;
  };

  return (
    <div className="flex flex-col justify-center space-y-4">
      <BannerContent
        displayStyle={bannerConfig?.display_style}
        spacing={bannerConfig?.spacing}
        height={bannerConfig?.height}
        lines={getBannerLines()}
        animationSpeed={bannerConfig?.animation_speed}
        title={generateTitleConfig()}
      />
      
      <p className="max-w-[600px] text-gray-500 md:text-xl dark:text-gray-400">
        {t('heroDescription')}
      </p>
      
      <div className="flex flex-col gap-4 sm:flex-row">
        <Button asChild variant="default" size="lg">
          <Link to="/dashboard" className="group">
            {t('getStarted')}
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Link>
        </Button>
        <Button asChild variant="outline" size="lg">
          <Link to="/features" className="group">
            {t('learnMore')}
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Link>
        </Button>
      </div>
      
      <FeatureGrid language={language} />
    </div>
  );
};

export default HeroContent;
