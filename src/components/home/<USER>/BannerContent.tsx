
import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { BannerTextLine, DEFAULT_BANNER_CONFIG, DisplayStyle } from '@/hooks/useBannerConfig';
import AnimatedGradientTitle from '@/components/common/AnimatedGradientTitle';

interface BannerContentProps {
  displayStyle?: DisplayStyle;
  spacing?: number;
  height?: number;
  lines: BannerTextLine[];
  animationSpeed?: number;
  title: {
    first: { text: string, gradient: string };
    second: { text: string, gradient: string };
    third?: { text: string, gradient: string };
    fourth?: { text: string, gradient: string };
  };
}

const BannerContent: React.FC<BannerContentProps> = ({
  displayStyle = 'default',
  spacing = DEFAULT_BANNER_CONFIG.spacing,
  height = DEFAULT_BANNER_CONFIG.height,
  lines,
  animationSpeed = DEFAULT_BANNER_CONFIG.animation_speed,
  title
}) => {
  const lineVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: [0.17, 0.67, 0.48, 0.99]
      }
    })
  };
  
  // Transform the title format for AnimatedGradientTitle
  const transformedTitle = {
    main: title.first.text,
    subtitle: title.second.text,
    first: title.first,
    second: title.second,
    third: title.third,
    fourth: title.fourth
  };
  
  switch (displayStyle) {
    case 'stacked':
      return (
        <div 
          className={cn(`space-y-${spacing}`, "text-center")}
          style={{ minHeight: `${height}px`, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}
        >
          <motion.div
            initial="hidden"
            animate="visible"
            className="space-y-3"
          >
            {lines.map((line, index) => (
              <motion.div 
                key={index}
                variants={lineVariants}
                custom={index}
                className={cn(
                  "text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent",
                  line.gradient
                )}
              >
                {line.text}
              </motion.div>
            ))}
          </motion.div>
          
          {/* Decorative element */}
          <motion.div 
            className="h-1 w-32 mx-auto mt-6 bg-gradient-to-r from-primary/80 via-accent1-400 to-brand-500 rounded-full"
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 128, opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6, ease: "easeOut" }}
          />
        </div>
      );
        
    case 'inline':
      return (
        <div 
          className="flex flex-wrap justify-center gap-x-4 gap-y-3 py-12"
          style={{ minHeight: `${height}px`, display: 'flex', alignItems: 'center' }}
        >
          <motion.div 
            initial="hidden"
            animate="visible"
            className="flex flex-wrap justify-center gap-x-4 gap-y-3"
          >
            {lines.map((line, index) => (
              <motion.div 
                key={index}
                variants={lineVariants}
                custom={index}
                className={cn(
                  "text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent",
                  line.gradient
                )}
              >
                {line.text}
              </motion.div>
            ))}
          </motion.div>
        </div>
      );
        
    case 'flow':
      return (
        <div 
          className="flex flex-wrap justify-center items-center py-12"
          style={{ minHeight: `${height}px` }}
        >
          <motion.div 
            className="flex flex-wrap justify-center items-center"
            initial="hidden"
            animate="visible"
          >
            {lines.map((line, index) => (
              <React.Fragment key={index}>
                <motion.div 
                  variants={lineVariants}
                  custom={index}
                  className={cn(
                    "text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent px-2",
                    line.gradient
                  )}
                >
                  {line.text}
                </motion.div>
                {index < lines.length - 1 && (
                  <motion.div 
                    variants={lineVariants}
                    custom={index + 0.5}
                    className="text-4xl md:text-5xl lg:text-6xl font-bold px-1 text-muted-foreground/60"
                  >
                    ·
                  </motion.div>
                )}
              </React.Fragment>
            ))}
          </motion.div>
          
          {/* Decorative underline */}
          <motion.div 
            className="h-1 w-32 mx-auto mt-8 bg-gradient-to-r from-primary/80 via-accent1-400 to-brand-500 rounded-full"
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 200, opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6, ease: "easeOut" }}
            style={{ position: 'absolute', bottom: '24px', left: '50%', transform: 'translateX(-50%)' }}
          />
        </div>
      );
      
    case 'default':
    default:
      return (
        <AnimatedGradientTitle 
          title={transformedTitle}
          className={cn(`space-y-${spacing}`)}
          animationSpeed={animationSpeed}
        />
      );
  }
};

export default BannerContent;
