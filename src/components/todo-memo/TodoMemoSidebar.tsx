
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppContext } from '@/context/AppContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  ListTodo, 
  StickyNote, 
  X, 
  PlusCircle, 
  Minimize2, 
  Maximize2, 
  ChevronLeft, 
  ChevronRight,
  Check,
  ArrowUp,
  ArrowRight,
  MoveIcon,
  Image
} from 'lucide-react';
import { useTodos } from '@/hooks/useTodos';
import { useMemos, Memo } from '@/hooks/useMemos';
import ReactMarkdown from 'react-markdown';
import { cn } from '@/lib/utils';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle
} from '@/components/ui/dialog';
import { Pagination } from '@/components/ui/pagination';
import { 
  PaginationContent, 
  PaginationItem, 
  PaginationNext, 
  PaginationPrevious 
} from '@/components/ui/pagination';

// 格式化文本内容
const formatContent = (content: string): string => {
  // 安全性处理
  let sanitizedContent = content
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
  
  // 保留换行符
  sanitizedContent = sanitizedContent.replace(/\n/g, '  \n');
  
  return sanitizedContent;
};

// 提取图片链接
const extractImageUrls = (content: string): string[] => {
  const imgRegex = /!\[.*?\]\((.*?)\)/g;
  const urls: string[] = [];
  let match;

  while ((match = imgRegex.exec(content)) !== null) {
    if (match[1]) urls.push(match[1]);
  }

  return urls;
};

// Markdown渲染组件
const MarkdownContent = ({ content }: { content: string }) => {
  return (
    <div className="text-sm prose prose-sm dark:prose-invert max-w-none break-words">
      <ReactMarkdown
        components={{
          a: ({ node, ...props }) => (
            <a {...props} target="_blank" rel="noopener noreferrer" className="text-primary underline" />
          ),
          code: ({ node, ...props }) => (
            <code {...props} className="px-1 py-0.5 bg-muted rounded text-xs" />
          ),
          img: ({ node, ...props }) => (
            <img 
              {...props} 
              alt={props.alt || '图片'} 
              className="max-w-full h-auto rounded-md my-1 hover:scale-105 transition-transform duration-200" 
              style={{ maxHeight: '120px' }} 
              loading="lazy"
            />
          ),
        }}
      >
        {formatContent(content)}
      </ReactMarkdown>
    </div>
  );
};

// 图片预览组件 - 改进版
const ImagePreview = ({ urls }: { urls: string[] }) => {
  if (urls.length === 0) return null;
  
  return (
    <div className="flex gap-1.5 mt-2 flex-wrap">
      {urls.slice(0, 3).map((url, index) => (
        <div 
          key={index} 
          className="relative h-16 w-16 rounded-lg overflow-hidden bg-muted/30 flex items-center justify-center group shadow-sm"
        >
          <img 
            src={url}
            alt={`图片${index+1}`}
            className="object-cover h-full w-full transition-all duration-300 group-hover:scale-110"
            loading="lazy"
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
        </div>
      ))}
      {urls.length > 3 && (
        <div className="h-16 w-16 rounded-lg overflow-hidden bg-muted/30 flex items-center justify-center text-muted-foreground hover:bg-muted/50 transition-colors shadow-sm">
          <span className="text-sm font-medium">+{urls.length - 3}</span>
        </div>
      )}
    </div>
  );
};

const TodoMemoSidebar: React.FC = () => {
  const { language } = useAppContext();
  const navigate = useNavigate();
  const [minimized, setMinimized] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const dragStartRef = useRef({ x: 0, y: 0, startX: 0, startY: 0 });
  const sidebarRef = useRef<HTMLDivElement>(null);
  
  // 获取待办和备忘录数据
  const { todos, loading: todosLoading } = useTodos();
  const { memos, loading: memosLoading } = useMemos();
  
  // 备忘录状态
  const [currentMemoIndex, setCurrentMemoIndex] = useState(0);
  const [selectedMemo, setSelectedMemo] = useState<Memo | null>(null);
  const [memoDialogOpen, setMemoDialogOpen] = useState(false);
  
  // 分页状态
  const [todosPage, setTodosPage] = useState(1);
  const [memosPage, setMemosPage] = useState(1);
  const itemsPerPage = 2;
  
  const isLoading = todosLoading || memosLoading;
  
  // 按照创建时间降序排序备忘录（最新的在前面）
  const sortedMemos = [...memos].sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );
  
  // 获取当前页的待办
  const activeTodos = todos.filter(todo => !todo.completed);
  const paginatedTodos = activeTodos.slice(
    (todosPage - 1) * 5, 
    todosPage * 5
  );
  
  // 获取当前页的备忘录
  const paginatedMemos = sortedMemos.slice(
    (memosPage - 1) * itemsPerPage, 
    memosPage * itemsPerPage
  );
  
  // 计算总页数
  const totalTodosPages = Math.ceil(activeTodos.length / 5);
  const totalMemosPages = Math.ceil(sortedMemos.length / itemsPerPage);
  
  // 切换最小化状态
  const toggleMinimized = () => {
    setMinimized(!minimized);
  };
  
  // 显示备忘录详情
  const showMemoDetail = (memo: Memo) => {
    setSelectedMemo(memo);
    setMemoDialogOpen(true);
  };
  
  // 切换到下一个备忘录
  const nextMemo = () => {
    if (currentMemoIndex < paginatedMemos.length - 1) {
      setCurrentMemoIndex(currentMemoIndex + 1);
    } else if (memosPage < totalMemosPages) {
      // 如果是当前页的最后一个，且还有下一页，则切换到下一页的第一个
      setMemosPage(memosPage + 1);
      setCurrentMemoIndex(0);
    }
  };
  
  // 切换到上一个备忘录
  const prevMemo = () => {
    if (currentMemoIndex > 0) {
      setCurrentMemoIndex(currentMemoIndex - 1);
    } else if (memosPage > 1) {
      // 如果是当前页的第一个，且还有上一页，则切换到上一页的最后一个
      setMemosPage(memosPage - 1);
      // 假设上一页是满的
      setCurrentMemoIndex(itemsPerPage - 1);
    }
  };
  
  // 跳转到最新备忘录
  const goToLatestMemo = () => {
    setMemosPage(1);
    setCurrentMemoIndex(0);
  };
  
  // 在弹窗中查看下一个备忘录
  const viewNextMemoInDialog = () => {
    const currentIndex = sortedMemos.findIndex(m => m.id === selectedMemo?.id);
    if (currentIndex !== -1 && currentIndex < sortedMemos.length - 1) {
      setSelectedMemo(sortedMemos[currentIndex + 1]);
    }
  };
  
  // 在弹窗中查看上一个备忘录
  const viewPrevMemoInDialog = () => {
    const currentIndex = sortedMemos.findIndex(m => m.id === selectedMemo?.id);
    if (currentIndex > 0) {
      setSelectedMemo(sortedMemos[currentIndex - 1]);
    }
  };
  
  // 初始化侧边栏位置 - 默认在右下角
  useEffect(() => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    const initialX = viewportWidth - 340;  // 距离右边40px
    const initialY = viewportHeight - 520; // 距离底部20px
    
    setPosition({ x: initialX, y: initialY });
  }, []);

  // 开始拖动处理 - 优化版
  const handleDragStart = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    // 阻止默认行为，防止文本选择和触摸事件引起的页面滚动
    e.preventDefault();
    
    // 确保DOM元素存在
    if (!sidebarRef.current) return;
    
    setIsDragging(true);
    
    // 获取鼠标/触摸起始位置
    let clientX, clientY;
    
    if ('touches' in e) {
      // 触摸事件
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      // 鼠标事件
      clientX = e.clientX;
      clientY = e.clientY;
    }
    
    // 记录起始位置
    dragStartRef.current = {
      x: clientX,
      y: clientY,
      startX: position.x,
      startY: position.y
    };
    
    // 添加视觉反馈
    document.body.style.cursor = 'grabbing';
    if (sidebarRef.current) {
      sidebarRef.current.style.transition = 'none';
    }
    
    // 添加全局样式，防止文本选择
    const style = document.createElement('style');
    style.id = 'drag-disable-select';
    style.textContent = '* { user-select: none !important; -webkit-user-select: none !important; }';
    document.head.appendChild(style);
  }, [position]);
  
  // 拖动过程处理 - 优化版
  const handleDrag = useCallback((e: MouseEvent | TouchEvent) => {
    if (!isDragging) return;
    
    // 获取当前指针位置
    let clientX, clientY;
    
    if ('touches' in e) {
      // 触摸事件
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      // 鼠标事件
      clientX = e.clientX;
      clientY = e.clientY;
    }
    
    // 计算移动距离
    const deltaX = clientX - dragStartRef.current.x;
    const deltaY = clientY - dragStartRef.current.y;
    
    // 计算新位置，使用保存的起始位置加上移动距离
    const newX = dragStartRef.current.startX + deltaX;
    const newY = dragStartRef.current.startY + deltaY;
    
    // 获取视口和侧边栏尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const sidebarWidth = sidebarRef.current?.offsetWidth || 300;
    const sidebarHeight = sidebarRef.current?.offsetHeight || 500;
    
    // 限制拖动边界，最多允许拖出边界1/4的宽度或高度
    const minX = -sidebarWidth * 0.75;
    const maxX = viewportWidth - sidebarWidth * 0.25;
    const minY = -sidebarHeight * 0.25;
    const maxY = viewportHeight - sidebarHeight * 0.25;
    
    // 应用边界限制
    const boundedX = Math.max(minX, Math.min(maxX, newX));
    const boundedY = Math.max(minY, Math.min(maxY, newY));
    
    // 使用 requestAnimationFrame 优化渲染性能
    requestAnimationFrame(() => {
      setPosition({ x: boundedX, y: boundedY });
    });
  }, [isDragging]);
  
  // 结束拖动处理 - 优化版
  const handleDragEnd = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // 恢复正常样式
    document.body.style.cursor = '';
    
    // 恢复平滑过渡
    if (sidebarRef.current) {
      sidebarRef.current.style.transition = 'box-shadow 0.3s ease, transform 0.1s ease';
    }
    
    // 移除全局样式
    const style = document.getElementById('drag-disable-select');
    if (style) {
      document.head.removeChild(style);
    }
    
    // 检查位置是否已经拖出边界太多，如果是则进行调整
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const sidebarWidth = sidebarRef.current?.offsetWidth || 300;
    const sidebarHeight = sidebarRef.current?.offsetHeight || 500;
    
    // 检查并调整位置
    let { x, y } = position;
    let needsAdjustment = false;
    
    // 如果侧边栏几乎完全拖出了视口，调整回来
    if (x < -sidebarWidth * 0.7) {
      x = -sidebarWidth * 0.7;
      needsAdjustment = true;
    } else if (x > viewportWidth - sidebarWidth * 0.3) {
      x = viewportWidth - sidebarWidth * 0.3;
      needsAdjustment = true;
    }
    
    if (y < -sidebarHeight * 0.2) {
      y = -sidebarHeight * 0.2;
      needsAdjustment = true;
    } else if (y > viewportHeight - sidebarHeight * 0.2) {
      y = viewportHeight - sidebarHeight * 0.2;
      needsAdjustment = true;
    }
    
    if (needsAdjustment) {
      // 使用平滑动画调整回适当的位置
      if (sidebarRef.current) {
        sidebarRef.current.style.transition = 'transform 0.3s ease';
      }
      setPosition({ x, y });
    }
  }, [isDragging, position]);
  
  // 当备忘录页面改变时，重置当前备忘录索引
  useEffect(() => {
    setCurrentMemoIndex(0);
  }, [memosPage]);
  
  // 添加和删除拖动事件监听器
  useEffect(() => {
    // 鼠标事件
    if (isDragging) {
      window.addEventListener('mousemove', handleDrag);
      window.addEventListener('mouseup', handleDragEnd);
      window.addEventListener('mouseleave', handleDragEnd);
      
      // 触摸事件
      window.addEventListener('touchmove', handleDrag, { passive: false });
      window.addEventListener('touchend', handleDragEnd);
      window.addEventListener('touchcancel', handleDragEnd);
    } else {
      window.removeEventListener('mousemove', handleDrag);
      window.removeEventListener('mouseup', handleDragEnd);
      window.removeEventListener('mouseleave', handleDragEnd);
      
      window.removeEventListener('touchmove', handleDrag);
      window.removeEventListener('touchend', handleDragEnd);
      window.removeEventListener('touchcancel', handleDragEnd);
    }
    
    return () => {
      window.removeEventListener('mousemove', handleDrag);
      window.removeEventListener('mouseup', handleDragEnd);
      window.removeEventListener('mouseleave', handleDragEnd);
      
      window.removeEventListener('touchmove', handleDrag);
      window.removeEventListener('touchend', handleDragEnd);
      window.removeEventListener('touchcancel', handleDragEnd);
    };
  }, [isDragging, handleDrag, handleDragEnd]);
  
  // 完成待办任务
  const completeTodo = async (todoId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    // TODO: 调用API完成待办，例如通过 useTodos 中的方法
    console.log('完成待办:', todoId);
  };

  // 处理触摸开始事件
  const handleTouchStart = (e: React.TouchEvent) => {
    handleDragStart(e);
  };

  return (
    <div 
      id="todo-memo-sidebar"
      ref={sidebarRef}
      className={cn(
        "fixed shadow-lg transition-shadow will-change-transform",
        isDragging ? "shadow-xl scale-105 z-50" : "z-40",
        minimized ? "w-12" : "w-[300px] md:w-[320px]"
      )}
      style={{ 
        transform: `translate(${position.x}px, ${position.y}px)`,
        touchAction: "none",
        willChange: isDragging ? 'transform' : 'auto',
      }}
    >
      <Card className="border shadow-lg h-full rounded-lg overflow-hidden bg-white/95 dark:bg-gray-900/95 backdrop-blur-md">
        <div 
          className={cn(
            "p-3 border-b flex items-center justify-between", 
            "bg-gradient-to-r from-primary/10 via-primary/5 to-transparent dark:from-primary/20",
            isDragging ? "cursor-grabbing" : "cursor-grab active:cursor-grabbing"
          )}
          onMouseDown={handleDragStart}
          onTouchStart={handleTouchStart}
        >
          {!minimized && (
            <div className="flex items-center gap-2">
              <MoveIcon className="h-4 w-4 text-primary" />
              <span className="font-medium flex items-center">
                {language === 'en' ? 'Todos & Memos' : '待办与备忘录'}
              </span>
            </div>
          )}
          <div className="flex items-center gap-1 ml-auto">
            <TooltipProvider>
              <Tooltip delayDuration={300}>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8 hover:bg-muted/80" 
                    onClick={toggleMinimized}
                  >
                    {minimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left">
                  {minimized ? (language === 'en' ? 'Expand' : '展开') : (language === 'en' ? 'Minimize' : '最小化')}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {!minimized && (
          <Tabs defaultValue="todos" className="w-full">
            <div className="px-3 pt-2 bg-muted/20">
              <TabsList className="w-full bg-muted/40 grid grid-cols-2">
                <TabsTrigger value="todos" className="flex items-center gap-1">
                  <ListTodo className="h-4 w-4" />
                  <span>{language === 'en' ? 'Todos' : '待办'}</span>
                  <Badge variant="secondary" className="ml-1 bg-primary/10">{activeTodos.length}</Badge>
                </TabsTrigger>
                <TabsTrigger value="memos" className="flex items-center gap-1">
                  <StickyNote className="h-4 w-4" />
                  <span>{language === 'en' ? 'Memos' : '备忘录'}</span>
                  <Badge variant="secondary" className="ml-1 bg-primary/10">{sortedMemos.length}</Badge>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="todos" className="mt-0 focus-visible:outline-none focus-visible:ring-0">
              <ScrollArea className="h-[300px] md:h-[450px]">
                <div className="p-3 space-y-2">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
                    </div>
                  ) : activeTodos.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                      <ListTodo className="h-10 w-10 mb-2 opacity-20" />
                      <p>{language === 'en' ? 'No active todos' : '没有进行中的待办'}</p>
                      <Button 
                        variant="link" 
                        size="sm" 
                        onClick={() => navigate('/memo-todo')}
                        className="mt-2"
                      >
                        <PlusCircle className="h-4 w-4 mr-1" />
                        {language === 'en' ? 'Add Todo' : '添加待办'}
                      </Button>
                    </div>
                  ) : (
                    <>
                      {paginatedTodos.map(todo => (
                        <div 
                          key={todo.id} 
                          className="flex items-start gap-2 p-2 rounded-md hover:bg-muted/50 transition-colors cursor-pointer group border border-transparent hover:border-muted/80"
                          onClick={() => navigate('/memo-todo')}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-5 w-5 rounded-full border border-primary/30 flex-shrink-0 mt-0.5 p-0 group-hover:bg-primary/10"
                            onClick={(e) => completeTodo(todo.id, e)}
                          >
                            <Check className="h-3 w-3 opacity-0 group-hover:opacity-100" />
                          </Button>
                          <div className="overflow-hidden">
                            <p className="text-sm font-medium line-clamp-2">{todo.title}</p>
                            {todo.due_date && (
                              <p className="text-xs text-muted-foreground mt-1 flex items-center">
                                <span className="inline-block w-2 h-2 rounded-full bg-primary/70 mr-1.5"></span>
                                {new Date(todo.due_date).toLocaleDateString(language === 'en' ? 'en-US' : 'zh-CN')}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                      
                      {/* 待办分页 */}
                      {totalTodosPages > 1 && (
                        <div className="pt-2 pb-1">
                          <Pagination>
                            <PaginationContent>
                              <PaginationItem>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  onClick={() => setTodosPage(prev => Math.max(prev - 1, 1))}
                                  disabled={todosPage === 1}
                                  className="h-8 w-8"
                                >
                                  <ChevronLeft className="h-4 w-4" />
                                </Button>
                              </PaginationItem>
                              <PaginationItem className="flex items-center">
                                <span className="text-xs">
                                  {language === 'en' 
                                    ? `Page ${todosPage} of ${totalTodosPages}` 
                                    : `第 ${todosPage}/${totalTodosPages} 页`}
                                </span>
                              </PaginationItem>
                              <PaginationItem>
                                <Button 
                                  variant="ghost" 
                                  size="icon"
                                  onClick={() => setTodosPage(prev => Math.min(prev + 1, totalTodosPages))}
                                  disabled={todosPage === totalTodosPages}
                                  className="h-8 w-8"
                                >
                                  <ChevronRight className="h-4 w-4" />
                                </Button>
                              </PaginationItem>
                            </PaginationContent>
                          </Pagination>
                        </div>
                      )}
                      
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full mt-2 flex items-center justify-center bg-muted/20 hover:bg-muted/30" 
                        onClick={() => navigate('/memo-todo')}
                      >
                        {language === 'en' ? 'View All' : '查看全部'}
                        <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="memos" className="mt-0 focus-visible:outline-none focus-visible:ring-0">
              <ScrollArea className="h-[300px] md:h-[450px]">
                <div className="p-3 space-y-3">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
                    </div>
                  ) : sortedMemos.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                      <StickyNote className="h-10 w-10 mb-2 opacity-20" />
                      <p>{language === 'en' ? 'No memos yet' : '暂无备忘录'}</p>
                      <Button 
                        variant="link" 
                        size="sm" 
                        onClick={() => navigate('/memo-todo')}
                        className="mt-2"
                      >
                        <PlusCircle className="h-4 w-4 mr-1" />
                        {language === 'en' ? 'Add Memo' : '添加备忘录'}
                      </Button>
                    </div>
                  ) : (
                    <>
                      {/* 跳转到最新备忘录的按钮 */}
                      {sortedMemos.length > 0 && (memosPage > 1 || currentMemoIndex > 0) && (
                        <div className="flex justify-end mb-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-7 px-2 text-xs bg-primary/5 hover:bg-primary/10 border-primary/20"
                                  onClick={goToLatestMemo}
                                >
                                  <ArrowUp className="h-3 w-3 mr-1" />
                                  {language === 'en' ? 'Latest' : '最新'}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                {language === 'en' ? 'Go to latest memo' : '跳转至最新备忘录'}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      )}
                      
                      {/* 备忘录列表 - 同时显示两个 */}
                      <div className="space-y-4">
                        {paginatedMemos.map((memo, index) => {
                          const imageUrls = extractImageUrls(memo.content);
                          const hasImages = imageUrls.length > 0;
                          
                          return (
                            <div 
                              key={memo.id}
                              className="p-3 rounded-md hover:bg-muted/50 transition-all duration-200 cursor-pointer border border-border/40 hover:shadow-sm bg-card/30 hover:bg-card/60 group"
                              onClick={() => showMemoDetail(memo)}
                            >
                              <div className={cn(
                                "overflow-hidden", 
                                hasImages ? "max-h-[100px]" : "max-h-[150px]"
                              )}>
                                <MarkdownContent content={memo.content} />
                              </div>
                              
                              {/* 显示提取出的图片 */}
                              {hasImages && <ImagePreview urls={imageUrls} />}
                              
                              <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                                <span>
                                  {new Date(memo.created_at).toLocaleDateString(
                                    language === 'en' ? 'en-US' : 'zh-CN', 
                                    { year: 'numeric', month: '2-digit', day: '2-digit' }
                                  )}
                                </span>
                                {hasImages && (
                                  <span className="flex items-center gap-1 bg-muted/40 px-1.5 py-0.5 rounded-full opacity-60 group-hover:opacity-100 transition-opacity">
                                    <Image className="h-3 w-3" />
                                    {imageUrls.length}
                                  </span>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                                             
                      {/* 备忘录分页 */}
                      {totalMemosPages > 1 && (
                        <div className="pt-2 pb-1">
                          <Pagination>
                            <PaginationContent>
                              <PaginationItem>
                                <Button 
                                  variant="ghost" 
                                  size="icon"
                                  onClick={() => setMemosPage(prev => Math.max(prev - 1, 1))}
                                  disabled={memosPage === 1}
                                  className="h-8 w-8"
                                >
                                  <ChevronLeft className="h-4 w-4" />
                                </Button>
                              </PaginationItem>
                              <PaginationItem className="flex items-center">
                                <span className="text-xs">
                                  {language === 'en' 
                                    ? `Page ${memosPage} of ${totalMemosPages}` 
                                    : `第 ${memosPage}/${totalMemosPages} 页`}
                                </span>
                              </PaginationItem>
                              <PaginationItem>
                                <Button 
                                  variant="ghost" 
                                  size="icon"
                                  onClick={() => setMemosPage(prev => Math.min(prev + 1, totalMemosPages))}
                                  disabled={memosPage === totalMemosPages}
                                  className="h-8 w-8"
                                >
                                  <ChevronRight className="h-4 w-4" />
                                </Button>
                              </PaginationItem>
                            </PaginationContent>
                          </Pagination>
                        </div>
                      )}
                      
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full mt-2 flex items-center justify-center bg-muted/20 hover:bg-muted/30" 
                        onClick={() => navigate('/memo-todo')}
                      >
                        {language === 'en' ? 'View All' : '查看全部'}
                        <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        )}
      </Card>
      
      {/* 备忘录详情对话框 */}
      <Dialog open={memoDialogOpen} onOpenChange={setMemoDialogOpen}>
        <DialogContent className="sm:max-w-xl max-h-[80vh] overflow-auto">
          <DialogHeader className="space-y-1">
            <DialogTitle className="flex items-center justify-between">
              <span>{language === 'en' ? 'Memo Details' : '备忘录详情'}</span>
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8 rounded-full"
                        onClick={viewPrevMemoInDialog}
                        disabled={sortedMemos.findIndex(m => m.id === selectedMemo?.id) <= 0}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      {language === 'en' ? 'Previous memo' : '上一条备忘录'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8 rounded-full"
                        onClick={viewNextMemoInDialog}
                        disabled={sortedMemos.findIndex(m => m.id === selectedMemo?.id) >= sortedMemos.length - 1}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      {language === 'en' ? 'Next memo' : '下一条备忘录'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <Button
                  variant="ghost" 
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={() => setMemoDialogOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </DialogTitle>
            {selectedMemo && (
              <div className="text-sm text-muted-foreground">
                {language === 'en' ? 'Created on: ' : '创建时间：'} 
                {new Date(selectedMemo.created_at).toLocaleString(language === 'en' ? 'en-US' : 'zh-CN')}
              </div>
            )}
          </DialogHeader>
          {selectedMemo && (
            <div className="space-y-4 pt-2">
              <div className="border rounded-md p-4 bg-muted/20">
                <MarkdownContent content={selectedMemo.content} />
              </div>
              
              {/* 图片预览部分 - 增强版 */}
              {extractImageUrls(selectedMemo.content).length > 0 && (
                <div className="border rounded-md p-4 bg-muted/10">
                  <h4 className="text-sm font-medium mb-3">
                    {language === 'en' ? 'Images' : '图片'}
                  </h4>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                    {extractImageUrls(selectedMemo.content).map((url, index) => (
                      <a 
                        key={index}
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block rounded-md overflow-hidden aspect-square bg-muted/30 hover:bg-muted/50 transition-colors group"
                      >
                        <img 
                          src={url} 
                          alt={`图片${index+1}`}
                          className="w-full h-full object-cover transition-all duration-300 group-hover:scale-105"
                          loading="lazy"
                        />
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TodoMemoSidebar;
