import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ScrollAwareLayoutProps {
  navbar: ReactNode;
  header: ReactNode;
  children: ReactNode;
  className?: string;
}

const ScrollAwareLayout: React.FC<ScrollAwareLayoutProps> = ({
  navbar,
  header,
  children,
  className
}) => {
  const [navbarVisible, setNavbarVisible] = useState(true);
  const [isAtTop, setIsAtTop] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [headerHeight, setHeaderHeight] = useState(0);
  
  const headerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // 获取标题栏高度
  useEffect(() => {
    if (headerRef.current) {
      const height = headerRef.current.offsetHeight;
      setHeaderHeight(height);
    }
  }, [header]);

  // 滚动监听
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const scrollingDown = currentScrollY > lastScrollY;
      const scrollingUp = currentScrollY < lastScrollY;
      
      // 判断是否在顶部
      const atTop = currentScrollY <= 10;
      setIsAtTop(atTop);
      
      // 当在顶部时，总是显示导航栏
      if (atTop) {
        setNavbarVisible(true);
      } else {
        // 不在顶部时，根据滚动方向控制导航栏显示
        if (scrollingDown && currentScrollY > 100) {
          // 向下滚动且超过100px时隐藏导航栏
          setNavbarVisible(false);
        } else if (scrollingUp) {
          // 向上滚动时显示导航栏
          setNavbarVisible(true);
        }
      }
      
      setLastScrollY(currentScrollY);
    };

    // 节流处理
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
    };
  }, [lastScrollY]);

  return (
    <div className={cn('min-h-screen flex flex-col', className)}>
      {/* 导航栏 */}
      <div
        className={cn(
          'fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ease-in-out',
          navbarVisible ? 'translate-y-0' : '-translate-y-full'
        )}
      >
        {navbar}
      </div>

      {/* 标题栏 */}
      <div
        ref={headerRef}
        className={cn(
          'transition-all duration-300 ease-in-out z-40',
          !isAtTop && 'fixed top-0 left-0 right-0 shadow-lg',
          !isAtTop && navbarVisible && 'top-16', // 当导航栏可见时，标题栏下移
          !isAtTop && !navbarVisible && 'top-0'   // 当导航栏隐藏时，标题栏置顶
        )}
        style={{
          marginTop: isAtTop ? '64px' : '0' // 在顶部时为导航栏留出空间
        }}
      >
        {header}
      </div>

      {/* 内容区域 */}
      <div
        ref={contentRef}
        className={cn(
          'flex-1 transition-all duration-300 ease-in-out',
          !isAtTop && 'overflow-auto'
        )}
        style={{
          marginTop: isAtTop ? '0' : `${headerHeight}px`, // 为固定的标题栏留出空间
          paddingTop: isAtTop ? '0' : '1rem'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default ScrollAwareLayout; 