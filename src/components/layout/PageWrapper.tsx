import React, { ReactNode, useEffect, useState, useRef } from 'react';
import { cn } from '@/lib/utils';

interface PageWrapperProps {
  children: ReactNode;
  headerContent?: ReactNode;
  enableSmartScroll?: boolean;
  className?: string;
}

const PageWrapper: React.FC<PageWrapperProps> = ({
  children,
  headerContent,
  enableSmartScroll = true,
  className
}) => {
  const [headerSticky, setHeaderSticky] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const headerRef = useRef<HTMLDivElement>(null);
  const [headerHeight, setHeaderHeight] = useState(0);

  // 获取标题栏高度
  useEffect(() => {
    if (headerRef.current) {
      setHeaderHeight(headerRef.current.offsetHeight);
    }
  }, [headerContent]);

  // 智能滚动逻辑
  useEffect(() => {
    if (!enableSmartScroll) return;

    const header = headerRef.current;
    const navbar = document.querySelector('[data-navbar]') as HTMLElement;

    if (!navbar) return;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const headerHeightValue = header?.offsetHeight || 0;

      // 标题栏固定逻辑 (当滚动超过导航栏高度时，标题栏固定)
      // 假设导航栏可见时高度约为60px，可以根据实际导航栏高度调整
      const navbarHeight = navbar.style.transform === 'translateY(0px)' ? navbar.offsetHeight : 0;
      const stickyThreshold = Math.max(5, navbarHeight); //确保至少滚动一点再固定，或者按导航栏高度
      const shouldSticky = currentScrollY > stickyThreshold;
      
      if (headerSticky !== shouldSticky) {
        setHeaderSticky(shouldSticky);
      }

      // 导航栏显示/隐藏逻辑
      const scrollingDown = currentScrollY > lastScrollY && Math.abs(currentScrollY - lastScrollY) > 5; //增加阈值避免抖动
      const scrollingUp = currentScrollY < lastScrollY && Math.abs(currentScrollY - lastScrollY) > 5;

      if (currentScrollY <= 10) { // 在页面最顶部，导航栏总是可见
        navbar.style.transform = 'translateY(0px)';
        navbar.style.zIndex = '50'; // 确保在最前面
      } else if (shouldSticky) { // 标题栏已经固定/准备固定
        if (scrollingDown) {
          navbar.style.transform = 'translateY(-100%)'; // 向下滚动，隐藏导航栏
          navbar.style.zIndex = '30'; // 隐藏时降低 z-index
        } else if (scrollingUp) {
          navbar.style.transform = 'translateY(0px)'; // 向上滚动，显示导航栏
          navbar.style.zIndex = '50'; // 显示时确保在最前面，覆盖标题栏
        }
      } else { // 标题栏未固定 (在导航栏下方滚动时)
        navbar.style.transform = 'translateY(0px)';
        navbar.style.zIndex = '50';
      }
      
      setLastScrollY(currentScrollY);
    };

    // 节流处理
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
      // 清理时重置导航栏
      const navbar = document.querySelector('[data-navbar]') as HTMLElement;
      if (navbar) {
        navbar.style.transform = 'translateY(0)';
      }
    };
  }, [lastScrollY, enableSmartScroll]);

  return (
    <div className={cn('min-h-screen', className)}>
      {/* 标题栏 */}
      {headerContent && (
        <div
          ref={headerRef}
          className={cn(
            'transition-transform duration-300 ease-in-out w-full',
            headerSticky && 'fixed top-0 left-0 right-0 shadow-lg',
            headerSticky ? 'z-40' : 'z-30'
          )}
        >
          {headerContent}
        </div>
      )}

      {/* 内容区域 */}
      <div
        className="relative z-10"
        style={{
          paddingTop: headerSticky && headerRef.current ? `${headerRef.current.offsetHeight}px` : '0',
          marginTop: headerContent && !headerSticky && headerRef.current ? `${headerRef.current.offsetHeight}px` : '0',
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default PageWrapper; 