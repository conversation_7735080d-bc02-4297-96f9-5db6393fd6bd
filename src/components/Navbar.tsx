import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Sun,
  Moon,
  Globe,
  LogIn,
  LogOut,
  User as UserIcon,
  Menu,
  Compass,
  TrendingUp,
  ExternalLink,
  ListTodo,
  Wrench,
  Info,
  ArrowRight,
  Home,
  Settings,
  UserCog,
  Shield
} from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User as AuthUser, logout } from "@/services/authService";
import HotNewsPanel from './navigation/hot-news/HotNewsPanel';
import { AuthModal } from '@/components/auth/AuthModal';

interface NavbarProps {
  onNavToggle?: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ onNavToggle }) => {
  const { theme, toggleTheme, language, toggleLanguage, t, user, isAuthReady, fetchUser } = useAppContext();
  const navigate = useNavigate();
  const location = useLocation();
  const [isHotNewsPanelOpen, setIsHotNewsPanelOpen] = useState(false);
  const [activeAboutTab, setActiveAboutTab] = useState<string>('about');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<"login" | "register" | "forgot">("login");

  const isAuthenticated = isAuthReady && user !== null;
  const isAdmin = isAuthenticated && user?.is_super_admin === true;

  const handleLogout = async () => {
    try {
      await logout();
      await fetchUser();

      // 触发自定义事件确保状态更新
      window.dispatchEvent(new Event('authStateChanged'));

      toast({
        description: language === 'zh' ? "登出成功" : "You have been logged out successfully.",
      });

      navigate('/');
    } catch (error) {
      console.error("Logout failed:", error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? "登出失败，请重试" : "Failed to log out. Please try again.",
      });
    }
  };

  const getInitials = () => {
    if (!user) return '?';
    const username = user.username;
    if (username) {
      return username.split(' ').map((n: string) => n[0]).join('').toUpperCase();
    }
    return user.email ? user.email[0].toUpperCase() : '?';
  };

  const getDisplayName = () => {
    if (!user) return '';
    return user.username || user.email || '';
  };

  const toggleHotNewsPanel = () => {
    setIsHotNewsPanelOpen(!isHotNewsPanelOpen);
  };

  const openAuthModal = (mode: "login" | "register" | "forgot" = "login") => {
    setAuthModalMode(mode);
    setIsAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
  };

  useEffect(() => {
    if (location.pathname === '/about') {
      const params = new URLSearchParams(location.search);
      const tabParam = params.get('tab');
      if (tabParam === 'features') {
        setActiveAboutTab('features');
      } else {
        setActiveAboutTab('about');
      }
    }
  }, [location]);

  const handleAboutNavigation = (tab: string) => {
    setActiveAboutTab(tab);

    if (location.pathname === '/about') {
      const event = new CustomEvent('switchAboutTab', {
        detail: { tab }
      });
      window.dispatchEvent(event);
    } else {
      navigate(`/about${tab === 'features' ? '?tab=features' : ''}`);
    }
  };

  return (
    <header
      data-navbar
      className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-transform duration-300 ease-in-out"
    >
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <Sheet>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="icon" className="mr-2">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="pr-0">
              <nav className="flex flex-col gap-4 pt-4">
                <Link to="/" className="text-lg font-medium hover:text-primary">
                  <Home className="mr-2 h-5 w-5 inline-block" />
                  {t('home')}
                </Link>
                <Link to="/navigation" className="text-lg font-medium hover:text-primary">
                  <Compass className="mr-2 h-5 w-5 inline-block" />
                  {language === 'en' ? 'Navigation' : '导航'}
                </Link>

                <Link to="/hot-news" className="text-lg font-medium hover:text-primary">
                  <TrendingUp className="mr-2 h-5 w-5 inline-block" />
                  {language === 'en' ? "Today's Hot List" : '今日热榜'}
                </Link>

                <Link to="/online-tools" className="text-lg font-medium hover:text-primary">
                  <Wrench className="mr-2 h-5 w-5 inline-block" />
                  {language === 'en' ? "Online Tools" : '在线工具'}
                </Link>

                <div className="border-t my-2 pt-2">
                  <h3 className="text-sm text-muted-foreground mb-2 px-1">
                    {language === 'en' ? 'About Us' : '关于我们'}
                  </h3>
                  <Button
                    variant={activeAboutTab === 'about' ? "secondary" : "ghost"}
                    className="text-lg font-medium hover:text-primary flex items-center justify-start w-full px-3 h-auto"
                    onClick={() => handleAboutNavigation('about')}
                  >
                    <Info className="mr-2 h-5 w-5" />
                    <span className="font-medium">
                      {language === 'en' ? 'About Us' : '关于我们'}
                    </span>
                  </Button>
                  <Button
                    variant={activeAboutTab === 'features' ? "secondary" : "ghost"}
                    className="text-lg font-medium hover:text-primary flex items-center justify-start w-full px-3 h-auto"
                    onClick={() => handleAboutNavigation('features')}
                  >
                    <ArrowRight className="mr-2 h-5 w-5" />
                    <span className="font-medium">
                      {language === 'en' ? 'Features' : '功能介绍'}
                    </span>
                  </Button>
                </div>

                {isAuthenticated && (
                  <Link to="/memo-todo" className="text-lg font-medium hover:text-primary">
                    <ListTodo className="mr-2 h-5 w-5 inline-block" />
                    {language === 'en' ? 'Memo & Todo' : '备忘录与待办'}
                  </Link>
                )}
              </nav>
            </SheetContent>
          </Sheet>

          <Link to="/" className="flex items-center space-x-2">
            <img
              src="https://pic.tuytuy.com/logo/g2al.png"
              alt="g2.al logo"
              className="h-12 w-auto"
            />
          </Link>

          <nav className="hidden md:flex md:items-center md:space-x-6 md:ml-6">
            <Link to="/" className="text-sm font-medium transition-colors hover:text-primary flex items-center">
              <Home className="mr-2 h-4 w-4" />
              <span>{t('home')}</span>
            </Link>
            <Link to="/navigation" className="text-sm font-medium transition-colors hover:text-primary flex items-center">
              <Compass className="mr-2 h-4 w-4" />
              <span>{language === 'en' ? 'Navigation' : '导航'}</span>
            </Link>

            {isAuthenticated && (
              <Link to="/memo-todo" className="text-sm font-medium transition-colors hover:text-primary flex items-center">
                <ListTodo className="mr-2 h-4 w-4" />
                <span>{language === 'en' ? 'Memo & Todo' : '备忘录与待办'}</span>
              </Link>
            )}

            <Link to="/hot-news" className="text-sm font-medium transition-colors hover:text-primary flex items-center">
              <TrendingUp className="mr-2 h-4 w-4" />
              <span>{language === 'en' ? "Today's Hot List" : '今日热榜'}</span>
            </Link>

            <Link to="/online-tools" className="text-sm font-medium transition-colors hover:text-primary flex items-center">
              <Wrench className="mr-2 h-4 w-4" />
              <span>{language === 'en' ? "Online Tools" : '在线工具'}</span>
            </Link>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="flex items-center gap-2 px-2 -ml-2 h-9">
                  <Info className="h-4 w-4" />
                  <span>{language === 'en' ? 'About' : '关于'}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onSelect={() => handleAboutNavigation('about')}
                  className={activeAboutTab === 'about' && location.pathname === '/about' ? "bg-muted" : ""}
                >
                  <div className="flex items-center w-full cursor-pointer">
                    <Info className={`h-4 w-4 mr-2 ${activeAboutTab === 'about' && location.pathname === '/about' ? "text-primary" : ""}`} />
                    <span>{language === 'en' ? 'About Us' : '关于我们'}</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onSelect={() => handleAboutNavigation('features')}
                  className={activeAboutTab === 'features' && location.pathname === '/about' ? "bg-muted" : ""}
                >
                  <div className="flex items-center w-full cursor-pointer">
                    <ArrowRight className={`h-4 w-4 mr-2 ${activeAboutTab === 'features' && location.pathname === '/about' ? "text-primary" : ""}`} />
                    <span>{language === 'en' ? 'Features' : '功能介绍'}</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </nav>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            title={theme === 'dark' ? t('lightMode') : t('darkMode')}
          >
            {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={toggleLanguage}
            title={language === 'en' ? t('toggleLanguage') : t('toggleLanguage')}
          >
            <Globe className="h-5 w-5" />
          </Button>

          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                  <Avatar className="h-9 w-9">
                    <AvatarImage src={user.avatar_url || ''} alt={getDisplayName()} />
                    <AvatarFallback>{getInitials()}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuItem onClick={() => navigate('/profile')}>
                  <UserCog className="mr-2 h-4 w-4" />
                  <span>{t('profile')}</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate('/dashboard')}>
                  <UserIcon className="mr-2 h-4 w-4" />
                  <span>{t('dashboard')}</span>
                </DropdownMenuItem>
                {isAdmin && (
                  <DropdownMenuItem onClick={() => navigate('/admin')}>
                    <Shield className="mr-2 h-4 w-4" />
                    <span>{t('admin')}</span>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>{t('logout')}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center space-x-2">
              <Button onClick={() => openAuthModal("login")} variant="ghost" size="sm">
                <LogIn className="mr-2 h-5 w-5" />
                {t('login')}
              </Button>
              <Button onClick={() => openAuthModal("register")} variant="outline" size="sm">
                {language === "en" ? "Register" : "注册"}
              </Button>
            </div>
          )}
        </div>
      </div>

      {isHotNewsPanelOpen && (
        <div className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm" onClick={toggleHotNewsPanel}></div>
      )}
      <HotNewsPanel isOpen={isHotNewsPanelOpen} onClose={toggleHotNewsPanel} />

      {/* 认证模态框 */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        defaultMode={authModalMode}
      />
    </header>
  );
};

export default Navbar;
