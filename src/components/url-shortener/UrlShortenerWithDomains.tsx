import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link, Globe, ShieldCheck, QrCode, ExternalLink, Link2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useUrlShortener } from '@/hooks/url-shortener/useUrlShortener';
import UrlForm from '@/pages/index/url-shortener/UrlForm';
import UrlResult from '@/pages/index/url-shortener/UrlResult';
import DomainForm from '@/pages/index/domain-whitelist/DomainForm';
import LoginPrompt from '@/pages/index/domain-whitelist/LoginPrompt';
import DomainSearch from '@/pages/index/domain-whitelist/DomainSearch';
import EmptyDomainState from '@/pages/index/domain-whitelist/EmptyDomainState';
import DomainsGrid from '@/pages/index/domain-whitelist/DomainsGrid';
import DomainsTable from '@/pages/index/domain-whitelist/DomainsTable';
import DomainFooter from '@/pages/index/domain-whitelist/DomainFooter';
import { useEffect } from 'react';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';

interface UrlShortenerWithDomainsProps {
  approvedDomains: string[];
}

const UrlShortenerWithDomains: React.FC<UrlShortenerWithDomainsProps> = ({ approvedDomains }) => {
  const { language } = useAppContext();
  const [activeTab, setActiveTab] = useState('shorten');

  // URL Shortener state
  const {
    url,
    setUrl,
    isLoading,
    shortUrl,
    showQRCode,
    expirationType,
    setExpirationType,
    handleShortenUrl,
    user: urlUser
  } = useUrlShortener(approvedDomains);

  // Domain whitelist state
  const [searchDomain, setSearchDomain] = useState("");
  const [filteredDomains, setFilteredDomains] = useState<string[]>(approvedDomains);
  const [whitelistView, setWhitelistView] = useState<"grid" | "table">("grid");
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const checkUser = async () => {
      try {
        if (isUsingSupabase()) {
          // 使用 Supabase
          const { supabase } = await import('@/integrations/supabase/client');
          const { data: { user } } = await supabase.auth.getUser();
          setUser(user);
        } else {
          // 使用 Go 后端
          const token = localStorage.getItem('authToken') || localStorage.getItem('auth_token');
          if (!token) {
            setUser(null);
            return;
          }

          const config = getBackendConfig();
          const baseURL = config.goBackend?.baseUrl;

          const response = await fetch(`${baseURL}/auth/me`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            setUser(null);
            return;
          }

          const userData = await response.json();
          setUser(userData);
        }
      } catch (error) {
        console.error('Error checking user:', error);
        setUser(null);
      }
    };

    checkUser();

    // Listen for auth state changes
    if (isUsingSupabase()) {
      // Supabase 认证状态监听
      const setupSupabaseListener = async () => {
        const { supabase } = await import('@/integrations/supabase/client');
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event, session) => {
            setUser(session?.user || null);
          }
        );

        return () => subscription.unsubscribe();
      };

      setupSupabaseListener().then(cleanup => {
        return cleanup;
      });
    } else {
      // Go 后端认证状态监听
      const handleAuthStateChange = () => {
        checkUser();
      };

      // 监听认证状态变化事件
      window.addEventListener('authStateChanged', handleAuthStateChange);

      return () => {
        window.removeEventListener('authStateChanged', handleAuthStateChange);
      };
    }
  }, []);

  useEffect(() => {
    if (searchDomain) {
      setFilteredDomains(
        approvedDomains.filter(domain =>
          domain.toLowerCase().includes(searchDomain.toLowerCase())
        )
      );
    } else {
      setFilteredDomains(approvedDomains);
    }
  }, [searchDomain, approvedDomains]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="border-2 border-primary/10 shadow-md overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-brand-50 to-accent1-50 dark:from-brand-950/30 dark:to-accent1-950/30 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                <Link className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-xl">
                  {language === 'en' ? 'URL Tools' : 'URL工具'}
                </CardTitle>
                <CardDescription className="text-sm">
                  {language === 'en'
                    ? 'Shorten URLs and manage domain whitelist'
                    : '缩短网址和管理域名白名单'}
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-primary/10 text-primary">
                {approvedDomains.length} {language === 'en' ? 'domains' : '域名'}
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 m-0 rounded-none border-b bg-muted/30">
              <TabsTrigger
                value="shorten"
                className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm"
              >
                <Link2 className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {language === 'en' ? 'Shorten URLs' : '缩短网址'}
                </span>
                <span className="sm:hidden">
                  {language === 'en' ? 'URLs' : '网址'}
                </span>
              </TabsTrigger>
              <TabsTrigger
                value="domains"
                className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm"
              >
                <ShieldCheck className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {language === 'en' ? 'Domain Whitelist' : '域名白名单'}
                </span>
                <span className="sm:hidden">
                  {language === 'en' ? 'Domains' : '域名'}
                </span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="shorten" className="p-6 pt-4 m-0">
              <div className="space-y-4">
                {/* URL Shortener content */}
                <UrlForm
                  url={url}
                  setUrl={setUrl}
                  expirationType={expirationType}
                  setExpirationType={setExpirationType}
                  isLoading={isLoading}
                  onSubmit={handleShortenUrl}
                  approvedDomains={approvedDomains}
                  user={urlUser}
                />

                {shortUrl && (
                  <UrlResult
                    shortUrl={shortUrl}
                    showQRCode={showQRCode}
                  />
                )}

                {/* Quick domain list for reference */}
                {approvedDomains.length > 0 && (
                  <div className="pt-4 border-t border-border/50">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-muted-foreground">
                        {language === 'en' ? 'Allowed Domains' : '允许的域名'}
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setActiveTab('domains')}
                        className="text-xs h-7 px-2"
                      >
                        {language === 'en' ? 'Manage' : '管理'}
                        <ExternalLink className="ml-1 h-3 w-3" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1.5 max-h-20 overflow-y-auto">
                      {approvedDomains.slice(0, 8).map((domain, index) => (
                        <Badge
                          key={domain}
                          variant="secondary"
                          className="text-xs px-2 py-0.5 bg-muted/50"
                        >
                          <Globe className="h-3 w-3 mr-1" />
                          {domain}
                        </Badge>
                      ))}
                      {approvedDomains.length > 8 && (
                        <Badge variant="outline" className="text-xs px-2 py-0.5">
                          +{approvedDomains.length - 8} {language === 'en' ? 'more' : '更多'}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="domains" className="p-6 pt-4 m-0">
              <div className="space-y-4">
                {/* Domain whitelist content */}
                {user ? (
                  <DomainForm approvedDomains={approvedDomains} isUserLoggedIn={!!user} />
                ) : (
                  <LoginPrompt />
                )}

                <DomainSearch
                  searchDomain={searchDomain}
                  setSearchDomain={setSearchDomain}
                  whitelistView={whitelistView}
                  setWhitelistView={setWhitelistView}
                />

                {filteredDomains.length === 0 ? (
                  <EmptyDomainState />
                ) : whitelistView === 'grid' ? (
                  <DomainsGrid domains={filteredDomains} />
                ) : (
                  <DomainsTable domains={filteredDomains} />
                )}

                <DomainFooter
                  filteredCount={filteredDomains.length}
                  totalCount={approvedDomains.length}
                />

                {/* Quick shortener link */}
                <div className="pt-4 border-t border-border/50">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {language === 'en'
                        ? 'Ready to shorten URLs with these domains?'
                        : '准备使用这些域名缩短URL？'}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveTab('shorten')}
                      className="text-xs h-7 px-2"
                    >
                      {language === 'en' ? 'Shorten URL' : '缩短网址'}
                      <Link2 className="ml-1 h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default UrlShortenerWithDomains;