
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAppContext } from "@/context/AppContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Lock, Check } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";

export default function UpdatePassword() {
  const { language } = useAppContext();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [updated, setUpdated] = useState(false);
  const [passwordError, setPasswordError] = useState("");

  useEffect(() => {
    // Check if we have a session with access token but no refresh token
    // This is the case when a user comes from a reset password link
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      // If no session, redirect to login
      if (!session) {
        toast({
          variant: "destructive",
          description: language === 'en' 
            ? "Your password reset link is invalid or has expired" 
            : "您的密码重置链接无效或已过期",
        });
        navigate("/login");
      }
    };
    
    checkSession();
  }, [navigate, toast, language]);

  const validatePassword = (password: string) => {
    if (password.length < 8) {
      return language === 'en' 
        ? "Password must be at least 8 characters" 
        : "密码必须至少为8个字符";
    }
    
    return "";
  };

  const handleUpdatePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setPasswordError("");
    
    // Validate passwords
    if (password !== confirmPassword) {
      setPasswordError(language === 'en' ? "Passwords do not match" : "密码不匹配");
      setIsLoading(false);
      return;
    }
    
    const validationError = validatePassword(password);
    if (validationError) {
      setPasswordError(validationError);
      setIsLoading(false);
      return;
    }
    
    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      });
      
      if (error) throw error;
      
      setUpdated(true);
      toast({
        description: language === 'en' 
          ? "Your password has been updated successfully" 
          : "您的密码已成功更新",
      });
      
      // Redirect to login after a few seconds
      setTimeout(() => {
        navigate("/login");
      }, 3000);
      
    } catch (error: any) {
      console.error("Update password error:", error);
      toast({
        variant: "destructive",
        description: language === 'en'
          ? error.message || "Failed to update password. Please try again."
          : "更新密码失败。请重试。",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>
          {language === 'en' ? "Update Your Password" : "更新您的密码"}
        </CardTitle>
        <CardDescription>
          {language === 'en'
            ? "Create a new secure password for your account"
            : "为您的账户创建一个新的安全密码"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!updated ? (
          <form onSubmit={handleUpdatePassword} className="space-y-4">
            <div className="space-y-2">
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="password"
                  placeholder={language === 'en' ? "New Password" : "新密码"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="pl-10"
                />
              </div>
              
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="password"
                  placeholder={language === 'en' ? "Confirm New Password" : "确认新密码"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="pl-10"
                />
              </div>
              
              {passwordError && (
                <p className="text-sm text-destructive">{passwordError}</p>
              )}
              
              <p className="text-xs text-muted-foreground">
                {language === 'en'
                  ? "Password must be at least 8 characters long"
                  : "密码必须至少8个字符"}
              </p>
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading
                ? (language === 'en' ? "Updating..." : "更新中...")
                : (language === 'en' ? "Update Password" : "更新密码")}
            </Button>
          </form>
        ) : (
          <div className="text-center py-4">
            <div className="bg-green-100 text-green-800 rounded-full p-3 mx-auto mb-4 inline-flex">
              <Check className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {language === 'en' ? "Password Updated" : "密码已更新"}
            </h3>
            <p className="text-muted-foreground mb-4">
              {language === 'en'
                ? "Your password has been updated successfully. You'll be redirected to login shortly."
                : "您的密码已成功更新。您将很快被重定向到登录页面。"}
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-col space-y-4">
        <div className="text-center w-full">
          <Button variant="link" asChild className="px-0">
            <Link to="/login">
              {language === 'en' ? "Back to login" : "返回登录"}
            </Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
