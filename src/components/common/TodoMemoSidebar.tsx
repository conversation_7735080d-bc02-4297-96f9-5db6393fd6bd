
import React, { useState, useEffect } from 'react';
import { useAppContext } from '@/context/AppContext';
import DraggableSidebar from './DraggableSidebar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ListTodo, 
  StickyNote, 
  Plus, 
  Calendar, 
  Search, 
  ArrowLeftRight,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Check
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { useTodos } from '@/hooks/useTodos';
import { useMemos, type Memo } from '@/hooks/useMemos';
import ReactMarkdown from 'react-markdown';
import { useNavigate } from 'react-router-dom';

const TodoMemoSidebar: React.FC = () => {
  const { language } = useAppContext();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('todo');
  const [searchTerm, setSearchTerm] = useState('');
  
  // 分页状态
  const [todoPage, setTodoPage] = useState(0);
  const [memoPage, setMemoPage] = useState(0);
  const itemsPerPage = 3;
  
  // 获取真实数据
  const { todos, loading: todosLoading } = useTodos();
  const { memos, loading: memosLoading } = useMemos();
  
  const title = language === 'en' 
    ? (activeTab === 'todo' ? 'My Tasks' : 'My Memos')
    : (activeTab === 'todo' ? '我的待办' : '我的备忘录');

  // 计算初始位置 - 在右侧
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });
  
  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // 默认位置计算
  const defaultX = Math.max(20, windowSize.width - 350);
  const defaultY = Math.min(100, windowSize.height / 4);

  // 过滤和排序数据
  const filteredTodos = todos
    .filter(todo => 
      todo.title.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (a.completed && !b.completed) return 1;
      if (!a.completed && b.completed) return -1;
      if (!a.due_date) return 1;
      if (!b.due_date) return -1;
      return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
    });

  const filteredMemos = memos
    .filter(memo => 
      memo.content.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  // 分页计算
  const totalTodoPages = Math.ceil(filteredTodos.length / itemsPerPage);
  const totalMemoPages = Math.ceil(filteredMemos.length / itemsPerPage);
  
  const paginatedTodos = filteredTodos.slice(
    todoPage * itemsPerPage,
    (todoPage + 1) * itemsPerPage
  );
  
  const paginatedMemos = filteredMemos.slice(
    memoPage * itemsPerPage,
    (memoPage + 1) * itemsPerPage
  );

  // 分页控制函数
  const handleTodoPrevPage = () => {
    setTodoPage(prev => Math.max(0, prev - 1));
  };

  const handleTodoNextPage = () => {
    setTodoPage(prev => Math.min(totalTodoPages - 1, prev + 1));
  };

  const handleMemoPrevPage = () => {
    setMemoPage(prev => Math.max(0, prev - 1));
  };

  const handleMemoNextPage = () => {
    setMemoPage(prev => Math.min(totalMemoPages - 1, prev + 1));
  };

  // 跳转到最新数据
  const handleJumpToLatest = () => {
    if (activeTab === 'todo') {
      setTodoPage(0);
    } else {
      setMemoPage(0);
    }
  };

  // 清空搜索并重置到最新
  const handleResetToLatest = () => {
    setSearchTerm('');
    handleJumpToLatest();
  };

  // 格式化备忘录内容
  const formatMemoContent = (content: string) => {
    if (content.length <= 80) return content;
    return content.substring(0, 80) + '...';
  };

  return (
    <DraggableSidebar 
      title={title}
      defaultPosition={{ x: defaultX, y: defaultY }}
    >
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex items-center justify-between mb-4">
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="todo" className="flex items-center gap-1">
              <ListTodo className="h-4 w-4" />
              <span>{language === 'en' ? 'Tasks' : '待办'}</span>
              <Badge variant="secondary" className="ml-1 h-5 px-1">
                {filteredTodos.filter(t => !t.completed).length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="memo" className="flex items-center gap-1">
              <StickyNote className="h-4 w-4" />
              <span>{language === 'en' ? 'Memos' : '备忘录'}</span>
              <Badge variant="secondary" className="ml-1 h-5 px-1">
                {filteredMemos.length}
              </Badge>
            </TabsTrigger>
          </TabsList>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8"
            onClick={handleResetToLatest}
            title={language === 'en' ? 'Jump to latest' : '跳转到最新'}
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>

        <div className="mb-4 relative">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input 
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={
              activeTab === 'todo'
                ? (language === 'en' ? 'Search tasks...' : '搜索待办事项...')
                : (language === 'en' ? 'Search memos...' : '搜索备忘录...')
            } 
            className="pl-9"
          />
        </div>

        <TabsContent value="todo" className="m-0">
          <div className="mb-3 flex justify-between items-center">
            <div className="text-sm font-medium flex items-center gap-2">
              {language === 'en' ? 'Task List' : '任务列表'}
              {totalTodoPages > 1 && (
                <span className="text-xs text-muted-foreground">
                  ({todoPage + 1}/{totalTodoPages})
                </span>
              )}
            </div>
            <Button 
              size="sm" 
              variant="outline" 
              className="h-8 px-2 text-xs"
              onClick={() => navigate('/memo-todo')}
            >
              <Plus className="h-3.5 w-3.5 mr-1" />
              {language === 'en' ? 'Add Task' : '添加任务'}
            </Button>
          </div>
          
          <div className="min-h-[240px]">
            {todosLoading ? (
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="h-16 bg-muted/30 rounded-md animate-pulse" />
                ))}
              </div>
            ) : paginatedTodos.length === 0 ? (
              <div className="text-center py-8 text-sm text-muted-foreground">
                <ListTodo className="h-8 w-8 mx-auto mb-2 opacity-50" />
                {searchTerm ? 
                  (language === 'en' ? 'No matching tasks' : '没有匹配的任务') :
                  (language === 'en' ? 'No tasks yet' : '暂无任务')
                }
              </div>
            ) : (
              <ScrollArea className="h-[240px]">
                <AnimatePresence>
                  {paginatedTodos.map((todo) => (
                    <motion.div
                      key={todo.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                      className={cn(
                        "mb-2 p-3 rounded-md border flex items-start gap-2 group hover:bg-muted/50 transition-colors cursor-pointer",
                        todo.completed && "bg-muted/30"
                      )}
                      onClick={() => navigate('/memo-todo')}
                    >
                      <div 
                        className={cn(
                          "mt-0.5 h-5 w-5 rounded-full border flex items-center justify-center flex-shrink-0",
                          todo.completed 
                            ? "bg-primary border-primary" 
                            : "border-primary/30 hover:border-primary"
                        )}
                      >
                        {todo.completed && (
                          <Check className="h-3 w-3 text-white" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className={cn(
                          "text-sm font-medium line-clamp-2",
                          todo.completed && "line-through text-muted-foreground"
                        )}>
                          {todo.title}
                        </p>
                        {todo.due_date && (
                          <div className="flex items-center text-xs text-muted-foreground mt-1">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(todo.due_date).toLocaleDateString(
                              language === 'en' ? 'en-US' : 'zh-CN',
                              { month: 'short', day: 'numeric' }
                            )}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </ScrollArea>
            )}
          </div>
          
          {/* 分页控制 */}
          {totalTodoPages > 1 && (
            <div className="flex items-center justify-between mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleTodoPrevPage}
                disabled={todoPage === 0}
                className="h-8 px-2"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-xs text-muted-foreground">
                {todoPage + 1} / {totalTodoPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleTodoNextPage}
                disabled={todoPage === totalTodoPages - 1}
                className="h-8 px-2"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="memo" className="m-0">
          <div className="mb-3 flex justify-between items-center">
            <div className="text-sm font-medium flex items-center gap-2">
              {language === 'en' ? 'Memo List' : '备忘录列表'}
              {totalMemoPages > 1 && (
                <span className="text-xs text-muted-foreground">
                  ({memoPage + 1}/{totalMemoPages})
                </span>
              )}
            </div>
            <Button 
              size="sm" 
              variant="outline" 
              className="h-8 px-2 text-xs"
              onClick={() => navigate('/memo-todo')}
            >
              <Plus className="h-3.5 w-3.5 mr-1" />
              {language === 'en' ? 'Add Memo' : '添加备忘录'}
            </Button>
          </div>
          
          <div className="min-h-[240px]">
            {memosLoading ? (
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="h-20 bg-muted/30 rounded-md animate-pulse" />
                ))}
              </div>
            ) : paginatedMemos.length === 0 ? (
              <div className="text-center py-8 text-sm text-muted-foreground">
                <StickyNote className="h-8 w-8 mx-auto mb-2 opacity-50" />
                {searchTerm ? 
                  (language === 'en' ? 'No matching memos' : '没有匹配的备忘录') :
                  (language === 'en' ? 'No memos yet' : '暂无备忘录')
                }
              </div>
            ) : (
              <ScrollArea className="h-[240px]">
                <AnimatePresence>
                  {paginatedMemos.map((memo) => (
                    <motion.div
                      key={memo.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                      className="mb-3 p-3 rounded-md border hover:bg-muted/50 transition-colors cursor-pointer"
                      onClick={() => navigate('/memo-todo')}
                    >
                      <div className="text-xs text-muted-foreground mb-2 flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {new Date(memo.created_at).toLocaleDateString(
                          language === 'en' ? 'en-US' : 'zh-CN',
                          { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' }
                        )}
                      </div>
                      <div className="text-sm prose prose-sm dark:prose-invert max-w-none">
                        <ReactMarkdown>
                          {formatMemoContent(memo.content)}
                        </ReactMarkdown>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </ScrollArea>
            )}
          </div>
          
          {/* 分页控制 */}
          {totalMemoPages > 1 && (
            <div className="flex items-center justify-between mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleMemoPrevPage}
                disabled={memoPage === 0}
                className="h-8 px-2"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-xs text-muted-foreground">
                {memoPage + 1} / {totalMemoPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleMemoNextPage}
                disabled={memoPage === totalMemoPages - 1}
                className="h-8 px-2"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </DraggableSidebar>
  );
};

export default TodoMemoSidebar;
