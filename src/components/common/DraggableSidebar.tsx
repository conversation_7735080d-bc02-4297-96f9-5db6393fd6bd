import React, { useState, ReactNode, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Grip, X, Minimize2, Maximize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DraggableSidebarProps {
  title: string;
  children: ReactNode;
  className?: string;
  defaultPosition?: { x: number; y: number };
}

const DraggableSidebar: React.FC<DraggableSidebarProps> = ({
  title,
  children,
  className,
  defaultPosition = { x: 20, y: 20 }
}) => {
  const [minimized, setMinimized] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState(defaultPosition);
  
  const sidebarRef = useRef<HTMLDivElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);
  
  // Handle drag logic
  useEffect(() => {
    const sidebar = sidebarRef.current;
    const dragHandle = dragHandleRef.current;
    
    if (!sidebar || !dragHandle) return;
    
    let offsetX = 0;
    let offsetY = 0;
    let animationFrameId: number;
    let isDraggingState = false;
    
    const onMouseDown = (e: MouseEvent) => {
      isDraggingState = true;
      setIsDragging(true);
      
      // Calculate offset between mouse position and element top-left corner
      const rect = sidebar.getBoundingClientRect();
      offsetX = e.clientX - rect.left;
      offsetY = e.clientY - rect.top;
      
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      
      // Prevent text selection during drag
      e.preventDefault();
    };
    
    const onMouseMove = (e: MouseEvent) => {
      if (!isDraggingState) return;
      
      // Use requestAnimationFrame for smoother animation
      cancelAnimationFrame(animationFrameId);
      animationFrameId = requestAnimationFrame(() => {
        // Calculate new position
        const newX = e.clientX - offsetX;
        const newY = e.clientY - offsetY;
        
        // Constrain to viewport boundaries
        const maxX = window.innerWidth - (sidebar?.offsetWidth || 320);
        const maxY = window.innerHeight - (sidebar?.offsetHeight || 500);
        
        const constrainedX = Math.max(0, Math.min(maxX, newX));
        const constrainedY = Math.max(0, Math.min(maxY, newY));
        
        // Set new position
        setPosition({ x: constrainedX, y: constrainedY });
      });
    };
    
    const onMouseUp = () => {
      isDraggingState = false;
      setIsDragging(false);
      cancelAnimationFrame(animationFrameId);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };
    
    // Add event listeners
    dragHandle.addEventListener('mousedown', onMouseDown);
    
    // Clean up event listeners
    return () => {
      dragHandle.removeEventListener('mousedown', onMouseDown);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
      cancelAnimationFrame(animationFrameId);
    };
  }, []); // Remove position and isDragging from dependencies
  
  // Close sidebar
  const handleClose = () => {
    console.log('DraggableSidebar: Close button clicked');
    const event = new CustomEvent('close-sidebar', { 
      bubbles: true, 
      detail: { source: 'draggable-sidebar' } 
    });
    // 直接在document上派发事件，确保能被监听到
    document.dispatchEvent(event);
  };

  return (
    <div 
      ref={sidebarRef}
      className={cn(
        "fixed z-50 bg-card shadow-lg rounded-lg border overflow-hidden",
        minimized ? "w-64" : "w-80",
        isDragging ? "shadow-xl opacity-90" : "",
        className
      )}
      style={{ 
        left: position.x, 
        top: position.y,
        transition: isDragging ? 'none' : 'all 0.2s ease'
      }}
    >
      <div 
        ref={dragHandleRef}
        className="bg-muted/50 p-2 flex items-center justify-between cursor-move select-none"
      >
        <div className="flex items-center gap-2">
          <Grip className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium text-sm">{title}</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => setMinimized(!minimized)}
          >
            {minimized ? (
              <Maximize2 className="h-3 w-3" />
            ) : (
              <Minimize2 className="h-3 w-3" />
            )}
          </Button>
          <Button
            variant="ghost" 
            size="icon"
            className="h-6 w-6 hover:text-destructive"
            onClick={handleClose}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      <div className={cn(
        "transition-all duration-300 ease-in-out",
        minimized ? "max-h-0 p-0" : "max-h-[500px] p-3"
      )}>
        {children}
      </div>
    </div>
  );
};

export default DraggableSidebar;
