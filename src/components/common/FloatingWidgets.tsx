import React, { useState, useEffect, useRef } from 'react';
import TodoMemoSidebar from './TodoMemoSidebar';
import { Button } from '@/components/ui/button';
import { ListTodo, StickyNote } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { motion, AnimatePresence } from 'framer-motion';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';

const FloatingWidgets: React.FC = () => {
  const { language } = useAppContext();
  const [showTodoMemo, setShowTodoMemo] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Check authentication state
  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (isUsingSupabase()) {
          // 使用 Supabase
          const { supabase } = await import('@/integrations/supabase/client');
          const { data: { session } } = await supabase.auth.getSession();
          setIsAuthenticated(!!session);
        } else {
          // 使用 Go 后端
          const token = localStorage.getItem('authToken') || localStorage.getItem('auth_token');
          if (!token) {
            setIsAuthenticated(false);
            return;
          }

          const config = getBackendConfig();
          const baseURL = config.goBackend?.baseUrl;

          const response = await fetch(`${baseURL}/auth/me`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          setIsAuthenticated(response.ok);
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        setIsAuthenticated(false);
      }
    };

    checkAuth();

    // Listen for auth state changes
    if (isUsingSupabase()) {
      // Supabase 认证状态监听
      const setupSupabaseListener = async () => {
        const { supabase } = await import('@/integrations/supabase/client');
        const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
          setIsAuthenticated(!!session);
          // If user logs out, close the sidebar
          if (!session) {
            setShowTodoMemo(false);
          }
        });

        return () => subscription.unsubscribe();
      };

      setupSupabaseListener().then(cleanup => {
        return cleanup;
      });
    } else {
      // Go 后端认证状态监听
      const handleAuthStateChange = () => {
        checkAuth();
      };

      // 监听认证状态变化事件
      window.addEventListener('authStateChanged', handleAuthStateChange);

      return () => {
        window.removeEventListener('authStateChanged', handleAuthStateChange);
      };
    }
  }, []);

  // Listen for close sidebar events
  useEffect(() => {
    const handleCloseSidebar = (event: CustomEvent) => {
      console.log('Close sidebar event received:', event);
      event.stopPropagation();
      setShowTodoMemo(false);
    };

    // 监听整个文档的close-sidebar事件
    document.addEventListener('close-sidebar', handleCloseSidebar as EventListener);

    return () => {
      document.removeEventListener('close-sidebar', handleCloseSidebar as EventListener);
    };
  }, []);

  // Don't render if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <div ref={containerRef} className="fixed bottom-6 right-6 z-[100]">
        <AnimatePresence>
          {showTodoMemo && (
            <div className="absolute bottom-16 right-0">
              <TodoMemoSidebar />
            </div>
          )}
        </AnimatePresence>

        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="flex flex-col gap-2 items-end"
        >
          <Button
            size="icon"
            className="h-12 w-12 rounded-full bg-primary text-primary-foreground shadow-lg hover:shadow-xl hover:bg-primary/90"
            onClick={() => setShowTodoMemo(!showTodoMemo)}
          >
            <div className="relative">
              <ListTodo className="h-5 w-5 absolute -left-3.5 -top-3.5" />
              <StickyNote className="h-5 w-5 absolute -right-3.5 -bottom-3.5" />
            </div>
          </Button>
        </motion.div>
      </div>
    </>
  );
};

export default FloatingWidgets;
