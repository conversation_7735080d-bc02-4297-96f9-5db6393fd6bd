import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useBannerConfig } from '@/hooks/useBannerConfig';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface FeatureCard {
  id: string;
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  gradient: string;
  badge?: string;
}

// Updated title interface to better match real usage
export interface TitleProps {
  main: string;
  subtitle: string;
  first?: { text: string, gradient: string };
  second?: { text: string, gradient: string };
  third?: { text: string, gradient: string };
  fourth?: { text: string, gradient: string };
}

interface AnimatedGradientTitleProps {
  title: TitleProps;
  features?: FeatureCard[];
  className?: string;
  animationSpeed?: number;
}

const AnimatedGradientTitle: React.FC<AnimatedGradientTitleProps> = ({
  title,
  features = [],
  className,
  animationSpeed
}) => {
  const { bannerConfig } = useBannerConfig();
  const speed = animationSpeed || bannerConfig?.animation_speed || 0.8;
  
  // Get grid configuration from banner config
  const gridRows = bannerConfig?.grid_rows || 1;
  const gridColumns = bannerConfig?.grid_columns || 6;
  
  // Calculate grid classes based on configuration
  const getGridClasses = () => {
    if (gridRows === 1) {
      // Single row layout
      if (gridColumns <= 3) {
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3";
      } else if (gridColumns <= 4) {
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4";
      } else if (gridColumns <= 5) {
        return "grid-cols-1 sm:grid-cols-3 lg:grid-cols-5";
      } else {
        return "grid-cols-2 sm:grid-cols-3 lg:grid-cols-6";
      }
    } else if (gridRows === 2) {
      // Two row layout
      return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3";
    } else {
      // Three or more rows
      return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3";
    }
  };
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: speed
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: speed * 0.8,
        ease: [0.17, 0.67, 0.48, 0.99]
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9, y: 20 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: speed * 0.6,
        ease: "backOut"
      }
    }
  };

  return (
    <div className={cn("relative text-center py-6", className)}>
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div 
          className="absolute -top-20 -right-20 w-64 h-64 bg-primary/5 rounded-full blur-3xl"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 0.6, scale: 1 }}
          transition={{ duration: 2, ease: "easeInOut" }}
        />
        <motion.div 
          className="absolute -bottom-20 -left-20 w-64 h-64 bg-accent1-400/5 rounded-full blur-3xl"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 0.6, scale: 1 }}
          transition={{ duration: 2, delay: 0.5, ease: "easeInOut" }}
        />
      </div>

      <motion.div 
        className="relative"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Main title */}
        <motion.div variants={itemVariants} className="mb-6">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl mb-3">
            <span className="bg-gradient-to-r from-primary via-accent1-500 to-brand-500 bg-clip-text text-transparent">
              {title.main}
            </span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {title.subtitle}
          </p>
        </motion.div>

        {/* Feature cards grid - Dynamic layout */}
        {features.length > 0 && (
          <motion.div 
            variants={itemVariants}
            className={cn(
              "grid gap-4 max-w-6xl mx-auto mb-6",
              getGridClasses()
            )}
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.id}
                variants={cardVariants}
                custom={index}
                whileHover={{ 
                  scale: 1.02, 
                  transition: { duration: 0.2 } 
                }}
                className="relative group"
              >
                <Card className="h-full border-0 bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm shadow-md hover:shadow-lg transition-all duration-300">
                  <CardContent className={cn(
                    "flex flex-col items-center text-center h-full relative overflow-hidden",
                    gridColumns >= 6 ? "p-4" : "p-6" // Smaller padding for more columns
                  )}>
                    {/* Feature badge */}
                    {feature.badge && (
                      <Badge 
                        variant="secondary" 
                        className="absolute top-2 right-2 text-xs bg-primary/10 text-primary border-primary/20"
                      >
                        {feature.badge}
                      </Badge>
                    )}
                    
                    {/* Icon with gradient background */}
                    <div className={cn(
                      "rounded-2xl flex items-center justify-center mb-3 bg-gradient-to-br shadow-sm",
                      feature.gradient,
                      gridColumns >= 6 ? "w-12 h-12" : "w-16 h-16" // Smaller icons for more columns
                    )}>
                      <div className="text-white">
                        {feature.icon}
                      </div>
                    </div>
                    
                    {/* Feature title and subtitle */}
                    <h3 className={cn(
                      "font-semibold mb-2 group-hover:text-primary transition-colors",
                      gridColumns >= 6 ? "text-sm" : "text-lg" // Smaller text for more columns
                    )}>
                      {feature.title}
                    </h3>
                    <p className={cn(
                      "text-muted-foreground leading-relaxed",
                      gridColumns >= 6 ? "text-xs" : "text-sm" // Smaller text for more columns
                    )}>
                      {feature.subtitle}
                    </p>
                    
                    {/* Hover effect overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent1-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg" />
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Decorative line */}
        <motion.div 
          variants={itemVariants}
          className="flex items-center justify-center"
        >
          <div className="h-px w-32 bg-gradient-to-r from-transparent via-primary/40 to-transparent" />
          <div className="mx-4 w-2 h-2 rounded-full bg-primary/60" />
          <div className="h-px w-32 bg-gradient-to-r from-transparent via-primary/40 to-transparent" />
        </motion.div>
      </motion.div>
    </div>
  );
};

export default AnimatedGradientTitle;
