import React from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export interface HeaderAction {
  id: string;
  label: string;
  icon?: LucideIcon;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'ghost' | 'destructive' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  disabled?: boolean;
}

export interface UnifiedHeaderProps {
  // 基础信息
  title: string;
  description?: string;
  icon?: LucideIcon;

  // 样式配置
  variant?: 'default' | 'gradient' | 'colorful';
  gradientFrom?: string;
  gradientTo?: string;

  // 操作按钮
  actions?: HeaderAction[];

  // 状态显示
  badges?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  }[];

  // 自定义样式
  className?: string;

  // 布局配置
  layout?: 'default' | 'compact' | 'centered';

  // 额外内容
  children?: React.ReactNode;
}

const UnifiedHeader: React.FC<UnifiedHeaderProps> = ({
  title,
  description,
  icon: Icon,
  variant = 'default',
  gradientFrom = 'from-primary',
  gradientTo = 'to-primary/80',
  actions = [],
  badges = [],
  className,
  layout = 'default',
  children
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'gradient':
        return {
          container: `bg-gradient-to-r ${gradientFrom} ${gradientTo} text-white`,
          title: 'text-white',
          description: 'text-white/90'
        };
      case 'colorful':
        return {
          container: 'bg-gradient-to-br from-primary/5 via-accent1-500/5 to-accent2-500/5 border',
          title: `bg-gradient-to-r ${gradientFrom} ${gradientTo} bg-clip-text text-transparent`,
          description: 'text-muted-foreground'
        };
      default:
        return {
          container: 'bg-card border-b',
          title: 'text-foreground',
          description: 'text-muted-foreground'
        };
    }
  };

  const styles = getVariantStyles();

  const getTitleSize = () => {
    switch (layout) {
      case 'compact':
        return 'text-xl';
      case 'centered':
        return 'text-3xl';
      default:
        return 'text-2xl';
    }
  };

  const getLayoutClasses = () => {
    switch (layout) {
      case 'compact':
        return 'py-1.5 px-4';
      case 'centered':
        return 'py-3 px-4 text-center';
      default:
        return 'py-2 px-4';
    }
  };

  return (
    <div className={cn(styles.container, getLayoutClasses(), 'w-full', className)}>
      <div className="container mx-auto">
        <div className={cn(
          'flex items-center justify-between',
          layout === 'centered' && 'flex-col space-y-4'
        )}>
          {/* 标题部分 */}
          <div className={cn(
            'flex items-center space-x-3',
            layout === 'centered' && 'flex-col space-x-0 space-y-2'
          )}>
            {Icon && (
              <div className={cn(
                'p-2 rounded-lg',
                variant === 'gradient'
                  ? 'bg-white/20'
                  : variant === 'colorful'
                  ? 'bg-primary/10'
                  : 'bg-primary/10'
              )}>
                <Icon className={cn(
                  'h-6 w-6',
                  variant === 'gradient'
                    ? 'text-white'
                    : 'text-primary'
                )} />
              </div>
            )}

            <div className={cn(layout === 'centered' && 'text-center')}>
              <div className="flex items-center space-x-3">
                <h1 className={cn(
                  getTitleSize(),
                  'font-bold tracking-tight',
                  styles.title
                )}>
                  {title}
                </h1>

                {/* 标题旁的徽章 */}
                {badges.map((badge, index) => (
                  <Badge
                    key={index}
                    variant={badge.variant || 'secondary'}
                    className={cn(
                      variant === 'gradient' && badge.variant === 'secondary' && 'bg-white/20 text-white border-white/30',
                      variant === 'gradient' && badge.variant === 'outline' && 'bg-transparent text-white border-white/40',
                      variant === 'gradient' && badge.variant === 'default' && 'bg-white text-gray-900'
                    )}
                  >
                    {badge.text}
                  </Badge>
                ))}
              </div>

              {description && (
                <p className={cn(
                  'mt-1.5 text-sm',
                  styles.description,
                  layout === 'compact' ? 'text-xs' : ''
                )}>
                  {description}
                </p>
              )}
            </div>
          </div>

          {/* 操作按钮部分 */}
          {(actions.length > 0 || children) && (
            <div className={cn(
              'flex items-center space-x-2',
              layout === 'centered' && 'justify-center'
            )}>
              {actions.map((action) => {
                const ActionIcon = action.icon;
                return (
                  <Button
                    key={action.id}
                    variant={action.variant || 'outline'}
                    size={action.size || 'default'}
                    onClick={action.onClick}
                    disabled={action.disabled}
                    className={cn(
                      'relative font-medium',
                      variant === 'gradient' && action.variant === 'outline' &&
                      'border-white/70 text-white hover:bg-white/20 hover:border-white/90 shadow-sm bg-white/10',
                      variant === 'gradient' && action.variant === 'default' &&
                      'bg-white text-gray-900 hover:bg-white/90 shadow-md',
                      variant === 'gradient' && action.variant === 'ghost' &&
                      'text-white hover:bg-white/20 hover:text-white bg-white/5',
                      variant === 'gradient' && action.variant === 'secondary' &&
                      'bg-white/20 text-white border-white/60 hover:bg-white/30 shadow-sm'
                    )}
                  >
                    {ActionIcon && <ActionIcon className="h-4 w-4 mr-2" />}
                    {action.label}
                    {action.badge && (
                      <Badge
                        variant={action.badge.variant || 'destructive'}
                        className="ml-2 h-5 w-5 p-0 flex items-center justify-center rounded-full"
                      >
                        {action.badge.text}
                      </Badge>
                    )}
                  </Button>
                );
              })}
              {children && (
                <div className={cn(
                  variant === 'gradient' && 'text-white/90'
                )}>
                  {children}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UnifiedHeader;