
import React from 'react';
import { LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export type ColorScheme = 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'info';

interface HeaderAction {
  label: string;
  icon?: LucideIcon;
  onClick: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
}

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  icon?: LucideIcon;
  colorScheme?: ColorScheme;
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'outline';
  };
  statusText?: string;
  actions?: HeaderAction[];
  className?: string;
}

const colorSchemes = {
  primary: {
    background: 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30',
    border: 'border-blue-200 dark:border-blue-800',
    icon: 'text-blue-600 dark:text-blue-400',
    title: 'text-blue-900 dark:text-blue-100',
    subtitle: 'text-blue-700 dark:text-blue-300',
    status: 'text-blue-600 dark:text-blue-400'
  },
  secondary: {
    background: 'bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30',
    border: 'border-purple-200 dark:border-purple-800',
    icon: 'text-purple-600 dark:text-purple-400',
    title: 'text-purple-900 dark:text-purple-100',
    subtitle: 'text-purple-700 dark:text-purple-300',
    status: 'text-purple-600 dark:text-purple-400'
  },
  accent: {
    background: 'bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/30 dark:to-teal-950/30',
    border: 'border-emerald-200 dark:border-emerald-800',
    icon: 'text-emerald-600 dark:text-emerald-400',
    title: 'text-emerald-900 dark:text-emerald-100',
    subtitle: 'text-emerald-700 dark:text-emerald-300',
    status: 'text-emerald-600 dark:text-emerald-400'
  },
  success: {
    background: 'bg-gradient-to-r from-green-50 to-lime-50 dark:from-green-950/30 dark:to-lime-950/30',
    border: 'border-green-200 dark:border-green-800',
    icon: 'text-green-600 dark:text-green-400',
    title: 'text-green-900 dark:text-green-100',
    subtitle: 'text-green-700 dark:text-green-300',
    status: 'text-green-600 dark:text-green-400'
  },
  warning: {
    background: 'bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-950/30 dark:to-amber-950/30',
    border: 'border-orange-200 dark:border-orange-800',
    icon: 'text-orange-600 dark:text-orange-400',
    title: 'text-orange-900 dark:text-orange-100',
    subtitle: 'text-orange-700 dark:text-orange-300',
    status: 'text-orange-600 dark:text-orange-400'
  },
  info: {
    background: 'bg-gradient-to-r from-cyan-50 to-sky-50 dark:from-cyan-950/30 dark:to-sky-950/30',
    border: 'border-cyan-200 dark:border-cyan-800',
    icon: 'text-cyan-600 dark:text-cyan-400',
    title: 'text-cyan-900 dark:text-cyan-100',
    subtitle: 'text-cyan-700 dark:text-cyan-300',
    status: 'text-cyan-600 dark:text-cyan-400'
  }
};

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  icon: Icon,
  colorScheme = 'primary',
  badge,
  statusText,
  actions = [],
  className
}) => {
  const scheme = colorSchemes[colorScheme];

  return (
    <div className={cn(
      'border-b backdrop-blur-sm',
      scheme.background,
      scheme.border,
      className
    )}>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          {/* Left side - Title and info */}
          <div className="flex items-center space-x-4 min-w-0 flex-1">
            {Icon && (
              <div className={cn(
                'p-3 rounded-xl shadow-sm border border-white/20 dark:border-gray-800/20 bg-white/50 dark:bg-gray-900/50',
                scheme.icon
              )}>
                <Icon className="h-8 w-8" />
              </div>
            )}
            
            <div className="min-w-0 flex-1">
              <div className="flex items-center space-x-3 mb-1">
                <h1 className={cn(
                  'text-3xl font-bold tracking-tight',
                  scheme.title
                )}>
                  {title}
                </h1>
                
                {badge && (
                  <Badge 
                    variant={badge.variant || 'default'}
                    className="bg-white/80 dark:bg-gray-800/80 border-white/20 dark:border-gray-700/20"
                  >
                    {badge.text}
                  </Badge>
                )}
              </div>
              
              {subtitle && (
                <p className={cn(
                  'text-lg leading-relaxed max-w-3xl',
                  scheme.subtitle
                )}>
                  {subtitle}
                </p>
              )}
              
              {statusText && (
                <div className={cn(
                  'text-sm mt-2 font-medium',
                  scheme.status
                )}>
                  {statusText}
                </div>
              )}
            </div>
          </div>

          {/* Right side - Actions */}
          {actions.length > 0 && (
            <div className="flex items-center space-x-3 flex-shrink-0">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'outline'}
                  onClick={action.onClick}
                  disabled={action.disabled || action.loading}
                  className="flex items-center space-x-2 bg-white/70 dark:bg-gray-800/70 hover:bg-white dark:hover:bg-gray-800 border-white/40 dark:border-gray-700/40"
                >
                  {action.icon && (
                    <action.icon className={cn(
                      'h-4 w-4',
                      action.loading && 'animate-spin'
                    )} />
                  )}
                  <span>{action.label}</span>
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PageHeader;
