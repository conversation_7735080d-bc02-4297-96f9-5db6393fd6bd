
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAppContext } from '@/context/AppContext';
import { supabase } from '@/integrations/supabase/client';
import { motion, AnimatePresence } from 'framer-motion';
import NavigationSection from './NavigationSection';

interface NavigationPanelProps {
  isAuthenticated: boolean;
  isAdmin: boolean;
  onClose: () => void;
}

const NavigationPanel: React.FC<NavigationPanelProps> = ({ isAuthenticated, isAdmin, onClose }) => {
  const { language } = useAppContext();
  const navigate = useNavigate();

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);
  
  const handleNavigateToNavPage = () => {
    navigate('/navigation');
    onClose();
  };

  return (
    <AnimatePresence>
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex"
      >
        <motion.div 
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -20, opacity: 0 }}
          className="bg-background border rounded-lg shadow-lg w-full max-w-6xl mx-auto my-8 p-0 max-h-[85vh] overflow-hidden"
        >
          <div className="flex justify-between items-center p-4 border-b">
            <h2 className="text-2xl font-bold">
              {language === 'en' ? 'Navigation Directory' : '导航目录'}
            </h2>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleNavigateToNavPage}>
                {language === 'en' ? 'Full Navigation Page' : '完整导航页面'}
              </Button>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-5 w-5" />
                <span className="sr-only">Close</span>
              </Button>
            </div>
          </div>
          
          <div className="h-[calc(85vh-70px)] overflow-y-auto">
            <NavigationSection />
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default NavigationPanel;
