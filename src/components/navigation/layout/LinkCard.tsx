
import React from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Star, Clock, LinkIcon, TrendingUp, Mouse<PERSON>ointer } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAppContext } from '@/context/AppContext';
import { NavLink } from '@/components/navigation/types';

interface LinkCardProps {
  link: NavLink;
  onLinkClick?: (linkId: string) => void;
}

const LinkCard: React.FC<LinkCardProps> = ({ link, onLinkClick }) => {
  const { language } = useAppContext();

  const getFavicon = (url: string): string => {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
    } catch (e) {
      return '/placeholder.svg';
    }
  };

  const handleClick = () => {
    // 记录点击
    if (onLinkClick) {
      onLinkClick(link.id);
    }
    
    // 打开链接
    window.open(link.url, '_blank', 'noopener,noreferrer');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <motion.div
      whileHover={{ 
        y: -4, 
        boxShadow: "0 8px 25px -8px rgba(0, 0, 0, 0.1)",
        scale: 1.01
      }}
      transition={{ duration: 0.2, ease: "easeOut" }}
      className="h-full"
    >
      <Card 
        className="h-full cursor-pointer group relative overflow-hidden bg-white border border-slate-200 shadow-sm hover:shadow-md transition-all duration-200 hover:border-slate-300"
        onClick={handleClick}
      >
        <CardContent className="p-4 h-full flex flex-col">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <div className="w-8 h-8 flex-shrink-0 bg-slate-50 rounded-lg flex items-center justify-center group-hover:bg-slate-100 transition-colors">
                {link.icon ? (
                  <img 
                    src={link.icon} 
                    alt={link.name} 
                    className="w-5 h-5 object-contain"
                    onError={(e) => {
                      e.currentTarget.src = getFavicon(link.url);
                    }}
                  />
                ) : (
                  <LinkIcon className="w-4 h-4 text-slate-600" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-slate-900 truncate group-hover:text-blue-600 transition-colors text-sm">
                  {link.name}
                </h3>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              {link.popularity && link.popularity > 0 && (
                <Badge variant="secondary" className="bg-orange-50 text-orange-600 border-0 text-xs px-1 py-0">
                  {link.popularity}
                </Badge>
              )}
              <ExternalLink className="w-3 h-3 text-slate-400 group-hover:text-blue-500 transition-colors opacity-60 group-hover:opacity-100" />
            </div>
          </div>

          <div className="flex-1 mb-3">
            <p className="text-slate-600 text-xs leading-relaxed line-clamp-2">
              {link.description || 
                (language === 'en' ? 'Click to explore this resource' : '点击探索这个资源')
              }
            </p>
          </div>

          <div className="flex items-center justify-between text-xs text-slate-500 pt-2 border-t border-slate-100">
            <div className="flex items-center space-x-1">
              {link.is_internal ? (
                <Badge variant="secondary" className="text-xs bg-green-50 text-green-600 border-0 px-1 py-0">
                  {language === 'en' ? '🏠' : '🏠'}
                </Badge>
              ) : (
                <Badge variant="outline" className="text-xs border-slate-200 px-1 py-0">
                  {language === 'en' ? '🌐' : '🌐'}
                </Badge>
              )}
              
              {link.clicks && link.clicks > 0 && (
                <Badge variant="secondary" className="bg-blue-50 text-blue-600 border-0 text-xs px-1 py-0">
                  <MousePointer className="w-2 h-2 mr-0.5" />
                  {link.clicks}
                </Badge>
              )}
            </div>
            
            {link.created_at && (
              <div className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                <span className="text-xs">{formatDate(link.created_at)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default LinkCard;
