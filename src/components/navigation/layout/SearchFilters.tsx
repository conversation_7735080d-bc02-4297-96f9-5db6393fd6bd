
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/navigation/types';

interface SearchFiltersProps {
  categories: Category[];
  selectedCategory: string | null;
  onCategoryChange: (categoryId: string) => void;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  categories,
  selectedCategory,
  onCategoryChange
}) => {
  const { language } = useAppContext();

  const mainCategories = categories.filter(cat => !cat.parent_id);

  return (
    <Card className="mt-4 bg-white/30 dark:bg-slate-800/30 backdrop-blur-sm border-slate-200 dark:border-slate-700">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div>
            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              {language === 'en' ? 'Filter by Category' : '按分类筛选'}
            </h4>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={!selectedCategory ? 'default' : 'secondary'}
                className="cursor-pointer"
                onClick={() => onCategoryChange('')}
              >
                {language === 'en' ? 'All' : '全部'}
              </Badge>
              {mainCategories.map((category) => (
                <Badge
                  key={category.id}
                  variant={selectedCategory === category.id ? 'default' : 'secondary'}
                  className="cursor-pointer"
                  onClick={() => onCategoryChange(category.id)}
                >
                  {category.name}
                  {category.link_count !== undefined && (
                    <span className="ml-1 opacity-70">({category.link_count})</span>
                  )}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchFilters;
