
import React from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Star, Clock, LinkIcon, TrendingUp, Mouse<PERSON>ointer } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAppContext } from '@/context/AppContext';
import { NavLink } from '@/components/navigation/types';

interface LinkListItemProps {
  link: NavLink;
  compact?: boolean;
  onLinkClick?: (linkId: string) => void;
}

const LinkListItem: React.FC<LinkListItemProps> = ({ link, compact = false, onLinkClick }) => {
  const { language } = useAppContext();

  const getFavicon = (url: string): string => {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
    } catch (e) {
      return '/placeholder.svg';
    }
  };

  const handleClick = () => {
    // 记录点击
    if (onLinkClick) {
      onLinkClick(link.id);
    }
    
    // 打开链接
    window.open(link.url, '_blank', 'noopener,noreferrer');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  if (compact) {
    return (
      <motion.div
        whileHover={{ scale: 1.01, x: 2 }}
        transition={{ duration: 0.15 }}
      >
        <Card 
          className="cursor-pointer group bg-white border border-slate-200 shadow-sm hover:shadow-md transition-all duration-150 hover:border-slate-300"
          onClick={handleClick}
        >
          <CardContent className="p-2">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 flex-shrink-0 bg-slate-50 rounded-md flex items-center justify-center">
                {link.icon ? (
                  <img 
                    src={link.icon} 
                    alt={link.name} 
                    className="w-4 h-4 object-contain"
                    onError={(e) => {
                      e.currentTarget.src = getFavicon(link.url);
                    }}
                  />
                ) : (
                  <LinkIcon className="w-3 h-3 text-slate-600" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <span className="text-xs font-medium text-slate-900 truncate group-hover:text-blue-600 transition-colors">
                  {link.name}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                {link.popularity && link.popularity > 0 && (
                  <Badge variant="secondary" className="bg-orange-50 text-orange-600 text-xs border-0 px-1 py-0">
                    {link.popularity}
                  </Badge>
                )}
                {link.clicks && link.clicks > 0 && (
                  <Badge variant="secondary" className="bg-blue-50 text-blue-600 border-0 text-xs px-1 py-0">
                    <MousePointer className="w-2 h-2 mr-0.5" />
                    {link.clicks}
                  </Badge>
                )}
                <ExternalLink className="w-3 h-3 text-slate-400 group-hover:text-blue-500 transition-colors flex-shrink-0" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      whileHover={{ x: 2, boxShadow: "0 4px 12px -4px rgba(0, 0, 0, 0.1)" }}
      transition={{ duration: 0.15 }}
    >
      <Card 
        className="cursor-pointer group bg-white border border-slate-200 shadow-sm hover:shadow-md transition-all duration-200 hover:border-slate-300"
        onClick={handleClick}
      >
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className="w-8 h-8 flex-shrink-0 bg-slate-50 rounded-lg flex items-center justify-center group-hover:bg-slate-100 transition-colors">
                {link.icon ? (
                  <img 
                    src={link.icon} 
                    alt={link.name} 
                    className="w-5 h-5 object-contain"
                    onError={(e) => {
                      e.currentTarget.src = getFavicon(link.url);
                    }}
                  />
                ) : (
                  <LinkIcon className="w-4 h-4 text-slate-600" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-slate-900 truncate group-hover:text-blue-600 transition-colors text-sm mb-0.5">
                  {link.name}
                </h3>
                {link.description && (
                  <p className="text-xs text-slate-600 truncate">
                    {link.description}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-3 flex-shrink-0">
              <div className="flex items-center space-x-2 text-xs text-slate-500">
                {link.is_internal ? (
                  <Badge variant="secondary" className="text-xs bg-green-50 text-green-600 border-0 px-1 py-0">
                    {language === 'en' ? '🏠' : '🏠'}
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-xs border-slate-200 px-1 py-0">
                    {language === 'en' ? '🌐' : '🌐'}
                  </Badge>
                )}
                
                {link.popularity && link.popularity > 0 && (
                  <Badge variant="secondary" className="bg-orange-50 text-orange-600 border-0 text-xs px-1 py-0">
                    {link.popularity}
                  </Badge>
                )}

                {link.clicks && link.clicks > 0 && (
                  <Badge variant="secondary" className="bg-blue-50 text-blue-600 border-0 text-xs px-1 py-0">
                    <MousePointer className="w-2 h-2 mr-0.5" />
                    {link.clicks}
                  </Badge>
                )}
                
                {link.created_at && (
                  <div className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    <span className="text-xs">{formatDate(link.created_at)}</span>
                  </div>
                )}
              </div>
              
              <ExternalLink className="w-4 h-4 text-slate-400 group-hover:text-blue-500 transition-colors" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default LinkListItem;
