import React from 'react';
import { motion } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { useAppContext } from '@/context/AppContext';
import { Category, NavLink } from '@/components/navigation/types';
import { ViewMode } from './NavigationLayout';
import LinkCard from './LinkCard';
import LinkListItem from './LinkListItem';
import EmptyState from '../EmptyState';

interface LinkDisplayProps {
  links: NavLink[];
  viewMode: ViewMode;
  isLoading: boolean;
  selectedCategory: Category | null;
  showSubmitButton?: boolean;
  onLinkClick?: (linkId: string) => void;
}

const LinkDisplay: React.FC<LinkDisplayProps> = ({
  links,
  viewMode,
  isLoading,
  selectedCategory,
  showSubmitButton = false,
  onLinkClick
}) => {
  const { language } = useAppContext();

  if (isLoading) {
    return (
      <div className="space-y-3">
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
            {Array.from({ length: 24 }).map((_, index) => (
              <Skeleton key={index} className="h-24 rounded-lg" />
            ))}
          </div>
        ) : viewMode === 'compact' ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
            {Array.from({ length: 16 }).map((_, index) => (
              <Skeleton key={index} className="h-12 rounded-lg" />
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {Array.from({ length: 12 }).map((_, index) => (
              <Skeleton key={index} className="h-16 rounded-lg" />
            ))}
          </div>
        )}
      </div>
    );
  }

  if (links.length === 0) {
    return (
      <EmptyState 
        selectedCategory={selectedCategory}
        showSubmitButton={showSubmitButton}
      />
    );
  }

  const renderLinks = () => {
    switch (viewMode) {
      case 'grid':
        return (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
            {links.map((link, index) => (
              <motion.div
                key={link.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.02 }}
                className="h-32"
              >
                <LinkCard link={link} onLinkClick={onLinkClick} />
              </motion.div>
            ))}
          </div>
        );

      case 'list':
        return (
          <div className="space-y-2">
            {links.map((link, index) => (
              <motion.div
                key={link.id}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.01 }}
              >
                <LinkListItem link={link} onLinkClick={onLinkClick} />
              </motion.div>
            ))}
          </div>
        );

      case 'compact':
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
            {links.map((link, index) => (
              <motion.div
                key={link.id}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.15, delay: index * 0.01 }}
              >
                <LinkListItem link={link} compact onLinkClick={onLinkClick} />
              </motion.div>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-slate-900">
            {selectedCategory?.name || (language === 'en' ? 'All Links' : '所有链接')}
          </h2>
          <p className="text-xs text-slate-600">
            {links.length} {language === 'en' ? 'links available' : '个链接可用'}
          </p>
        </div>
      </div>

      {renderLinks()}
    </div>
  );
};

export default LinkDisplay;
