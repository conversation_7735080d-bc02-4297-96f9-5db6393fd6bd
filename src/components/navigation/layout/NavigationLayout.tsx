import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter, Grid, List, LayoutGrid, Star, TrendingUp, Clock, Hash, MousePointer, Compass } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import LinkDisplay from './LinkDisplay';
import { Category, NavLink } from '@/components/navigation/types';
import { isUsingSupabase } from '@/config/backend';
import apiClient from '@/services/api';
import UnifiedHeader from '@/components/common/UnifiedHeader';
import PageWrapper from '@/components/layout/PageWrapper';

export type ViewMode = 'grid' | 'list' | 'compact';
export type SortMode = 'name' | 'created' | 'popularity' | 'recent' | 'clicks';

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }

  try {
    const { supabase } = await import('@/integrations/supabase/client');
    return supabase;
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

interface NavigationLayoutProps {
  showSubmitButton?: boolean;
}

const NavigationLayout: React.FC<NavigationLayoutProps> = ({ showSubmitButton = false }) => {
  const { language } = useAppContext();

  const [categories, setCategories] = useState<Category[]>([]);
  const [links, setLinks] = useState<NavLink[]>([]);
  const [filteredLinks, setFilteredLinks] = useState<NavLink[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortMode, setSortMode] = useState<SortMode>('popularity');
  const [isLoading, setIsLoading] = useState(true);
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  // 记录链接点击
  const handleLinkClick = async (linkId: string) => {
    try {
      if (isUsingSupabase()) {
        // 使用 Supabase
        const supabase = await getSupabaseClient();
        const { error } = await supabase.rpc('increment_link_clicks', {
          link_id: linkId
        });

        if (error) {
          console.error('Error updating click count:', error);
        }
      } else {
        // 使用 Go 后端
        try {
          await apiClient.post(`/navigation/links/${linkId}/click`);
        } catch (error) {
          console.error('Error updating click count via Go backend:', error);
        }
      }
    } catch (error) {
      console.error('Error in handleLinkClick:', error);
    }
  };

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        if (isUsingSupabase()) {
          // 使用 Supabase
          const supabase = await getSupabaseClient();
          const { data, error } = await supabase
            .from('nav_categories')
            .select('*')
            .is('user_id', null)
            .eq('is_public', true)
            .order('sort_order', { ascending: true });

          if (error) {
            console.error('Error fetching categories:', error);
            return;
          }

          if (data) {
            // 计算每个分类的链接数量
            const categoriesWithCount = await Promise.all(
              data.map(async (category) => {
                const { count } = await supabase
                  .from('nav_links')
                  .select('*', { count: 'exact', head: true })
                  .eq('category_id', category.id);

                return { ...category, link_count: count || 0 };
              })
            );

            setCategories(categoriesWithCount);
          }
        } else {
          // 使用 Go 后端
          const response = await apiClient.get('/navigation/categories?is_public=true&user_id=null');
          const data = response.data;

          if (data && Array.isArray(data)) {
            // 计算每个分类的链接数量
            const categoriesWithCount = await Promise.all(
              data.map(async (category) => {
                try {
                  const linksResponse = await apiClient.get(`/navigation/links?category_id=${category.id}`);
                  const links = linksResponse.data || [];
                  return { ...category, link_count: links.length };
                } catch (error) {
                  console.error(`Error fetching links for category ${category.id}:`, error);
                  return { ...category, link_count: 0 };
                }
              })
            );

            setCategories(categoriesWithCount);
          }
        }
      } catch (error) {
        console.error('Error in fetchCategories:', error);
      }
    };

    fetchCategories();
  }, []);

  // 获取链接数据
  useEffect(() => {
    const fetchLinks = async () => {
      setIsLoading(true);
      try {
        if (isUsingSupabase()) {
          // 使用 Supabase
          const supabase = await getSupabaseClient();
          let query = supabase
            .from('nav_links')
            .select('*')
            .order('sort_order', { ascending: true });

          if (selectedCategory) {
            query = query.eq('category_id', selectedCategory);
          }

          const { data, error } = await query;

          if (error) {
            console.error('Error fetching links:', error);
            return;
          }

          if (data) {
            setLinks(data);
          }
        } else {
          // 使用 Go 后端
          let url = '/navigation/links';
          if (selectedCategory) {
            url += `?category_id=${selectedCategory}`;
          }

          const response = await apiClient.get(url);
          const data = response.data;

          if (data && Array.isArray(data)) {
            setLinks(data);
          }
        }
      } catch (error) {
        console.error('Error in fetchLinks:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLinks();
  }, [selectedCategory]);

  // 过滤和排序链接
  useEffect(() => {
    let filtered = [...links];

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(link =>
        link.name.toLowerCase().includes(query) ||
        link.url.toLowerCase().includes(query) ||
        (link.description && link.description.toLowerCase().includes(query))
      );
    }

    filtered.sort((a, b) => {
      switch (sortMode) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'created':
          return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
        case 'popularity':
          return (b.popularity || 0) - (a.popularity || 0);
        case 'recent':
          return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
        case 'clicks':
          return (b.clicks || 0) - (a.clicks || 0);
        default:
          return 0;
      }
    });

    setFilteredLinks(filtered);
  }, [links, searchQuery, sortMode]);

  const selectedCategoryData = categories.find(cat => cat.id === selectedCategory);

  const handleCategorySelect = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
    setSearchQuery('');
  };

  const getSortIcon = (sort: SortMode) => {
    switch (sort) {
      case 'popularity': return <TrendingUp className="w-4 h-4" />;
      case 'created': return <Clock className="w-4 h-4" />;
      case 'recent': return <Clock className="w-4 h-4" />;
      case 'clicks': return <MousePointer className="w-4 h-4" />;
      default: return <Hash className="w-4 h-4" />;
    }
  };

  const headerContent = (
    <UnifiedHeader
      title={language === 'en' ? 'Navigation Hub' : '导航中心'}
      description={language === 'en'
        ? 'Discover amazing websites and tools'
        : '发现优秀网站和工具'}
      icon={Compass}
      variant="gradient"
      gradientFrom="from-blue-600"
      gradientTo="to-purple-600"
      layout="default"
    />
  );

  return (
    <PageWrapper headerContent={headerContent} className="bg-white">
      <div className="container mx-auto px-4 py-4">

        {/* 搜索和筛选区域 */}
        <motion.div
          className="mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="p-3 bg-white/95 backdrop-blur-sm border shadow-sm">
            {/* 搜索和控制栏 */}
            <div className="flex flex-col lg:flex-row gap-2 items-center mb-3">
              <div className="relative flex-1 w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder={language === 'en' ? 'Search websites and tools...' : '搜索网站和工具...'}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-10 bg-slate-50 border-slate-200 focus:border-blue-400 text-base"
                />
              </div>

              <div className="flex items-center gap-2 w-full lg:w-auto">
                {/* 视图切换 */}
                <div className="flex items-center border border-slate-200 rounded-md bg-slate-50 p-1">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="h-8 px-3 text-sm"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="h-8 px-3 text-sm"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'compact' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('compact')}
                    className="h-8 px-3 text-sm"
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                </div>

                {/* 排序选择 */}
                <select
                  value={sortMode}
                  onChange={(e) => setSortMode(e.target.value as SortMode)}
                  className="h-10 px-3 rounded-md border border-slate-200 bg-slate-50 text-sm min-w-24"
                >
                  <option value="popularity">{language === 'en' ? '🔥 Hot' : '🔥 热门'}</option>
                  <option value="clicks">{language === 'en' ? '👆 Clicks' : '👆 点击'}</option>
                  <option value="recent">{language === 'en' ? '🕒 New' : '🕒 最新'}</option>
                  <option value="name">{language === 'en' ? '📝 A-Z' : '📝 名称'}</option>
                  <option value="created">{language === 'en' ? '📅 Date' : '📅 时间'}</option>
                </select>

                {/* 移动端筛选按钮 */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowMobileFilters(!showMobileFilters)}
                  className="lg:hidden h-10 px-3"
                >
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 分类筛选 */}
            <AnimatePresence>
              {(showMobileFilters || window.innerWidth >= 1024) && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="border-t border-slate-100 pt-2"
                >
                  <div className="flex flex-wrap gap-1.5">
                    <Badge
                      variant={!selectedCategory ? 'default' : 'secondary'}
                      className={`cursor-pointer px-3 py-1.5 text-sm transition-all hover:scale-105 ${
                        !selectedCategory
                          ? 'bg-blue-500 text-white shadow-sm'
                          : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                      }`}
                      onClick={() => handleCategorySelect(null)}
                    >
                      ✨ {language === 'en' ? 'All' : '全部'}
                      <span className="ml-1 opacity-80">({links.length})</span>
                    </Badge>

                    {categories.map((category) => (
                      <Badge
                        key={category.id}
                        variant={selectedCategory === category.id ? 'default' : 'secondary'}
                        className={`cursor-pointer px-3 py-1.5 text-sm transition-all hover:scale-105 ${
                          selectedCategory === category.id
                            ? 'bg-blue-500 text-white shadow-sm'
                            : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                        }`}
                        onClick={() => handleCategorySelect(category.id)}
                      >
                        {category.name}
                        {category.link_count !== undefined && (
                          <span className="ml-1 opacity-80">({category.link_count})</span>
                        )}
                      </Badge>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </Card>
        </motion.div>

        {/* 统计信息 */}
        {filteredLinks.length > 0 && (
          <motion.div
            className="mb-2 flex items-center justify-between"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-white px-2 py-0.5 text-xs">
                {getSortIcon(sortMode)}
                <span className="ml-1">
                  {filteredLinks.length} {language === 'en' ? 'results' : '个结果'}
                </span>
              </Badge>
              {searchQuery && (
                <Badge variant="outline" className="bg-blue-50 text-blue-700 px-2 py-0.5 text-xs">
                  <Search className="w-3 h-3 mr-1" />
                  "{searchQuery}"
                </Badge>
              )}
            </div>
          </motion.div>
        )}

        {/* 链接展示区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <LinkDisplay
            links={filteredLinks}
            viewMode={viewMode}
            isLoading={isLoading}
            selectedCategory={selectedCategoryData}
            showSubmitButton={showSubmitButton}
            onLinkClick={handleLinkClick}
          />
        </motion.div>
      </div>
    </PageWrapper>
  );
};

export default NavigationLayout;
