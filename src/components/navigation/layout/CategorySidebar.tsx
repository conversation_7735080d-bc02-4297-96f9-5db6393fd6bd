
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, ChevronDown, Folder, FolderOpen, Hash } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/navigation/types';

interface CategorySidebarProps {
  categories: Category[];
  subcategories: Category[];
  selectedCategory: string | null;
  selectedSubcategory: string | null;
  onCategorySelect: (categoryId: string) => void;
  onSubcategorySelect: (subcategoryId: string | null) => void;
}

const CategorySidebar: React.FC<CategorySidebarProps> = ({
  categories,
  subcategories,
  selectedCategory,
  selectedSubcategory,
  onCategorySelect,
  onSubcategorySelect
}) => {
  const { language } = useAppContext();
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set([selectedCategory].filter(Boolean)));

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const handleCategoryClick = (categoryId: string) => {
    onCategorySelect(categoryId);
    onSubcategorySelect(null);
    
    // Auto-expand when selecting a category
    if (!expandedCategories.has(categoryId)) {
      toggleCategory(categoryId);
    }
  };

  const handleSubcategoryClick = (subcategoryId: string) => {
    onSubcategorySelect(subcategoryId);
  };

  const mainCategories = categories.filter(cat => !cat.parent_id);

  return (
    <Card className="w-64 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-slate-200 dark:border-slate-700">
      <CardContent className="p-4">
        <h3 className="font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
          <Hash className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Categories' : '分类'}
        </h3>
        
        <div className="space-y-1">
          {mainCategories.map((category) => {
            const isSelected = selectedCategory === category.id;
            const isExpanded = expandedCategories.has(category.id);
            const categorySubcategories = categories.filter(cat => cat.parent_id === category.id);
            const hasSubcategories = categorySubcategories.length > 0;

            return (
              <motion.div key={category.id} layout>
                <div
                  className={`
                    flex items-center justify-between p-2 rounded-lg cursor-pointer transition-all
                    ${isSelected 
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' 
                      : 'hover:bg-slate-100 dark:hover:bg-slate-700/50 text-slate-700 dark:text-slate-300'
                    }
                  `}
                  onClick={() => handleCategoryClick(category.id)}
                >
                  <div className="flex items-center flex-1 min-w-0">
                    {hasSubcategories ? (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleCategory(category.id);
                        }}
                        className="p-1 mr-1 hover:bg-slate-200 dark:hover:bg-slate-600 rounded"
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-3 w-3" />
                        ) : (
                          <ChevronRight className="h-3 w-3" />
                        )}
                      </button>
                    ) : (
                      <div className="w-5" />
                    )}
                    
                    {isExpanded && hasSubcategories ? (
                      <FolderOpen className="h-4 w-4 mr-2 flex-shrink-0" />
                    ) : (
                      <Folder className="h-4 w-4 mr-2 flex-shrink-0" />
                    )}
                    
                    <span className="font-medium truncate">{category.name}</span>
                  </div>
                  
                  {category.link_count !== undefined && (
                    <Badge variant="secondary" className="ml-2 bg-white/50 dark:bg-slate-700/50">
                      {category.link_count}
                    </Badge>
                  )}
                </div>

                {/* Subcategories */}
                <AnimatePresence>
                  {isExpanded && hasSubcategories && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="ml-6 mt-1 space-y-1 overflow-hidden"
                    >
                      {categorySubcategories.map((subcategory) => {
                        const isSubSelected = selectedSubcategory === subcategory.id;
                        
                        return (
                          <motion.div
                            key={subcategory.id}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            className={`
                              flex items-center justify-between p-2 pl-4 rounded-lg cursor-pointer transition-all text-sm
                              ${isSubSelected 
                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                                : 'hover:bg-slate-50 dark:hover:bg-slate-700/30 text-slate-600 dark:text-slate-400'
                              }
                            `}
                            onClick={() => handleSubcategoryClick(subcategory.id)}
                          >
                            <div className="flex items-center flex-1 min-w-0">
                              <div className="w-2 h-2 bg-slate-300 dark:bg-slate-600 rounded-full mr-2 flex-shrink-0" />
                              <span className="truncate">{subcategory.name}</span>
                            </div>
                            
                            {subcategory.link_count !== undefined && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                {subcategory.link_count}
                              </Badge>
                            )}
                          </motion.div>
                        );
                      })}
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            );
          })}
        </div>

        {mainCategories.length === 0 && (
          <div className="text-center py-8 text-slate-500 dark:text-slate-400">
            <Folder className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              {language === 'en' ? 'No categories available' : '暂无分类'}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CategorySidebar;
