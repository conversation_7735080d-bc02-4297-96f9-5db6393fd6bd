
import React from 'react';
import { useAppContext } from '@/context/AppContext';

const LoadingState: React.FC = () => {
  const { language } = useAppContext();
  
  return (
    <div className="py-8 text-center">
      <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
      <p className="mt-4 text-muted-foreground">
        {language === 'en' ? 'Loading resources...' : '正在加载资源...'}
      </p>
    </div>
  );
};

export default LoadingState;
