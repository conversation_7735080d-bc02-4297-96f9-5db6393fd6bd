
import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ErrorDisplayProps {
  message: string;
  onRetry: () => void;
  language: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ message, onRetry, language }) => (
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
    <p className="text-lg font-medium mb-4">{message}</p>
    <Button onClick={onRetry} variant="outline" className="gap-2">
      <RefreshCw className="h-4 w-4" />
      {language === 'en' ? 'Retry' : '重试'}
    </Button>
  </div>
);

export default ErrorDisplay;
