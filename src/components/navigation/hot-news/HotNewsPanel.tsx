import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, TrendingUp, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAppContext } from '@/context/AppContext';
import { useHotNews } from '@/hooks/hot-news';
import { useToast } from '@/components/ui/use-toast';
import PlatformSidebar from './PlatformSidebar';
import NewsList from './NewsList';

interface HotNewsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const HotNewsPanel: React.FC<HotNewsPanelProps> = ({ isOpen, onClose }) => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const { 
    platforms, 
    selectedPlatform, 
    setSelectedPlatform, 
    news, 
    isLoadingPlatforms, 
    isLoadingNews,
    error,
    refetchNews,
    refetchPlatforms
  } = useHotNews();

  // Format timestamp to readable date
  const formatTimestamp = (timestamp: string): string => {
    if (!timestamp) return '';
    
    try {
      const date = new Date(parseInt(timestamp));
      return date.toLocaleString(language === 'en' ? 'en-US' : 'zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (err) {
      console.error('Error formatting timestamp:', err);
      return '';
    }
  };

  // Handler for platform change
  const handlePlatformChange = (platformPath: string) => {
    setSelectedPlatform(platformPath);
  };

  // Handler for refreshing a specific platform
  const handleRefreshPlatform = (platformPath: string) => {
    setSelectedPlatform(platformPath);
    
    // Show toast to indicate refresh
    toast({
      description: language === 'en' 
        ? `Refreshing ${platforms.find(p => p.path === platformPath)?.name || 'news'}...` 
        : `正在刷新${platforms.find(p => p.path === platformPath)?.chineseName || '新闻'}...`,
      duration: 2000
    });
    
    refetchNews();
  };

  // Get current platform name
  const getCurrentPlatformName = (): string => {
    const platform = platforms.find(p => p.path === selectedPlatform);
    return language === 'en' ? platform?.name || '' : platform?.chineseName || '';
  };

  // Handle retry
  const handleRetry = () => {
    if (selectedPlatform) {
      toast({
        description: language === 'en' ? 'Retrying...' : '正在重试...',
        duration: 2000
      });
      refetchNews();
    } else {
      refetchPlatforms();
    }
  };

  // Close on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Prevent body scroll when panel is open
  useEffect(() => {
    document.body.style.overflow = isOpen ? 'hidden' : 'auto';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              onClose();
            }
          }}
        >
          <motion.div 
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -20, opacity: 0 }}
            className="bg-background border rounded-lg shadow-lg w-full max-w-6xl mx-auto my-8 p-0 max-h-[85vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-2xl font-bold flex items-center">
                <TrendingUp className="w-6 h-6 mr-2 text-primary" />
                {language === 'en' ? "Today's Hot List" : '今日热榜'}
              </h2>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-5 w-5" />
                <span className="sr-only">Close</span>
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 h-[calc(85vh-70px)]">
              {/* Left sidebar with platforms */}
              <PlatformSidebar
                platforms={platforms}
                selectedPlatform={selectedPlatform}
                onPlatformChange={handlePlatformChange}
                isLoading={isLoadingPlatforms}
                error={error}
                onRetry={refetchPlatforms}
                onRefreshPlatform={handleRefreshPlatform}
                language={language}
              />
              
              {/* Main content area */}
              <div className="col-span-3 flex flex-col h-full">
                {/* Platform header */}
                <div className="p-4 border-b dark:border-gray-700 flex justify-between items-center">
                  <h3 className="text-xl font-medium">
                    {getCurrentPlatformName()} 
                    <span className="ml-2 text-sm text-muted-foreground">
                      {language === 'en' ? 'Hot News' : '热搜'}
                    </span>
                  </h3>
                  {selectedPlatform && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="gap-1"
                      onClick={() => refetchNews()}
                      disabled={isLoadingNews}
                    >
                      <RefreshCw className={`h-4 w-4 ${isLoadingNews ? 'animate-spin' : ''}`} />
                      {language === 'en' ? 'Refresh' : '刷新'}
                    </Button>
                  )}
                </div>
                
                {/* News list */}
                <NewsList
                  news={news}
                  isLoading={isLoadingNews}
                  error={error}
                  onRetry={handleRetry}
                  formatTimestamp={formatTimestamp}
                  language={language}
                />
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default HotNewsPanel;
