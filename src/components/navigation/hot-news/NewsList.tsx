
import React from 'react';
import { motion } from 'framer-motion';
import { Clock, TrendingUp, RefreshCw, ImageOff } from 'lucide-react';
import { NewsItem } from '@/hooks/hot-news/types';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';

interface ErrorDisplayProps {
  message: string;
  onRetry: () => void;
  language: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ message, onRetry, language }) => (
  <div className="flex flex-col items-center justify-center py-8 px-2 text-center">
    <p className="text-muted-foreground mb-4">{message}</p>
    <Button size="sm" onClick={onRetry}>
      <RefreshCw className="h-4 w-4 mr-2" />
      {language === 'en' ? 'Retry' : '重试'}
    </Button>
  </div>
);

interface NewsListProps {
  news: NewsItem[];
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
  formatTimestamp: (timestamp: string) => string;
  language: string;
}

const NewsList: React.FC<NewsListProps> = ({ 
  news, 
  isLoading, 
  error, 
  onRetry, 
  formatTimestamp,
  language
}) => {
  const renderNewsItem = (item: NewsItem, index: number) => (
    <motion.div
      key={`${item.url}-${index}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      className="border-b last:border-b-0 dark:border-gray-700"
    >
      <a 
        href={item.url} 
        target="_blank" 
        rel="noopener noreferrer"
        className="block p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
      >
        <div className="flex flex-col sm:flex-row gap-4">
          {item.cover && (
            <div className="flex-shrink-0 w-full sm:w-24 h-24 overflow-hidden rounded relative">
              <img 
                src={item.cover} 
                alt={item.title} 
                className="w-full h-full object-cover"
                loading="eager"
                onError={(e) => {
                  const target = e.currentTarget;
                  const container = target.parentElement;
                  
                  if (container) {
                    // Create fallback element with ImageOff icon
                    const fallback = document.createElement('div');
                    fallback.className = "w-full h-full flex items-center justify-center bg-slate-100 dark:bg-slate-800";
                    
                    // Use a simple SVG structure for the fallback
                    fallback.innerHTML = `
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" 
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-slate-400">
                        <line x1="2" y1="2" x2="22" y2="22" />
                        <path d="M10.41 10.41a2 2 0 1 1-2.83-2.83" />
                        <line x1="13.5" y1="13.5" x2="6" y2="21" />
                        <rect x="2" y="2" width="20" height="20" rx="5" />
                      </svg>
                    `;
                    
                    // Replace the image with fallback
                    container.replaceChild(fallback, target);
                  }
                }}
                style={{ display: 'block' }} // Ensure image is visible
              />
            </div>
          )}
          <div className="flex-1">
            <h3 className="text-base font-medium mb-2 line-clamp-2">{item.title}</h3>
            
            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              <span className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {formatTimestamp(item.timestamp)}
              </span>
              
              <span className="inline-flex items-center text-primary">
                <TrendingUp className="w-3 h-3 mr-1" />
                {language === 'en' ? 'Trending' : '热门'}
              </span>
            </div>
          </div>
        </div>
      </a>
    </motion.div>
  );

  const renderLoadingItems = () => (
    <div className="p-4 space-y-6">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="flex gap-4 animate-pulse">
          <div className="relative">
            <Skeleton className="h-24 w-24 rounded" />
            <div className="absolute inset-0 flex items-center justify-center">
              <RefreshCw className="h-6 w-6 text-primary/40 animate-spin" />
            </div>
          </div>
          <div className="space-y-3 flex-1">
            <Skeleton className="h-5 w-full" />
            <Skeleton className="h-5 w-3/4" />
            <div className="pt-2">
              <Progress value={Math.random() * 100} className="h-1 w-1/4" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <ScrollArea className="flex-1 h-full">
      {isLoading ? (
        renderLoadingItems()
      ) : error ? (
        <ErrorDisplay 
          message={error || (language === 'en' ? 'Error loading news' : '加载新闻失败')}
          onRetry={onRetry}
          language={language}
        />
      ) : news.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          {language === 'en' ? 'No news available' : '暂无新闻'}
        </div>
      ) : (
        <div>
          {news.map((item, index) => renderNewsItem(item, index))}
        </div>
      )}
    </ScrollArea>
  );
};

export default NewsList;
