import React from 'react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { NewsPlatform } from '@/hooks/hot-news/types';
import { RefreshCw } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';

interface ErrorDisplayProps {
  message: string;
  onRetry: () => void;
  language: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ message, onRetry, language }) => (
  <div className="flex flex-col items-center justify-center py-8 px-2 text-center">
    <p className="text-muted-foreground mb-4">{message}</p>
    <Button size="sm" onClick={onRetry}>
      <RefreshCw className="h-4 w-4 mr-2" />
      {language === 'en' ? 'Retry' : '重试'}
    </Button>
  </div>
);

interface PlatformSidebarProps {
  platforms: NewsPlatform[];
  selectedPlatform: string | null;
  onPlatformChange: (platformPath: string) => void;
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
  onRefreshPlatform: (platformPath: string) => void;
  language: string;
}

const PlatformSidebar: React.FC<PlatformSidebarProps> = ({
  platforms,
  selectedPlatform,
  onPlatformChange,
  isLoading,
  error,
  onRetry,
  onRefreshPlatform,
  language
}) => {
  // 将平台按类别分组
  const groupPlatformsByCategory = () => {
    // 定义平台类别
    const categories = {
      news: {
        title: language === 'en' ? 'News' : '新闻',
        platforms: [] as NewsPlatform[]
      },
      social: {
        title: language === 'en' ? 'Social Media' : '社交媒体',
        platforms: [] as NewsPlatform[]
      },
      tech: {
        title: language === 'en' ? 'Technology' : '科技',
        platforms: [] as NewsPlatform[]
      },
      entertainment: {
        title: language === 'en' ? 'Entertainment' : '娱乐',
        platforms: [] as NewsPlatform[]
      },
      other: {
        title: language === 'en' ? 'Others' : '其他',
        platforms: [] as NewsPlatform[]
      }
    };

    // 根据平台名称或特征进行分类
    platforms.forEach(platform => {
      const name = platform.name.toLowerCase();
      
      if (name.includes('news') || name.includes('baidu') || name.includes('netease') || name.includes('163') || name.includes('36kr') || name.includes('huxiu') || name.includes('ifanr')) {
        categories.news.platforms.push(platform);
      } else if (name.includes('douyin') || name.includes('bilibili') || name.includes('douban') || name.includes('weibo')) {
        categories.social.platforms.push(platform);
      } else if (name.includes('juejin') || name.includes('github') || name.includes('csdn') || name.includes('51cto') || name.includes('ithome')) {
        categories.tech.platforms.push(platform);
      } else if (name.includes('acfun') || name.includes('movie') || name.includes('xijiayi')) {
        categories.entertainment.platforms.push(platform);
      } else {
        categories.other.platforms.push(platform);
      }
    });

    // 按照sort字段排序每个分组内的平台
    Object.values(categories).forEach(category => {
      category.platforms.sort((a, b) => a.sort - b.sort);
    });

    return categories;
  };

  // 渲染平台分组
  const renderPlatformGroups = () => {
    const categories = groupPlatformsByCategory();
    
    return (
      <div className="flex flex-col space-y-4">
        {Object.entries(categories).map(([key, category]) => 
          category.platforms.length > 0 ? (
            <div key={key} className="space-y-2">
              <h4 className="text-xs font-semibold text-muted-foreground px-2 uppercase">
                {category.title}
              </h4>
              <div className="space-y-1">
                {category.platforms.map(platform => (
                  <div key={platform.path} className="flex items-center gap-1">
                    <Button
                      variant={selectedPlatform === platform.path ? "default" : "ghost"}
                      size="sm"
                      className="justify-start w-full truncate"
                      onClick={() => onPlatformChange(platform.path)}
                    >
                      {language === 'en' ? platform.name : platform.chineseName}
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-7 w-7 flex-shrink-0" 
                      onClick={(e) => {
                        e.stopPropagation();
                        onRefreshPlatform(platform.path);
                      }}
                      title={language === 'en' ? 'Refresh' : '刷新'}
                    >
                      <RefreshCw className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          ) : null
        )}
      </div>
    );
  };

  return (
    <div className="border-r dark:border-gray-700 p-4 h-full">
      <h3 className="font-medium mb-4">
        {language === 'en' ? 'Platforms' : '平台'}
      </h3>
      
      {isLoading ? (
        <div className="space-y-2">
          {[1, 2, 3, 4, 5].map((i) => (
            <Skeleton key={i} className="h-8 w-full" />
          ))}
        </div>
      ) : platforms.length === 0 ? (
        <ErrorDisplay 
          message={language === 'en' ? 'Failed to load platforms' : '加载平台失败'} 
          onRetry={onRetry} 
          language={language}
        />
      ) : (
        <ScrollArea className="h-[520px] pr-3">
          {renderPlatformGroups()}
        </ScrollArea>
      )}
    </div>
  );
};

export default PlatformSidebar;
