
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Image, UploadCloud } from 'lucide-react';

interface IconUploaderProps {
  onIconSelected: (iconUrl: string) => void;
  currentIconUrl?: string;
}

const IconUploader: React.FC<IconUploaderProps> = ({ onIconSelected, currentIconUrl }) => {
  const { toast } = useToast();
  const { language } = useAppContext();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [iconUrl, setIconUrl] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  
  const handleSubmit = () => {
    if (!iconUrl) {
      toast({
        variant: "destructive",
        description: language === 'en' 
          ? "Please enter an icon URL" 
          : "请输入图标URL"
      });
      return;
    }
    
    onIconSelected(iconUrl);
    setDialogOpen(false);
  };
  
  return (
    <>
      <Button
        type="button"
        size="icon"
        variant="outline"
        onClick={() => setDialogOpen(true)}
        title={language === 'en' ? 'Upload or add icon' : '上传或添加图标'}
      >
        <Image className="h-4 w-4" />
      </Button>
      
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {language === 'en' ? 'Add Icon' : '添加图标'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="flex items-center">
              <input
                type="text"
                className="flex-1 px-3 py-2 border rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder={language === 'en' ? 'Enter icon URL' : '输入图标URL'}
                value={iconUrl}
                onChange={(e) => setIconUrl(e.target.value)}
              />
              <Button
                type="button"
                variant="default"
                className="rounded-l-none"
                onClick={handleSubmit}
                disabled={isUploading}
              >
                {language === 'en' ? 'Add' : '添加'}
              </Button>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                {language === 'en' 
                  ? 'Or paste a publicly accessible image URL. Recommended size: 64x64px.' 
                  : '或粘贴公开可访问的图像URL。建议尺寸：64x64像素。'}
              </p>
              
              {iconUrl && (
                <div className="mt-4 border rounded-md p-4 text-center">
                  <p className="text-sm mb-2 text-muted-foreground">
                    {language === 'en' ? 'Preview:' : '预览:'}
                  </p>
                  <div className="inline-block p-2 border rounded">
                    <img
                      src={iconUrl}
                      alt="Icon preview"
                      className="w-16 h-16 object-contain"
                      onError={(e) => {
                        e.currentTarget.src = '/placeholder.svg';
                        toast({
                          variant: "destructive",
                          description: language === 'en' 
                            ? "Failed to load image. Please check the URL." 
                            : "无法加载图像。请检查URL。"
                        });
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setDialogOpen(false)}
            >
              {language === 'en' ? 'Cancel' : '取消'}
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={!iconUrl || isUploading}
            >
              {language === 'en' ? 'Add Icon' : '添加图标'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default IconUploader;
