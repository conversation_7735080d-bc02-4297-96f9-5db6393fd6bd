
import React from 'react';
import { motion } from 'framer-motion';
import { Compass, Plus, Search, Star, Settings, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/navigation/types';
import { useNavigate } from 'react-router-dom';

interface EmptyStateProps {
  searchQuery?: string;
  selectedCategory?: Category | null;
  showSubmitButton?: boolean;
}

const EmptyState: React.FC<EmptyStateProps> = ({ 
  searchQuery, 
  selectedCategory,
  showSubmitButton = false 
}) => {
  const { language } = useAppContext();
  const navigate = useNavigate();
  
  const getEmptyContent = () => {
    if (searchQuery) {
      return {
        icon: <Search className="w-12 h-12 text-slate-400" />,
        title: language === 'en' 
          ? `No results found for "${searchQuery}"` 
          : `未找到与"${searchQuery}"相关的结果`,
        description: language === 'en' 
          ? 'Try adjusting your search terms or explore different categories' 
          : '尝试调整搜索关键词或浏览其他分类',
        emoji: '🔍',
        gradient: 'from-amber-400 to-orange-500'
      };
    }
    
    if (selectedCategory) {
      return {
        icon: <Compass className="w-12 h-12 text-slate-400" />,
        title: language === 'en' 
          ? `No links found in "${selectedCategory.name}"` 
          : `在"${selectedCategory.name}"分类中未找到链接`,
        description: language === 'en' 
          ? 'This category is waiting for amazing resources to be added' 
          : '这个分类正在等待优秀资源的加入',
        emoji: '📂',
        gradient: 'from-blue-400 to-purple-500'
      };
    }
    
    return {
      icon: <Star className="w-12 h-12 text-slate-400" />,
      title: language === 'en' 
        ? 'Welcome to Navigation Hub!' 
        : '欢迎来到导航中心！',
      description: language === 'en' 
        ? 'Discover amazing websites and tools, or contribute your favorites' 
        : '发现精彩网站和工具，或分享您的收藏',
      emoji: '🚀',
      gradient: 'from-emerald-400 to-cyan-500'
    };
  };

  const content = getEmptyContent();
  
  const handleNavigateToDashboard = () => {
    navigate('/dashboard?tab=navigation');
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="flex items-center justify-center min-h-96"
    >
      <Card className="max-w-lg w-full mx-auto bg-white border border-slate-200 shadow-lg">
        <div className="p-8 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mb-6 flex justify-center"
          >
            <div className={`w-16 h-16 bg-gradient-to-br ${content.gradient} rounded-2xl flex items-center justify-center shadow-lg`}>
              {content.icon}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="mb-6"
          >
            <div className="text-3xl mb-3">{content.emoji}</div>
            <h3 className="text-xl font-bold text-slate-900 mb-2">
              {content.title}
            </h3>
            <p className="text-slate-600 leading-relaxed text-sm">
              {content.description}
            </p>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-3 justify-center"
          >
            {showSubmitButton && !searchQuery && (
              <Button 
                onClick={handleNavigateToDashboard}
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 shadow-md hover:shadow-lg transition-all duration-300 px-6 py-2"
              >
                <Settings className="w-4 h-4 mr-2" />
                {language === 'en' ? 'Manage Navigation' : '管理导航'}
                <ExternalLink className="w-3 h-3 ml-2" />
              </Button>
            )}

            {searchQuery && (
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
                className="border-slate-300 hover:bg-slate-50"
              >
                <Search className="w-4 h-4 mr-2" />
                {language === 'en' ? 'Clear Search' : '清空搜索'}
              </Button>
            )}
            
            {!searchQuery && !showSubmitButton && (
              <Button 
                variant="outline"
                onClick={() => window.location.reload()}
                className="border-slate-300 hover:bg-slate-50"
              >
                {language === 'en' ? 'Refresh' : '刷新'}
              </Button>
            )}
          </motion.div>
        </div>
      </Card>
    </motion.div>
  );
};

export default EmptyState;
