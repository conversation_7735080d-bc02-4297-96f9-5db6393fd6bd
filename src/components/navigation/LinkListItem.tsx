
import React from 'react';
import { ArrowUpR<PERSON>, Globe, Star } from 'lucide-react';
import { motion } from 'framer-motion';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Toolt<PERSON>, TooltipContent, Too<PERSON><PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import { useAppContext } from '@/context/AppContext';
import { NavLink } from './types';

interface LinkListItemProps {
  link: NavLink;
  index: number;
  getLinkTypeLabel: (link: NavLink) => string;
  getFavicon: (url: string) => string | null;
  onClick?: () => void; // Add this optional onClick prop
}

const LinkListItem: React.FC<LinkListItemProps> = ({ link, index, getLinkTypeLabel, getFavicon, onClick }) => {
  const { language } = useAppContext();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      window.open(link.url, '_blank');
    }
  };

  return (
    <motion.div
      key={link.id}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.3, 
        delay: index * 0.03,
        ease: [0.4, 0, 0.2, 1]
      }}
      className="border-b last:border-0"
    >
      <div className="p-3 flex items-center hover:bg-muted/30 transition-colors">
        <div className="flex-shrink-0 mr-4">
          {link.icon ? (
            <img 
              src={link.icon} 
              alt={link.name} 
              className="w-8 h-8 object-contain rounded"
              onError={(e) => {
                const favicon = getFavicon(link.url);
                if (favicon) {
                  e.currentTarget.src = favicon;
                } else {
                  e.currentTarget.src = '/placeholder.svg';
                }
              }}
            />
          ) : (
            <div className="w-8 h-8 flex items-center justify-center bg-muted rounded">
              <Globe className="w-4 h-4 text-muted-foreground" />
            </div>
          )}
        </div>
        
        <div className="flex-1 min-w-0 mr-3">
          <div className="flex items-center">
            <h3 className="text-sm font-medium truncate">
              {link.name}
            </h3>
            {link.popularity && link.popularity > 80 && (
              <Badge variant="secondary" className="ml-2 px-1 py-0 h-4">
                <Star className="h-3 w-3 text-amber-500 mr-0.5" />
                <span className="text-[10px]">{link.popularity}</span>
              </Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground truncate mt-0.5">
            {link.description || getLinkTypeLabel(link)}
          </p>
        </div>
        
        <div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className="h-8 w-8 p-0" 
                  onClick={handleClick}
                >
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{language === 'en' ? 'Open Link' : '打开链接'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </motion.div>
  );
};

export default LinkListItem;
