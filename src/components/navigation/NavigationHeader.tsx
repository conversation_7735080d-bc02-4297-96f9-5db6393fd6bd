
import React from 'react';
import { useAppContext } from '@/context/AppContext';
import { Compass } from 'lucide-react';
import PageHeader from '@/components/common/PageHeader';

const NavigationHeader = () => {
  const { language } = useAppContext();
  
  return (
    <PageHeader
      title={language === 'en' ? 'Navigation Directory' : '导航目录'}
      subtitle={language === 'en' 
        ? 'Browse and discover useful tools and resources organized by category.'
        : '浏览和发现按类别组织的实用工具和资源。'}
      icon={Compass}
      colorScheme="primary"
    />
  );
};

export default NavigationHeader;
