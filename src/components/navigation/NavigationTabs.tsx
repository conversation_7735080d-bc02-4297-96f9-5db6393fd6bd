
import React from 'react';
import { useAppContext } from '@/context/AppContext';
import NavigationSection from './NavigationSection';
import { motion } from 'framer-motion';
import { Tabs } from '@/components/ui/tabs'; // Import Tabs component

interface NavigationTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const NavigationTabs: React.FC<NavigationTabsProps> = () => {
  const { language } = useAppContext();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1, duration: 0.4 }}
      className="w-full"
    >
      <Tabs defaultValue="navigation"> {/* Wrap with Tabs component */}
        <NavigationSection />
      </Tabs>
    </motion.div>
  );
};

export default NavigationTabs;
