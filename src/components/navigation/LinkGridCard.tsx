
import React from 'react';
import { ExternalLink, Star, Globe } from 'lucide-react';
import { motion } from 'framer-motion';
import { useAppContext } from '@/context/AppContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { NavLink } from './types';

interface LinkGridCardProps {
  link: NavLink;
  index: number;
  getLinkTypeLabel: (link: NavLink) => string;
  getFavicon: (url: string) => string | null;
  onClick?: () => void; // Add this optional onClick prop
}

const LinkGridCard: React.FC<LinkGridCardProps> = ({ link, index, getLinkTypeLabel, getFavicon, onClick }) => {
  const { language } = useAppContext();

  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <motion.div
      key={link.id}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.3, 
        delay: index * 0.05,
        ease: [0.4, 0, 0.2, 1]
      }}
      whileHover={{ scale: 1.02 }}
    >
      <Card className="h-full overflow-hidden hover:border-primary/50 hover:shadow-md transition-all">
        <a
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          className="block h-full"
          onClick={handleClick}
        >
          <CardHeader className="p-4 pb-2">
            <div className="flex items-start justify-between">
              <div className="flex items-center">
                <div className="mr-3">
                  {link.icon ? (
                    <img 
                      src={link.icon} 
                      alt={link.name} 
                      className="w-10 h-10 object-contain rounded"
                      onError={(e) => {
                        const favicon = getFavicon(link.url);
                        if (favicon) {
                          e.currentTarget.src = favicon;
                        } else {
                          e.currentTarget.src = '/placeholder.svg';
                        }
                      }}
                    />
                  ) : (
                    <div className="w-10 h-10 flex items-center justify-center bg-muted rounded">
                      <Globe className="w-5 h-5 text-muted-foreground" />
                    </div>
                  )}
                </div>
                <div>
                  <CardTitle className="text-base truncate flex items-center">
                    {link.name}
                    {link.popularity && link.popularity > 80 && (
                      <Star className="h-3.5 w-3.5 text-amber-500 ml-1.5" />
                    )}
                  </CardTitle>
                </div>
              </div>
              <Badge variant="outline" className="px-1.5 text-xs">
                {getLinkTypeLabel(link)}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="p-4 pt-1">
            <CardDescription className="line-clamp-2 text-xs min-h-[2.5rem]">
              {link.description || (language === 'en' 
                ? `${link.name} - online tool` 
                : `${link.name} - ${link.is_internal ? '内部' : '外部'}工具`)}
            </CardDescription>
          </CardContent>
          
          <CardFooter className="p-3 pt-0 flex justify-end">
            <div className="text-xs text-muted-foreground flex items-center">
              <span>{language === 'en' ? 'Visit' : '访问'}</span>
              <ExternalLink className="ml-1 h-3 w-3" />
            </div>
          </CardFooter>
        </a>
      </Card>
    </motion.div>
  );
};

export default LinkGridCard;
