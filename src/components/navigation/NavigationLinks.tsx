
import React from 'react';
import { NavLink } from './types';
import Link<PERSON>istItem from './LinkListItem';
import LinkGridCard from './LinkGridCard';
import EmptyState from './EmptyState';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { useAppContext } from '@/context/AppContext';
import { useAuth } from '@/hooks/useAuth';
import LinkSubmissionForm from './LinkSubmissionForm';
import { Skeleton } from '@/components/ui/skeleton';

interface NavigationLinksProps {
  links?: NavLink[];
  isGrid?: boolean;
  onLinkClick?: (link: NavLink) => void;
  isLoading?: boolean;
  showSubmitButton?: boolean;
  hideLinkCount?: boolean;
  categoryId?: string;
  subcategoryId?: string | null;
  searchQuery?: string;
  viewMode?: 'list' | 'grid';
}

const NavigationLinks: React.FC<NavigationLinksProps> = ({
  links = [],
  isGrid = false,
  onLinkClick,
  isLoading = false,
  showSubmitButton = false,
  hideLinkCount = false,
  categoryId,
  subcategoryId,
  searchQuery = '',
  viewMode = 'list',
}) => {
  const { language } = useAppContext();
  const { session } = useAuth();
  const [isSubmitModalOpen, setIsSubmitModalOpen] = React.useState(false);
  
  // Handle category-based links fetching if categoryId is provided
  const [categoryLinks, setCategoryLinks] = React.useState<NavLink[]>([]);
  const [fetchingLinks, setFetchingLinks] = React.useState(false);

  React.useEffect(() => {
    if (categoryId) {
      // This is just a placeholder since we don't have the actual implementation
      // In a real app, we would fetch links based on categoryId and subcategoryId
      setCategoryLinks(links);
      setFetchingLinks(false);
    }
  }, [categoryId, subcategoryId, links]);

  // Use the appropriate source of links
  const displayLinks = categoryId ? categoryLinks : links;
  const displayLoading = categoryId ? fetchingLinks : isLoading;
  
  // Helper function to get favicon from URL
  const getFavicon = (url: string): string | null => {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
    } catch (e) {
      return null;
    }
  };
  
  // Helper function to get link type label
  const getLinkTypeLabel = (link: NavLink): string => {
    if (link.is_internal) {
      return language === 'en' ? 'Internal' : '内部';
    }
    return language === 'en' ? 'External' : '外部';
  };

  // Loading state
  if (displayLoading) {
    return (
      <>
        <div className="flex justify-between items-center mb-4">
          <Skeleton className="h-8 w-40" />
          {showSubmitButton && <Skeleton className="h-10 w-32" />}
        </div>
        <div className={isGrid ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "space-y-2"}>
          {Array.from({ length: 9 }).map((_, index) => (
            <Skeleton key={index} className={isGrid ? "h-24" : "h-14"} />
          ))}
        </div>
      </>
    );
  }

  // Empty state
  if (displayLinks.length === 0) {
    return <EmptyState />;
  }

  const displayMode = viewMode || (isGrid ? 'grid' : 'list');
  
  return (
    <>
      <div className="flex justify-between items-center mb-4">
        {!hideLinkCount && (
          <p className="text-sm text-muted-foreground">
            {language === 'en'
              ? `${displayLinks.length} links available`
              : `${displayLinks.length} 个链接可用`}
          </p>
        )}
        
        {showSubmitButton && (
          <Button 
            onClick={() => setIsSubmitModalOpen(true)}
            className="gap-1"
          >
            {language === 'en' ? 'Submit Link' : '提交链接'}
          </Button>
        )}
      </div>

      <div className={displayMode === 'grid' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "space-y-2"}>
        {displayLinks.map((link, index) => {
          // Ensure link has a description property (add empty string if not present)
          const linkWithDescription = {
            ...link,
            description: link.description || ''
          };
          
          return displayMode === 'grid' ? (
            <LinkGridCard 
              key={link.id} 
              link={linkWithDescription}
              index={index}
              getLinkTypeLabel={getLinkTypeLabel}
              getFavicon={getFavicon}
              onClick={() => onLinkClick && onLinkClick(link)} 
            />
          ) : (
            <LinkListItem 
              key={link.id} 
              link={linkWithDescription} 
              index={index}
              getLinkTypeLabel={getLinkTypeLabel}
              getFavicon={getFavicon}
              onClick={() => onLinkClick && onLinkClick(link)} 
            />
          );
        })}
      </div>
      
      {isSubmitModalOpen && (
        <LinkSubmissionForm 
          open={isSubmitModalOpen} 
          onOpenChange={setIsSubmitModalOpen} 
        />
      )}
    </>
  );
};

export default NavigationLinks;
