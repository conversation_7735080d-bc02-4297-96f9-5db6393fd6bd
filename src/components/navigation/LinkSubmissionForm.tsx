import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import IconUploader from './IconUploader';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';
import { Compass } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface Category {
  id: string;
  name: string;
}

interface LinkSubmissionFormProps {
  onSuccess?: () => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const LinkSubmissionForm: React.FC<LinkSubmissionFormProps> = ({ onSuccess, open, onOpenChange }) => {
  const { toast } = useToast();
  const { language, user } = useAppContext();

  const [categories, setCategories] = useState<Category[]>([]);
  const [categoryId, setCategoryId] = useState<string>('');
  const [name, setName] = useState('');
  const [url, setUrl] = useState('');
  const [icon, setIcon] = useState('');
  const [isInternal, setIsInternal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchCategories = async () => {
      const { data, error } = await supabase
        .from('nav_categories')
        .select('id, name')
        .is('user_id', null)
        .eq('is_public', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }

      if (data) {
        setCategories(data);
        if (data.length > 0) {
          setCategoryId(data[0].id);
        }
      }
    };

    fetchCategories();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !url || !categoryId) {
      toast({
        variant: "destructive",
        description: language === 'en'
          ? "Please fill out all required fields"
          : "请填写所有必填字段"
      });
      return;
    }

    if (!user) {
      toast({
        variant: "destructive",
        description: language === 'en'
          ? "You must be logged in to submit links"
          : "您必须登录才能提交链接"
      });
      return;
    }

    setIsLoading(true);

    try {
      const { error } = await supabase
        .from('nav_links')
        .insert({
          name,
          url,
          icon: icon || null,
          is_internal: isInternal,
          category_id: categoryId,
          submission_status: 'pending',
          submitted_by: user.id
        });

      if (error) {
        console.error('Error submitting link:', error);
        toast({
          variant: "destructive",
          description: language === 'en'
            ? "Failed to submit link. Please try again."
            : "提交链接失败。请重试。"
        });
        setIsLoading(false);
        return;
      }

      toast({
        description: language === 'en'
          ? "Link submitted successfully. It will be reviewed by an administrator."
          : "链接提交成功。它将由管理员审核。"
      });

      setName('');
      setUrl('');
      setIcon('');
      setIsInternal(false);

      if (onSuccess) {
        onSuccess();
      }

      if (onOpenChange) {
        onOpenChange(false);
      }

    } catch (error) {
      console.error('Error:', error);
      toast({
        variant: "destructive",
        description: language === 'en'
          ? "An unexpected error occurred"
          : "发生意外错误"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleIconSelected = (iconUrl: string) => {
    setIcon(iconUrl);
  };

  if (categories.length === 0) {
    return (
      <Alert className="my-4">
        <Compass className="h-4 w-4 mr-2" />
        <AlertTitle>
          {language === 'en'
            ? "No categories available"
            : "没有可用的分类"}
        </AlertTitle>
        <AlertDescription>
          {language === 'en'
            ? "There are no public categories available for submissions at this time."
            : "目前没有可供提交的公共分类。"}
        </AlertDescription>
      </Alert>
    );
  }

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="category">
          {language === 'en' ? 'Category' : '分类'} *
        </Label>
        <select
          id="category"
          value={categoryId}
          onChange={(e) => setCategoryId(e.target.value)}
          className="w-full p-2 rounded-md border"
          required
        >
          {categories.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
        <p className="text-xs text-muted-foreground">
          {language === 'en'
            ? "Select the most appropriate category for your link"
            : "为您的链接选择最合适的分类"}
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="name">
          {language === 'en' ? 'Link Name' : '链接名称'} *
        </Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder={language === 'en' ? 'Enter link name' : '输入链接名称'}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="url">
          {language === 'en' ? 'URL' : '网址'} *
        </Label>
        <Input
          id="url"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder={language === 'en' ? 'https://example.com' : 'https://示例.com'}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="icon">
          {language === 'en' ? 'Icon URL (optional)' : '图标URL（可选）'}
        </Label>
        <div className="flex gap-2 items-center">
          <Input
            id="icon"
            value={icon}
            onChange={(e) => setIcon(e.target.value)}
            placeholder={language === 'en' ? 'Icon URL or leave empty' : '图标URL或留空'}
            className="flex-1"
          />
          <IconUploader
            onIconSelected={handleIconSelected}
            currentIconUrl={icon}
          />
        </div>
        {icon && (
          <div className="mt-2 p-2 border rounded-md w-16 h-16 flex items-center justify-center">
            <img
              src={icon}
              alt="Icon preview"
              className="max-w-full max-h-full"
              onError={(e) => {
                e.currentTarget.src = '/placeholder.svg';
              }}
            />
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="isInternal"
          checked={isInternal}
          onCheckedChange={setIsInternal}
        />
        <Label htmlFor="isInternal">
          {language === 'en' ? 'Internal Link' : '内部链接'}
        </Label>
        <p className="text-xs text-muted-foreground ml-2">
          {language === 'en'
            ? "(Check if this link points to a page within this site)"
            : "（如果此链接指向此站点内的页面，请选中）"}
        </p>
      </div>

      <div className="pt-2">
        <Button
          type="submit"
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? (
            <span className="flex items-center">
              <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></span>
              {language === 'en' ? 'Submitting...' : '提交中...'}
            </span>
          ) : (
            language === 'en' ? 'Submit Link for Review' : '提交链接供审核'
          )}
        </Button>
      </div>
    </form>
  );

  if (open !== undefined && onOpenChange) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {language === 'en' ? 'Submit a New Link' : '提交新链接'}
            </DialogTitle>
          </DialogHeader>
          {formContent}
        </DialogContent>
      </Dialog>
    );
  }

  return formContent;
};

export default LinkSubmissionForm;
