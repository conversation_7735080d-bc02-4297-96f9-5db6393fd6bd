
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { NavLink } from './types';
import { useAppContext } from '@/context/AppContext';
import { useAuth } from '@/hooks/useAuth';
import NavigationLinks from './NavigationLinks';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { AuthModal } from '@/components/auth/AuthModal';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';

const NavigationSection = () => {
  const { language } = useAppContext();
  const { session } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const [personalLinks, setPersonalLinks] = useState<NavLink[]>([]);
  const [recommendedLinks, setRecommendedLinks] = useState<NavLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('personal');

  // 认证弹框状态
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  // Fetch personal and recommended links
  useEffect(() => {
    const fetchLinks = async () => {
      setIsLoading(true);
      try {
        // Fetch personal links if user is logged in
        if (session?.user) {
          const { data: personal, error: personalError } = await supabase
            .from('nav_links')
            .select('*')
            .eq('user_id', session.user.id)
            .order('sort_order', { ascending: true });

          if (personalError) throw personalError;
          setPersonalLinks(personal || []);
        }

        // Fetch recommended (public) links
        const { data: recommended, error: recommendedError } = await supabase
          .from('nav_links')
          .select('*')
          .eq('is_public', true)
          .order('popularity', { ascending: false })
          .limit(10);

        if (recommendedError) throw recommendedError;
        setRecommendedLinks(recommended || []);

      } catch (error) {
        console.error('Error fetching links:', error);
        toast({
          variant: "destructive",
          description: language === 'en'
            ? "Failed to load navigation links"
            : "加载导航链接失败"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchLinks();
  }, [session, language]);

  const handleCreateLink = () => {
    if (!session) {
      setAuthMode('login');
      setIsAuthModalOpen(true);
      return;
    }
    navigate('/dashboard?tab=navigation');
  };

  const handleAuthModalClose = () => {
    setIsAuthModalOpen(false);
  };

  return (
    <Card className="p-6">
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-2xl font-bold">
          {language === 'en' ? 'Navigation Center' : '导航中心'}
        </h2>
        <Button onClick={handleCreateLink}>
          {language === 'en' ? 'Create Link' : '创建链接'}
        </Button>
      </div>

      <Tabs defaultValue="personal" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="w-full justify-start mb-6">
          <TabsTrigger value="personal">
            {language === 'en' ? 'Personal Navigation' : '个人导航'}
          </TabsTrigger>
          <TabsTrigger value="recommended">
            {language === 'en' ? 'Recommended' : '推荐导航'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal">
          <NavigationLinks
            links={personalLinks}
            isLoading={isLoading}
            showSubmitButton={!!session}
            viewMode="grid"
            hideLinkCount={false}
          />
        </TabsContent>

        <TabsContent value="recommended">
          <NavigationLinks
            links={recommendedLinks}
            isLoading={isLoading}
            showSubmitButton={false}
            viewMode="grid"
            hideLinkCount={false}
          />
        </TabsContent>
      </Tabs>

      {/* 认证弹框 */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={handleAuthModalClose}
        defaultMode={authMode}
      />
    </Card>
  );
};

export default NavigationSection;
