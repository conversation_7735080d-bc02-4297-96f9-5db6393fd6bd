export interface NavLink {
  id: string;
  name: string;
  url: string;
  icon: string | null;
  category_id: string;
  sort_order: number;
  is_internal: boolean;
  is_public?: boolean;
  popularity?: number;
  submitted_by?: string;
  submission_status?: string;
  created_at?: string;
  description?: string | null;
  user_id?: string;
  clicks?: number;
}

export interface Category {
  id: string;
  name: string;
  sort_order: number;
  icon?: string;
  created_at: string;
  link_count?: number;
  parent_id?: string | null;
  description?: string | null;
}

export interface NestedCategory extends Category {
  subcategories?: NestedCategory[];
  links?: NavLink[];
}

export interface HotNewsTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: {
      name?: string;
      title?: string;
      newsCount?: number;
      hot?: number;
      platform?: string;
    };
  }>;
  activeTab: string;
  language: string;
}
