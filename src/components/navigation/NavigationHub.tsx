import React, {useEffect, useState} from 'react';
import {useAppContext} from '@/context/AppContext';
import {Category, NavLink} from '@/components/dashboard/navigation/types';
import {getBackendConfig} from '@/config/backend';
import {
    Compass,
    Search,
    Globe,
    Grid,
    List,
    User,
    Users,
    LayoutGrid,
    ExternalLink,
    ChevronRight,
    Star,
    Bookmark,
    Filter,
    BookOpen,
    Code,
    FileText,
    Image,
    Music,
    ShoppingCart,
    Folder,
    Layers,
    AlertCircle
} from 'lucide-react';
import {motion, AnimatePresence} from 'framer-motion';
import {Tabs, TabsList, TabsTrigger, TabsContent} from "@/components/ui/tabs";
import {Input} from '@/components/ui/input';
import {Button} from "@/components/ui/button";
import {Badge} from "@/components/ui/badge";
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card";
import {ScrollArea} from "@/components/ui/scroll-area";
import {Separator} from "@/components/ui/separator";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

interface NavLinkItem extends NavLink {
    popularity?: number;
}

/**
 * 完全重构的现代化导航中心
 */
const NavigationHub: React.FC = () => {
    const {language, user} = useAppContext();

    // 显示模式
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

    // 导航类型选择 - 根据是否登录自动选择默认显示的内容
    const [navType, setNavType] = useState<'all' | 'personal' | 'global'>(user ? 'personal' : 'all');

    // 搜索状态
    const [searchQuery, setSearchQuery] = useState('');

    // 分类过滤器
    const [categoryFilter, setCategoryFilter] = useState<string | null>(null);

    // 数据状态
    const [loading, setLoading] = useState(true);
    const [globalCategories, setGlobalCategories] = useState<Category[]>([]);
    const [personalCategories, setPersonalCategories] = useState<Category[]>([]);

    // 当前选择的分类
    const [activeCategory, setActiveCategory] = useState<string | null>(null);
    const [activeSubCategory, setActiveSubCategory] = useState<string | null>(null);

    // 链接数据
    const [globalLinks, setGlobalLinks] = useState<NavLinkItem[]>([]);
    const [personalLinks, setPersonalLinks] = useState<NavLinkItem[]>([]);
    const [filteredLinks, setFilteredLinks] = useState<NavLinkItem[]>([]);

    // 检查链接状态
    const [linkStatus, setLinkStatus] = useState<Record<string, boolean>>({});
    const [isCheckingLinks, setIsCheckingLinks] = useState(false);
    const [checkedLinksCount, setCheckedLinksCount] = useState(0);

    // 加载所有分类数据
    useEffect(() => {
        const fetchAllCategories = async () => {
            setLoading(true);

            try {
                // 使用Go后端API获取数据
                const config = getBackendConfig();
                const baseURL = config.goBackend?.baseUrl;
                const token = localStorage.getItem('authToken');

                if (!token) {
                    console.log('用户未登录，只获取公共分类');
                    setLoading(false);
                    return;
                }

                // 获取用户分类和公共分类
                const categoriesResponse = await fetch(`${baseURL}/navigation/categories`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (categoriesResponse.ok) {
                    const categories = await categoriesResponse.json();

                    // 分离用户分类和公共分类
                    const userCategories = categories.filter((c: any) => c.user_id !== null);
                    const publicCategories = categories.filter((c: any) => c.user_id === null && c.is_public);

                    setPersonalCategories(userCategories || []);
                    setGlobalCategories(publicCategories || []);

                    // 获取链接
                    const linksResponse = await fetch(`${baseURL}/navigation/links`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });

                    if (linksResponse.ok) {
                        const links = await linksResponse.json();

                        // 分离用户链接和公共链接
                        const userLinks = links.filter((l: any) => l.user_id !== null);
                        const publicLinks = links.filter((l: any) => l.user_id === null && l.is_public);

                        setPersonalLinks(
                            (userLinks || []).map((link: any) => ({
                                ...link,
                                popularity: Math.floor(Math.random() * 100)
                            }))
                        );

                        setGlobalLinks(
                            (publicLinks || []).map((link: any) => ({
                                ...link,
                                popularity: Math.floor(Math.random() * 100)
                            }))
                        );
                    }
                }
            } catch (error) {
                console.error('获取分类数据失败:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchAllCategories();
    }, [user]);

    // 根据用户登录状态设置默认导航类型
    useEffect(() => {
        if (user && personalCategories.length > 0) {
            setNavType('personal');
        }
    }, [user, personalCategories]);

    // 获取所有可见分类
    const getVisibleCategories = () => {
        switch (navType) {
            case 'personal':
                return personalCategories;
            case 'global':
                return globalCategories;
            case 'all':
            default:
                // 优先显示用户的导航，再显示推荐导航
                return [...personalCategories, ...globalCategories];
        }
    };

    // 获取所有父级分类
    const getParentCategories = () => {
        return getVisibleCategories().filter(c => !c.parent_id);
    };

    // 获取特定父级下的子分类
    const getSubCategories = (parentId: string) => {
        return getVisibleCategories().filter(c => c.parent_id === parentId);
    };

    // 获取当前显示的链接
    useEffect(() => {
        let links: NavLinkItem[] = [];

        // 根据导航类型筛选链接
        switch (navType) {
            case 'personal':
                links = [...personalLinks];
                break;
            case 'global':
                links = [...globalLinks];
                break;
            case 'all':
            default:
                // 确保用户的导航优先显示在推荐导航前面
                links = [...personalLinks, ...globalLinks];
                break;
        }

        // 根据分类筛选
        if (activeCategory) {
            const categoryIds = [activeCategory];

            // 如果选中了子分类，只显示子分类的链接
            if (activeSubCategory) {
                links = links.filter(link => link.category_id === activeSubCategory);
            }
            // 如果选中了父分类，显示父分类及其所有子分类的链接
            else {
                const subCategories = getSubCategories(activeCategory);
                if (subCategories.length > 0) {
                    subCategories.forEach(sub => categoryIds.push(sub.id));
                }
                links = links.filter(link => categoryIds.includes(link.category_id));
            }
        }

        // 根据搜索关键词筛选
        if (searchQuery.trim() !== '') {
            const query = searchQuery.toLowerCase();
            links = links.filter(link =>
                link.name.toLowerCase().includes(query) ||
                (link.description && link.description.toLowerCase().includes(query))
            );
        }

        setFilteredLinks(links);
    }, [navType, activeCategory, activeSubCategory, searchQuery, globalLinks, personalLinks]);

    // 获取链接类型标签
    const getLinkTypeLabel = (link: NavLinkItem) => {
        if (link.is_internal) {
            return language === 'en' ? 'Internal' : '内部';
        }

        try {
            const url = new URL(link.url);
            return url.hostname.replace('www.', '');
        } catch (e) {
            return '';
        }
    };

    // 获取网站图标
    const getFavicon = (url: string) => {
        try {
            const urlObj = new URL(url);
            return `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=64`;
        } catch (e) {
            return null;
        }
    };

    // 获取分类图标
    const getCategoryIcon = (category: Category) => {
        const name = category.name.toLowerCase();

        if (name.includes('tool') || name.includes('工具')) return <LayoutGrid className="h-4 w-4"/>;
        if (name.includes('develop') || name.includes('dev') || name.includes('开发')) return <Code
            className="h-4 w-4"/>;
        if (name.includes('design') || name.includes('设计')) return <Image className="h-4 w-4"/>;
        if (name.includes('social') || name.includes('社交')) return <Users className="h-4 w-4"/>;
        if (name.includes('news') || name.includes('新闻')) return <FileText className="h-4 w-4"/>;
        if (name.includes('learn') || name.includes('edu') || name.includes('学习')) return <BookOpen
            className="h-4 w-4"/>;
        if (name.includes('entertain') || name.includes('娱乐')) return <Music className="h-4 w-4"/>;
        if (name.includes('shop') || name.includes('购物')) return <ShoppingCart className="h-4 w-4"/>;

        // 针对个人和推荐分类使用不同的默认图标
        if (category.is_admin_default) {
            return <Globe className="h-4 w-4"/>;
        }

        return <Folder className="h-4 w-4"/>;
    };

    // 渲染链接图标
    const renderLinkIcon = (link: NavLinkItem, linkIcon: string | null) => {
        return (
            <div className="w-10 h-10 relative flex-shrink-0">
                {linkIcon ? (
                    <img
                        src={linkIcon}
                        alt={link.name}
                        className="w-10 h-10 object-contain rounded-md"
                        onError={(e) => {
                            const target = e.currentTarget;
                            target.style.display = 'none';

                            const svgContainer = document.createElement('div');
                            svgContainer.className = 'w-10 h-10 flex items-center justify-center bg-muted rounded-md';
                            svgContainer.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="2" x2="22" y2="22"></line></svg>`;

                            target.parentNode?.appendChild(svgContainer);
                        }}
                    />
                ) : (
                    <div className="w-10 h-10 flex items-center justify-center bg-muted rounded-md">
                        <Globe className="w-5 h-5 text-muted-foreground"/>
                    </div>
                )}
            </div>
        );
    };

    // 检查链接状态
    const checkLinkStatus = async (url: string, id: string) => {
        try {
            // 创建一个AbortController来设置超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            // 改用直接HEAD请求检查链接，而不是通过API
            try {
                const response = await fetch(url, {
                    method: 'HEAD',
                    signal: controller.signal,
                    mode: 'no-cors' // 添加no-cors模式以避免CORS错误
                });

                clearTimeout(timeoutId);

                setLinkStatus(prev => ({
                    ...prev,
                    [id]: true // 如果能够发送请求，认为链接是可访问的
                }));
            } catch (error) {
                console.error('直接检查链接失败:', error);
                setLinkStatus(prev => ({
                    ...prev,
                    [id]: false
                }));
            }

            // 更新已检查的链接数量
            setCheckedLinksCount(prev => prev + 1);
        } catch (error) {
            console.error('检查链接状态失败:', error);
            setLinkStatus(prev => ({
                ...prev,
                [id]: false
            }));

            // 更新已检查的链接数量，即使出错也算作已检查
            setCheckedLinksCount(prev => prev + 1);
        }
    };

    // 在链接加载完成后按顺序检查状态
    useEffect(() => {
        const allLinks = [...globalLinks, ...personalLinks];

        // 如果没有链接，或者已经在检查中，则直接返回
        if (allLinks.length === 0 || isCheckingLinks) return;

        // 按顺序检查链接
        const checkLinksSequentially = async () => {
            setIsCheckingLinks(true);
            setCheckedLinksCount(0);

            console.log(`开始检查 ${allLinks.length} 个链接的状态`);

            // 筛选出需要检查的链接（非内部链接）
            const linksToCheck = allLinks.filter(link => !link.is_internal && link.url);

            // 逐个检查链接
            for (const link of linksToCheck) {
                await checkLinkStatus(link.url, link.id);
                // 添加小延迟以减轻服务器负担
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            console.log('链接状态检查完成');
            setIsCheckingLinks(false);
        };

        checkLinksSequentially();
    }, [globalLinks, personalLinks]);

    // 渲染列表视图中的链接项
    const renderListItem = (link: NavLinkItem, index: number) => (
        <motion.div
            key={link.id}
            initial={{opacity: 0, y: 10}}
            animate={{opacity: 1, y: 0}}
            transition={{
                duration: 0.3,
                delay: index * 0.03,
                ease: [0.4, 0, 0.2, 1]
            }}
            className="border-b last:border-0"
        >
            <div className="p-4 flex items-center hover:bg-muted/30 transition-colors rounded-md">
                <div className="flex-shrink-0 mr-4 relative">
                    {link.icon ? (
                        <img
                            src={link.icon}
                            alt={link.name}
                            className="w-10 h-10 object-contain rounded-md"
                            onError={(e) => {
                                const favicon = getFavicon(link.url);
                                if (favicon) {
                                    e.currentTarget.src = favicon;
                                } else {
                                    // 使用内联SVG替代外部占位图
                                    const target = e.currentTarget;
                                    target.style.display = 'none';

                                    const svgContainer = document.createElement('div');
                                    svgContainer.className = 'w-10 h-10 flex items-center justify-center bg-muted rounded-md';
                                    svgContainer.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="2" x2="22" y2="22"></line></svg>`;

                                    target.parentNode?.appendChild(svgContainer);
                                }
                            }}
                        />
                    ) : (
                        <div className="w-10 h-10 flex items-center justify-center bg-muted rounded-md">
                            <Globe className="w-5 h-5 text-muted-foreground"/>
                        </div>
                    )}

                    {/* 来源标识 */}
                    {personalCategories.some(c => c.id === link.category_id) ? (
                        <Badge variant="secondary"
                               className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center rounded-full bg-blue-500 text-white border-2 border-background">
                            <User className="h-3 w-3"/>
                        </Badge>
                    ) : (
                        <Badge variant="secondary"
                               className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center rounded-full bg-amber-500 text-white border-2 border-background">
                            <Globe className="h-3 w-3"/>
                        </Badge>
                    )}
                </div>

                <div className="flex-1 min-w-0 mr-4">
                    <div className="flex items-center">
                        <h3 className="text-base font-medium truncate">
                            {link.name}
                        </h3>
                        {link.popularity && link.popularity > 85 && (
                            <Badge variant="secondary" className="ml-2 px-1.5 py-0 h-5">
                                <Star className="h-3 w-3 text-amber-500 mr-1"/>
                                <span className="text-[10px]">{link.popularity}</span>
                            </Badge>
                        )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-0.5">
                        {link.description || getLinkTypeLabel(link)}
                    </p>
                </div>

                <div>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    size="sm"
                                    variant="outline"
                                    className="h-9 px-3"
                                    onClick={() => window.open(link.url, '_blank')}
                                >
                                    <ExternalLink className="h-4 w-4 mr-1"/>
                                    {language === 'en' ? 'Open' : '打开'}
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{language === 'en' ? 'Open in new tab' : '在新标签页中打开'}</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </div>
        </motion.div>
    );

    // 渲染网格视图中的链接卡片
    const renderGridCard = (link: NavLinkItem, index: number) => {
        const isPersonal = personalLinks.some(pLink => pLink.id === link.id);
        const linkIcon = (link.icon && link.icon.startsWith('http')) ? link.icon : getFavicon(link.url);
        const isLinkDown = linkStatus[link.id] === false;

        return (
            <motion.div
                key={link.id}
                layout
                initial={{opacity: 0, scale: 0.9}}
                animate={{opacity: 1, scale: 1}}
                exit={{opacity: 0, scale: 0.9}}
                transition={{duration: 0.2}}
                className={`group ${isLinkDown ? 'opacity-60' : ''}`}
            >
                <Card
                    className={`h-full transition-all duration-200 border hover:border-primary/40 hover:shadow-md overflow-hidden group-hover:shadow-sm ${
                        isLinkDown ? 'border-red-300 bg-red-50 dark:bg-red-950/20' : ''
                    }`}>
                    <a
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block h-full"
                    >
                        <CardContent className="p-4 flex flex-col h-full justify-between">
                            <div>
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center space-x-3">
                                        {renderLinkIcon(link, linkIcon)}

                                        <div className="space-x-1.5">
                                            {isPersonal && (
                                                <Badge variant="outline"
                                                       className="bg-blue-500 text-xs text-white border-blue-500">
                                                    <User className="h-2.5 w-2.5 mr-1"/>
                                                    {language === 'en' ? 'Personal' : '个人'}
                                                </Badge>
                                            )}

                                            {!isPersonal && (
                                                <Badge variant="outline"
                                                       className="bg-amber-500 text-xs text-white border-amber-500">
                                                    <Globe className="h-2.5 w-2.5 mr-1"/>
                                                    {language === 'en' ? 'Recommended' : '推荐'}
                                                </Badge>
                                            )}

                                            {isLinkDown && (
                                                <Badge variant="outline"
                                                       className="bg-red-500 text-xs text-white border-red-500">
                                                    {language === 'en' ? 'Unavailable' : '不可用'}
                                                </Badge>
                                            )}
                                        </div>
                                    </div>

                                    <ExternalLink className="h-3.5 w-3.5 text-muted-foreground"/>
                                </div>

                                <h3 className="font-medium mb-1 line-clamp-1 group-hover:text-primary transition-colors">
                                    {link.name}
                                </h3>

                                {link.description && (
                                    <p className="text-sm text-muted-foreground line-clamp-2">
                                        {link.description}
                                    </p>
                                )}
                            </div>

                            <div className="flex items-center justify-between mt-3 pt-3 border-t border-border/60">
                                <div>
                                    <Badge variant="secondary" className="text-xs bg-muted">
                                        {getLinkTypeLabel(link)}
                                    </Badge>
                                </div>

                                <div className="flex items-center space-x-2">
                                    {isLinkDown ? (
                                        <div className="flex items-center text-xs text-red-500">
                                            <AlertCircle className="h-3 w-3 mr-1"/>
                                            {language === 'en' ? 'Connection failed' : '连接失败'}
                                        </div>
                                    ) : linkStatus[link.id] === true ? (
                                        <div className="flex items-center text-xs text-green-500">
                                            <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                                            {language === 'en' ? 'Online' : '在线'}
                                        </div>
                                    ) : null}

                                    {link.popularity !== undefined && (
                                        <div className="flex items-center text-xs text-muted-foreground">
                                            <Star className="h-3 w-3 mr-1 text-yellow-500"/>
                                            {link.popularity}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </a>
                </Card>
            </motion.div>
        );
    };

    // 加载状态
    if (loading && !globalLinks.length && !personalLinks.length) {
        return (
            <div className="container max-w-screen-xl mx-auto py-16">
                <div className="flex flex-col items-center justify-center h-64">
                    <div
                        className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full mb-4"></div>
                    <p className="text-muted-foreground">
                        {language === 'en' ? 'Loading navigation data...' : '正在加载导航数据...'}
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="container max-w-screen-xl mx-auto pt-1 px-4">
            {/* 控制面板 */}
            <div className="rounded-xl p-4 mb-4 bg-gradient-to-br from-muted/80 to-muted/30 border shadow-sm">
                <div className="flex flex-col lg:flex-row gap-5">
                    {/* 搜索框 */}
                    <div className="flex-1 relative">
                        <Search
                            className="absolute left-3.5 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5"/>
                        <Input
                            type="text"
                            placeholder={language === 'en' ? "Search tools and resources..." : "搜索工具和资源..."}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-11 py-6 h-12 bg-background pr-4 text-base rounded-lg"
                        />
                    </div>

                    {/* 控制选项 */}
                    <div className="flex flex-wrap gap-3 items-center">
                        {/* 导航类型选择 */}
                        <div className="border bg-background rounded-lg overflow-hidden">
                            <div className="flex">
                                <Button
                                    variant={navType === 'all' ? 'secondary' : 'ghost'}
                                    size="sm"
                                    onClick={() => setNavType('all')}
                                    className="rounded-none border-r h-12 px-4"
                                >
                                    <Layers className="h-4 w-4 mr-2"/>
                                    <span>{language === 'en' ? 'All' : '全部'}</span>
                                </Button>
                                <Button
                                    variant={navType === 'personal' ? 'secondary' : 'ghost'}
                                    size="sm"
                                    onClick={() => setNavType('personal')}
                                    className="rounded-none border-r h-12 px-4"
                                    disabled={!user || personalCategories.length === 0}
                                >
                                    <User className="h-4 w-4 mr-2"/>
                                    <span>{language === 'en' ? 'Personal' : '个人'}</span>
                                </Button>
                                <Button
                                    variant={navType === 'global' ? 'secondary' : 'ghost'}
                                    size="sm"
                                    onClick={() => setNavType('global')}
                                    className="rounded-none h-12 px-4"
                                >
                                    <Globe className="h-4 w-4 mr-2"/>
                                    <span>{language === 'en' ? 'Recommended' : '推荐'}</span>
                                </Button>
                            </div>
                        </div>

                        {/* 视图模式选择 */}
                        <div className="flex rounded-lg overflow-hidden border bg-background">
                            <Button
                                variant={viewMode === 'grid' ? 'secondary' : 'ghost'}
                                size="sm"
                                onClick={() => setViewMode('grid')}
                                className="rounded-none border-r h-12 w-12"
                            >
                                <Grid className="h-4 w-4"/>
                            </Button>
                            <Button
                                variant={viewMode === 'list' ? 'secondary' : 'ghost'}
                                size="sm"
                                onClick={() => setViewMode('list')}
                                className="rounded-none h-12 w-12"
                            >
                                <List className="h-4 w-4"/>
                            </Button>
                        </div>

                        {/* 链接检查状态指示符 */}
                        {isCheckingLinks && (
                            <div className="flex items-center text-sm text-muted-foreground">
                                <span className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full mr-2"></span>
                                {language === 'en'
                                    ? `Checking links: ${checkedLinksCount}/${[...globalLinks, ...personalLinks].filter(link => !link.is_internal && link.url).length}`
                                    : `正在检查链接: ${checkedLinksCount}/${[...globalLinks, ...personalLinks].filter(link => !link.is_internal && link.url).length}`
                                }
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* 主内容区域 */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
                {/* 左侧分类导航 */}
                <div className="lg:col-span-3">
                    <Card className="sticky top-6 shadow-sm">
                        <CardHeader className="pb-2">
                            <CardTitle className="text-xl flex items-center">
                                <Filter className="mr-2 h-5 w-5 text-primary"/>
                                {language === 'en' ? 'Categories' : '分类'}
                            </CardTitle>
                            <CardDescription>
                                {language === 'en' ? 'Filter by category' : '按分类筛选'}
                            </CardDescription>
                        </CardHeader>

                        <CardContent>
                            <div className="space-y-1 pt-1">
                                <Button
                                    variant={!activeCategory ? 'secondary' : 'outline'}
                                    className="w-full justify-start"
                                    onClick={() => {
                                        setActiveCategory(null);
                                        setActiveSubCategory(null);
                                    }}
                                >
                                    <LayoutGrid className="mr-2 h-4 w-4"/>
                                    <span>{language === 'en' ? 'All Categories' : '所有分类'}</span>
                                </Button>
                            </div>

                            <ScrollArea className="h-[calc(100vh-300px)] pr-4 mt-4">
                                <div className="space-y-5">
                                    {getParentCategories().map(category => {
                                        const subCategories = getSubCategories(category.id);

                                        return (
                                            <div key={category.id} className="space-y-1">
                                                <Button
                                                    variant={activeCategory === category.id && !activeSubCategory ? 'secondary' : 'outline'}
                                                    className={`w-full justify-start text-left ${
                                                        activeCategory === category.id ? 'font-medium' : ''
                                                    }`}
                                                    onClick={() => {
                                                        setActiveCategory(category.id);
                                                        setActiveSubCategory(null);
                                                    }}
                                                >
                                                    <div className="flex items-center w-full">
                                                        <div className="flex-shrink-0 mr-2">
                                                            {getCategoryIcon(category)}
                                                        </div>
                                                        <span className="flex-1 truncate">{category.name}</span>
                                                        {subCategories.length > 0 && (
                                                            <ChevronRight
                                                                className={`ml-auto h-4 w-4 transition-transform ${
                                                                    activeCategory === category.id ? 'transform rotate-90' : ''
                                                                }`}
                                                            />
                                                        )}
                                                    </div>
                                                </Button>

                                                {/* 子分类 */}
                                                {activeCategory === category.id && subCategories.length > 0 && (
                                                    <div className="pl-4 ml-3 border-l space-y-1 pt-1 mt-1">
                                                        {subCategories.map(sub => (
                                                            <Button
                                                                key={sub.id}
                                                                variant={activeSubCategory === sub.id ? 'secondary' : 'ghost'}
                                                                size="sm"
                                                                className={`w-full justify-start text-sm h-9 ${
                                                                    activeSubCategory === sub.id ? 'font-medium' : ''
                                                                }`}
                                                                onClick={() => setActiveSubCategory(sub.id)}
                                                            >
                                                                <span className="truncate">{sub.name}</span>
                                                            </Button>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </ScrollArea>
                        </CardContent>
                    </Card>
                </div>

                {/* 右侧内容区域 */}
                <div className="lg:col-span-9">
                    {/* 内容标题 */}
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-5">
                        <div className="mb-3 sm:mb-0">
                            <h2 className="text-2xl font-bold flex items-center">
                                <Badge variant="outline" className="mr-2 bg-primary text-white border-primary">
                                    {filteredLinks.length}
                                </Badge>

                                {!activeCategory && (
                                    <span>
                    {language === 'en' ? 'All Resources' : '所有资源'}
                  </span>
                                )}

                                {activeCategory && (
                                    <span>
                    {getParentCategories().find(c => c.id === activeCategory)?.name}
                  </span>
                                )}

                                {activeSubCategory && (
                                    <>
                                        <ChevronRight className="mx-1 h-5 w-5 text-muted-foreground"/>
                                        <span>
                      {getVisibleCategories().find(c => c.id === activeSubCategory)?.name}
                    </span>
                                    </>
                                )}
                            </h2>

                            {activeCategory && (
                                <p className="text-muted-foreground text-sm mt-1">
                                    {language === 'en' ? 'Showing resources in selected category' : '显示所选分类中的资源'}
                                </p>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            <Badge
                                variant="outline"
                                className={`px-3 py-1.5 ${navType === 'personal' ? 'bg-blue-500 text-white' : 'bg-muted'}`}
                            >
                                <User className="h-3.5 w-3.5 mr-1"/>
                                {personalLinks.length} {language === 'en' ? 'Personal' : '个人'}
                            </Badge>

                            <Badge
                                variant="outline"
                                className={`px-3 py-1.5 ${navType === 'global' ? 'bg-amber-500 text-white' : 'bg-muted'}`}
                            >
                                <Globe className="h-3.5 w-3.5 mr-1"/>
                                {globalLinks.length} {language === 'en' ? 'Recommended' : '推荐'}
                            </Badge>
                        </div>
                    </div>

                    {/* 链接列表 */}
                    <AnimatePresence mode="wait">
                        {searchQuery && filteredLinks.length === 0 ? (
                            <motion.div
                                initial={{opacity: 0, y: 20}}
                                animate={{opacity: 1, y: 0}}
                                exit={{opacity: 0, y: 20}}
                                transition={{duration: 0.3}}
                                className="bg-muted/30 border rounded-xl p-10 text-center"
                            >
                                <Search className="h-10 w-10 mx-auto text-muted-foreground mb-3"/>
                                <h3 className="text-xl font-medium mb-2">
                                    {language === 'en' ? 'No results found' : '未找到结果'}
                                </h3>
                                <p className="text-muted-foreground max-w-md mx-auto">
                                    {language === 'en'
                                        ? `No matches for "${searchQuery}". Try different keywords or browse by category.`
                                        : `没有找到与"${searchQuery}"匹配的结果。尝试使用不同的关键词或按分类浏览。`}
                                </p>
                                <Button
                                    variant="outline"
                                    className="mt-4"
                                    onClick={() => setSearchQuery('')}
                                >
                                    {language === 'en' ? 'Clear Search' : '清除搜索'}
                                </Button>
                            </motion.div>
                        ) : filteredLinks.length === 0 ? (
                            <motion.div
                                initial={{opacity: 0, y: 20}}
                                animate={{opacity: 1, y: 0}}
                                exit={{opacity: 0, y: 20}}
                                transition={{duration: 0.3}}
                                className="bg-muted/30 border rounded-xl p-10 text-center"
                            >
                                <Bookmark className="h-10 w-10 mx-auto text-muted-foreground mb-3"/>
                                <h3 className="text-xl font-medium mb-2">
                                    {language === 'en' ? 'No resources in this category' : '此分类下暂无资源'}
                                </h3>
                                <p className="text-muted-foreground max-w-md mx-auto">
                                    {language === 'en'
                                        ? 'Try selecting a different category or changing your filter options.'
                                        : '尝试选择其他分类或更改筛选选项。'}
                                </p>
                                <Button
                                    variant="outline"
                                    className="mt-4"
                                    onClick={() => {
                                        setActiveCategory(null);
                                        setActiveSubCategory(null);
                                    }}
                                >
                                    {language === 'en' ? 'View All Categories' : '查看所有分类'}
                                </Button>
                            </motion.div>
                        ) : viewMode === 'list' ? (
                            <Card className="overflow-hidden shadow-sm">
                                <ScrollArea className="h-[calc(100vh-280px)]">
                                    <motion.div
                                        key="list-view"
                                        initial={{opacity: 0}}
                                        animate={{opacity: 1}}
                                        exit={{opacity: 0}}
                                        transition={{duration: 0.3}}
                                        className="p-1"
                                    >
                                        {filteredLinks.map((link, index) => renderListItem(link, index))}
                                    </motion.div>
                                </ScrollArea>
                            </Card>
                        ) : (
                            <motion.div
                                key="grid-view"
                                initial={{opacity: 0}}
                                animate={{opacity: 1}}
                                exit={{opacity: 0}}
                                transition={{duration: 0.3}}
                                className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-5"
                            >
                                {filteredLinks.map((link, index) => renderGridCard(link, index))}
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            </div>
        </div>
    );
};

export default NavigationHub;