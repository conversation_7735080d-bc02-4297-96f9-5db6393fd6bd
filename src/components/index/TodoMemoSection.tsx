import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppContext } from '@/context/AppContext';
import { Button } from '@/components/ui/button';
import { ListTodo, StickyNote, ArrowRight, Check, Calendar, Clock, Bell } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { AuthModal } from '@/components/auth/AuthModal';

const TodoMemoSection: React.FC = () => {
  const { language } = useAppContext();
  const navigate = useNavigate();

  // 认证弹框状态
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  const handleGetStarted = () => {
    setAuthMode('login');
    setIsAuthModalOpen(true);
  };

  const handleAuthModalClose = () => {
    setIsAuthModalOpen(false);
  };

  // 特性列表
  const features = [
    {
      icon: <Check className="h-4 w-4" />,
      title: language === 'en' ? 'Task Management' : '任务管理',
      description: language === 'en'
        ? 'Create, track, and complete tasks easily'
        : '轻松创建、跟踪和完成任务'
    },
    {
      icon: <Clock className="h-4 w-4" />,
      title: language === 'en' ? 'Quick Notes' : '快速笔记',
      description: language === 'en'
        ? 'Capture ideas and thoughts instantly'
        : '即时捕捉想法和思路'
    },
    {
      icon: <Calendar className="h-4 w-4" />,
      title: language === 'en' ? 'Due Dates' : '截止日期',
      description: language === 'en'
        ? 'Set and track deadlines for your tasks'
        : '为任务设置和跟踪截止日期'
    },
    {
      icon: <Bell className="h-4 w-4" />,
      title: language === 'en' ? 'Reminders' : '提醒功能',
      description: language === 'en'
        ? 'Never miss important deadlines'
        : '永不错过重要截止日期'
    }
  ];

  return (
    <div className="bg-gradient-to-br from-background to-muted/30 p-8 rounded-xl border relative overflow-hidden">
      {/* 装饰元素 */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary/5 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2"></div>

      <div className="relative z-10">
        <Badge className="absolute top-0 right-0 bg-gradient-to-r from-primary to-primary/80 text-white">
          {language === 'en' ? 'NEW' : '新功能'}
        </Badge>

        <div className="md:grid md:grid-cols-2 md:gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center md:text-left mb-8 md:mb-0"
          >
            <div className="flex justify-center md:justify-start mb-6">
              <div className="flex gap-3">
                <div className="p-3 bg-primary/10 rounded-full">
                  <ListTodo className="h-6 w-6 text-primary" />
                </div>
                <div className="p-3 bg-primary/10 rounded-full">
                  <StickyNote className="h-6 w-6 text-primary" />
                </div>
              </div>
            </div>

            <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
              {language === 'en' ? 'Todos & Memos' : '待办与备忘录'}
            </h2>

            <p className="text-muted-foreground mb-8">
              {language === 'en'
                ? 'Organize your tasks and capture your thoughts in one place. Our todo lists and memos help you stay organized and productive.'
                : '在一处组织您的任务并记录您的想法。我们的待办事项列表和备忘录帮助您保持条理和提高效率。'}
            </p>

            <div className="grid grid-cols-2 gap-4 mb-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start gap-3"
                >
                  <div className="mt-0.5 flex-shrink-0 p-2 bg-primary/10 rounded-full">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">{feature.title}</h3>
                    <p className="text-xs text-muted-foreground">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <Button
              onClick={handleGetStarted}
              className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary shadow-md hover:shadow-lg transition-all"
            >
              {language === 'en' ? 'Get Started' : '立即开始'}
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </motion.div>

          {/* 右侧预览 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="hidden md:block"
          >
            <div className="relative">
              {/* 任务预览卡片 */}
              <Card className="w-full max-w-sm mx-auto bg-card shadow-xl border">
                <CardContent className="p-5">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-medium flex items-center">
                      <ListTodo className="h-4 w-4 mr-2 text-primary" />
                      {language === 'en' ? 'My Tasks' : '我的任务'}
                    </h3>
                    <Badge variant="secondary" className="ml-2">3</Badge>
                  </div>

                  <div className="space-y-3">
                    {[
                      { done: false, text: language === 'en' ? 'Project proposal' : '项目提案', date: '2023-12-10' },
                      { done: true, text: language === 'en' ? 'Team meeting' : '团队会议', date: '2023-12-08' },
                      { done: false, text: language === 'en' ? 'Client presentation' : '客户演示', date: '2023-12-15' }
                    ].map((task, i) => (
                      <div
                        key={i}
                        className="flex items-center p-2 rounded-lg bg-muted/40 border border-muted"
                      >
                        <div className={`h-5 w-5 rounded-full border flex items-center justify-center mr-3 flex-shrink-0 ${
                          task.done ? 'bg-primary border-primary' : 'border-primary/30'
                        }`}>
                          {task.done && <Check className="h-3 w-3 text-white" />}
                        </div>
                        <div className="flex-1">
                          <p className={`text-sm ${task.done ? 'line-through text-muted-foreground' : 'font-medium'}`}>
                            {task.text}
                          </p>
                          <p className="text-xs text-muted-foreground">{task.date}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-5 pt-4 border-t flex justify-between items-center">
                    <div className="text-sm text-muted-foreground">
                      {language === 'en' ? 'Progress' : '进度'}: 1/3
                    </div>
                    <div className="w-32 h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full w-1/3 bg-primary rounded-full"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 备忘录预览卡片 */}
              <Card className="w-full max-w-xs mx-auto bg-card shadow-xl border absolute -bottom-10 -left-16">
                <CardContent className="p-4">
                  <div className="flex items-center mb-2">
                    <StickyNote className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="font-medium text-sm">
                      {language === 'en' ? 'Quick Note' : '快速笔记'}
                    </h3>
                  </div>
                  <p className="text-sm">
                    {language === 'en'
                      ? 'Remember to check the latest updates for the project before the meeting tomorrow.'
                      : '记得在明天会议前检查项目的最新更新。'}
                  </p>
                  <p className="text-xs text-muted-foreground mt-2">
                    {new Date().toLocaleDateString()}
                  </p>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </div>
      </div>

      {/* 认证弹框 */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={handleAuthModalClose}
        defaultMode={authMode}
      />
    </div>
  );
};

export default TodoMemoSection;