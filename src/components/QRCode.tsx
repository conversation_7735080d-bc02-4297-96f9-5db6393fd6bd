
import React, { useEffect, useRef } from 'react';
import QRCodeLib from 'qrcode';

interface QRCodeProps {
  url: string;
  size?: number;
  style?: React.CSSProperties;
  className?: string;
}

const QRCode: React.FC<QRCodeProps> = ({ 
  url, 
  size = 200, 
  style = {}, 
  className = "" 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!url || !canvasRef.current) return;

    try {
      QRCodeLib.toCanvas(
        canvasRef.current,
        url,
        {
          width: size,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          },
          errorCorrectionLevel: 'H'
        },
        (error) => {
          if (error) {
            console.error('Error generating QR code:', error);
          }
        }
      );
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    }
  }, [url, size]);

  return (
    <div className={`flex justify-center ${className}`}>
      <canvas 
        ref={canvasRef} 
        style={{
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
          borderRadius: '8px',
          ...style
        }}
      />
    </div>
  );
};

export default QRCode;
