
import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mail, Plus, Trash2 } from 'lucide-react';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';

interface EmailDomain {
  id: string;
  domain: string;
  created_at: string;
}

const EmailDomainsManager = () => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [domains, setDomains] = useState<EmailDomain[]>([]);
  const [newDomain, setNewDomain] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchDomains();
  }, []);

  const fetchDomains = async () => {
    setIsLoading(true);
    try {
      let data: EmailDomain[] = [];

      if (isUsingSupabase()) {
        const { supabase } = await import('@/integrations/supabase/client');
        const { data: supabaseData, error } = await supabase
          .from('email_domains')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) throw error;
        data = supabaseData || [];
      } else {
        // Use Go backend API
        const config = getBackendConfig();
        const response = await fetch(`${config.goBackend?.baseUrl}/api/v1/email-domains`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        data = await response.json();
      }

      setDomains(data);
    } catch (error) {
      console.error('Error fetching email domains:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to load email domains" : "加载邮箱域名失败",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addDomain = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newDomain) return;

    setIsLoading(true);
    try {
      // Check if domain already exists
      if (domains.some(d => d.domain.toLowerCase() === newDomain.toLowerCase())) {
        toast({
          variant: "destructive",
          description: language === 'en' ? "Domain already exists" : "域名已存在",
        });
        setIsLoading(false);
        return;
      }

      let newDomainData: EmailDomain;

      if (isUsingSupabase()) {
        const { supabase } = await import('@/integrations/supabase/client');
        const { data, error } = await supabase
          .from('email_domains')
          .insert({ domain: newDomain.trim() })
          .select();

        if (error) throw error;
        newDomainData = data[0];
      } else {
        // Use Go backend API
        const config = getBackendConfig();
        const response = await fetch(`${config.goBackend?.baseUrl}/api/v1/email-domains`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ domain: newDomain.trim() }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        newDomainData = await response.json();
      }

      setDomains([newDomainData, ...domains]);
      setNewDomain('');

      toast({
        description: language === 'en' ? "Email domain added successfully" : "邮箱域名添加成功",
      });
    } catch (error) {
      console.error('Error adding email domain:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to add email domain" : "添加邮箱域名失败",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const deleteDomain = async (id: string) => {
    if (domains.length <= 1) {
      toast({
        variant: "destructive",
        description: language === 'en' ? "Cannot delete the last domain" : "无法删除最后一个域名",
      });
      return;
    }

    setIsLoading(true);
    try {
      if (isUsingSupabase()) {
        const { supabase } = await import('@/integrations/supabase/client');
        const { error } = await supabase
          .from('email_domains')
          .delete()
          .eq('id', id);

        if (error) throw error;
      } else {
        // Use Go backend API
        const config = getBackendConfig();
        const response = await fetch(`${config.goBackend?.baseUrl}/api/v1/email-domains/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      }

      setDomains(domains.filter(d => d.id !== id));

      toast({
        description: language === 'en' ? "Email domain deleted successfully" : "邮箱域名删除成功",
      });
    } catch (error) {
      console.error('Error deleting email domain:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to delete email domain" : "删除邮箱域名失败",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h3 className="text-lg font-medium mb-2">
        {language === 'en' ? 'Email Domains' : '邮箱域名'}
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        {language === 'en'
          ? 'Manage domains used for temporary email addresses'
          : '管理用于临时邮箱地址的域名'}
      </p>

      <form onSubmit={addDomain} className="flex gap-2 mb-4">
        <div className="relative flex-1">
          <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={language === 'en' ? "Enter domain (e.g., example.com)" : "输入域名（例如，example.com）"}
            value={newDomain}
            onChange={(e) => setNewDomain(e.target.value)}
            disabled={isLoading}
            className="pl-10"
          />
        </div>
        <Button type="submit" disabled={isLoading || !newDomain}>
          <Plus className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Add' : '添加'}
        </Button>
      </form>

      <div className="border rounded-md overflow-hidden">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-4 py-2 text-left text-sm font-medium">
                {language === 'en' ? 'Domain' : '域名'}
              </th>
              <th className="px-4 py-2 text-right text-sm font-medium">
                {language === 'en' ? 'Actions' : '操作'}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {domains.map((domain) => (
              <tr key={domain.id} className="hover:bg-muted/50">
                <td className="px-4 py-2 text-sm">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                    {domain.domain}
                  </div>
                </td>
                <td className="px-4 py-2 text-sm text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteDomain(domain.id)}
                    disabled={isLoading || domains.length <= 1}
                    className="h-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {domains.length === 0 && (
        <div className="text-center p-4 border rounded-md mt-2">
          <p className="text-muted-foreground">
            {language === 'en' ? 'No email domains found' : '未找到邮箱域名'}
          </p>
        </div>
      )}
    </div>
  );
};

export default EmailDomainsManager;
