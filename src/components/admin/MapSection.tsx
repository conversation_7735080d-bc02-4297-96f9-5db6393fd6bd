
import React from 'react';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import VisitorsMap from '../../pages/admin/VisitorsMap';

const MapSection = () => {
  const { language } = useAppContext();

  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'Visitor Map' : '访客地图'}</CardTitle>
        <CardDescription>
          {language === 'en' 
            ? 'Geographic distribution of URL visits' 
            : '短链接访问的地理分布'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[500px] w-full">
          <VisitorsMap />
        </div>
      </CardContent>
    </Card>
  );
};

export default MapSection;
