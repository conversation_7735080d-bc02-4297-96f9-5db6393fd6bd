
import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import { <PERSON><PERSON>he<PERSON>, BellDot } from 'lucide-react';
import { useDomainWhitelist } from '@/hooks'; // Updated import
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Import subcomponents
import DomainForm from './domains/DomainForm';
import DomainSearch from './domains/DomainSearch';
import DomainsList from './domains/DomainsList';
import PendingDomainsList from './domains/PendingDomainsList';

interface DomainWhitelistSectionProps {
  approvedDomains: string[];
}

const DomainWhitelistSection: React.FC<DomainWhitelistSectionProps> = ({ approvedDomains: initialDomains }) => {
  const { language } = useAppContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('approved');
  
  const {
    domains,
    pendingDomains,
    isLoading,
    submitDomain,
    approveDomain,
    rejectDomain,
    toggleDomainApproval,
    deleteDomain
  } = useDomainWhitelist(initialDomains);
  
  const filteredDomains = domains.filter(domain =>
    domain.domain.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{language === 'en' ? 'Domain Whitelist' : '域名白名单'}</CardTitle>
            <CardDescription>
              {language === 'en' 
                ? 'Manage which domains are allowed for URL shortening' 
                : '管理允许用于URL缩短的域名'}
            </CardDescription>
          </div>
          <div className="bg-primary/10 p-2 rounded-full">
            <ShieldCheck className="h-6 w-6 text-primary" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <DomainForm 
          onAddDomain={submitDomain}
          isLoading={isLoading}
        />
        
        <Tabs 
          value={activeTab} 
          onValueChange={setActiveTab}
          className="mt-6"
        >
          <TabsList className="mb-4">
            <TabsTrigger value="approved">
              {language === 'en' ? 'Approved Domains' : '已批准域名'}
            </TabsTrigger>
            <TabsTrigger value="pending" className="relative">
              {language === 'en' ? 'Pending Approval' : '待审核域名'}
              {pendingDomains.length > 0 && (
                <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white">
                  {pendingDomains.length}
                </span>
              )}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="approved" className="space-y-4">
            <DomainSearch 
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              disabled={isLoading}
            />
            
            <DomainsList 
              domains={filteredDomains}
              onToggleApproval={toggleDomainApproval}
              onDelete={deleteDomain}
              isLoading={isLoading}
              // 根据错误提示，移除了showDelete属性，因为DomainsList组件未定义该属性
            />
            
            <p className="text-sm text-muted-foreground mt-4">
              {language === 'en' 
                ? `Showing ${filteredDomains.length} of ${domains.length} domains` 
                : `显示 ${filteredDomains.length} 个域名（共 ${domains.length} 个）`}
            </p>
          </TabsContent>
          
          <TabsContent value="pending" className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-4">
              <div className="flex items-center">
                <BellDot className="h-5 w-5 text-amber-500 mr-2" />
                <h3 className="font-medium text-amber-800">
                  {language === 'en' ? 'Domains Awaiting Approval' : '等待批准的域名'}
                </h3>
              </div>
              <p className="text-sm text-amber-700 mt-1">
                {language === 'en' 
                  ? 'Review and approve or reject user-submitted domains. Approved domains will be added to the whitelist.'
                  : '审核并批准或拒绝用户提交的域名。批准的域名将被添加到白名单中。'}
              </p>
            </div>
            
            <PendingDomainsList 
              domains={pendingDomains}
              onApprove={approveDomain}
              onReject={rejectDomain}
              isLoading={isLoading}
            />
            
            {pendingDomains.length > 0 && (
              <p className="text-sm text-muted-foreground mt-4">
                {language === 'en' 
                  ? `${pendingDomains.length} domain${pendingDomains.length !== 1 ? 's' : ''} pending approval` 
                  : `${pendingDomains.length} 个域名等待批准`}
              </p>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DomainWhitelistSection;
