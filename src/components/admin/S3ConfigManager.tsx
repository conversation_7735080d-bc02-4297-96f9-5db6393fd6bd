import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Trash2, Edit, Plus, TestTube, Star } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAppContext } from '@/context/AppContext';
import apiClient from '@/services/api';

interface S3Config {
  id: string;
  name: string;
  access_key_id: string;
  secret_access_key?: string;
  region: string;
  bucket: string;
  endpoint?: string;
  use_ssl: boolean;
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface S3ConfigForm {
  name: string;
  access_key_id: string;
  secret_access_key: string;
  region: string;
  bucket: string;
  endpoint: string;
  use_ssl: boolean;
  is_default: boolean;
  is_active: boolean;
}

const S3ConfigManager: React.FC = () => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [configs, setConfigs] = useState<S3Config[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<S3Config | null>(null);
  const [formData, setFormData] = useState<S3ConfigForm>({
    name: '',
    access_key_id: '',
    secret_access_key: '',
    region: '',
    bucket: '',
    endpoint: '',
    use_ssl: true,
    is_default: false,
    is_active: true,
  });

  const fetchConfigs = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.get('/s3-configs');
      setConfigs(response.data);
    } catch (error) {
      console.error('获取S3配置失败:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to fetch S3 configurations" : "获取S3配置失败",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingConfig) {
        // 更新配置
        await apiClient.put(`/s3-configs/${editingConfig.id}`, formData);
        toast({
          description: language === 'en' ? "S3 configuration updated successfully" : "S3配置更新成功",
        });
      } else {
        // 创建新配置
        await apiClient.post('/s3-configs', formData);
        toast({
          description: language === 'en' ? "S3 configuration created successfully" : "S3配置创建成功",
        });
      }

      setIsDialogOpen(false);
      setEditingConfig(null);
      resetForm();
      fetchConfigs();
    } catch (error) {
      console.error('保存S3配置失败:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to save S3 configuration" : "保存S3配置失败",
      });
    }
  };

  const handleEdit = (config: S3Config) => {
    setEditingConfig(config);
    setFormData({
      name: config.name,
      access_key_id: config.access_key_id,
      secret_access_key: '', // 不显示现有密钥
      region: config.region,
      bucket: config.bucket,
      endpoint: config.endpoint || '',
      use_ssl: config.use_ssl,
      is_default: config.is_default,
      is_active: config.is_active,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm(language === 'en' ? 'Are you sure you want to delete this S3 configuration?' : '确定要删除这个S3配置吗？')) {
      return;
    }

    try {
      await apiClient.delete(`/s3-configs/${id}`);
      toast({
        description: language === 'en' ? "S3 configuration deleted successfully" : "S3配置删除成功",
      });
      fetchConfigs();
    } catch (error) {
      console.error('删除S3配置失败:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to delete S3 configuration" : "删除S3配置失败",
      });
    }
  };

  const handleSetDefault = async (id: string) => {
    try {
      await apiClient.post(`/s3-configs/${id}/set-default`);
      toast({
        description: language === 'en' ? "Default S3 configuration set successfully" : "默认S3配置设置成功",
      });
      fetchConfigs();
    } catch (error) {
      console.error('设置默认S3配置失败:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to set default S3 configuration" : "设置默认S3配置失败",
      });
    }
  };

  const handleTest = async (id: string) => {
    try {
      await apiClient.post(`/s3-configs/${id}/test`);
      toast({
        description: language === 'en' ? "S3 configuration test successful" : "S3配置测试成功",
      });
    } catch (error) {
      console.error('测试S3配置失败:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "S3 configuration test failed" : "S3配置测试失败",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      access_key_id: '',
      secret_access_key: '',
      region: '',
      bucket: '',
      endpoint: '',
      use_ssl: true,
      is_default: false,
      is_active: true,
    });
  };

  const handleAddNew = () => {
    setEditingConfig(null);
    resetForm();
    setIsDialogOpen(true);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{language === 'en' ? 'S3 Configuration Management' : 'S3配置管理'}</CardTitle>
            <CardDescription>
              {language === 'en' ? 'Manage S3 storage configurations for file uploads' : '管理文件上传的S3存储配置'}
            </CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={handleAddNew}>
                <Plus className="h-4 w-4 mr-2" />
                {language === 'en' ? 'Add S3 Config' : '添加S3配置'}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingConfig
                    ? (language === 'en' ? 'Edit S3 Configuration' : '编辑S3配置')
                    : (language === 'en' ? 'Add S3 Configuration' : '添加S3配置')
                  }
                </DialogTitle>
                <DialogDescription>
                  {language === 'en'
                    ? 'Configure your S3 storage settings for file uploads'
                    : '配置文件上传的S3存储设置'
                  }
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">{language === 'en' ? 'Configuration Name' : '配置名称'}</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="region">{language === 'en' ? 'Region' : '区域'}</Label>
                    <Input
                      id="region"
                      value={formData.region}
                      onChange={(e) => setFormData({ ...formData, region: e.target.value })}
                      placeholder="us-east-1"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="access_key_id">Access Key ID</Label>
                    <Input
                      id="access_key_id"
                      value={formData.access_key_id}
                      onChange={(e) => setFormData({ ...formData, access_key_id: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="secret_access_key">Secret Access Key</Label>
                    <Input
                      id="secret_access_key"
                      type="password"
                      value={formData.secret_access_key}
                      onChange={(e) => setFormData({ ...formData, secret_access_key: e.target.value })}
                      required={!editingConfig}
                      placeholder={editingConfig ? "留空保持不变" : ""}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="bucket">{language === 'en' ? 'Bucket Name' : '存储桶名称'}</Label>
                    <Input
                      id="bucket"
                      value={formData.bucket}
                      onChange={(e) => setFormData({ ...formData, bucket: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="endpoint">{language === 'en' ? 'Custom Endpoint (Optional)' : '自定义端点（可选）'}</Label>
                    <Input
                      id="endpoint"
                      value={formData.endpoint}
                      onChange={(e) => setFormData({ ...formData, endpoint: e.target.value })}
                      placeholder="s3.example.com"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="use_ssl"
                      checked={formData.use_ssl}
                      onCheckedChange={(checked) => setFormData({ ...formData, use_ssl: checked })}
                    />
                    <Label htmlFor="use_ssl">{language === 'en' ? 'Use SSL' : '使用SSL'}</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_default"
                      checked={formData.is_default}
                      onCheckedChange={(checked) => setFormData({ ...formData, is_default: checked })}
                    />
                    <Label htmlFor="is_default">{language === 'en' ? 'Set as Default' : '设为默认'}</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                    />
                    <Label htmlFor="is_active">{language === 'en' ? 'Active' : '启用'}</Label>
                  </div>
                </div>

                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    {language === 'en' ? 'Cancel' : '取消'}
                  </Button>
                  <Button type="submit">
                    {editingConfig
                      ? (language === 'en' ? 'Update' : '更新')
                      : (language === 'en' ? 'Create' : '创建')
                    }
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-4">
            {language === 'en' ? 'Loading...' : '加载中...'}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === 'en' ? 'Name' : '名称'}</TableHead>
                <TableHead>{language === 'en' ? 'Region' : '区域'}</TableHead>
                <TableHead>{language === 'en' ? 'Bucket' : '存储桶'}</TableHead>
                <TableHead>{language === 'en' ? 'Status' : '状态'}</TableHead>
                <TableHead>{language === 'en' ? 'Actions' : '操作'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {configs && configs.length > 0 ? configs.map((config) => (
                <TableRow key={config.id}>
                  <TableCell className="font-medium">
                    {config.name}
                    {config.is_default && (
                      <Badge variant="secondary" className="ml-2">
                        <Star className="h-3 w-3 mr-1" />
                        {language === 'en' ? 'Default' : '默认'}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>{config.region}</TableCell>
                  <TableCell>{config.bucket}</TableCell>
                  <TableCell>
                    <Badge variant={config.is_active ? "default" : "secondary"}>
                      {config.is_active
                        ? (language === 'en' ? 'Active' : '启用')
                        : (language === 'en' ? 'Inactive' : '禁用')
                      }
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTest(config.id)}
                      >
                        <TestTube className="h-4 w-4" />
                      </Button>
                      {!config.is_default && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSetDefault(config.id)}
                        >
                          <Star className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(config)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(config.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4">
                    {language === 'en' ? 'No S3 configurations found' : '未找到S3配置'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default S3ConfigManager;
