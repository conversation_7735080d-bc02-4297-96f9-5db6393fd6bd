
import React, { useState } from 'react';
import {
  Card,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';

import StatCards from './stats/StatCards';
import VisitorDistribution from './stats/VisitorDistribution';
import { VisitStat } from '@/hooks/useAdminData';
import StatsDetailDialog from './stats/StatsDetailDialog';
import CategoryStats from './stats/CategoryStats';
import HotNewsStats from './stats/hot-news/HotNewsStats';
import SiteVisitorStats from './stats/SiteVisitorStats';

interface StatsSectionProps {
  statistics: {
    totalUsers: number;
    totalUrls: number;
    totalEmails: number;
    totalClicks: number;
  };
  visitStats: VisitStat[];
}

const StatsSection = ({ statistics, visitStats }: StatsSectionProps) => {
  const { language } = useAppContext();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedStat, setSelectedStat] = useState<{
    type: 'users' | 'urls' | 'emails' | 'clicks';
    title: string;
  } | null>(null);

  const handleStatClick = (type: 'users' | 'urls' | 'emails' | 'clicks', title: string) => {
    setSelectedStat({ type, title });
    setDialogOpen(true);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'Statistics' : '统计'}</CardTitle>
        <CardDescription>
          {language === 'en' ? 'System usage and analytics.' : '系统使用情况和分析。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          <StatCards statistics={statistics} onStatClick={handleStatClick} />
          
          <div className="flex flex-col space-y-6">
            <VisitorDistribution />
            <CategoryStats />
            <HotNewsStats />
            <SiteVisitorStats language={language} />
          </div>
        </div>
        
        {selectedStat && (
          <StatsDetailDialog
            open={dialogOpen}
            onOpenChange={setDialogOpen}
            statType={selectedStat.type}
            title={selectedStat.title}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default StatsSection;
