import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ExternalLink, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { NavLink } from '@/integrations/supabase/types-utils';

// Define the Submission interface to match our database schema
interface Submission extends Omit<NavLink, 'sort_order'> {
  user_email?: string;
  category_name?: string;
}

const LinkModeration = () => {
  const { toast } = useToast();
  const { language } = useAppContext();

  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [pendingCount, setPendingCount] = useState(0);

  const fetchSubmissions = async (status: 'pending' | 'approved' | 'rejected') => {
    setLoading(true);

    try {
      // Join with nav_categories for category name
      const { data, error } = await supabase
        .from('nav_links')
        .select(`
          *,
          nav_categories (name)
        `)
        .eq('submission_status', status)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching submissions:', error);
        setLoading(false);
        return;
      }

      if (data) {
        // Get user emails in a separate query
        const userIds = data
          .filter(item => item.submitted_by)
          .map(item => item.submitted_by as string)
          .filter(Boolean);

        const uniqueUserIds = [...new Set(userIds)];

        let users: { id: string, email: string }[] = [];

        if (uniqueUserIds.length > 0) {
          // Fetch users data from profiles or another table that contains user emails
          // Since we can't directly query auth.users
          const { data: usersData } = await supabase
            .from('user_roles')
            .select('user_id, role')
            .in('user_id', uniqueUserIds);

          if (usersData) {
            // For demonstration purposes, we'll just use the user ID with @example.com
            // In a real application, you'd have a proper user profile table with emails
            users = usersData.map(user => ({
              id: user.user_id,
              email: `${user.user_id.slice(0, 8)}@example.com` // Simplified for demo
            }));
          }
        }

        // Map user emails to submissions
        const enhancedSubmissions = data.map(submission => {
          const user = users.find(u => u.id === submission.submitted_by);

          return {
            ...submission,
            user_email: user?.email || 'Unknown',
            category_name: submission.nav_categories?.name || 'Unknown Category'
          } as Submission;
        });

        setSubmissions(enhancedSubmissions);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error:', error);
      setLoading(false);
    }
  };

  const fetchPendingCount = async () => {
    const { count, error } = await supabase
      .from('nav_links')
      .select('*', { count: 'exact', head: true })
      .eq('submission_status', 'pending');

    if (error) {
      console.error('Error fetching pending count:', error);
      return;
    }

    if (count !== null) {
      setPendingCount(count);
    }
  };

  useEffect(() => {
    fetchPendingCount();
    fetchSubmissions('pending');

    // Set up real-time subscription for new submissions
    const channel = supabase
      .channel('nav_links_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'nav_links', filter: 'submission_status=eq.pending' },
        () => {
          fetchPendingCount();
          if (activeTab === 'pending') {
            fetchSubmissions('pending');
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  useEffect(() => {
    fetchSubmissions(activeTab as 'pending' | 'approved' | 'rejected');
  }, [activeTab]);

  const handleApprove = async (submission: Submission) => {
    try {
      const { error } = await supabase
        .from('nav_links')
        .update({
          submission_status: 'approved'
        })
        .eq('id', submission.id);

      if (error) {
        console.error('Error approving submission:', error);
        toast({
          variant: "destructive",
          description: language === 'en'
            ? "Failed to approve submission"
            : "批准提交失败"
        });
        return;
      }

      toast({
        description: language === 'en'
          ? "Submission approved successfully"
          : "提交已成功批准"
      });

      // Update local state
      setSubmissions(submissions.filter(s => s.id !== submission.id));
      fetchPendingCount();

    } catch (error) {
      console.error('Error:', error);
      toast({
        variant: "destructive",
        description: language === 'en'
          ? "An unexpected error occurred"
          : "发生意外错误"
      });
    }
  };

  const handleReject = async () => {
    if (!selectedSubmission) return;

    try {
      const { error } = await supabase
        .from('nav_links')
        .update({
          submission_status: 'rejected'
        })
        .eq('id', selectedSubmission.id);

      if (error) {
        console.error('Error rejecting submission:', error);
        toast({
          variant: "destructive",
          description: language === 'en'
            ? "Failed to reject submission"
            : "拒绝提交失败"
        });
        setRejectDialogOpen(false);
        return;
      }

      toast({
        description: language === 'en'
          ? "Submission rejected"
          : "提交已拒绝"
      });

      // Update local state
      setSubmissions(submissions.filter(s => s.id !== selectedSubmission.id));
      setRejectDialogOpen(false);
      setSelectedSubmission(null);
      fetchPendingCount();

    } catch (error) {
      console.error('Error:', error);
      toast({
        variant: "destructive",
        description: language === 'en'
          ? "An unexpected error occurred"
          : "发生意外错误"
      });
      setRejectDialogOpen(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>
            {language === 'en' ? 'Link Submission Moderation' : '链接提交审核'}
          </span>
          {pendingCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {pendingCount} {language === 'en' ? 'pending' : '待审核'}
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          {language === 'en'
            ? 'Review and approve user-submitted navigation links'
            : '审核和批准用户提交的导航链接'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="pending" className="relative">
              {language === 'en' ? 'Pending' : '待审核'}
              {pendingCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {pendingCount}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="approved">
              {language === 'en' ? 'Approved' : '已批准'}
            </TabsTrigger>
            <TabsTrigger value="rejected">
              {language === 'en' ? 'Rejected' : '已拒绝'}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending">
            {loading ? (
              <div className="py-12 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
                <p className="mt-4 text-muted-foreground">
                  {language === 'en' ? 'Loading submissions...' : '加载提交...'}
                </p>
              </div>
            ) : submissions.length === 0 ? (
              <div className="py-12 text-center">
                <AlertCircle className="h-10 w-10 text-muted-foreground mx-auto" />
                <p className="mt-4 font-medium">
                  {language === 'en' ? 'No pending submissions' : '没有待审核的提交'}
                </p>
                <p className="text-muted-foreground">
                  {language === 'en'
                    ? 'All user submissions have been reviewed'
                    : '所有用户提交都已审核'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {submissions.map((submission) => (
                  <div key={submission.id} className="border p-4 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <div className="flex items-center">
                          {submission.icon ? (
                            <img
                              src={submission.icon}
                              alt=""
                              className="w-8 h-8 mr-2 object-contain"
                              onError={(e) => {
                                e.currentTarget.src = '/placeholder.svg';
                              }}
                            />
                          ) : (
                            <div className="w-8 h-8 mr-2 bg-muted rounded-md flex items-center justify-center">
                              <ExternalLink className="w-4 h-4 text-muted-foreground" />
                            </div>
                          )}
                          <h3 className="font-medium">{submission.name}</h3>
                        </div>

                        <p className="text-sm">
                          <a href={submission.url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline flex items-center">
                            {submission.url}
                            <ExternalLink className="ml-1 h-3 w-3" />
                          </a>
                        </p>

                        <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                          <span>
                            {language === 'en' ? 'Category: ' : '分类：'}
                            {submission.category_name}
                          </span>
                          <span>•</span>
                          <span>
                            {language === 'en' ? 'Submitted by: ' : '提交者：'}
                            {submission.user_email}
                          </span>
                          <span>•</span>
                          <span>
                            {language === 'en' ? 'Type: ' : '类型：'}
                            {submission.is_internal ?
                              (language === 'en' ? 'Internal' : '内部') :
                              (language === 'en' ? 'External' : '外部')}
                          </span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-destructive hover:text-destructive hover:bg-destructive/10"
                          onClick={() => {
                            setSelectedSubmission(submission);
                            setRejectDialogOpen(true);
                          }}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          {language === 'en' ? 'Reject' : '拒绝'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-green-600 hover:text-green-600 hover:bg-green-100"
                          onClick={() => handleApprove(submission)}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          {language === 'en' ? 'Approve' : '批准'}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="approved">
            {loading ? (
              <div className="py-12 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
                <p className="mt-4 text-muted-foreground">
                  {language === 'en' ? 'Loading approved submissions...' : '加载已批准的提交...'}
                </p>
              </div>
            ) : submissions.length === 0 ? (
              <div className="py-12 text-center">
                <p className="text-muted-foreground">
                  {language === 'en' ? 'No approved submissions' : '没有已批准的提交'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {submissions.map((submission) => (
                  <div key={submission.id} className="border p-4 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <h3 className="font-medium">{submission.name}</h3>
                        <p className="text-sm">
                          <a href={submission.url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline flex items-center">
                            {submission.url}
                            <ExternalLink className="ml-1 h-3 w-3" />
                          </a>
                        </p>
                        <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                          <span>
                            {language === 'en' ? 'Category: ' : '分类：'}
                            {submission.category_name}
                          </span>
                          <span>•</span>
                          <span>
                            {language === 'en' ? 'Submitted by: ' : '提交者：'}
                            {submission.user_email}
                          </span>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        {language === 'en' ? 'Approved' : '已批准'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="rejected">
            {loading ? (
              <div className="py-12 text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
                <p className="mt-4 text-muted-foreground">
                  {language === 'en' ? 'Loading rejected submissions...' : '加载已拒绝的提交...'}
                </p>
              </div>
            ) : submissions.length === 0 ? (
              <div className="py-12 text-center">
                <p className="text-muted-foreground">
                  {language === 'en' ? 'No rejected submissions' : '没有已拒绝的提交'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {submissions.map((submission) => (
                  <div key={submission.id} className="border p-4 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <h3 className="font-medium">{submission.name}</h3>
                        <p className="text-sm">
                          <a href={submission.url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline flex items-center">
                            {submission.url}
                            <ExternalLink className="ml-1 h-3 w-3" />
                          </a>
                        </p>
                        <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                          <span>
                            {language === 'en' ? 'Category: ' : '分类：'}
                            {submission.category_name}
                          </span>
                          <span>•</span>
                          <span>
                            {language === 'en' ? 'Submitted by: ' : '提交者：'}
                            {submission.user_email}
                          </span>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                        {language === 'en' ? 'Rejected' : '已拒绝'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Confirmation Dialog for Rejecting Submissions */}
        <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {language === 'en' ? 'Confirm Rejection' : '确认拒绝'}
              </DialogTitle>
              <DialogDescription>
                {language === 'en'
                  ? 'Are you sure you want to reject this submission?'
                  : '您确定要拒绝此提交吗？'}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              {selectedSubmission && (
                <div className="space-y-2">
                  <p><strong>{language === 'en' ? 'Name:' : '名称:'}</strong> {selectedSubmission.name}</p>
                  <p><strong>{language === 'en' ? 'URL:' : '网址:'}</strong> {selectedSubmission.url}</p>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setRejectDialogOpen(false)}>
                {language === 'en' ? 'Cancel' : '取消'}
              </Button>
              <Button variant="destructive" onClick={handleReject}>
                {language === 'en' ? 'Reject' : '拒绝'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default LinkModeration;
