import React from 'react';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAppContext } from '@/context/AppContext';
import {
  BarChart3, Link, Settings, Users, 
  Compass, SlidersHorizontal, Globe
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface AdminTabsNavigationProps {
  pendingDomainsCount: number;
  pendingLinksCount: number;
}

const AdminTabsNavigation = ({ 
  pendingDomainsCount,
  pendingLinksCount 
}: AdminTabsNavigationProps) => {
  const { t, language } = useAppContext();
  
  return (
    <TabsList className="flex flex-wrap justify-start">
      <TabsTrigger value="stats">
        <BarChart3 className="w-4 h-4 mr-2" />
        {t('statistics')}
      </TabsTrigger>
      <TabsTrigger value="users">
        <Users className="w-4 h-4 mr-2" />
        {t('userManagement')}
      </TabsTrigger>
      <TabsTrigger value="domains">
        <Link className="w-4 h-4 mr-2" />
        {language === 'en' ? 'Domain Whitelist' : '域名白名单'}
        {pendingDomainsCount > 0 && (
          <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 flex items-center justify-center rounded-full">
            {pendingDomainsCount}
          </Badge>
        )}
      </TabsTrigger>
      <TabsTrigger value="navigation">
        <Compass className="w-4 h-4 mr-2" />
        {language === 'en' ? 'Navigation' : '导航'}
        {pendingLinksCount > 0 && (
          <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 flex items-center justify-center rounded-full">
            {pendingLinksCount}
          </Badge>
        )}
      </TabsTrigger>
      <TabsTrigger value="features-order">
        <SlidersHorizontal className="w-4 h-4 mr-2" />
        {language === 'en' ? 'Homepage Layout' : '首页布局'}
      </TabsTrigger>
      <TabsTrigger value="map">
        <Globe className="w-4 h-4 mr-2" />
        {t('visitorMap')}
      </TabsTrigger>
      <TabsTrigger value="settings">
        <Settings className="w-4 h-4 mr-2" />
        {t('systemSettings')}
      </TabsTrigger>
    </TabsList>
  );
};

export default AdminTabsNavigation;
