
import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useAppContext } from '@/context/AppContext';

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCount: number;
  onConfirm: () => Promise<void>;
}

const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  isOpen,
  onClose,
  selectedCount,
  onConfirm
}) => {
  const { language } = useAppContext();
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {language === 'en' ? 'Confirm Deletion' : '确认删除'}
          </DialogTitle>
          <DialogDescription>
            {language === 'en' 
              ? `Are you sure you want to delete ${selectedCount} selected categories? This will also delete all subcategories and links within those categories. This action cannot be undone.`
              : `您确定要删除 ${selectedCount} 个所选分类吗？这也将删除这些分类中的所有子分类和链接。此操作无法撤消。`}
          </DialogDescription>
        </DialogHeader>
        
        <DialogFooter className="sm:justify-end">
          <Button
            variant="secondary"
            onClick={onClose}
          >
            {language === 'en' ? 'Cancel' : '取消'}
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
          >
            {language === 'en' ? 'Delete' : '删除'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteConfirmDialog;
