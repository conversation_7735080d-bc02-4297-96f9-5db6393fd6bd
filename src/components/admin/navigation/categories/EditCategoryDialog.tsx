
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/dashboard/navigation/types';

interface EditCategoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  category: Category | null;
  editName: string;
  editIsPublic: boolean;
  editIsDefault: boolean;
  editParentId: string | null;
  isSubmitting: boolean;
  categories: Category[];
  onNameChange: (value: string) => void;
  onPublicChange: (value: boolean) => void;
  onDefaultChange: (value: boolean) => void;
  onParentChange: (value: string | null) => void;
  onSubmit: () => Promise<void>;
}

const EditCategoryDialog: React.FC<EditCategoryDialogProps> = ({
  isOpen,
  onClose,
  category,
  editName,
  editIsPublic,
  editIsDefault,
  editParentId,
  isSubmitting,
  categories,
  onNameChange,
  onPublicChange,
  onDefaultChange,
  onParentChange,
  onSubmit
}) => {
  const { language } = useAppContext();
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {language === 'en' ? 'Edit Category' : '编辑分类'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="edit-parent">
              {language === 'en' ? 'Parent Category' : '父分类'}
            </Label>
            <select
              id="edit-parent"
              className="w-full p-2 border rounded-md"
              value={editParentId || ''}
              onChange={(e) => onParentChange(e.target.value || null)}
            >
              <option value="">
                {language === 'en' ? '-- No Parent (Top Level) --' : '-- 无父分类（顶级）--'}
              </option>
              {categories
                .filter(cat => cat.id !== category?.id && !cat.parent_id)
                .map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.name}
                  </option>
                ))
              }
            </select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="edit-name">
              {language === 'en' ? 'Name' : '名称'}
            </Label>
            <Input
              id="edit-name"
              value={editName}
              onChange={(e) => onNameChange(e.target.value)}
              placeholder={language === 'en' ? 'Enter category name' : '输入分类名称'}
              required
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="edit-public"
              checked={editIsPublic}
              onCheckedChange={onPublicChange}
            />
            <Label htmlFor="edit-public">
              {language === 'en' ? 'Public' : '公开'}
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="edit-default"
              checked={editIsDefault}
              onCheckedChange={onDefaultChange}
            />
            <Label htmlFor="edit-default">
              {language === 'en' ? 'Default for All Users' : '所有用户默认显示'}
            </Label>
          </div>
        </div>
        
        <DialogFooter className="sm:justify-end">
          <Button
            variant="secondary"
            onClick={onClose}
          >
            {language === 'en' ? 'Cancel' : '取消'}
          </Button>
          <Button
            onClick={onSubmit}
            disabled={isSubmitting || !editName.trim()}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                {language === 'en' ? 'Saving...' : '保存中...'}
              </div>
            ) : (
              language === 'en' ? 'Save Changes' : '保存更改'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditCategoryDialog;
