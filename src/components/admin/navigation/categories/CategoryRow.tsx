import React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronRight, ChevronDown, Trash2, Pencil, GripVertical } from 'lucide-react';
import { TableCell, TableRow } from '@/components/ui/table';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/dashboard/navigation/types';
import { DraggableProvidedDragHandleProps, DraggableProvidedDraggableProps } from 'react-beautiful-dnd';

interface CategoryRowProps {
  category: Category;
  isSelected: boolean;
  isCurrentCategory: boolean;
  hasSubcategories: boolean;
  isExpanded: boolean;
  onSelect: (categoryId: string, event: React.MouseEvent) => void;
  onEdit: (category: Category, event: React.MouseEvent) => void;
  onDelete: (categoryId: string, event: React.MouseEvent) => void;
  onToggleExpand: (categoryId: string) => void;
  onSelectCategory: (category: Category) => void;
  isSubcategory?: boolean;
  dragHandleProps?: DraggableProvidedDragHandleProps;
  draggableProps?: DraggableProvidedDraggableProps;
  forwardedRef?: React.Ref<HTMLTableRowElement>;
}

const CategoryRow: React.FC<CategoryRowProps> = ({
  category,
  isSelected,
  isCurrentCategory,
  hasSubcategories,
  isExpanded,
  onSelect,
  onEdit,
  onDelete,
  onToggleExpand,
  onSelectCategory,
  isSubcategory = false,
  dragHandleProps,
  draggableProps,
  forwardedRef
}) => {
  const { language } = useAppContext();
  
  return (
    <TableRow 
      ref={forwardedRef}
      {...draggableProps}
      className={`${isCurrentCategory ? "bg-muted/50" : ""} ${isSubcategory ? "border-l-4 border-l-muted" : ""}`}
      onClick={() => onSelectCategory(category)}
    >
      <TableCell onClick={(e) => e.stopPropagation()}>
        <Checkbox 
          checked={isSelected}
          onCheckedChange={() => {}}
          onClick={(e) => {
            e.stopPropagation();
            onSelect(category.id, e as React.MouseEvent);
          }}
          aria-label={language === 'en' ? 'Select category' : '选择分类'}
        />
      </TableCell>
      <TableCell {...dragHandleProps} onClick={(e) => e.stopPropagation()} className="cursor-grab">
        <GripVertical className="h-4 w-4 text-muted-foreground" />
      </TableCell>
      <TableCell>
        {hasSubcategories ? (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand(category.id);
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        ) : (
          <span className="w-8 block"></span>
        )}
      </TableCell>
      <TableCell className={`font-medium ${isSubcategory ? "pl-6" : ""}`}>
        {category.name}
      </TableCell>
      <TableCell>
        {category.is_public ? (
          <span className="text-green-500">
            {language === 'en' ? 'Yes' : '是'}
          </span>
        ) : (
          <span className="text-muted-foreground">
            {language === 'en' ? 'No' : '否'}
          </span>
        )}
      </TableCell>
      <TableCell>
        {category.is_admin_default ? (
          <span className="text-green-500">
            {language === 'en' ? 'Yes' : '是'}
          </span>
        ) : (
          <span className="text-muted-foreground">
            {language === 'en' ? 'No' : '否'}
          </span>
        )}
      </TableCell>
      <TableCell>
        {isSubcategory ? (
          <span className="text-purple-500">
            {language === 'en' ? 'Sub' : '子类'}
          </span>
        ) : (
          <span className="text-blue-500">
            {language === 'en' ? 'Main' : '主要'}
          </span>
        )}
      </TableCell>
      <TableCell className="text-right">
        <div className="flex justify-end gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => onEdit(category, e)}
          >
            <Pencil className="h-4 w-4 text-muted-foreground" />
            <span className="sr-only">{language === 'en' ? 'Edit' : '编辑'}</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(category.id, e);
            }}
          >
            <Trash2 className="h-4 w-4 text-destructive" />
            <span className="sr-only">{language === 'en' ? 'Delete' : '删除'}</span>
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
};

export default CategoryRow;
