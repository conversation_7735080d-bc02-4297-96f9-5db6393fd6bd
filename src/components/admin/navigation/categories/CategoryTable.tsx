import React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/dashboard/navigation/types';
import CategoryRow from './CategoryRow';
import { Droppable, Draggable } from 'react-beautiful-dnd';
import { GripVertical } from 'lucide-react';

interface CategoryTableProps {
  categories: Category[];
  selectedCategoryId: string | null;
  selectedCategories: string[];
  expandedCategories: Record<string, boolean>;
  subcategoriesByParent: Record<string, Category[]>;
  onSelectCategory: (category: Category) => void;
  onSelectCategoryCheckbox: (categoryId: string, event: React.MouseEvent) => void;
  onEditCategory: (category: Category, event: React.MouseEvent) => void;
  onDeleteCategory: (categoryId: string) => void;
  onToggleExpand: (categoryId: string) => void;
  onSelectAll: () => void;
  selectAllChecked: boolean;
}

const CategoryTable: React.FC<CategoryTableProps> = ({
  categories,
  selectedCategoryId,
  selectedCategories,
  expandedCategories,
  subcategoriesByParent,
  onSelectCategory,
  onSelectCategoryCheckbox,
  onEditCategory,
  onDeleteCategory,
  onToggleExpand,
  onSelectAll,
  selectAllChecked
}) => {
  const { language } = useAppContext();
  const topLevelCategories = categories.filter(cat => !cat.parent_id);
  
  if (categories.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-muted-foreground">
          {language === 'en' 
            ? 'No categories found. Create your first category!' 
            : '未找到分类。创建您的第一个分类！'}
        </p>
      </div>
    );
  }
  
  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]">
              <Checkbox 
                checked={selectAllChecked} 
                onCheckedChange={onSelectAll} 
                aria-label={language === 'en' ? 'Select all' : '全选'}
              />
            </TableHead>
            <TableHead className="w-[50px]"></TableHead>
            <TableHead className="w-[40px]"></TableHead>
            <TableHead>
              {language === 'en' ? 'Name' : '名称'}
            </TableHead>
            <TableHead>
              {language === 'en' ? 'Public' : '公开'}
            </TableHead>
            <TableHead>
              {language === 'en' ? 'Default' : '默认'}
            </TableHead>
            <TableHead>
              {language === 'en' ? 'Type' : '类型'}
            </TableHead>
            <TableHead className="w-[100px] text-right">
              {language === 'en' ? 'Actions' : '操作'}
            </TableHead>
          </TableRow>
        </TableHeader>
        <Droppable droppableId="main-categories" type="category">
          {(provided) => (
            <TableBody
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {topLevelCategories
                .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
                .map((category, index) => (
                  <React.Fragment key={category.id}>
                    <Draggable draggableId={category.id} index={index}>
                      {(provided) => (
                        <CategoryRow 
                          category={category}
                          isSelected={selectedCategories.includes(category.id)}
                          isCurrentCategory={selectedCategoryId === category.id}
                          hasSubcategories={!!subcategoriesByParent[category.id]?.length}
                          isExpanded={!!expandedCategories[category.id]}
                          onSelect={onSelectCategoryCheckbox}
                          onSelectCategory={onSelectCategory}
                          onEdit={onEditCategory}
                          onDelete={onDeleteCategory}
                          onToggleExpand={onToggleExpand}
                          dragHandleProps={provided.dragHandleProps}
                          draggableProps={provided.draggableProps}
                          forwardedRef={provided.innerRef}
                        />
                      )}
                    </Draggable>
                    
                    {expandedCategories[category.id] && subcategoriesByParent[category.id] && (
                      <TableRow>
                        <TableCell colSpan={8} className="p-0">
                          <Droppable droppableId={category.id} type="category">
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                                className="pl-8 border-l-4 border-l-muted"
                              >
                                <Table>
                                  <TableBody>
                                    {subcategoriesByParent[category.id]
                                      ?.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
                                      .map((subcat, index) => (
                                        <Draggable 
                                          key={subcat.id} 
                                          draggableId={subcat.id} 
                                          index={index}
                                        >
                                          {(provided) => (
                                            <CategoryRow 
                                              category={subcat}
                                              isSelected={selectedCategories.includes(subcat.id)}
                                              isCurrentCategory={selectedCategoryId === subcat.id}
                                              hasSubcategories={false}
                                              isExpanded={false}
                                              onSelect={onSelectCategoryCheckbox}
                                              onSelectCategory={onSelectCategory}
                                              onEdit={onEditCategory}
                                              onDelete={onDeleteCategory}
                                              onToggleExpand={onToggleExpand}
                                              isSubcategory={true}
                                              dragHandleProps={provided.dragHandleProps}
                                              draggableProps={provided.draggableProps}
                                              forwardedRef={provided.innerRef}
                                            />
                                          )}
                                        </Draggable>
                                      ))}
                                    {provided.placeholder}
                                  </TableBody>
                                </Table>
                              </div>
                            )}
                          </Droppable>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              {provided.placeholder}
            </TableBody>
          )}
        </Droppable>
      </Table>
    </div>
  );
};

export default CategoryTable;
