
import React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Trash2 } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface CategoryTableHeaderProps {
  selectedCount: number;
  onDeleteSelected: () => void;
  onSelectAll: () => void;
  selectAllChecked: boolean;
}

const CategoryTableHeader: React.FC<CategoryTableHeaderProps> = ({
  selectedCount,
  onDeleteSelected,
  onSelectAll,
  selectAllChecked
}) => {
  const { language } = useAppContext();
  
  return (
    <div className="flex justify-between items-center mb-4">
      <div className="text-sm text-muted-foreground">
        {selectedCount > 0 && (
          <span>
            {language === 'en' 
              ? `${selectedCount} selected` 
              : `已选择 ${selectedCount} 项`}
          </span>
        )}
      </div>
      
      {selectedCount > 0 && (
        <Button 
          variant="destructive" 
          size="sm"
          onClick={onDeleteSelected}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Delete Selected' : '删除所选'}
        </Button>
      )}
    </div>
  );
};

export default CategoryTableHeader;
