import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON><PERSON>er, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import AdminLinkList from './AdminLinkList';
import IconUploader from '@/components/navigation/IconUploader';
import { Category } from '@/hooks/admin/navigation/types';
import { NavLink } from '@/hooks/admin/navigation/types';

interface AdminLinksTabProps {
  categories: Category[];
  links: NavLink[];
  selectedCategory: Category | null;
  newLinkName: string;
  newLinkUrl: string;
  newLinkIcon: string;
  newLinkInternal: boolean;
  newLinkCategoryId: string;
  topLevelCategories: Category[];
  onLinkNameChange: (value: string) => void;
  onLinkUrlChange: (value: string) => void;
  onLinkIconChange: (value: string) => void;
  onLinkInternalChange: (value: boolean) => void;
  onLinkCategoryChange: (value: string) => void;
  onIconSelected: (iconUrl: string) => void;
  onCreateLink: () => void;
  onDeleteLink: (linkId: string) => Promise<void>;
  onEditLink: (link: NavLink) => Promise<void>;
  onBatchDeleteLinks: (linkIds: string[]) => Promise<void>;
}

const AdminLinksTab: React.FC<AdminLinksTabProps> = ({
  categories,
  links,
  selectedCategory,
  newLinkName,
  newLinkUrl,
  newLinkIcon,
  newLinkInternal,
  newLinkCategoryId,
  topLevelCategories,
  onLinkNameChange,
  onLinkUrlChange,
  onLinkIconChange,
  onLinkInternalChange,
  onLinkCategoryChange,
  onIconSelected,
  onCreateLink,
  onDeleteLink,
  onEditLink,
  onBatchDeleteLinks
}) => {
  const { language } = useAppContext();
  
  return (
    <div className="grid gap-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'Add Link' : '添加链接'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="linkCategory">
                {language === 'en' ? 'Main Category' : '主分类'}
              </Label>
              <select
                id="linkCategory"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newLinkCategoryId}
                onChange={(e) => onLinkCategoryChange(e.target.value)}
              >
                <option value="" disabled>
                  {language === 'en' ? 'Select a category' : '选择分类'}
                </option>
                {topLevelCategories.length > 0 && (
                  <optgroup label={language === 'en' ? 'Main Categories' : '主分类'}>
                    {topLevelCategories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </optgroup>
                )}
                {categories.filter(c => c.parent_id).length > 0 && (
                  <optgroup label={language === 'en' ? 'Subcategories' : '子分类'}>
                    {categories.filter(c => c.parent_id).map((category) => {
                      const parent = categories.find(c => c.id === category.parent_id);
                      const parentName = parent ? parent.name : '';
                      return (
                        <option key={category.id} value={category.id}>
                          {parentName && `${parentName} > `}{category.name}
                        </option>
                      );
                    })}
                  </optgroup>
                )}
              </select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="linkName">
                {language === 'en' ? 'Link Name' : '链接名称'}
              </Label>
              <Input
                id="linkName"
                value={newLinkName}
                onChange={(e) => onLinkNameChange(e.target.value)}
                placeholder={language === 'en' ? 'Enter link name' : '输入链接名称'}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="linkUrl">
                {language === 'en' ? 'URL' : '网址'}
              </Label>
              <Input
                id="linkUrl"
                value={newLinkUrl}
                onChange={(e) => onLinkUrlChange(e.target.value)}
                placeholder={language === 'en' ? 'https://example.com' : 'https://示例.com'}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="linkIcon">
                {language === 'en' ? 'Icon URL (optional)' : '图标URL（可选）'}
              </Label>
              <div className="flex gap-2 items-center">
                <Input
                  id="linkIcon"
                  value={newLinkIcon}
                  onChange={(e) => onLinkIconChange(e.target.value)}
                  placeholder={language === 'en' ? 'Enter icon URL' : '输入图标URL'}
                  className="flex-1"
                />
                <IconUploader 
                  onIconSelected={onIconSelected} 
                  currentIconUrl={newLinkIcon}
                />
              </div>
              {newLinkIcon && (
                <div className="mt-2 p-2 border rounded-md w-16 h-16 flex items-center justify-center">
                  <img 
                    src={newLinkIcon} 
                    alt="Icon preview" 
                    className="max-w-full max-h-full"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder.svg';
                    }}
                  />
                </div>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="linkInternal"
                checked={newLinkInternal}
                onCheckedChange={onLinkInternalChange}
              />
              <Label htmlFor="linkInternal">
                {language === 'en' ? 'Internal Link' : '内部链接'}
              </Label>
            </div>
            
            <Button 
              className="w-full" 
              onClick={onCreateLink}
              disabled={categories.length === 0}
            >
              <Plus className="mr-2 h-4 w-4" />
              {language === 'en' ? 'Add Link' : '添加链接'}
            </Button>
            
            {categories.length === 0 && (
              <p className="text-sm text-muted-foreground text-center">
                {language === 'en' 
                  ? 'Create a category first before adding links.' 
                  : '请先创建一个分类，然后再添加链接。'}
              </p>
            )}
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              <span>
                {selectedCategory 
                  ? (language === 'en' 
                      ? `Links in ${selectedCategory.name}` 
                      : `${selectedCategory.name} 中的链接`) 
                  : (language === 'en' ? 'Links' : '链接')}
              </span>
              
              {!selectedCategory && (
                <span className="text-sm font-normal text-muted-foreground">
                  {language === 'en' 
                    ? `Select a category to view its links` 
                    : `选择一个分类以查看其链接`}
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AdminLinkList
              links={links}
              selectedCategory={selectedCategory}
              categories={categories}
              onDeleteLink={onDeleteLink}
              onEditLink={onEditLink}
              onBatchDeleteLinks={onBatchDeleteLinks}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminLinksTab;
