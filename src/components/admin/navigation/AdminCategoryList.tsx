
import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useToast } from '@/components/ui/use-toast';
import { Category } from '@/components/dashboard/navigation/types';
import CategoryTableHeader from './categories/CategoryTableHeader';
import CategoryTable from './categories/CategoryTable';
import EditCategoryDialog from './categories/EditCategoryDialog';
import DeleteConfirmDialog from './categories/DeleteConfirmDialog';

interface AdminCategoryListProps {
  categories: Category[];
  selectedCategoryId: string | null;
  onSelectCategory: (category: Category) => void;
  onDeleteCategory: (categoryId: string) => Promise<void>;
  onEditCategory: (category: Category) => Promise<void>;
  onBatchDeleteCategories: (categoryIds: string[]) => Promise<void>;
}

const AdminCategoryList: React.FC<AdminCategoryListProps> = ({
  categories,
  selectedCategoryId,
  onSelectCategory,
  onDeleteCategory,
  onEditCategory,
  onBatchDeleteCategories
}) => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [editName, setEditName] = useState('');
  const [editIsPublic, setEditIsPublic] = useState(false);
  const [editIsDefault, setEditIsDefault] = useState(false);
  const [editParentId, setEditParentId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const subcategoriesByParent: Record<string, Category[]> = {};
  
  categories.forEach(category => {
    if (category.parent_id) {
      if (!subcategoriesByParent[category.parent_id]) {
        subcategoriesByParent[category.parent_id] = [];
      }
      subcategoriesByParent[category.parent_id].push(category);
    }
  });
  
  const toggleExpand = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };
  
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedCategories([]);
    } else {
      setSelectedCategories(categories.map(category => category.id));
    }
    setSelectAll(!selectAll);
  };
  
  const handleSelectCategory = (categoryId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (selectedCategories.includes(categoryId)) {
      setSelectedCategories(selectedCategories.filter(id => id !== categoryId));
      setSelectAll(false);
    } else {
      setSelectedCategories([...selectedCategories, categoryId]);
      if (selectedCategories.length + 1 === categories.length) {
        setSelectAll(true);
      }
    }
  };
  
  const handleEditClick = (category: Category, event: React.MouseEvent) => {
    event.stopPropagation();
    setEditingCategory(category);
    setEditName(category.name);
    setEditIsPublic(category.is_public);
    setEditIsDefault(category.is_admin_default);
    setEditParentId(category.parent_id);
  };
  
  const handleEditSubmit = async () => {
    if (!editingCategory || !editName.trim()) return;
    
    setIsSubmitting(true);
    
    try {
      await onEditCategory({
        ...editingCategory,
        name: editName.trim(),
        is_public: editIsPublic,
        is_admin_default: editIsDefault,
        parent_id: editParentId
      });
      
      setEditingCategory(null);
      
      toast({
        description: language === 'en' ? "Category updated successfully." : "分类更新成功。",
      });
    } catch (error) {
      console.error('Error updating category:', error);
      
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to update category." : "更新分类失败。",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleDeleteClick = (categoryId: string) => {
    onDeleteCategory(categoryId);
  };
  
  const handleBatchDelete = async () => {
    if (selectedCategories.length === 0) return;
    
    try {
      await onBatchDeleteCategories(selectedCategories);
      setSelectedCategories([]);
      setSelectAll(false);
      setShowConfirmDialog(false);
      
      toast({
        description: language === 'en' 
          ? `${selectedCategories.length} categories deleted successfully.` 
          : `${selectedCategories.length} 个分类删除成功。`,
      });
    } catch (error) {
      console.error('Error batch deleting categories:', error);
      
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to delete categories." : "删除分类失败。",
      });
    }
  };
  
  return (
    <>
      <CategoryTableHeader 
        selectedCount={selectedCategories.length}
        onDeleteSelected={() => setShowConfirmDialog(true)}
        onSelectAll={handleSelectAll}
        selectAllChecked={selectAll}
      />
      
      <CategoryTable 
        categories={categories}
        selectedCategoryId={selectedCategoryId}
        selectedCategories={selectedCategories}
        expandedCategories={expandedCategories}
        subcategoriesByParent={subcategoriesByParent}
        onSelectCategory={onSelectCategory}
        onSelectCategoryCheckbox={handleSelectCategory}
        onEditCategory={handleEditClick}
        onDeleteCategory={handleDeleteClick}
        onToggleExpand={toggleExpand}
        onSelectAll={handleSelectAll}
        selectAllChecked={selectAll}
      />
      
      <EditCategoryDialog 
        isOpen={editingCategory !== null}
        onClose={() => setEditingCategory(null)}
        category={editingCategory}
        editName={editName}
        editIsPublic={editIsPublic}
        editIsDefault={editIsDefault}
        editParentId={editParentId}
        isSubmitting={isSubmitting}
        categories={categories}
        onNameChange={setEditName}
        onPublicChange={setEditIsPublic}
        onDefaultChange={setEditIsDefault}
        onParentChange={setEditParentId}
        onSubmit={handleEditSubmit}
      />
      
      <DeleteConfirmDialog 
        isOpen={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        selectedCount={selectedCategories.length}
        onConfirm={handleBatchDelete}
      />
    </>
  );
};

export default AdminCategoryList;
