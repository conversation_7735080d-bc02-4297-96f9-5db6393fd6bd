
import React from 'react';
import ConfirmationDialog from '@/components/admin/common/ConfirmationDialog';
import { useAppContext } from '@/context/AppContext';

interface ConfirmDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedCount: number;
  onConfirm: () => Promise<void>;
}

const ConfirmDeleteDialog: React.FC<ConfirmDeleteDialogProps> = ({
  open,
  onOpenChange,
  selectedCount,
  onConfirm
}) => {
  const { language } = useAppContext();
  
  const title = language === 'en' ? 'Confirm Deletion' : '确认删除';
  const description = language === 'en' 
    ? `Are you sure you want to delete ${selectedCount} selected links? This action cannot be undone.`
    : `您确定要删除 ${selectedCount} 个所选链接吗？此操作无法撤消。`;
  const confirmLabel = language === 'en' ? 'Delete' : '删除';
  
  return (
    <ConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      title={title}
      description={description}
      confirmLabel={confirmLabel}
      onConfirm={onConfirm}
      variant="destructive"
    />
  );
};

export default ConfirmDeleteDialog;
