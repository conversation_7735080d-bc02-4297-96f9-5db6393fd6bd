
import React from 'react';
import { useAppContext } from '@/context/AppContext';

interface LinkTableEmptyProps {
  hasCategory: boolean;
}

const LinkTableEmpty: React.FC<LinkTableEmptyProps> = ({ hasCategory }) => {
  const { language } = useAppContext();
  
  if (!hasCategory) {
    return (
      <div className="text-center py-4">
        <p className="text-muted-foreground">
          {language === 'en' 
            ? 'Select a category to view its links' 
            : '选择一个分类以查看其链接'}
        </p>
      </div>
    );
  }
  
  return (
    <div className="text-center py-4">
      <p className="text-muted-foreground">
        {language === 'en' 
          ? 'No links found in this category. Add your first link!' 
          : '此分类中未找到链接。添加您的第一个链接！'}
      </p>
    </div>
  );
};

export default LinkTableEmpty;
