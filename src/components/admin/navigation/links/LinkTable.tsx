
import React from 'react';
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { useAppContext } from '@/context/AppContext';
import LinkTableRow from './LinkTableRow';

interface NavLink {
  id: string;
  name: string;
  url: string;
  icon: string | null;
  is_internal: boolean;
  category_id: string;
}

interface LinkTableProps {
  links: NavLink[];
  selectedLinks: string[];
  selectAll: boolean;
  onSelectAll: () => void;
  onSelectLink: (linkId: string) => void;
  onEditClick: (link: NavLink) => void;
  onDeleteLink: (linkId: string) => Promise<void>;
}

const LinkTable: React.FC<LinkTableProps> = ({
  links,
  selectedLinks,
  selectAll,
  onSelectAll,
  onSelectLink,
  onEditClick,
  onDeleteLink
}) => {
  const { language } = useAppContext();
  
  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]">
              <Checkbox 
                checked={selectAll} 
                onCheckedChange={onSelectAll} 
                aria-label={language === 'en' ? 'Select all' : '全选'}
              />
            </TableHead>
            <TableHead className="w-[50px]">
              {language === 'en' ? 'Icon' : '图标'}
            </TableHead>
            <TableHead>
              {language === 'en' ? 'Name' : '名称'}
            </TableHead>
            <TableHead>
              {language === 'en' ? 'URL' : '网址'}
            </TableHead>
            <TableHead>
              {language === 'en' ? 'Type' : '类型'}
            </TableHead>
            <TableHead className="w-[100px] text-right">
              {language === 'en' ? 'Actions' : '操作'}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {links.map((link) => (
            <LinkTableRow
              key={link.id}
              link={link}
              selected={selectedLinks.includes(link.id)}
              onSelect={onSelectLink}
              onEdit={() => onEditClick(link)}
              onDelete={onDeleteLink}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default LinkTable;
