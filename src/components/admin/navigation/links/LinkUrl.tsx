
import React from 'react';
import { ExternalLink } from 'lucide-react';

interface LinkUrlProps {
  url: string;
}

const LinkUrl: React.FC<LinkUrlProps> = ({ url }) => {
  return (
    <a 
      href={url} 
      target="_blank" 
      rel="noopener noreferrer"
      className="text-primary hover:underline flex items-center"
    >
      <span className="truncate">{url}</span>
      <ExternalLink className="ml-1 h-3 w-3 flex-shrink-0" />
    </a>
  );
};

export default LinkUrl;
