
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import ConfirmationDialog from '@/components/admin/common/ConfirmationDialog';

interface LinkSelectionHeaderProps {
  selectedCount: number;
  onDeleteSelected: () => void;
}

const LinkSelectionHeader: React.FC<LinkSelectionHeaderProps> = ({
  selectedCount,
  onDeleteSelected
}) => {
  const { language } = useAppContext();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  
  if (selectedCount === 0) {
    return null;
  }
  
  const handleConfirmDelete = async () => {
    onDeleteSelected();
    setShowConfirmDialog(false);
  };
  
  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <div className="text-sm text-muted-foreground">
          {language === 'en' 
            ? `${selectedCount} selected` 
            : `已选择 ${selectedCount} 项`}
        </div>
        
        <Button 
          variant="destructive" 
          size="sm"
          onClick={() => setShowConfirmDialog(true)}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Delete Selected' : '删除所选'}
        </Button>
      </div>
      
      <ConfirmationDialog
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        title={language === 'en' ? 'Confirm Deletion' : '确认删除'}
        description={language === 'en' 
          ? `Are you sure you want to delete ${selectedCount} selected links? This action cannot be undone.`
          : `您确定要删除 ${selectedCount} 个所选链接吗？此操作无法撤消。`}
        confirmLabel={language === 'en' ? 'Delete' : '删除'}
        onConfirm={handleConfirmDelete}
        variant="destructive"
      />
    </>
  );
};

export default LinkSelectionHeader;
