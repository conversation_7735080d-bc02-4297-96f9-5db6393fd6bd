
import React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { TableRow, TableCell } from '@/components/ui/table';
import LinkIcon from './LinkIcon';
import LinkTypeLabel from './LinkTypeLabel';
import LinkUrl from './LinkUrl';
import LinkActions from './LinkActions';

interface NavLink {
  id: string;
  name: string;
  url: string;
  icon: string | null;
  is_internal: boolean;
  category_id: string;
}

interface LinkTableRowProps {
  link: NavLink;
  selected: boolean;
  onSelect: (linkId: string) => void;
  onEdit: () => void;
  onDelete: (linkId: string) => Promise<void>;
}

const LinkTableRow: React.FC<LinkTableRowProps> = ({
  link,
  selected,
  onSelect,
  onEdit,
  onDelete
}) => {
  return (
    <TableRow>
      <TableCell>
        <Checkbox 
          checked={selected} 
          onCheckedChange={() => onSelect(link.id)}
          aria-label="Select link"
        />
      </TableCell>
      <TableCell>
        <LinkIcon icon={link.icon} name={link.name} />
      </TableCell>
      <TableCell className="font-medium">
        {link.name}
      </TableCell>
      <TableCell className="max-w-[200px] truncate">
        <LinkUrl url={link.url} />
      </TableCell>
      <TableCell>
        <LinkTypeLabel isInternal={link.is_internal} />
      </TableCell>
      <TableCell className="text-right">
        <LinkActions 
          linkId={link.id} 
          onEdit={onEdit} 
          onDelete={onDelete} 
        />
      </TableCell>
    </TableRow>
  );
};

export default LinkTableRow;
