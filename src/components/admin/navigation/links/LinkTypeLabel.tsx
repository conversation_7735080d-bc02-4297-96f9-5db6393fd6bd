
import React from 'react';
import { useAppContext } from '@/context/AppContext';

interface LinkTypeLabelProps {
  isInternal: boolean;
}

const LinkTypeLabel: React.FC<LinkTypeLabelProps> = ({ isInternal }) => {
  const { language } = useAppContext();
  
  if (isInternal) {
    return (
      <span className="text-blue-500">
        {language === 'en' ? 'Internal' : '内部'}
      </span>
    );
  }
  
  return (
    <span className="text-green-500">
      {language === 'en' ? 'External' : '外部'}
    </span>
  );
};

export default LinkTypeLabel;
