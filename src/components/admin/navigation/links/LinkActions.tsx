
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Pencil, Trash2 } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface LinkActionsProps {
  linkId: string;
  onEdit: () => void;
  onDelete: (linkId: string) => Promise<void>;
}

const LinkActions: React.FC<LinkActionsProps> = ({
  linkId,
  onEdit,
  onDelete
}) => {
  const { language } = useAppContext();
  
  return (
    <div className="flex justify-end gap-2">
      <Button
        variant="ghost"
        size="icon"
        onClick={onEdit}
      >
        <Pencil className="h-4 w-4 text-muted-foreground" />
        <span className="sr-only">{language === 'en' ? 'Edit' : '编辑'}</span>
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => onDelete(linkId)}
      >
        <Trash2 className="h-4 w-4 text-destructive" />
        <span className="sr-only">{language === 'en' ? 'Delete' : '删除'}</span>
      </Button>
    </div>
  );
};

export default LinkActions;
