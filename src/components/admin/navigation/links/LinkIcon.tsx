
import React from 'react';
import { ExternalLink } from 'lucide-react';

interface LinkIconProps {
  icon: string | null;
  name: string;
}

const LinkIcon: React.FC<LinkIconProps> = ({ icon, name }) => {
  if (icon) {
    return (
      <div className="w-8 h-8 flex items-center justify-center">
        <img 
          src={icon} 
          alt={name} 
          className="max-w-full max-h-full object-contain" 
          onError={(e) => {
            e.currentTarget.src = '/placeholder.svg';
          }}
        />
      </div>
    );
  }
  
  return (
    <div className="w-8 h-8 flex items-center justify-center bg-muted rounded-md">
      <ExternalLink className="h-4 w-4 text-muted-foreground" />
    </div>
  );
};

export default LinkIcon;
