
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { useAppContext } from '@/context/AppContext';
import IconUploader from '@/components/navigation/IconUploader';
import { Category } from '@/components/dashboard/navigation/types';

interface NavLink {
  id: string;
  name: string;
  url: string;
  icon: string | null;
  is_internal: boolean;
  category_id: string;
}

interface EditLinkDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingLink: NavLink | null;
  categories: Category[];
  onEditSubmit: (link: NavLink) => Promise<void>;
}

const EditLinkDialog: React.FC<EditLinkDialogProps> = ({
  open,
  onOpenChange,
  editingLink,
  categories,
  onEditSubmit
}) => {
  const { language } = useAppContext();
  
  const [editName, setEditName] = useState('');
  const [editUrl, setEditUrl] = useState('');
  const [editIcon, setEditIcon] = useState('');
  const [editIsInternal, setEditIsInternal] = useState(false);
  const [editCategoryId, setEditCategoryId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Initialize form values when editingLink changes
  React.useEffect(() => {
    if (editingLink) {
      setEditName(editingLink.name);
      setEditUrl(editingLink.url);
      setEditIcon(editingLink.icon || '');
      setEditIsInternal(editingLink.is_internal);
      setEditCategoryId(editingLink.category_id);
    }
  }, [editingLink]);
  
  const handleSubmit = async () => {
    if (!editingLink || !editName.trim() || !editUrl.trim() || !editCategoryId) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await onEditSubmit({
        ...editingLink,
        name: editName.trim(),
        url: editUrl.trim(),
        icon: editIcon || null,
        is_internal: editIsInternal,
        category_id: editCategoryId
      });
      
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating link:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {language === 'en' ? 'Edit Link' : '编辑链接'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="edit-category">
              {language === 'en' ? 'Category' : '分类'}
            </Label>
            <select
              id="edit-category"
              className="w-full p-2 border rounded-md"
              value={editCategoryId}
              onChange={(e) => setEditCategoryId(e.target.value)}
              required
            >
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="edit-name">
              {language === 'en' ? 'Name' : '名称'}
            </Label>
            <Input
              id="edit-name"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              placeholder={language === 'en' ? 'Enter link name' : '输入链接名称'}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="edit-url">
              {language === 'en' ? 'URL' : '链接'}
            </Label>
            <Input
              id="edit-url"
              value={editUrl}
              onChange={(e) => setEditUrl(e.target.value)}
              placeholder={language === 'en' ? 'https://example.com' : 'https://示例.com'}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="edit-icon">
              {language === 'en' ? 'Icon' : '图标'}
            </Label>
            <div className="flex gap-2 items-center">
              <Input
                id="edit-icon"
                value={editIcon}
                onChange={(e) => setEditIcon(e.target.value)}
                placeholder={language === 'en' ? 'Icon URL or leave empty' : '图标URL或留空'}
                className="flex-1"
              />
              <IconUploader 
                onIconSelected={setEditIcon} 
                currentIconUrl={editIcon}
              />
            </div>
            {editIcon && (
              <div className="mt-2 p-2 border rounded-md w-16 h-16 flex items-center justify-center">
                <img 
                  src={editIcon} 
                  alt="Icon preview" 
                  className="max-w-full max-h-full"
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder.svg';
                  }}
                />
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="edit-internal"
              checked={editIsInternal}
              onCheckedChange={setEditIsInternal}
            />
            <Label htmlFor="edit-internal">
              {language === 'en' ? 'Internal Link' : '内部链接'}
            </Label>
          </div>
        </div>
        
        <DialogFooter className="sm:justify-end">
          <Button
            variant="secondary"
            onClick={() => onOpenChange(false)}
          >
            {language === 'en' ? 'Cancel' : '取消'}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || !editName.trim() || !editUrl.trim() || !editCategoryId}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                {language === 'en' ? 'Saving...' : '保存中...'}
              </div>
            ) : (
              language === 'en' ? 'Save Changes' : '保存更改'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditLinkDialog;
