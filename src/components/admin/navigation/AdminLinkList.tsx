
import React, { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/dashboard/navigation/types';
import LinkTable from './links/LinkTable';
import LinkTableEmpty from './links/LinkTableEmpty';
import LinkSelectionHeader from './links/LinkSelectionHeader';
import EditLinkDialog from './links/EditLinkDialog';
import ConfirmDeleteDialog from './links/ConfirmDeleteDialog';

interface NavLink {
  id: string;
  name: string;
  url: string;
  icon: string | null;
  is_internal: boolean;
  category_id: string;
}

interface AdminLinkListProps {
  links: NavLink[];
  selectedCategory: Category | null;
  categories: Category[];
  onDeleteLink: (linkId: string) => Promise<void>;
  onEditLink: (link: NavLink) => Promise<void>;
  onBatchDeleteLinks: (linkIds: string[]) => Promise<void>;
}

const AdminLinkList: React.FC<AdminLinkListProps> = ({
  links,
  selectedCategory,
  categories,
  onDeleteLink,
  onEditLink,
  onBatchDeleteLinks
}) => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [selectedLinks, setSelectedLinks] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  
  // Link editing state
  const [editingLink, setEditingLink] = useState<NavLink | null>(null);
  
  // Confirm dialog for batch delete
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedLinks([]);
    } else {
      setSelectedLinks(links.map(link => link.id));
    }
    setSelectAll(!selectAll);
  };
  
  const handleSelectLink = (linkId: string) => {
    if (selectedLinks.includes(linkId)) {
      setSelectedLinks(selectedLinks.filter(id => id !== linkId));
      setSelectAll(false);
    } else {
      setSelectedLinks([...selectedLinks, linkId]);
      if (selectedLinks.length + 1 === links.length) {
        setSelectAll(true);
      }
    }
  };
  
  const handleEditClick = (link: NavLink) => {
    setEditingLink(link);
  };
  
  const handleEditSubmit = async (updatedLink: NavLink) => {
    try {
      await onEditLink(updatedLink);
      
      toast({
        description: language === 'en' ? "Link updated successfully." : "链接更新成功。",
      });
    } catch (error) {
      console.error('Error updating link:', error);
      
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to update link." : "更新链接失败。",
      });
      throw error;
    }
  };
  
  const handleBatchDelete = async () => {
    if (selectedLinks.length === 0) return;
    
    try {
      await onBatchDeleteLinks(selectedLinks);
      setSelectedLinks([]);
      setSelectAll(false);
      setShowConfirmDialog(false);
      
      toast({
        description: language === 'en' 
          ? `${selectedLinks.length} links deleted successfully.` 
          : `${selectedLinks.length} 个链接删除成功。`,
      });
    } catch (error) {
      console.error('Error batch deleting links:', error);
      
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to delete links." : "删除链接失败。",
      });
    }
  };
  
  const handleDeleteSelected = () => {
    setShowConfirmDialog(true);
  };
  
  if (!selectedCategory) {
    return <LinkTableEmpty hasCategory={false} />;
  }
  
  if (links.length === 0) {
    return <LinkTableEmpty hasCategory={true} />;
  }
  
  return (
    <>
      <LinkSelectionHeader 
        selectedCount={selectedLinks.length} 
        onDeleteSelected={handleDeleteSelected} 
      />
      
      <LinkTable 
        links={links}
        selectedLinks={selectedLinks}
        selectAll={selectAll}
        onSelectAll={handleSelectAll}
        onSelectLink={handleSelectLink}
        onEditClick={handleEditClick}
        onDeleteLink={onDeleteLink}
      />
      
      <EditLinkDialog 
        open={editingLink !== null}
        onOpenChange={(open) => !open && setEditingLink(null)}
        editingLink={editingLink}
        categories={categories}
        onEditSubmit={handleEditSubmit}
      />
      
      <ConfirmDeleteDialog 
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        selectedCount={selectedLinks.length}
        onConfirm={handleBatchDelete}
      />
    </>
  );
};

export default AdminLinkList;
