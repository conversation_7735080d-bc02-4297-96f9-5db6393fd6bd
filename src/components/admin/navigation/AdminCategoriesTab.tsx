
import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import AdminCategoryList from './AdminCategoryList';
import { Category } from '@/components/dashboard/navigation/types';

interface AdminCategoriesTabProps {
  categories: Category[];
  selectedCategory: Category | null;
  newCategoryName: string;
  newCategoryPublic: boolean;
  newCategoryDefault: boolean;
  newCategoryParentId: string | null;
  topLevelCategories: Category[];
  onCategoryNameChange: (value: string) => void;
  onCategoryPublicChange: (value: boolean) => void;
  onCategoryDefaultChange: (value: boolean) => void;
  onCategoryParentChange: (value: string | null) => void;
  onCreateCategory: () => void;
  onSelectCategory: (category: Category) => void;
  onDeleteCategory: (categoryId: string) => Promise<void>;
  onEditCategory: (category: Category) => Promise<void>;
  onBatchDeleteCategories: (categoryIds: string[]) => Promise<void>;
}

const AdminCategoriesTab: React.FC<AdminCategoriesTabProps> = ({
  categories,
  selectedCategory,
  newCategoryName,
  newCategoryPublic,
  newCategoryDefault,
  newCategoryParentId,
  topLevelCategories,
  onCategoryNameChange,
  onCategoryPublicChange,
  onCategoryDefaultChange,
  onCategoryParentChange,
  onCreateCategory,
  onSelectCategory,
  onDeleteCategory,
  onEditCategory,
  onBatchDeleteCategories
}) => {
  const { language } = useAppContext();
  
  return (
    <div className="grid gap-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'Add Category' : '添加分类'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="categoryParent">
                {language === 'en' ? 'Parent Category (Optional)' : '父分类（可选）'}
              </Label>
              <select
                id="categoryParent"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newCategoryParentId || ''}
                onChange={(e) => onCategoryParentChange(e.target.value || null)}
              >
                <option value="">
                  {language === 'en' ? '-- No Parent (Top Level) --' : '-- 无父分类（顶级）--'}
                </option>
                {topLevelCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-muted-foreground">
                {language === 'en' 
                  ? "Leave empty to create a top-level category" 
                  : "留空以创建顶级分类"}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="categoryName">
                {language === 'en' ? 'Category Name' : '分类名称'}
              </Label>
              <Input
                id="categoryName"
                value={newCategoryName}
                onChange={(e) => onCategoryNameChange(e.target.value)}
                placeholder={language === 'en' ? 'Enter category name' : '输入分类名称'}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="categoryPublic"
                checked={newCategoryPublic}
                onCheckedChange={onCategoryPublicChange}
              />
              <Label htmlFor="categoryPublic">
                {language === 'en' ? 'Public' : '公开'}
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="categoryDefault"
                checked={newCategoryDefault}
                onCheckedChange={onCategoryDefaultChange}
              />
              <Label htmlFor="categoryDefault">
                {language === 'en' ? 'Default for All Users' : '所有用户默认显示'}
              </Label>
            </div>
            
            <Button 
              className="w-full" 
              onClick={onCreateCategory}
            >
              <Plus className="mr-2 h-4 w-4" />
              {language === 'en' ? 'Create Category' : '创建分类'}
            </Button>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'Categories' : '分类列表'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AdminCategoryList
              categories={categories}
              selectedCategoryId={selectedCategory?.id || null}
              onSelectCategory={onSelectCategory}
              onDeleteCategory={onDeleteCategory}
              onEditCategory={onEditCategory}
              onBatchDeleteCategories={onBatchDeleteCategories}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminCategoriesTab;
