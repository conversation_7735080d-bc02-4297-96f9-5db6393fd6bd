
import React from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAppContext } from '@/context/AppContext';
import { Compass } from 'lucide-react';
import { useAdminNavigationManager } from '@/hooks/admin/useAdminNavigationManager';
import AdminCategoriesTab from './navigation/AdminCategoriesTab';
import AdminLinksTab from './navigation/AdminLinksTab';
import LinkModeration from './navigation/LinkModeration';
import AdminNavigationPreview from './AdminNavigationPreview';
import { DragDropContext } from 'react-beautiful-dnd';
import { NavLink } from '@/hooks/admin/navigation/types';

const AdminNavigationManager = () => {
  const { language } = useAppContext();
  const {
    // State
    categories,
    links,
    selectedCategory,
    newCategoryName,
    newCategoryPublic,
    newCategoryDefault,
    newCategoryParentId,
    newLinkName,
    newLinkUrl,
    newLinkIcon,
    newLinkInternal,
    newLinkCategoryId,
    topLevelCategories,
    
    // Setters
    setSelectedCategory,
    setNewCategoryName,
    setNewCategoryPublic,
    setNewCategoryDefault,
    setNewCategoryParentId,
    setNewLinkName,
    setNewLinkUrl,
    setNewLinkIcon,
    setNewLinkInternal,
    setNewLinkCategoryId,
    
    // Handlers
    handleCreateCategory,
    handleEditCategory,
    handleDeleteCategory,
    handleBatchDeleteCategories,
    handleCreateLink,
    handleEditLink,
    handleDeleteLink,
    handleBatchDeleteLinks,
    handleIconSelected,
    
    // 拖拽处理
    handleDragEnd
  } = useAdminNavigationManager();
  
  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center">
              <Compass className="mr-2 h-5 w-5" />
              {language === 'en' ? 'Site Navigation Management' : '网站导航管理'}
            </div>
          </CardTitle>
          <CardDescription>
            {language === 'en' 
              ? 'Create and manage public navigation categories and links available to all users' 
              : '创建和管理所有用户可用的公共导航分类和链接'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="categories">
            <TabsList className="mb-6">
              <TabsTrigger value="categories">
                {language === 'en' ? 'Categories' : '分类'}
              </TabsTrigger>
              <TabsTrigger value="links">
                {language === 'en' ? 'Links' : '链接'}
              </TabsTrigger>
              <TabsTrigger value="moderation">
                {language === 'en' ? 'Moderation' : '审核'}
              </TabsTrigger>
              <TabsTrigger value="preview">
                {language === 'en' ? 'Preview' : '预览'}
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="categories" className="space-y-6">
              <AdminCategoriesTab
                categories={categories}
                selectedCategory={selectedCategory}
                newCategoryName={newCategoryName}
                newCategoryPublic={newCategoryPublic}
                newCategoryDefault={newCategoryDefault}
                newCategoryParentId={newCategoryParentId}
                topLevelCategories={topLevelCategories}
                onCategoryNameChange={setNewCategoryName}
                onCategoryPublicChange={setNewCategoryPublic}
                onCategoryDefaultChange={setNewCategoryDefault}
                onCategoryParentChange={setNewCategoryParentId}
                onCreateCategory={handleCreateCategory}
                onSelectCategory={setSelectedCategory}
                onDeleteCategory={handleDeleteCategory}
                onEditCategory={handleEditCategory}
                onBatchDeleteCategories={handleBatchDeleteCategories}
              />
            </TabsContent>
            
            <TabsContent value="links" className="space-y-6">
              <AdminLinksTab
                categories={categories}
                links={links}
                selectedCategory={selectedCategory}
                newLinkName={newLinkName}
                newLinkUrl={newLinkUrl}
                newLinkIcon={newLinkIcon}
                newLinkInternal={newLinkInternal}
                newLinkCategoryId={newLinkCategoryId}
                topLevelCategories={topLevelCategories}
                onLinkNameChange={setNewLinkName}
                onLinkUrlChange={setNewLinkUrl}
                onLinkIconChange={setNewLinkIcon}
                onLinkInternalChange={setNewLinkInternal}
                onLinkCategoryChange={setNewLinkCategoryId}
                onIconSelected={handleIconSelected}
                onCreateLink={handleCreateLink}
                onDeleteLink={handleDeleteLink}
                onEditLink={handleEditLink}
                onBatchDeleteLinks={handleBatchDeleteLinks}
              />
            </TabsContent>
            
            <TabsContent value="moderation" className="space-y-6">
              <LinkModeration />
            </TabsContent>
            
            <TabsContent value="preview">
              <AdminNavigationPreview />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </DragDropContext>
  );
};

export default AdminNavigationManager;
