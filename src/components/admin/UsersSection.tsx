import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAppContext } from '@/context/AppContext';
import { UserData, useUserData } from '@/hooks/admin/useUserData';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Loader2, Edit, Trash2 } from 'lucide-react';

const UsersSection = () => {
  const { t, language } = useAppContext();
  const { users, total, isLoading, fetchUsers, updateUser, deleteUser } = useUserData();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  
  const handleSearch = async () => {
    setCurrentPage(1);
    await fetchUsers(1, itemsPerPage, searchTerm);
  };

  const handlePageChange = async (page: number) => {
    setCurrentPage(page);
    await fetchUsers(page, itemsPerPage, searchTerm);
  };

  const handleEditUser = async (user: UserData) => {
    // TODO: 实现编辑用户对话框
    console.log('Edit user:', user);
  };

  const handleDeleteUser = async (user: UserData) => {
    if (window.confirm(language === 'en' 
      ? `Are you sure you want to delete user ${user.username}?`
      : `确认删除用户 ${user.username}？`
    )) {
      try {
        await deleteUser(user.id);
      } catch (error) {
        console.error('Failed to delete user:', error);
      }
    }
  };

  const totalPages = Math.max(1, Math.ceil(total / itemsPerPage));

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('userManagement')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('userManagement')}</CardTitle>
        <CardDescription>
          {language === 'en' ? 'Manage user accounts and permissions.' : '管理用户账户和权限。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-2">
          <Input
            placeholder={language === 'en' ? 'Search users...' : '搜索用户...'}
            className="max-w-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <Button variant="secondary" onClick={handleSearch}>
            {language === 'en' ? 'Search' : '搜索'}
          </Button>
          <Button variant="outline" onClick={() => {
            setSearchTerm('');
            fetchUsers(1, itemsPerPage);
          }}>
            {language === 'en' ? 'Clear' : '清除'}
          </Button>
        </div>
        
        <div className="rounded-md border overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">ID</TableHead>
                <TableHead>{language === 'en' ? 'Username' : '用户名'}</TableHead>
                <TableHead>{language === 'en' ? 'Email' : '邮箱'}</TableHead>
                <TableHead>{language === 'en' ? 'Role' : '角色'}</TableHead>
                <TableHead>{language === 'en' ? 'Status' : '状态'}</TableHead>
                <TableHead>{language === 'en' ? 'Actions' : '操作'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.length > 0 ? (
                users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{user.id}</TableCell>
                    <TableCell className="font-medium">{user.username}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge variant={user.is_super_admin ? 'default' : 'secondary'}>
                        {user.is_super_admin ? 'Super Admin' : user.role}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={user.email_verified ? 'default' : 'secondary'}>
                        {user.email_verified ? 'Verified' : 'Unverified'}
                      </Badge>
                    </TableCell>
                    <TableCell className="flex space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleEditUser(user)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="destructive" 
                        size="sm"
                        onClick={() => handleDeleteUser(user)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center text-muted-foreground py-4">
                    {language === 'en' ? 'No users found.' : '未找到用户。'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {totalPages > 1 && (
          <div className="mt-4 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
                
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNumber: number;
                  
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else if (currentPage <= 3) {
                    pageNumber = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + i;
                  } else {
                    pageNumber = currentPage - 2 + i;
                  }
                  
                  return (
                    <PaginationItem key={pageNumber}>
                      <PaginationLink 
                        onClick={() => handlePageChange(pageNumber)}
                        isActive={currentPage === pageNumber}
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}
                
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
        
        <div className="mt-2 text-center text-sm text-muted-foreground">
          {language === 'en' 
            ? `Showing ${users.length} of ${total} users` 
            : `显示 ${users.length} 共 ${total} 个用户`}
        </div>
      </CardContent>
    </Card>
  );
};

export default UsersSection;
