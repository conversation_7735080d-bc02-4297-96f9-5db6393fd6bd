
import React from 'react';
import { Link2, Mail, Compass, BarChart3, Info, Users, Rocket } from 'lucide-react';

interface FeatureIconProps {
  icon: string;
}

const FeatureIcon: React.FC<FeatureIconProps> = ({ icon }) => {
  switch (icon) {
    case 'link':
      return <Link2 className="h-5 w-5" />;
    case 'mail':
      return <Mail className="h-5 w-5" />;
    case 'compass':
      return <Compass className="h-5 w-5" />;
    case 'bar-chart':
      return <BarChart3 className="h-5 w-5" />;
    case 'info':
      return <Info className="h-5 w-5" />;
    case 'users':
      return <Users className="h-5 w-5" />;
    case 'rocket':
      return <Rocket className="h-5 w-5" />;
    default:
      return <Info className="h-5 w-5" />;
  }
};

export default FeatureIcon;
