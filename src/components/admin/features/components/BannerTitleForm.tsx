import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAppContext } from '@/context/AppContext';
import { BannerConfig } from '@/hooks/useBannerConfig';

interface BannerTitleFormProps {
  bannerConfig: BannerConfig;
  onBannerConfigChange: (field: 'main_title' | 'main_description', lang: 'en' | 'zh', value: string) => void;
  onGridConfigChange?: (field: 'grid_rows' | 'grid_columns', value: number) => void;
}

const BannerTitleForm: React.FC<BannerTitleFormProps> = ({
  bannerConfig,
  onBannerConfigChange,
  onGridConfigChange
}) => {
  const { language } = useAppContext();
  
  return (
    <div className="border rounded-md p-4 space-y-4">
      <h3 className="text-lg font-medium">
        {language === 'en' ? 'Main Title & Description' : '主标题和描述'}
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="main-title-en">
            {language === 'en' ? 'Main Title (English)' : '主标题（英文）'}
          </Label>
          <Input 
            id="main-title-en"
            value={bannerConfig.main_title?.en || ''}
            onChange={(e) => onBannerConfigChange('main_title', 'en', e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="main-title-zh">
            {language === 'en' ? 'Main Title (Chinese)' : '主标题（中文）'}
          </Label>
          <Input 
            id="main-title-zh"
            value={bannerConfig.main_title?.zh || ''}
            onChange={(e) => onBannerConfigChange('main_title', 'zh', e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="main-desc-en">
            {language === 'en' ? 'Description (English)' : '描述（英文）'}
          </Label>
          <Textarea 
            id="main-desc-en"
            value={bannerConfig.main_description?.en || ''}
            onChange={(e) => onBannerConfigChange('main_description', 'en', e.target.value)}
            rows={2}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="main-desc-zh">
            {language === 'en' ? 'Description (Chinese)' : '描述（中文）'}
          </Label>
          <Textarea 
            id="main-desc-zh"
            value={bannerConfig.main_description?.zh || ''}
            onChange={(e) => onBannerConfigChange('main_description', 'zh', e.target.value)}
            rows={2}
          />
        </div>
      </div>
      
      {/* Grid Layout Configuration */}
      <div className="border-t pt-4">
        <h4 className="text-md font-medium mb-3">
          {language === 'en' ? 'Feature Grid Layout' : '功能网格布局'}
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="grid-rows">
              {language === 'en' ? 'Number of Rows' : '行数'}
            </Label>
            <Select
              value={String(bannerConfig.grid_rows || 1)}
              onValueChange={(value) => onGridConfigChange?.('grid_rows', parseInt(value))}
            >
              <SelectTrigger id="grid-rows">
                <SelectValue placeholder={language === 'en' ? 'Select rows' : '选择行数'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 {language === 'en' ? 'Row' : '行'}</SelectItem>
                <SelectItem value="2">2 {language === 'en' ? 'Rows' : '行'}</SelectItem>
                <SelectItem value="3">3 {language === 'en' ? 'Rows' : '行'}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="grid-columns">
              {language === 'en' ? 'Features Per Row' : '每行功能数'}
            </Label>
            <Select
              value={String(bannerConfig.grid_columns || 6)}
              onValueChange={(value) => onGridConfigChange?.('grid_columns', parseInt(value))}
            >
              <SelectTrigger id="grid-columns">
                <SelectValue placeholder={language === 'en' ? 'Select columns' : '选择列数'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2">2 {language === 'en' ? 'Features' : '个功能'}</SelectItem>
                <SelectItem value="3">3 {language === 'en' ? 'Features' : '个功能'}</SelectItem>
                <SelectItem value="4">4 {language === 'en' ? 'Features' : '个功能'}</SelectItem>
                <SelectItem value="5">5 {language === 'en' ? 'Features' : '个功能'}</SelectItem>
                <SelectItem value="6">6 {language === 'en' ? 'Features' : '个功能'}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <p className="text-xs text-muted-foreground mt-2">
          {language === 'en' 
            ? 'Configure how feature cards are displayed in the homepage banner. With 1 row and 6 features, all cards will be shown in a single row.'
            : '配置首页横幅中功能卡片的显示方式。选择1行6个功能时，所有卡片将在一行中显示。'}
        </p>
      </div>
    </div>
  );
};

export default BannerTitleForm;
