
import React from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useAppContext } from '@/context/AppContext';
import { FeatureConfig } from '../types/feature-types';
import { renderIcon } from '../utils/iconUtils';
import { featureIcons, gradientOptions } from '../utils/featureConstants';

interface FeatureEditDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  editingFeature: FeatureConfig | null;
  setEditingFeature: (feature: FeatureConfig | null) => void;
  onSaveFeature: () => void;
}

const FeatureEditDialog: React.FC<FeatureEditDialogProps> = ({
  isOpen,
  onOpenChange,
  editingFeature,
  setEditingFeature,
  onSaveFeature
}) => {
  const { language } = useAppContext();
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {editingFeature?.id.startsWith('feature-') 
              ? (language === 'en' ? 'Add New Feature' : '添加新功能')
              : (language === 'en' ? 'Edit Feature' : '编辑功能')}
          </DialogTitle>
          <DialogDescription>
            {language === 'en' 
              ? 'Customize how this feature appears on the site.'
              : '自定义此功能在网站上的显示方式。'}
          </DialogDescription>
        </DialogHeader>
        
        {editingFeature && (
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title-en">
                  {language === 'en' ? 'Title (English)' : '标题（英文）'}
                </Label>
                <Input
                  id="title-en"
                  value={editingFeature.title.en}
                  onChange={(e) => setEditingFeature({
                    ...editingFeature,
                    title: { ...editingFeature.title, en: e.target.value }
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="title-zh">
                  {language === 'en' ? 'Title (Chinese)' : '标题（中文）'}
                </Label>
                <Input
                  id="title-zh"
                  value={editingFeature.title.zh}
                  onChange={(e) => setEditingFeature({
                    ...editingFeature,
                    title: { ...editingFeature.title, zh: e.target.value }
                  })}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="desc-en">
                  {language === 'en' ? 'Description (English)' : '描述（英文）'}
                </Label>
                <Textarea
                  id="desc-en"
                  value={editingFeature.description.en}
                  onChange={(e) => setEditingFeature({
                    ...editingFeature,
                    description: { ...editingFeature.description, en: e.target.value }
                  })}
                  rows={2}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="desc-zh">
                  {language === 'en' ? 'Description (Chinese)' : '描述（中文）'}
                </Label>
                <Textarea
                  id="desc-zh"
                  value={editingFeature.description.zh}
                  onChange={(e) => setEditingFeature({
                    ...editingFeature,
                    description: { ...editingFeature.description, zh: e.target.value }
                  })}
                  rows={2}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="icon">
                  {language === 'en' ? 'Icon' : '图标'}
                </Label>
                <Select
                  value={editingFeature.icon}
                  onValueChange={(value) => setEditingFeature({
                    ...editingFeature,
                    icon: value
                  })}
                >
                  <SelectTrigger id="icon">
                    <SelectValue placeholder={language === 'en' ? 'Select icon' : '选择图标'} />
                  </SelectTrigger>
                  <SelectContent>
                    {featureIcons.map((icon) => (
                      <SelectItem key={icon.value} value={icon.value}>
                        <div className="flex items-center gap-2">
                          {renderIcon(icon.value)}
                          {icon.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="gradient">
                  {language === 'en' ? 'Icon Background' : '图标背景'}
                </Label>
                <Select
                  value={editingFeature.iconBgGradient}
                  onValueChange={(value) => setEditingFeature({
                    ...editingFeature,
                    iconBgGradient: value
                  })}
                >
                  <SelectTrigger id="gradient">
                    <SelectValue placeholder={language === 'en' ? 'Select gradient' : '选择渐变'} />
                  </SelectTrigger>
                  <SelectContent>
                    {gradientOptions.map((grad) => (
                      <SelectItem key={grad.value} value={grad.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-4 h-4 rounded bg-gradient-to-br ${grad.value}`}></div>
                          {grad.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="section">
                  {language === 'en' ? 'Section' : '区域'}
                </Label>
                <Select
                  value={editingFeature.section}
                  onValueChange={(value) => setEditingFeature({
                    ...editingFeature,
                    section: value
                  })}
                >
                  <SelectTrigger id="section">
                    <SelectValue placeholder={language === 'en' ? 'Select section' : '选择区域'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="main">Main Banner</SelectItem>
                    <SelectItem value="features">Features Section</SelectItem>
                    <SelectItem value="sidebar">Sidebar</SelectItem>
                    <SelectItem value="footer">Footer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="visible">
                    {language === 'en' ? 'Visible' : '可见'}
                  </Label>
                  <Switch 
                    id="visible" 
                    checked={editingFeature.visible}
                    onCheckedChange={(checked) => setEditingFeature({
                      ...editingFeature,
                      visible: checked
                    })}
                  />
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="badge-en" className="flex items-center gap-2">
                  <span>{language === 'en' ? 'Badge (English)' : '角标（英文）'}</span>
                  <span className="text-xs text-muted-foreground">
                    {language === 'en' ? '(Optional)' : '（可选）'}
                  </span>
                </Label>
                <Input
                  id="badge-en"
                  value={editingFeature.badge?.en || ''}
                  onChange={(e) => setEditingFeature({
                    ...editingFeature,
                    badge: { 
                      ...(editingFeature.badge || { en: '', zh: '' }),
                      en: e.target.value 
                    }
                  })}
                  placeholder={language === 'en' ? 'e.g. NEW' : '例如：NEW'}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="badge-zh" className="flex items-center gap-2">
                  <span>{language === 'en' ? 'Badge (Chinese)' : '角标（中文）'}</span>
                  <span className="text-xs text-muted-foreground">
                    {language === 'en' ? '(Optional)' : '（可选）'}
                  </span>
                </Label>
                <Input
                  id="badge-zh"
                  value={editingFeature.badge?.zh || ''}
                  onChange={(e) => setEditingFeature({
                    ...editingFeature,
                    badge: { 
                      ...(editingFeature.badge || { en: '', zh: '' }),
                      zh: e.target.value 
                    }
                  })}
                  placeholder={language === 'en' ? 'e.g. 新功能' : '例如：新功能'}
                />
              </div>
            </div>
          </div>
        )}
        
        <DialogFooter>
          <Button variant="ghost" onClick={() => onOpenChange(false)}>
            {language === 'en' ? 'Cancel' : '取消'}
          </Button>
          <Button onClick={onSaveFeature}>
            {language === 'en' ? 'Save Changes' : '保存更改'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FeatureEditDialog;
