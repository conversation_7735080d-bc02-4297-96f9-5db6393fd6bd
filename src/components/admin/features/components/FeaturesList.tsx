
import React from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, ArrowUpDown, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppContext } from '@/context/AppContext';
import { FeatureConfig } from '../types/feature-types';
import { renderIcon } from '../utils/iconUtils';

interface FeaturesListProps {
  features: FeatureConfig[];
  loading: boolean;
  onToggleFeatureVisibility: (id: string) => void;
  onEditFeature: (feature: FeatureConfig) => void;
  onDeleteFeature: (id: string) => void;
  onDragEnd: (result: any) => void;
  onAddNewFeature: () => void;
}

const FeaturesList: React.FC<FeaturesListProps> = ({
  features,
  loading,
  onToggleFeatureVisibility,
  onEditFeature,
  onDeleteFeature,
  onDragEnd,
  onAddNewFeature
}) => {
  const { language } = useAppContext();
  
  return (
    <div className="border rounded-md p-4">
      <div className="flex justify-between mb-6">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">
            {language === 'en' ? 'Banner Features' : '横幅功能'}
          </h3>
          <p className="text-sm text-muted-foreground">
            {language === 'en' 
              ? 'Add, edit, or remove features. Drag to reorder.'
              : '添加、编辑或删除功能。拖动以重新排序。'}
          </p>
        </div>
        <Button onClick={onAddNewFeature} className="gap-1">
          <Plus className="h-4 w-4" />
          {language === 'en' ? 'Add Feature' : '添加功能'}
        </Button>
      </div>
      
      {loading ? (
        <div className="space-y-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="h-16 bg-muted animate-pulse rounded" />
          ))}
        </div>
      ) : (
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="features">
            {(provided) => (
              <div 
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="space-y-2"
              >
                {features.map((feature, index) => (
                  <Draggable 
                    key={feature.id} 
                    draggableId={feature.id} 
                    index={index}
                  >
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={cn(
                          "flex items-center justify-between p-3 border rounded-md bg-card",
                          !feature.visible && "opacity-60"
                        )}
                      >
                        <div className="flex items-center gap-4">
                          <div 
                            {...provided.dragHandleProps}
                            className="cursor-grab hover:bg-muted p-1 rounded"
                          >
                            <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
                          </div>
                          
                          <div className={cn(
                            "w-8 h-8 rounded flex items-center justify-center bg-gradient-to-br",
                            feature.iconBgGradient
                          )}>
                            <div className="text-white">
                              {renderIcon(feature.icon)}
                            </div>
                          </div>
                          
                          <div>
                            <div className="font-medium">
                              {feature.title[language === 'en' ? 'en' : 'zh']}
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {feature.section} section
                            </div>
                          </div>
                          
                          {feature.badge && (
                            <Badge variant="outline" className="ml-2">
                              {feature.badge[language === 'en' ? 'en' : 'zh']}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <div className="flex items-center space-x-2 mr-4">
                            <Label htmlFor={`visible-${feature.id}`} className="text-sm">
                              {language === 'en' ? 'Show' : '显示'}
                            </Label>
                            <Switch 
                              id={`visible-${feature.id}`} 
                              checked={feature.visible}
                              onCheckedChange={() => onToggleFeatureVisibility(feature.id)}
                            />
                          </div>
                          
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => onEditFeature(feature)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            onClick={() => onDeleteFeature(feature.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
    </div>
  );
};

export default FeaturesList;
