
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { ArrowUpDown, Save, EyeOff, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { Json } from '@/integrations/supabase/types';

interface PageComponent {
  id: string;
  name: { en: string; zh: string };
  component: string;
  visible: boolean;
  order: number;
}

const AdminFeaturesOrderingSection: React.FC = () => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [components, setComponents] = useState<PageComponent[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchPageComponents();
  }, []);

  const fetchPageComponents = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('feature_ordering')
        .select('*')
        .eq('component_type', 'home_page')
        .limit(1);

      if (error) {
        throw error;
      }

      if (data && data.length > 0 && data[0].components_config) {
        // Cast to the expected type with explicit type assertion
        setComponents((data[0].components_config as unknown) as PageComponent[]);
      } else {
        // Use default components
        setComponents(defaultComponents);
      }
    } catch (err) {
      console.error('Error fetching page components:', err);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en'
          ? 'Failed to load page components.'
          : '加载页面组件失败。'
      });
      setComponents(defaultComponents);
    } finally {
      setLoading(false);
    }
  };

  const savePageComponents = async () => {
    try {
      setSaving(true);

      // Check if record exists
      const { data: existingData, error: checkError } = await supabase
        .from('feature_ordering')
        .select('id')
        .eq('component_type', 'home_page')
        .limit(1);

      if (checkError) throw checkError;

      if (existingData && existingData.length > 0) {
        // Update existing record with explicit type casting to Json
        const { error: updateError } = await supabase
          .from('feature_ordering')
          .update({ components_config: (components as unknown) as Json })
          .eq('id', existingData[0].id);

        if (updateError) throw updateError;
      } else {
        // Insert new record with explicit type casting to Json
        const { error: insertError } = await supabase
          .from('feature_ordering')
          .insert({
            component_type: 'home_page',
            components_config: (components as unknown) as Json
          });

        if (insertError) throw insertError;
      }

      toast({
        description: language === 'en'
          ? 'Page components saved successfully.'
          : '页面组件已成功保存。'
      });
    } catch (err) {
      console.error('Error saving page components:', err);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en'
          ? 'Failed to save page components.'
          : '保存页面组件失败。'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(components);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index
    }));

    setComponents(updatedItems);

    toast({
      description: language === 'en'
        ? 'Order updated. Remember to save your changes.'
        : '顺序已更新。记得保存您的更改。',
      duration: 2000,
    });
  };

  const toggleComponentVisibility = (id: string) => {
    setComponents(prevComponents =>
      prevComponents.map(component =>
        component.id === id
          ? { ...component, visible: !component.visible }
          : component
      )
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {language === 'en' ? 'Homepage Components Order' : '首页组件顺序'}
        </CardTitle>
        <CardDescription>
          {language === 'en'
            ? 'Reorder homepage components and toggle their visibility.'
            : '重新排序首页组件并切换它们的可见性。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-14 w-full" />
            ))}
          </div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="components">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-2"
                >
                  {components.map((component, index) => (
                    <Draggable
                      key={component.id}
                      draggableId={component.id}
                      index={index}
                    >
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={cn(
                            "flex items-center justify-between p-4 border rounded-md bg-card",
                            !component.visible && "opacity-60"
                          )}
                        >
                          <div className="flex items-center gap-4">
                            <div
                              {...provided.dragHandleProps}
                              className="cursor-grab hover:bg-muted p-1 rounded"
                            >
                              <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <div>
                              <div className="font-medium">
                                {component.name[language === 'en' ? 'en' : 'zh']}
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                Component: {component.component}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center">
                            <div className="flex items-center space-x-2">
                              {component.visible ? (
                                <Eye className="h-4 w-4 text-muted-foreground mr-2" />
                              ) : (
                                <EyeOff className="h-4 w-4 text-muted-foreground mr-2" />
                              )}
                              <Switch
                                checked={component.visible}
                                onCheckedChange={() => toggleComponentVisibility(component.id)}
                                aria-label={`Toggle ${component.name.en} visibility`}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={fetchPageComponents}
          disabled={loading || saving}
        >
          {language === 'en' ? 'Reset' : '重置'}
        </Button>
        <Button
          onClick={savePageComponents}
          disabled={loading || saving}
        >
          {saving && <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>}
          <Save className="mr-2 h-4 w-4" />
          {language === 'en' ? 'Save Order' : '保存顺序'}
        </Button>
      </CardFooter>
    </Card>
  );
};

// Default components for homepage
const defaultComponents: PageComponent[] = [
  {
    id: 'hero',
    name: { en: 'Hero Banner', zh: '英雄横幅' },
    component: 'HeroSection',
    visible: true,
    order: 0
  },
  {
    id: 'features',
    name: { en: 'Features Section', zh: '功能部分' },
    component: 'HomeFeaturesSection',
    visible: true,
    order: 1
  },
  {
    id: 'hot-news',
    name: { en: 'Hot News Section', zh: '热榜部分' },
    component: 'HomeHotNewsSection',
    visible: true,
    order: 2
  },
  {
    id: 'navigation',
    name: { en: 'Navigation Section', zh: '导航部分' },
    component: 'HomeNavigationSection',
    visible: true,
    order: 3
  },
  {
    id: 'todo-memo',
    name: { en: 'Todo & Memo Section', zh: '待办与备忘录部分' },
    component: 'TodoMemoSection',
    visible: true,
    order: 4
  },
  {
    id: 'about',
    name: { en: 'About Section', zh: '关于部分' },
    component: 'HomeAboutSection',
    visible: true,
    order: 5
  },
  {
    id: 'cta',
    name: { en: 'Call to Action', zh: '行动召唤' },
    component: 'HomeCTASection',
    visible: true,
    order: 6
  }
];

export default AdminFeaturesOrderingSection;
