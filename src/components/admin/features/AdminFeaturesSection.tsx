import React from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Save } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import BannerTitleForm from './components/BannerTitleForm';
import FeaturesList from './components/FeaturesList';
import FeatureEditDialog from './components/FeatureEditDialog';
import { useBannerFeatures } from './hooks/useBannerFeatures';

const AdminFeaturesSection: React.FC = () => {
  const { language } = useAppContext();
  
  const {
    loading,
    saving,
    features,
    bannerConfig,
    editingFeature,
    isDialogOpen,
    setIsDialogOpen,
    setEditingFeature,
    fetchFeatures,
    saveFeatures,
    handleDragEnd,
    toggleFeatureVisibility,
    editFeature,
    addNewFeature,
    saveFeature,
    deleteFeature,
    handleBannerConfigChange,
    handleGridConfigChange
  } = useBannerFeatures(language);

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'en' ? 'Banner Configuration' : '横幅配置'}
          </CardTitle>
          <CardDescription>
            {language === 'en' 
              ? 'Customize the features and main title displayed in the homepage banner.'
              : '自定义首页横幅中显示的功能和主标题。'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Main Title & Description Configuration */}
          <BannerTitleForm 
            bannerConfig={bannerConfig}
            onBannerConfigChange={handleBannerConfigChange}
            onGridConfigChange={handleGridConfigChange}
          />
          
          {/* Features Configuration */}
          <FeaturesList
            features={features}
            loading={loading}
            onToggleFeatureVisibility={toggleFeatureVisibility}
            onEditFeature={editFeature}
            onDeleteFeature={deleteFeature}
            onDragEnd={handleDragEnd}
            onAddNewFeature={addNewFeature}
          />
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button 
            variant="outline" 
            onClick={fetchFeatures}
            disabled={loading || saving}
          >
            {language === 'en' ? 'Reset' : '重置'}
          </Button>
          <Button 
            onClick={saveFeatures} 
            disabled={loading || saving}
          >
            {saving && <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>}
            <Save className="mr-2 h-4 w-4" />
            {language === 'en' ? 'Save Configuration' : '保存配置'}
          </Button>
        </CardFooter>
      </Card>
      
      {/* Feature Edit Dialog */}
      <FeatureEditDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        editingFeature={editingFeature}
        setEditingFeature={setEditingFeature}
        onSaveFeature={saveFeature}
      />
    </>
  );
};

export default AdminFeaturesSection;
