import React from 'react';
import { 
  Link, 
  Mail, 
  Compass, 
  Bar<PERSON>hart, 
  ListTodo, 
  StickyNote,
  Info,
  Users,
  Globe,
  Settings,
  Search,
  Star,
  Code,
  Database,
  Shield,
  Lock,
  Key,
  Heart,
  ThumbsUp,
  Bookmark,
  Folder,
  File,
  Image,
  Video,
  Music,
  Download,
  Upload,
  Share,
  Copy,
  Scissors,
  Edit,
  Trash,
  Plus,
  Minus,
  X,
  Check,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  ExternalLink,
  RefreshCw,
  Home,
  Phone,
  MessageCircle,
  Bell,
  Calendar,
  Clock,
  MapPin,
  Camera,
  Mic,
  Headphones,
  Smartphone,
  Laptop,
  Monitor,
  Server,
  Wifi,
  Bluetooth,
  Battery,
  Zap,
  Sun,
  Moon,
  Cloud,
  Umbrella,
  Gift,
  ShoppingCart,
  CreditCard,
  DollarSign,
  TrendingUp,
  TrendingDown,
  PieChart,
  Activity,
  Target,
  Award,
  Trophy,
  Flag,
  Tag,
  Layers,
  Grid3X3,
  Layout,
  Maximize,
  Minimize,
  Move,
  RotateCw,
  Eye,
  EyeOff,
  <PERSON>ch,
  <PERSON>,
  Paintbrush,
  Palette,
  Book,
  BookmarkPlus,
  GraduationCap,
  Briefcase,
  Building,
  Car,
  Plane,
  Ship,
  Train,
  Truck,
  Bike,
  Gamepad2,
  <PERSON>uzzle,
  Dices
} from 'lucide-react';

// Map of icon names to their corresponding Lucide React components
export const iconMap = {
  "link": Link,
  "mail": Mail,
  "compass": Compass,
  "bar-chart": BarChart,
  "list-todo": ListTodo,
  "sticky-note": StickyNote,
  "info": Info,
  "users": Users,
  "globe": Globe,
  "settings": Settings,
  "search": Search,
  "star": Star,
  "code": Code,
  "database": Database,
  "shield": Shield,
  "lock": Lock,
  "key": Key,
  "heart": Heart,
  "thumbs-up": ThumbsUp,
  "bookmark": Bookmark,
  "folder": Folder,
  "file": File,
  "image": Image,
  "video": Video,
  "music": Music,
  "download": Download,
  "upload": Upload,
  "share": Share,
  "copy": Copy,
  "scissors": Scissors,
  "edit": Edit,
  "trash": Trash,
  "plus": Plus,
  "minus": Minus,
  "x": X,
  "check": Check,
  "arrow-right": ArrowRight,
  "arrow-left": ArrowLeft,
  "arrow-up": ArrowUp,
  "arrow-down": ArrowDown,
  "external-link": ExternalLink,
  "refresh": RefreshCw,
  "home": Home,
  "phone": Phone,
  "message-circle": MessageCircle,
  "bell": Bell,
  "calendar": Calendar,
  "clock": Clock,
  "map-pin": MapPin,
  "camera": Camera,
  "mic": Mic,
  "headphones": Headphones,
  "smartphone": Smartphone,
  "laptop": Laptop,
  "monitor": Monitor,
  "server": Server,
  "wifi": Wifi,
  "bluetooth": Bluetooth,
  "battery": Battery,
  "zap": Zap,
  "sun": Sun,
  "moon": Moon,
  "cloud": Cloud,
  "umbrella": Umbrella,
  "gift": Gift,
  "shopping-cart": ShoppingCart,
  "credit-card": CreditCard,
  "dollar-sign": DollarSign,
  "trending-up": TrendingUp,
  "trending-down": TrendingDown,
  "pie-chart": PieChart,
  "activity": Activity,
  "target": Target,
  "award": Award,
  "trophy": Trophy,
  "flag": Flag,
  "tag": Tag,
  "layers": Layers,
  "grid": Grid3X3,
  "layout": Layout,
  "maximize": Maximize,
  "minimize": Minimize,
  "move": Move,
  "rotate-cw": RotateCw,
  "eye": Eye,
  "eye-off": EyeOff,
  "tool": Settings,
  "wrench": Wrench,
  "hammer": Hammer,
  "paintbrush": Paintbrush,
  "palette": Palette,
  "book": Book,
  "bookmark-plus": BookmarkPlus,
  "graduation-cap": GraduationCap,
  "briefcase": Briefcase,
  "building": Building,
  "car": Car,
  "plane": Plane,
  "ship": Ship,
  "train": Train,
  "truck": Truck,
  "bike": Bike,
  "gamepad": Gamepad2,
  "puzzle": Puzzle,
  "dices": Dices,
  "sidebar": Settings
};

// Render icon component from icon name
export const renderIcon = (iconName: string, size = 16) => {
  const IconComponent = iconMap[iconName as keyof typeof iconMap] || Star; // Default to Star if icon not found
  return <IconComponent size={size} />;
};
