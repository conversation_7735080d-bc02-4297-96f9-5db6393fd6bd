// Feature icon options for dropdown
export const featureIcons = [
  { value: "link", label: "Link" },
  { value: "mail", label: "Mail" },
  { value: "compass", label: "Compass" },
  { value: "bar-chart", label: "Chart" },
  { value: "list-todo", label: "Todo" },
  { value: "sticky-note", label: "Note" },
  { value: "info", label: "Info" },
  { value: "users", label: "Users" },
  { value: "globe", label: "Globe" },
  { value: "settings", label: "Settings" },
  { value: "search", label: "Search" },
  { value: "star", label: "Star" },
  { value: "code", label: "Code" },
  { value: "database", label: "Database" },
  { value: "shield", label: "Shield" },
  { value: "lock", label: "Lock" },
  { value: "key", label: "Key" },
  { value: "heart", label: "Heart" },
  { value: "thumbs-up", label: "Thumbs Up" },
  { value: "bookmark", label: "Bookmark" },
  { value: "folder", label: "Folder" },
  { value: "file", label: "File" },
  { value: "image", label: "Image" },
  { value: "video", label: "Video" },
  { value: "music", label: "Music" },
  { value: "download", label: "Download" },
  { value: "upload", label: "Upload" },
  { value: "share", label: "Share" },
  { value: "copy", label: "Copy" },
  { value: "scissors", label: "Cut" },
  { value: "edit", label: "Edit" },
  { value: "trash", label: "Trash" },
  { value: "plus", label: "Plus" },
  { value: "minus", label: "Minus" },
  { value: "x", label: "Close" },
  { value: "check", label: "Check" },
  { value: "arrow-right", label: "Arrow Right" },
  { value: "arrow-left", label: "Arrow Left" },
  { value: "arrow-up", label: "Arrow Up" },
  { value: "arrow-down", label: "Arrow Down" },
  { value: "external-link", label: "External Link" },
  { value: "refresh", label: "Refresh" },
  { value: "home", label: "Home" },
  { value: "phone", label: "Phone" },
  { value: "message-circle", label: "Chat" },
  { value: "bell", label: "Bell" },
  { value: "calendar", label: "Calendar" },
  { value: "clock", label: "Clock" },
  { value: "map-pin", label: "Location" },
  { value: "camera", label: "Camera" },
  { value: "mic", label: "Microphone" },
  { value: "headphones", label: "Headphones" },
  { value: "smartphone", label: "Smartphone" },
  { value: "laptop", label: "Laptop" },
  { value: "monitor", label: "Monitor" },
  { value: "server", label: "Server" },
  { value: "wifi", label: "WiFi" },
  { value: "bluetooth", label: "Bluetooth" },
  { value: "battery", label: "Battery" },
  { value: "zap", label: "Lightning" },
  { value: "sun", label: "Sun" },
  { value: "moon", label: "Moon" },
  { value: "cloud", label: "Cloud" },
  { value: "umbrella", label: "Umbrella" },
  { value: "gift", label: "Gift" },
  { value: "shopping-cart", label: "Shopping Cart" },
  { value: "credit-card", label: "Credit Card" },
  { value: "dollar-sign", label: "Dollar" },
  { value: "trending-up", label: "Trending Up" },
  { value: "trending-down", label: "Trending Down" },
  { value: "pie-chart", label: "Pie Chart" },
  { value: "activity", label: "Activity" },
  { value: "target", label: "Target" },
  { value: "award", label: "Award" },
  { value: "trophy", label: "Trophy" },
  { value: "flag", label: "Flag" },
  { value: "tag", label: "Tag" },
  { value: "layers", label: "Layers" },
  { value: "grid", label: "Grid" },
  { value: "layout", label: "Layout" },
  { value: "sidebar", label: "Sidebar" },
  { value: "maximize", label: "Maximize" },
  { value: "minimize", label: "Minimize" },
  { value: "move", label: "Move" },
  { value: "rotate-cw", label: "Rotate" },
  { value: "eye", label: "Eye" },
  { value: "eye-off", label: "Eye Off" },
  { value: "tool", label: "Tool" },
  { value: "wrench", label: "Wrench" },
  { value: "hammer", label: "Hammer" },
  { value: "paintbrush", label: "Paintbrush" },
  { value: "palette", label: "Palette" },
  { value: "book", label: "Book" },
  { value: "bookmark-plus", label: "Bookmark Plus" },
  { value: "graduation-cap", label: "Education" },
  { value: "briefcase", label: "Business" },
  { value: "building", label: "Building" },
  { value: "car", label: "Car" },
  { value: "plane", label: "Plane" },
  { value: "ship", label: "Ship" },
  { value: "train", label: "Train" },
  { value: "truck", label: "Truck" },
  { value: "bike", label: "Bike" },
  { value: "gamepad", label: "Gaming" },
  { value: "puzzle", label: "Puzzle" },
  { value: "dices", label: "Dice" }
];

// Gradient options for icon backgrounds
export const gradientOptions = [
  { value: "from-primary to-brand-500", label: "Primary to Brand" },
  { value: "from-accent1-400 to-brand-500", label: "Accent to Brand" },
  { value: "from-primary to-accent1-400", label: "Primary to Accent" },
  { value: "from-violet-500 to-purple-500", label: "Violet to Purple" },
  { value: "from-blue-500 to-cyan-500", label: "Blue to Cyan" },
  { value: "from-emerald-500 to-green-500", label: "Emerald to Green" },
  { value: "from-amber-500 to-orange-500", label: "Amber to Orange" },
  { value: "from-rose-500 to-pink-500", label: "Rose to Pink" },
  { value: "from-blue-500 to-blue-600", label: "Blue" },
  { value: "from-green-500 to-green-600", label: "Green" },
  { value: "from-purple-500 to-purple-600", label: "Purple" },
  { value: "from-red-500 to-red-600", label: "Red" },
  { value: "from-yellow-500 to-yellow-600", label: "Yellow" },
  { value: "from-indigo-500 to-indigo-600", label: "Indigo" },
  { value: "from-pink-500 to-pink-600", label: "Pink" },
  { value: "from-gray-500 to-gray-600", label: "Gray" },
  { value: "from-teal-500 to-teal-600", label: "Teal" },
  { value: "from-orange-500 to-orange-600", label: "Orange" },
  { value: "from-slate-500 to-slate-600", label: "Slate" },
  { value: "from-zinc-500 to-zinc-600", label: "Zinc" },
  { value: "from-stone-500 to-stone-600", label: "Stone" },
  { value: "from-neutral-500 to-neutral-600", label: "Neutral" },
  { value: "from-lime-500 to-lime-600", label: "Lime" },
  { value: "from-cyan-500 to-cyan-600", label: "Cyan" },
  { value: "from-sky-500 to-sky-600", label: "Sky" },
  { value: "from-violet-500 to-violet-600", label: "Violet" },
  { value: "from-fuchsia-500 to-fuchsia-600", label: "Fuchsia" },
  { value: "from-emerald-500 to-emerald-600", label: "Emerald" }
];

// Default features when no data exists
export const defaultFeatures = [
  {
    id: 'url-shortener',
    title: { en: 'URL Shortener', zh: '缩短网址' },
    description: { en: 'Create short links quickly', zh: '快速创建短链接' },
    icon: 'link',
    iconBgGradient: 'from-primary to-brand-500',
    badge: { en: 'POPULAR', zh: '热门' },
    order: 0,
    visible: true,
    section: 'main'
  },
  {
    id: 'email',
    title: { en: 'Temporary Email', zh: '临时邮箱' },
    description: { en: 'Create disposable emails', zh: '创建一次性邮箱' },
    icon: 'mail',
    iconBgGradient: 'from-blue-500 to-cyan-500',
    order: 1,
    visible: true,
    section: 'main'
  },
  {
    id: 'navigation',
    title: { en: 'Navigation Hub', zh: '导航中心' },
    description: { en: 'Discover useful resources', zh: '发现有用资源' },
    icon: 'compass',
    iconBgGradient: 'from-violet-500 to-purple-500',
    order: 2,
    visible: true,
    section: 'main'
  },
  {
    id: 'hot-news',
    title: { en: 'Hot News', zh: '今日热榜' },
    description: { en: 'Stay updated with trends', zh: '掌握最新热点' },
    icon: 'bar-chart',
    iconBgGradient: 'from-amber-500 to-orange-500',
    order: 3,
    visible: true,
    section: 'main'
  },
  {
    id: 'todo-memo',
    title: { en: 'Todo & Memos', zh: '待办与备忘录' },
    description: { en: 'Organize your tasks', zh: '整理您的任务' },
    icon: 'list-todo',
    iconBgGradient: 'from-emerald-500 to-green-500',
    badge: { en: 'NEW', zh: '新功能' },
    order: 4,
    visible: true,
    section: 'main'
  }
];
