
import { useState } from 'react';
import { FeatureConfig } from '../types/feature-types';

export const useFeatureActions = (
  features: FeatureConfig[],
  setFeatures: React.Dispatch<React.SetStateAction<FeatureConfig[]>>,
  language: string,
  toast: any
) => {
  const [editingFeature, setEditingFeature] = useState<FeatureConfig | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    
    const items = Array.from(features);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    // Update order
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index
    }));
    
    setFeatures(updatedItems);
    
    toast({
      description: language === 'en' 
        ? 'Order updated. Remember to save your changes.'
        : '顺序已更新。记得保存您的更改。',
      duration: 2000,
    });
  };
  
  const toggleFeatureVisibility = (id: string) => {
    setFeatures(prevFeatures => 
      prevFeatures.map(feature => 
        feature.id === id 
          ? { ...feature, visible: !feature.visible } 
          : feature
      )
    );
  };
  
  const editFeature = (feature: FeatureConfig) => {
    setEditingFeature(feature);
    setIsDialogOpen(true);
  };
  
  const addNewFeature = () => {
    const newFeature: FeatureConfig = {
      id: `feature-${Date.now()}`,
      title: { en: 'New Feature', zh: '新功能' },
      description: { en: 'Feature description', zh: '功能描述' },
      icon: 'star',
      iconBgGradient: 'from-primary to-brand-500',
      order: features.length,
      visible: true,
      section: 'main'
    };
    
    setEditingFeature(newFeature);
    setIsDialogOpen(true);
  };
  
  const saveFeature = () => {
    if (!editingFeature) return;
    
    if (features.some(f => f.id === editingFeature.id)) {
      // Update existing
      setFeatures(prevFeatures => 
        prevFeatures.map(feature => 
          feature.id === editingFeature.id 
            ? editingFeature 
            : feature
        )
      );
    } else {
      // Add new
      setFeatures(prevFeatures => [...prevFeatures, editingFeature]);
    }
    
    setIsDialogOpen(false);
    setEditingFeature(null);
  };
  
  const deleteFeature = (id: string) => {
    setFeatures(prevFeatures => 
      prevFeatures.filter(feature => feature.id !== id)
    );
    
    toast({
      description: language === 'en' 
        ? 'Feature deleted. Remember to save your changes.'
        : '功能已删除。记得保存您的更改。',
      duration: 2000,
    });
  };

  return {
    editingFeature,
    isDialogOpen,
    setIsDialogOpen,
    setEditingFeature,
    handleDragEnd,
    toggleFeatureVisibility,
    editFeature,
    addNewFeature,
    saveFeature,
    deleteFeature
  };
};
