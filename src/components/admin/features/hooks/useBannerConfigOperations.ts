import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import { <PERSON><PERSON> } from '@/integrations/supabase/types';
import { BannerConfig } from '@/hooks/useBannerConfig';

export const fetchBannerConfig = async (): Promise<BannerConfig> => {
  try {
    // Initialize with default values
    const config: BannerConfig = {
      main_title: { en: 'All-in-One Productivity Platform', zh: '一站式生产力平台' },
      main_description: { en: 'Streamline your workflow with powerful tools designed for modern productivity', zh: '使用为现代生产力设计的强大工具简化您的工作流程' },
      grid_rows: 1,
      grid_columns: 6
    };

    if (isUsingSupabase()) {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase
        .from('banner_config')
        .select('*')
        .limit(1);

      if (error) {
        console.error('Error fetching banner config:', error);
        throw error;
      }

      if (data && data.length > 0) {
        const rawConfig = data[0] as any;

        // If there's existing data for main title and description in the database
        if (rawConfig.main_title) {
          try {
            config.main_title = typeof rawConfig.main_title === 'string'
              ? JSON.parse(rawConfig.main_title)
              : rawConfig.main_title as { en: string; zh: string };
          } catch (e) {
            console.error('Error parsing main_title:', e);
          }
        }

        if (rawConfig.main_description) {
          try {
            config.main_description = typeof rawConfig.main_description === 'string'
              ? JSON.parse(rawConfig.main_description)
              : rawConfig.main_description as { en: string; zh: string };
          } catch (e) {
            console.error('Error parsing main_description:', e);
          }
        }

        // Add other banner config fields if they exist
        if (rawConfig.first_text !== undefined) config.first_text = rawConfig.first_text;
        if (rawConfig.first_gradient !== undefined) config.first_gradient = rawConfig.first_gradient;
        if (rawConfig.second_text !== undefined) config.second_text = rawConfig.second_text;
        if (rawConfig.second_gradient !== undefined) config.second_gradient = rawConfig.second_gradient;
        if (rawConfig.third_text !== undefined) config.third_text = rawConfig.third_text;
        if (rawConfig.third_gradient !== undefined) config.third_gradient = rawConfig.third_gradient;
        if (rawConfig.fourth_text !== undefined) config.fourth_text = rawConfig.fourth_text;
        if (rawConfig.fourth_gradient !== undefined) config.fourth_gradient = rawConfig.fourth_gradient;
        if (rawConfig.use_third_line !== undefined) config.use_third_line = rawConfig.use_third_line;
        if (rawConfig.height !== undefined) config.height = rawConfig.height;
        if (rawConfig.spacing !== undefined) config.spacing = rawConfig.spacing;
        if (rawConfig.animation_speed !== undefined) config.animation_speed = rawConfig.animation_speed;
        if (rawConfig.custom_class !== undefined) config.custom_class = rawConfig.custom_class;
        if (rawConfig.display_style !== undefined) config.display_style = rawConfig.display_style as any;
        if (rawConfig.grid_rows !== undefined) config.grid_rows = rawConfig.grid_rows;
        if (rawConfig.grid_columns !== undefined) config.grid_columns = rawConfig.grid_columns;
        if (rawConfig.id !== undefined) config.id = rawConfig.id;
      }
    } else {
      // Use Go backend API
      const backendConfig = getBackendConfig();
      try {
        const response = await fetch(`${backendConfig.goBackend?.baseUrl}/banner/config`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const rawConfig = await response.json();

          // Parse JSON fields if they exist
          if (rawConfig.main_title) {
            try {
              config.main_title = typeof rawConfig.main_title === 'string'
                ? JSON.parse(rawConfig.main_title)
                : rawConfig.main_title as { en: string; zh: string };
            } catch (e) {
              console.error('Error parsing main_title:', e);
            }
          }

          if (rawConfig.main_description) {
            try {
              config.main_description = typeof rawConfig.main_description === 'string'
                ? JSON.parse(rawConfig.main_description)
                : rawConfig.main_description as { en: string; zh: string };
            } catch (e) {
              console.error('Error parsing main_description:', e);
            }
          }

          // Add other banner config fields if they exist
          if (rawConfig.first_text !== undefined) config.first_text = rawConfig.first_text;
          if (rawConfig.first_gradient !== undefined) config.first_gradient = rawConfig.first_gradient;
          if (rawConfig.second_text !== undefined) config.second_text = rawConfig.second_text;
          if (rawConfig.second_gradient !== undefined) config.second_gradient = rawConfig.second_gradient;
          if (rawConfig.third_text !== undefined) config.third_text = rawConfig.third_text;
          if (rawConfig.third_gradient !== undefined) config.third_gradient = rawConfig.third_gradient;
          if (rawConfig.fourth_text !== undefined) config.fourth_text = rawConfig.fourth_text;
          if (rawConfig.fourth_gradient !== undefined) config.fourth_gradient = rawConfig.fourth_gradient;
          if (rawConfig.use_third_line !== undefined) config.use_third_line = rawConfig.use_third_line;
          if (rawConfig.height !== undefined) config.height = rawConfig.height;
          if (rawConfig.spacing !== undefined) config.spacing = rawConfig.spacing;
          if (rawConfig.animation_speed !== undefined) config.animation_speed = rawConfig.animation_speed;
          if (rawConfig.custom_class !== undefined) config.custom_class = rawConfig.custom_class;
          if (rawConfig.display_style !== undefined) config.display_style = rawConfig.display_style as any;
          if (rawConfig.grid_rows !== undefined) config.grid_rows = rawConfig.grid_rows;
          if (rawConfig.grid_columns !== undefined) config.grid_columns = rawConfig.grid_columns;
          if (rawConfig.id !== undefined) config.id = rawConfig.id;
        }
      } catch (error) {
        console.warn('Failed to fetch banner config from Go backend, using defaults:', error);
      }
    }

    return config;
  } catch (error) {
    console.error('Failed to fetch banner config:', error);
    throw error;
  }
};

export const saveBannerConfig = async (bannerConfig: BannerConfig): Promise<void> => {
  try {
    console.log('Saving banner config:', bannerConfig);

    if (isUsingSupabase()) {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data: bannerData, error: bannerCheckError } = await supabase
        .from('banner_config')
        .select('id')
        .limit(1);

      if (bannerCheckError) {
        console.error('Error checking existing banner config:', bannerCheckError);
        throw bannerCheckError;
      }

    // 确保main_title和main_description有正确的格式
    const validMainTitle = bannerConfig.main_title || {
      en: 'All-in-One Productivity Platform',
      zh: '一站式生产力平台'
    };
    const validMainDescription = bannerConfig.main_description || {
      en: 'Streamline your workflow with powerful tools designed for modern productivity',
      zh: '使用为现代生产力设计的强大工具简化您的工作流程'
    };

    // Prepare the banner config data to save
    const bannerConfigToSave: Record<string, any> = {
      main_title: JSON.stringify(validMainTitle),
      main_description: JSON.stringify(validMainDescription)
    };

    // Add standard banner config fields if they exist in our current state
    if (bannerConfig.first_text !== undefined) bannerConfigToSave['first_text'] = bannerConfig.first_text;
    if (bannerConfig.first_gradient !== undefined) bannerConfigToSave['first_gradient'] = bannerConfig.first_gradient;
    if (bannerConfig.second_text !== undefined) bannerConfigToSave['second_text'] = bannerConfig.second_text;
    if (bannerConfig.second_gradient !== undefined) bannerConfigToSave['second_gradient'] = bannerConfig.second_gradient;
    if (bannerConfig.third_text !== undefined) bannerConfigToSave['third_text'] = bannerConfig.third_text;
    if (bannerConfig.third_gradient !== undefined) bannerConfigToSave['third_gradient'] = bannerConfig.third_gradient;
    if (bannerConfig.fourth_text !== undefined) bannerConfigToSave['fourth_text'] = bannerConfig.fourth_text;
    if (bannerConfig.fourth_gradient !== undefined) bannerConfigToSave['fourth_gradient'] = bannerConfig.fourth_gradient;
    if (bannerConfig.use_third_line !== undefined) bannerConfigToSave['use_third_line'] = bannerConfig.use_third_line;
    if (bannerConfig.height !== undefined) bannerConfigToSave['height'] = bannerConfig.height;
    if (bannerConfig.spacing !== undefined) bannerConfigToSave['spacing'] = bannerConfig.spacing;
    if (bannerConfig.animation_speed !== undefined) bannerConfigToSave['animation_speed'] = bannerConfig.animation_speed;
    if (bannerConfig.custom_class !== undefined) bannerConfigToSave['custom_class'] = bannerConfig.custom_class;
    if (bannerConfig.display_style !== undefined) bannerConfigToSave['display_style'] = bannerConfig.display_style;
    if (bannerConfig.grid_rows !== undefined) bannerConfigToSave['grid_rows'] = bannerConfig.grid_rows;
    if (bannerConfig.grid_columns !== undefined) bannerConfigToSave['grid_columns'] = bannerConfig.grid_columns;

    console.log('Banner config to save:', bannerConfigToSave);

    if (bannerData && bannerData.length > 0) {
      // Update existing banner config
      console.log('Updating existing banner config with ID:', bannerData[0].id);
      const { error: bannerUpdateError } = await supabase
        .from('banner_config')
        .update(bannerConfigToSave)
        .eq('id', bannerData[0].id);

      if (bannerUpdateError) {
        console.error('Error updating banner config:', bannerUpdateError);
        throw bannerUpdateError;
      }
      console.log('Banner config updated successfully');
    } else {
      // Insert new banner config record with default values for required fields
      console.log('Inserting new banner config');
      const newBannerConfig = {
        ...bannerConfigToSave,
        first_text: bannerConfig.first_text || '短网址',
        first_gradient: bannerConfig.first_gradient || 'from-blue-500 to-blue-600',
        second_text: bannerConfig.second_text || '临时邮箱',
        second_gradient: bannerConfig.second_gradient || 'from-green-500 to-green-600'
      };

      console.log('New banner config:', newBannerConfig);
      const { error: bannerInsertError } = await supabase
        .from('banner_config')
        .insert(newBannerConfig);

      if (bannerInsertError) {
        console.error('Error inserting banner config:', bannerInsertError);
        throw bannerInsertError;
      }
      console.log('Banner config inserted successfully');
    }
    } else {
      // Use Go backend API
      const backendConfig = getBackendConfig();

      // 确保main_title和main_description有正确的格式
      const validMainTitle = bannerConfig.main_title || {
        en: 'All-in-One Productivity Platform',
        zh: '一站式生产力平台'
      };
      const validMainDescription = bannerConfig.main_description || {
        en: 'Streamline your workflow with powerful tools designed for modern productivity',
        zh: '使用为现代生产力设计的强大工具简化您的工作流程'
      };

      const bannerConfigToSave = {
        main_title: validMainTitle,
        main_description: validMainDescription,
        grid_rows: bannerConfig.grid_rows,
        grid_columns: bannerConfig.grid_columns
      };

      const response = await fetch(`${backendConfig.goBackend?.baseUrl}/banner/config`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bannerConfigToSave),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log('Banner config saved successfully to Go backend');
    }
  } catch (error) {
    console.error('Failed to save banner config:', error);
    throw error;
  }
};
