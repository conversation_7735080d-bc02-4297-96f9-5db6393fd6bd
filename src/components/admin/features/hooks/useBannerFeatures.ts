import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { FeatureConfig } from '../types/feature-types';
import { defaultFeatures } from '../utils/featureConstants';
import { BannerConfig } from '@/hooks/useBannerConfig';
import { fetchFeatures, saveFeatures } from './useFeatureOperations';
import { fetchBannerConfig, saveBannerConfig } from './useBannerConfigOperations';
import { useFeatureActions } from './useFeatureActions';

export const useBannerFeatures = (language: string) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [features, setFeatures] = useState<FeatureConfig[]>([]);
  const [bannerConfig, setBannerConfig] = useState<BannerConfig>({
    main_title: { en: 'All-in-One Toolbox', zh: '一站式工具集' },
    main_description: { en: 'Boost your productivity', zh: '提升工作效率' }
  });
  
  const featureActions = useFeatureActions(features, setFeatures, language, toast);
  
  useEffect(() => {
    fetchFeaturesData();
    fetchBannerConfigData();
  }, []);
  
  const fetchFeaturesData = async () => {
    try {
      setLoading(true);
      const featuresData = await fetchFeatures();
      setFeatures(featuresData);
    } catch (err) {
      console.error('Error fetching features:', err);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' 
          ? 'Failed to load feature configurations.' 
          : '加载功能配置失败。'
      });
      // Fallback to default
      setFeatures(defaultFeatures);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchBannerConfigData = async () => {
    try {
      const config = await fetchBannerConfig();
      setBannerConfig(config);
    } catch (err) {
      console.error('Error fetching banner config:', err);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' 
          ? 'Failed to load banner title configuration.' 
          : '加载横幅标题配置失败。'
      });
    }
  };
  
  const saveFeaturesData = async () => {
    try {
      setSaving(true);
      
      await saveFeatures(features);
      await saveBannerConfig(bannerConfig);
      
      toast({
        description: language === 'en' 
          ? 'Feature configurations saved successfully.' 
          : '功能配置已成功保存。'
      });
    } catch (err) {
      console.error('Error saving features:', err);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' 
          ? 'Failed to save feature configurations.' 
          : '保存功能配置失败。'
      });
    } finally {
      setSaving(false);
    }
  };
  
  const handleBannerConfigChange = (field: 'main_title' | 'main_description', lang: 'en' | 'zh', value: string) => {
    setBannerConfig(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        [lang]: value
      }
    }));
  };
  
  const handleGridConfigChange = (field: 'grid_rows' | 'grid_columns', value: number) => {
    setBannerConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  return {
    loading,
    saving,
    features,
    bannerConfig,
    fetchFeatures: fetchFeaturesData,
    saveFeatures: saveFeaturesData,
    handleBannerConfigChange,
    handleGridConfigChange,
    ...featureActions
  };
};
