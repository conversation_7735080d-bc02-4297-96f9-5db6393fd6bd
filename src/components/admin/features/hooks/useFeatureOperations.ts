import { supabase } from '@/integrations/supabase/client';
import { J<PERSON> } from '@/integrations/supabase/types';
import { FeatureConfig } from '../types/feature-types';
import { defaultFeatures } from '../utils/featureConstants';

export const fetchFeatures = async (): Promise<FeatureConfig[]> => {
  try {
    // Try to get features from feature_ordering table
    const { data, error } = await supabase
      .from('feature_ordering')
      .select('*')
      .eq('component_type', 'banner_features')
      .limit(1);
      
    if (error) {
      console.warn('Error fetching features from database:', error);
      // If there's an error (like RLS policy), return default features
      return defaultFeatures;
    }
    
    if (data && data.length > 0 && data[0].components_config) {
      // Parse the JSON config with proper type assertion
      const featureConfigs = data[0].components_config as unknown as FeatureConfig[];
      return featureConfigs;
    } else {
      // Use default if no data
      return defaultFeatures;
    }
  } catch (err) {
    console.warn('Failed to fetch features, using defaults:', err);
    return defaultFeatures;
  }
};

export const saveFeatures = async (features: FeatureConfig[]): Promise<void> => {
  try {
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      throw new Error('用户认证失败：' + userError.message);
    }
    
    if (!user) {
      throw new Error('您必须登录后才能保存配置');
    }
    
    // Check if record exists for this user
    const { data: existingData, error: checkError } = await supabase
      .from('feature_ordering')
      .select('id')
      .eq('component_type', 'banner_features')
      .eq('user_id', user.id)
      .limit(1);
      
    if (checkError) {
      console.error('Error checking existing data:', checkError);
      throw checkError;
    }
    
    if (existingData && existingData.length > 0) {
      // Update existing record with proper type casting
      const { error: updateError } = await supabase
        .from('feature_ordering')
        .update({ 
          components_config: features as unknown as Json,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingData[0].id);
        
      if (updateError) {
        console.error('Error updating features:', updateError);
        throw updateError;
      }
    } else {
      // Insert new record with proper type casting
      const { error: insertError } = await supabase
        .from('feature_ordering')
        .insert({
          component_type: 'banner_features',
          components_config: features as unknown as Json,
          user_id: user.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        
      if (insertError) {
        console.error('Error inserting features:', insertError);
        throw insertError;
      }
    }
  } catch (err) {
    console.error('Failed to save features:', err);
    throw err;
  }
};
