
import React from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { GripVertical, Link2, Mail, Compass, BarChart3, Info, Users, Rocket } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface Component {
  id: string;
  name: {
    en: string;
    zh: string;
  };
  icon: string;
  visible: boolean;
}

interface AdminFeatureItemProps {
  component: Component;
  index: number;
  onVisibilityToggle: (id: string) => void;
  language: string;
}

const AdminFeatureItem: React.FC<AdminFeatureItemProps> = ({
  component,
  index,
  onVisibilityToggle,
  language,
}) => {
  return (
    <Draggable draggableId={component.id} index={index}>
      {(provided) => (
        <li 
          ref={provided.innerRef}
          {...provided.draggableProps}
          className="flex items-center p-3 bg-card rounded-md border border-border hover:border-muted-foreground/30 transition-colors"
        >
          <div className="mr-3 p-1 rounded-md bg-muted/50" {...provided.dragHandleProps}>
            <GripVertical className="h-5 w-5 text-muted-foreground" />
          </div>
          <div className="mr-3">
            {component.icon === 'link' && <Link2 className="h-5 w-5" />}
            {component.icon === 'mail' && <Mail className="h-5 w-5" />}
            {component.icon === 'compass' && <Compass className="h-5 w-5" />}
            {component.icon === 'bar-chart' && <BarChart3 className="h-5 w-5" />}
            {component.icon === 'info' && <Info className="h-5 w-5" />}
            {component.icon === 'users' && <Users className="h-5 w-5" />}
            {component.icon === 'rocket' && <Rocket className="h-5 w-5" />}
          </div>
          <div className="flex-1">
            <p className="font-medium">
              {language === 'en' ? component.name.en : component.name.zh}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox 
              id={`admin-visibility-${component.id}`}
              checked={component.visible}
              onCheckedChange={() => onVisibilityToggle(component.id)}
            />
            <Label htmlFor={`admin-visibility-${component.id}`} className="cursor-pointer mr-4">
              {language === 'en' ? 'Visible' : '显示'}
            </Label>
          </div>
        </li>
      )}
    </Draggable>
  );
};

export default AdminFeatureItem;
