import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, CheckCircle, XCircle, AlertCircle, LinkIcon } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { useToast } from '@/components/ui/use-toast';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';

interface LinkModerationListProps {
  refreshTrigger?: number;
}

interface UserWithUsername {
  username: string;
}

type LinkSubmission = {
  id: string;
  name: string;
  url: string;
  icon: string | null;
  category_id: string;
  is_internal: boolean;
  submission_status: 'pending' | 'approved' | 'rejected';
  submitted_by: string;
  created_at: string;
  category?: {
    name: string;
  };
  submitted_by_user?: UserWithUsername;
};

const LinkModerationList: React.FC<LinkModerationListProps> = ({ refreshTrigger }) => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [pendingLinks, setPendingLinks] = useState<LinkSubmission[]>([]);
  const [recentlyModerated, setRecentlyModerated] = useState<LinkSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchSubmissions();
  }, [refreshTrigger]);

  const fetchSubmissions = async () => {
    setLoading(true);
    try {
      // 获取待审核的链接
      const { data: pendingData, error: pendingError } = await supabase
        .from('nav_links')
        .select(`
          *,
          category:category_id(name),
          submitted_by_user:submitted_by(username)
        `)
        .eq('submission_status', 'pending')
        .order('created_at', { ascending: false });

      if (pendingError) throw pendingError;

      // 获取最近审核过的链接
      const { data: recentData, error: recentError } = await supabase
        .from('nav_links')
        .select(`
          *,
          category:category_id(name),
          submitted_by_user:submitted_by(username)
        `)
        .in('submission_status', ['approved', 'rejected'])
        .order('updated_at', { ascending: false })
        .limit(5);

      if (recentError) throw recentError;

      // 转换数据为 LinkSubmission 类型
      const typedPendingData: LinkSubmission[] = pendingData?.map(item => ({
        id: item.id,
        name: item.name,
        url: item.url,
        icon: item.icon,
        category_id: item.category_id,
        is_internal: item.is_internal || false,
        submission_status: item.submission_status as 'pending' | 'approved' | 'rejected',
        submitted_by: item.submitted_by || '',
        created_at: item.created_at,
        category: item.category,
        submitted_by_user: item.submitted_by_user && typeof item.submitted_by_user === 'object' ? {
          username: (item.submitted_by_user as UserWithUsername).username || ''
        } : undefined
      })) || [];

      const typedRecentData: LinkSubmission[] = recentData?.map(item => ({
        id: item.id,
        name: item.name,
        url: item.url,
        icon: item.icon,
        category_id: item.category_id,
        is_internal: item.is_internal || false,
        submission_status: item.submission_status as 'pending' | 'approved' | 'rejected',
        submitted_by: item.submitted_by || '',
        created_at: item.created_at,
        category: item.category,
        submitted_by_user: item.submitted_by_user && typeof item.submitted_by_user === 'object' ? {
          username: (item.submitted_by_user as UserWithUsername).username || ''
        } : undefined
      })) || [];

      setPendingLinks(typedPendingData);
      setRecentlyModerated(typedRecentData);
    } catch (error) {
      console.error('Error fetching link submissions:', error);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en'
          ? 'Failed to load link submissions'
          : '加载链接提交失败',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (link: LinkSubmission) => {
    setProcessingIds(prev => new Set(prev).add(link.id));

    try {
      const { error } = await supabase
        .from('nav_links')
        .update({ submission_status: 'approved' })
        .eq('id', link.id);

      if (error) throw error;

      toast({
        title: language === 'en' ? 'Link Approved' : '链接已批准',
        description: language === 'en'
          ? 'The link has been approved and added to the directory.'
          : '链接已批准并添加到目录中。',
      });

      // 更新本地状态
      setPendingLinks(prev => prev.filter(item => item.id !== link.id));
      setRecentlyModerated(prev => [
        { ...link, submission_status: 'approved' },
        ...prev.slice(0, 4)
      ]);
    } catch (error) {
      console.error('Error approving link:', error);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en'
          ? 'Failed to approve link'
          : '批准链接失败',
      });
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(link.id);
        return newSet;
      });
    }
  };

  const handleReject = async (link: LinkSubmission) => {
    setProcessingIds(prev => new Set(prev).add(link.id));

    try {
      const { error } = await supabase
        .from('nav_links')
        .update({ submission_status: 'rejected' })
        .eq('id', link.id);

      if (error) throw error;

      toast({
        title: language === 'en' ? 'Link Rejected' : '链接已拒绝',
        description: language === 'en'
          ? 'The link has been rejected.'
          : '链接已被拒绝。',
      });

      // 更新本地状态
      setPendingLinks(prev => prev.filter(item => item.id !== link.id));
      setRecentlyModerated(prev => [
        { ...link, submission_status: 'rejected' },
        ...prev.slice(0, 4)
      ]);
    } catch (error) {
      console.error('Error rejecting link:', error);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en'
          ? 'Failed to reject link'
          : '拒绝链接失败',
      });
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(link.id);
        return newSet;
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {language === 'en' ? 'Pending' : '待审核'}
          </Badge>
        );
      case 'approved':
        return (
          <Badge variant="outline" className="flex items-center gap-1 text-green-600 border-green-600">
            <CheckCircle className="h-3 w-3" />
            {language === 'en' ? 'Approved' : '已批准'}
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="flex items-center gap-1 text-red-600 border-red-600">
            <XCircle className="h-3 w-3" />
            {language === 'en' ? 'Rejected' : '已拒绝'}
          </Badge>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
        <p className="mt-4 text-muted-foreground">
          {language === 'en' ? 'Loading submissions...' : '加载提交...'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'en' ? 'Pending Link Submissions' : '待审核链接提交'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {pendingLinks.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              {language === 'en' ? 'No pending submissions' : '没有待审核的提交'}
            </div>
          ) : (
            <div className="space-y-4">
              {pendingLinks.map(link => (
                <Card key={link.id} className="overflow-hidden">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="h-12 w-12 flex items-center justify-center rounded-md bg-muted">
                        {link.icon ? (
                          <img
                            src={link.icon}
                            alt=""
                            className="h-8 w-8 object-contain"
                            onError={(e) => {
                              e.currentTarget.src = '/placeholder.svg';
                            }}
                          />
                        ) : (
                          <LinkIcon className="h-6 w-6 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                          <h3 className="font-medium truncate">{link.name}</h3>
                          {getStatusBadge(link.submission_status)}
                        </div>
                        <a
                          href={link.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-500 hover:underline flex items-center gap-1 mt-0.5"
                        >
                          {link.url}
                          <ExternalLink className="h-3 w-3" />
                        </a>
                        <div className="mt-1.5 text-xs text-muted-foreground flex flex-wrap gap-x-4 gap-y-1">
                          <span>{language === 'en' ? 'Category:' : '分类:'} {link.category?.name}</span>
                          <span>{language === 'en' ? 'Submitted by:' : '提交者:'} {link.submitted_by_user?.username || link.submitted_by}</span>
                          <span>{language === 'en' ? 'Submitted on:' : '提交日期:'} {new Date(link.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="px-4 py-2 bg-muted/30 flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReject(link)}
                      disabled={processingIds.has(link.id)}
                    >
                      <XCircle className="h-4 w-4 mr-1" />
                      {language === 'en' ? 'Reject' : '拒绝'}
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleApprove(link)}
                      disabled={processingIds.has(link.id)}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      {language === 'en' ? 'Approve' : '批准'}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {recentlyModerated.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'Recently Moderated' : '最近审核'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentlyModerated.map(link => (
                <div key={link.id} className="flex items-center gap-3 p-2 rounded-md hover:bg-muted/50">
                  <div className="h-8 w-8 flex items-center justify-center rounded-md bg-muted">
                    {link.icon ? (
                      <img
                        src={link.icon}
                        alt=""
                        className="h-5 w-5 object-contain"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder.svg';
                        }}
                      />
                    ) : (
                      <LinkIcon className="h-4 w-4 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm truncate">{link.name}</span>
                      {getStatusBadge(link.submission_status)}
                    </div>
                    <a
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-500 hover:underline truncate block"
                    >
                      {link.url}
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LinkModerationList;