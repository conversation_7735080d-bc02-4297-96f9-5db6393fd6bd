import React, { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAppContext } from '@/context/AppContext';
import EmailDomainsManager from './EmailDomainsManager';
import S3ConfigManager from './S3ConfigManager';
import { InfoIcon } from 'lucide-react';

const SettingsSection = () => {
  const { t, language } = useAppContext();
  const [currentDomain, setCurrentDomain] = useState(window.location.hostname);

  // 获取当前站点域名
  useEffect(() => {
    const domain = window.location.hostname;
    setCurrentDomain(domain);
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('systemSettings')}</CardTitle>
        <CardDescription>
          {language === 'en' ? 'Configure system-wide settings.' : '配置系统范围的设置。'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-2">{language === 'en' ? 'URL Settings' : '链接设置'}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <InfoIcon className="h-5 w-5 text-blue-500" />
                <label className="text-sm font-medium flex items-center gap-2">
                  {language === 'en' ? 'Domain for Short URLs' : '短链接域名'}
                  <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                    {language === 'en' ? 'System Default' : '系统默认'}
                  </span>
                </label>
              </div>
              <div className="p-3 bg-blue-50 rounded-md border border-blue-100">
                <p className="font-medium text-blue-700">{currentDomain}</p>
                <p className="text-sm text-blue-600 mt-1">
                  {language === 'en'
                    ? 'Short URLs always use current domain to maximize redirection speed.'
                    : '短链接始终使用当前域名以最大化重定向速度。'}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">{language === 'en' ? 'Default URL Expiration (days, -1 for never)' : '默认链接过期时间（天，-1为永不过期）'}</label>
              <Input type="number" defaultValue="-1" />
            </div>
          </div>
        </div>

        <div className="border-t pt-6">
          <EmailDomainsManager />
        </div>

        <div className="border-t pt-6">
          <S3ConfigManager />
        </div>

        <Button className="mt-4">
          {language === 'en' ? 'Save Settings' : '保存设置'}
        </Button>
      </CardContent>
    </Card>
  );
};

export default SettingsSection;
