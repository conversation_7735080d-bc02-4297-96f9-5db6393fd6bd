import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { isUsingSupabase } from '@/config/backend';
import { useAppContext } from '@/context/AppContext';
import { ExternalLink, LinkIcon, GripVertical, X, Network } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { Category, NavLink, PingResult } from '../dashboard/navigation/types';

const AdminNavigationPreview = () => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [mainCategories, setMainCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Record<string, Category[]>>({});
  const [activeMainCategory, setActiveMainCategory] = useState<string | null>(null);
  const [activeSubcategory, setActiveSubcategory] = useState<string | null>(null);
  const [links, setLinks] = useState<Record<string, NavLink[]>>({});
  const [pingResults, setPingResults] = useState<Record<string, PingResult>>({});
  const [isReordering, setIsReordering] = useState(false);
  const [isSorting, setIsSorting] = useState(false);

  // 获取当前活动的分类ID
  const activeCategoryId = activeSubcategory || activeMainCategory;

  // Fetch categories and organize them by main/sub
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);

      try {
        // 管理员面板下只获取公共分类
        const { data, error } = await supabase
          .from('nav_categories')
          .select('*')
          .is('user_id', null) // Admin categories don't have a user_id
          .order('sort_order', { ascending: true });

        if (error) {
          console.error('Error fetching categories:', error);
          setLoading(false);
          return;
        }

        if (data && data.length > 0) {
          // 分离主分类和子分类
          const main = data.filter(c => !c.parent_id);
          const subs: Record<string, Category[]> = {};

          // 按父分类组织子分类
          data.filter(c => c.parent_id).forEach(sub => {
            if (!subs[sub.parent_id!]) {
              subs[sub.parent_id!] = [];
            }
            subs[sub.parent_id!].push(sub);
          });

          setMainCategories(main);
          setSubcategories(subs);

          // 设置初始活动分类
          if (main.length > 0) {
            setActiveMainCategory(main[0].id);

            // 如果这个主分类有子分类，选择第一个子分类
            if (subs[main[0].id] && subs[main[0].id].length > 0) {
              setActiveSubcategory(subs[main[0].id][0].id);
            } else {
              setActiveSubcategory(null);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Fetch links for all categories
  useEffect(() => {
    const fetchAllLinks = async () => {
      const categoryIds = [
        ...mainCategories.map(c => c.id),
        ...Object.values(subcategories).flat().map(c => c.id)
      ];

      if (categoryIds.length === 0) return;

      const linksMap: Record<string, NavLink[]> = {};

      await Promise.all(
        categoryIds.map(async (categoryId) => {
          const { data, error } = await supabase
            .from('nav_links')
            .select('*')
            .eq('category_id', categoryId)
            .order('sort_order', { ascending: true });

          if (error) {
            console.error(`Error fetching links for category ${categoryId}:`, error);
            return;
          }

          if (data) {
            linksMap[categoryId] = data;
          }
        })
      );

      setLinks(linksMap);
    };

    fetchAllLinks();
  }, [mainCategories, subcategories]);

  const handleMainCategoryChange = (categoryId: string) => {
    setActiveMainCategory(categoryId);

    // If this main category has subcategories, select the first one
    if (subcategories[categoryId] && subcategories[categoryId].length > 0) {
      setActiveSubcategory(subcategories[categoryId][0].id);
    } else {
      setActiveSubcategory(null);
    }
  };

  // 更新链接排序顺序
  const updateLinkSortOrders = async (categoryLinks: NavLink[]) => {
    setIsSorting(true);

    try {
      // 准备更新数据，确保包含所有必需的字段
      const updates = categoryLinks.map((link, index) => ({
        id: link.id,
        category_id: link.category_id,
        name: link.name,
        url: link.url,
        sort_order: index,
        // 包含其他可能需要的字段
        icon: link.icon,
        is_internal: link.is_internal
      }));

      // 批量更新数据库
      const { error } = await supabase
        .from('nav_links')
        .upsert(updates);

      if (error) {
        console.error('Error updating sort orders:', error);
        toast({
          title: language === 'zh' ? "排序更新失败" : "Failed to update sort order",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      toast({
        title: language === 'zh' ? "排序已更新" : "Sort order updated",
        variant: "default",
      });
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: language === 'zh' ? "排序更新失败" : "Failed to update sort order",
        variant: "destructive",
      });
    } finally {
      setIsSorting(false);
    }
  };

  // 处理拖放结束事件
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination } = result;

    // 确保有有效的源和目标
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return;
    }

    const categoryId = source.droppableId;
    if (!categoryId || !links[categoryId]) return;

    // 获取当前分类的链接
    const categoryLinks = [...links[categoryId]];

    // 从源位置删除拖动的项目并插入到目标位置
    const [movedLink] = categoryLinks.splice(source.index, 1);
    categoryLinks.splice(destination.index, 0, movedLink);

    // 更新本地状态
    setLinks({
      ...links,
      [categoryId]: categoryLinks
    });

    // 更新排序值
    updateLinkSortOrders(categoryLinks);
  };

  // Ping测试链接
  const handlePing = async (link: NavLink) => {
    setPingResults({
      ...pingResults,
      [link.id]: { status: 'pending' }
    });

    try {
      const startTime = Date.now();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(link.url, {
        method: 'HEAD',
        signal: controller.signal,
        mode: 'no-cors'
      });

      clearTimeout(timeoutId);
      const endTime = Date.now();
      const elapsed = endTime - startTime;

      const result: PingResult = {
        status: 'success',
        time: elapsed
      };

      setPingResults({
        ...pingResults,
        [link.id]: result
      });

      return result;
    } catch (error) {
      console.error(`Error pinging ${link.url}:`, error);

      const result: PingResult = {
        status: 'error',
        message: error instanceof Error ? error.message : String(error)
      };

      setPingResults({
        ...pingResults,
        [link.id]: result
      });

      return result;
    }
  };

  if (loading) {
    return (
      <div className="py-12 text-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
        <p className="mt-4 text-muted-foreground">
          {language === 'en' ? 'Loading preview...' : '加载预览...'}
        </p>
      </div>
    );
  }

  if (mainCategories.length === 0) {
    return (
      <div className="py-12 text-center">
        <p className="text-muted-foreground">
          {language === 'en'
            ? 'No navigation categories found. Create some categories and links to see a preview.'
            : '未找到导航分类。创建一些分类和链接以查看预览。'}
        </p>
      </div>
    );
  }

  // Determine which category's links to show
  const currentLinks = activeCategoryId ? links[activeCategoryId] || [] : [];

  return (
    <div className="border rounded-lg p-4 sm:p-6 bg-muted/20">
      <h3 className="text-xl font-semibold mb-4 sm:mb-6 text-center">
        {language === 'en' ? 'Navigation Preview' : '导航预览'}
      </h3>

      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsReordering(!isReordering)}
          disabled={isSorting}
          className="text-xs sm:text-sm flex items-center gap-2"
        >
          {isReordering ? (
            <>
              <X className="h-4 w-4" />
              {language === 'en' ? 'Done Reordering' : '完成排序'}
            </>
          ) : (
            <>
              <GripVertical className="h-4 w-4" />
              {language === 'en' ? 'Reorder Links' : '排序链接'}
            </>
          )}
        </Button>
      </div>

      {isReordering && (
        <div className="mb-4 px-4 py-2 bg-muted rounded-md text-center text-sm">
          {language === 'en'
            ? 'Drag and drop links to reorder them'
            : '拖放链接来调整它们的顺序'}
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-4 sm:gap-6">
        {/* Main categories sidebar */}
        <div className="w-full md:w-40 lg:w-48 space-y-1 sm:space-y-2">
          {mainCategories.map(category => (
            <button
              key={category.id}
              className={`w-full text-left px-3 py-1.5 text-sm rounded-md transition-colors ${
                activeMainCategory === category.id
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              }`}
              onClick={() => handleMainCategoryChange(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Content area with subcategory tabs and links */}
        <div className="flex-1">
          {activeMainCategory && (
            <>
              {/* Subcategory tabs (if any) */}
              {subcategories[activeMainCategory] && subcategories[activeMainCategory].length > 0 ? (
                <Tabs
                  value={activeSubcategory || ""}
                  onValueChange={setActiveSubcategory}
                  className="mb-4 sm:mb-6"
                >
                  <TabsList className="w-full grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
                    {subcategories[activeMainCategory].map(sub => (
                      <TabsTrigger key={sub.id} value={sub.id} className="text-xs sm:text-sm px-2 py-1">
                        {sub.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </Tabs>
              ) : null}

              {/* Links grid */}
              {currentLinks.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    {language === 'en'
                      ? 'No links in this category yet.'
                      : '此分类中还没有链接。'}
                  </p>
                </div>
              ) : (
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId={activeCategoryId || "uncategorized"} type="link">
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className={isReordering
                          ? "flex flex-col gap-2 sm:gap-3"
                          : "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-2 sm:gap-3"
                        }
                      >
                        {currentLinks.map((link, index) => (
                          <Draggable
                            key={link.id}
                            draggableId={link.id}
                            index={index}
                            isDragDisabled={!isReordering}
                          >
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`${isReordering ? 'w-full' : ''} transition-all ${
                                  snapshot.isDragging ? 'opacity-70 scale-105 z-10' : ''
                                }`}
                              >
                                <Card className={`h-full transition-all duration-200 ${
                                  isReordering ? 'hover:shadow-md hover:border-primary/50 cursor-grab' : 'hover:shadow-sm'
                                }`}>
                                  <CardContent className="p-3 sm:p-4">
                                    <div className="flex flex-col items-center text-center gap-1.5">
                                      {isReordering && (
                                        <div className="self-stretch flex justify-center items-center py-1 mb-1 rounded-md">
                                          <GripVertical className="h-5 w-5 text-muted-foreground" />
                                        </div>
                                      )}

                                      <div className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full bg-muted">
                                        {link.icon ? (
                                          <img
                                            src={link.icon}
                                            alt={`${link.name} icon`}
                                            className="w-5 h-5 sm:w-6 sm:h-6 object-contain"
                                          />
                                        ) : (
                                          <LinkIcon className="w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground" />
                                        )}
                                      </div>
                                      <div className="font-medium text-sm leading-tight">{link.name}</div>
                                      <a
                                        href={link.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-500 hover:underline break-all"
                                      >
                                        {link.url.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0]}
                                      </a>
                                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                                        {link.is_internal ? (
                                          <span>{language === 'en' ? 'Internal' : '内部'}</span>
                                        ) : (
                                          <>
                                            <ExternalLink className="w-3 h-3" />
                                            <span>{language === 'en' ? 'External' : '外部'}</span>
                                          </>
                                        )}
                                      </div>

                                      {/* Ping button */}
                                      <div className="mt-1.5 flex flex-wrap gap-1 text-xs justify-center">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7 px-2 text-xs"
                                          onClick={() => handlePing(link)}
                                          disabled={pingResults[link.id]?.status === 'pending'}
                                        >
                                          <Network className="h-3 w-3 mr-1" />
                                          {language === 'en' ? 'Ping' : '测试'}
                                        </Button>
                                      </div>

                                      {pingResults[link.id] && (
                                        <div className="mt-1 w-full">
                                          {pingResults[link.id].status === 'pending' && (
                                            <Progress value={50} className="w-full h-1" />
                                          )}
                                          {pingResults[link.id].status === 'success' && (
                                            <div className="text-xs text-green-600">
                                              {language === 'en'
                                                ? `${pingResults[link.id].time}ms`
                                                : `${pingResults[link.id].time}毫秒`}
                                            </div>
                                          )}
                                          {pingResults[link.id].status === 'failed' && (
                                            <div className="text-xs text-red-500">
                                              {language === 'en' ? 'Failed' : '失败'}
                                            </div>
                                          )}
                                          {pingResults[link.id].status === 'error' && (
                                            <div className="text-xs text-red-500">
                                              {language === 'en' ? 'Error' : '错误'}
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </CardContent>
                                </Card>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminNavigationPreview;
