
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { useAppContext } from '@/context/AppContext';
import { Globe, Check, X, ExternalLink, User } from 'lucide-react';
import { formatDistance } from 'date-fns';

interface PendingDomain {
  id: string;
  domain: string;
  created_at: string;
  user_id?: string | null;
}

interface PendingDomainsListProps {
  domains: PendingDomain[];
  onApprove: (id: string) => Promise<boolean>;
  onReject: (id: string) => Promise<boolean>;
  isLoading: boolean;
}

const PendingDomainsList: React.FC<PendingDomainsListProps> = ({
  domains,
  onApprove,
  onReject,
  isLoading
}) => {
  const { language } = useAppContext();

  if (domains.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md">
        <Check className="h-12 w-12 mx-auto mb-3 text-muted-foreground opacity-50" />
        <h3 className="text-lg font-medium">
          {language === 'en' ? 'No pending domains' : '没有待审核的域名'}
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          {language === 'en'
            ? 'All domain submissions have been processed'
            : '所有域名提交均已处理'}
        </p>
      </div>
    );
  }

  return (
    <div className="border rounded-md overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{language === 'en' ? 'Domain' : '域名'}</TableHead>
            <TableHead>{language === 'en' ? 'Submitted' : '提交时间'}</TableHead>
            <TableHead>{language === 'en' ? 'Submitted By' : '提交者'}</TableHead>
            <TableHead className="text-right">{language === 'en' ? 'Actions' : '操作'}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {domains.map((domain, index) => (
            <TableRow key={domain.id || `domain-${index}`}>
              <TableCell className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{domain.domain}</span>
                <a
                  href={`https://${domain.domain}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-700"
                >
                  <ExternalLink className="h-3 w-3" />
                </a>
              </TableCell>
              <TableCell>
                {domain.created_at ?
                  (() => {
                    try {
                      const createdDate = new Date(domain.created_at);
                      if (isNaN(createdDate.getTime())) {
                        return language === 'en' ? 'Unknown' : '未知';
                      }
                      return formatDistance(createdDate, new Date(), { addSuffix: true });
                    } catch (error) {
                      console.error('Error formatting date:', error, domain.created_at);
                      return language === 'en' ? 'Unknown' : '未知';
                    }
                  })()
                  : (language === 'en' ? 'Unknown' : '未知')
                }
              </TableCell>
              <TableCell>
                <div className="flex items-center">
                  <User className="h-3 w-3 mr-1 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    {domain.user_id ? domain.user_id.substring(0, 8) : 'system'}
                  </span>
                </div>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onApprove(domain.id)}
                    disabled={isLoading}
                    className="h-8 text-green-600 border-green-600 hover:bg-green-50"
                  >
                    <Check className="h-4 w-4 mr-1" />
                    {language === 'en' ? 'Approve' : '批准'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onReject(domain.id)}
                    disabled={isLoading}
                    className="h-8 text-red-600 border-red-600 hover:bg-red-50"
                  >
                    <X className="h-4 w-4 mr-1" />
                    {language === 'en' ? 'Reject' : '拒绝'}
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default PendingDomainsList;
