
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAppContext } from '@/context/AppContext';
import { Plus, Globe, AlertTriangle } from 'lucide-react';

interface DomainFormProps {
  onAddDomain: (domain: string) => Promise<boolean>;
  isLoading: boolean;
  showNotification?: boolean;
}

const DomainForm: React.FC<DomainFormProps> = ({ 
  onAddDomain, 
  isLoading, 
  showNotification = false 
}) => {
  const { language } = useAppContext();
  const [newDomain, setNewDomain] = useState('');
  const [isValid, setIsValid] = useState(true);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newDomain.trim()) {
      setIsValid(false);
      return;
    }
    
    const success = await onAddDomain(newDomain);
    if (success) {
      setNewDomain('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-2">
      <div className="relative flex-1">
        <Globe className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder={
            language === 'en' 
              ? "Enter domain to submit for approval (e.g., example.com)" 
              : "输入要提交审核的域名（例如，example.com）"
          }
          value={newDomain}
          onChange={(e) => {
            setNewDomain(e.target.value);
            setIsValid(true);
          }}
          className={`pl-10 ${!isValid ? "border-red-500" : ""}`}
          disabled={isLoading}
        />
      </div>
      
      {showNotification && (
        <p className="text-xs text-muted-foreground mt-1 mb-2">
          <AlertTriangle className="inline h-3 w-3 mr-1" />
          {language === 'en' 
            ? 'Domain will be reviewed by an administrator before being approved.' 
            : '域名将由管理员审核后批准。'}
        </p>
      )}
      
      <Button type="submit" disabled={isLoading || !newDomain} className="w-full sm:w-auto self-end">
        <Plus className="h-4 w-4 mr-2" />
        {language === 'en' ? 'Submit Domain' : '提交域名'}
      </Button>
    </form>
  );
};

export default DomainForm;
