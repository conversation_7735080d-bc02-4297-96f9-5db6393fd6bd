
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { useAppContext } from '@/context/AppContext';
import { Globe, Trash2, ExternalLink } from 'lucide-react';
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from '@/components/ui/pagination';

interface Domain {
  id: string;
  domain: string;
  approved: boolean;
  created_at: string;
}

interface DomainsListProps {
  domains: Domain[];
  onToggleApproval: (id: string, currentStatus: boolean) => Promise<boolean>;
  onDelete: (id: string) => Promise<boolean>;
  isLoading: boolean;
}

const DomainsList: React.FC<DomainsListProps> = ({ 
  domains, 
  onToggleApproval, 
  onDelete,
  isLoading
}) => {
  const { language } = useAppContext();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  
  const totalPages = Math.ceil(domains.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedDomains = domains.slice(startIndex, startIndex + itemsPerPage);

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  if (domains.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md">
        <Globe className="h-12 w-12 mx-auto mb-3 text-muted-foreground opacity-50" />
        <h3 className="text-lg font-medium">
          {language === 'en' ? 'No domains found' : '未找到域名'}
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          {language === 'en' 
            ? 'Try adjusting your search or add a new domain' 
            : '尝试调整搜索条件或添加新域名'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-md overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">#</TableHead>
              <TableHead>{language === 'en' ? 'Domain' : '域名'}</TableHead>
              <TableHead className="text-right">{language === 'en' ? 'Status' : '状态'}</TableHead>
              <TableHead className="text-right">{language === 'en' ? 'Actions' : '操作'}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedDomains.map((domain, index) => (
              <TableRow key={domain.id}>
                <TableCell className="font-medium">{startIndex + index + 1}</TableCell>
                <TableCell className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{domain.domain}</span>
                  <a 
                    href={`https://${domain.domain}`} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:text-blue-700"
                  >
                    <ExternalLink className="h-3 w-3" />
                  </a>
                </TableCell>
                <TableCell className="text-right">
                  <Button 
                    variant={domain.approved ? "outline" : "ghost"}
                    size="sm"
                    className={`px-2 py-1 ${domain.approved ? 'bg-green-100 hover:bg-green-200 text-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`}
                    onClick={() => onToggleApproval(domain.id, domain.approved)}
                    disabled={isLoading}
                  >
                    {domain.approved 
                      ? (language === 'en' ? 'Approved' : '已批准') 
                      : (language === 'en' ? 'Not Approved' : '未批准')}
                  </Button>
                </TableCell>
                <TableCell className="text-right">
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => onDelete(domain.id)}
                    disabled={isLoading}
                    className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious 
                onClick={() => goToPage(currentPage - 1)}
                className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
              />
            </PaginationItem>
            
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNumber: number;
              
              if (totalPages <= 5) {
                pageNumber = i + 1;
              } else if (currentPage <= 3) {
                pageNumber = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNumber = totalPages - 4 + i;
              } else {
                pageNumber = currentPage - 2 + i;
              }
              
              return (
                <PaginationItem key={pageNumber}>
                  <PaginationLink 
                    onClick={() => goToPage(pageNumber)}
                    isActive={currentPage === pageNumber}
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            })}
            
            <PaginationItem>
              <PaginationNext 
                onClick={() => goToPage(currentPage + 1)}
                className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
      
      <p className="text-sm text-muted-foreground">
        {language === 'en' 
          ? `Showing ${Math.min(domains.length, startIndex + 1)}-${Math.min(startIndex + itemsPerPage, domains.length)} of ${domains.length} domains` 
          : `显示第 ${Math.min(domains.length, startIndex + 1)}-${Math.min(startIndex + itemsPerPage, domains.length)} 个域名（共 ${domains.length} 个）`}
      </p>
    </div>
  );
};

export default DomainsList;
