
import React from 'react';
import { useAppContext } from '@/context/AppContext';
import { Globe } from 'lucide-react';

const EmptyDomainState: React.FC = () => {
  const { language } = useAppContext();
  
  return (
    <div className="text-center p-8 border rounded-md">
      <Globe className="h-12 w-12 mx-auto mb-3 text-muted-foreground opacity-50" />
      <h3 className="text-lg font-medium">
        {language === 'en' ? 'No domains found' : '未找到域名'}
      </h3>
      <p className="text-sm text-muted-foreground mt-1">
        {language === 'en' 
          ? 'Try adjusting your search or add a new domain' 
          : '尝试调整搜索条件或添加新域名'}
      </p>
    </div>
  );
};

export default EmptyDomainState;
