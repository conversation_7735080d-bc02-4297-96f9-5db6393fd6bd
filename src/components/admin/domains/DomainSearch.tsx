
import React from 'react';
import { Input } from '@/components/ui/input';
import { useAppContext } from '@/context/AppContext';
import { Search } from 'lucide-react';

interface DomainSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  disabled?: boolean;
}

const DomainSearch: React.FC<DomainSearchProps> = ({ 
  searchQuery, 
  onSearchChange,
  disabled = false
}) => {
  const { language } = useAppContext();

  return (
    <div className="relative mb-6">
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        placeholder={
          language === 'en' 
            ? "Search domains..." 
            : "搜索域名..."
        }
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        className="pl-10"
        disabled={disabled}
      />
    </div>
  );
};

export default DomainSearch;
