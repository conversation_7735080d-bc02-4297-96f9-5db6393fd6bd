
import React from 'react';
import { TabsContent } from '@/components/ui/tabs';
import StatsSection from '@/components/admin/StatsSection';
import UsersSection from '@/components/admin/UsersSection';
import MapSection from '@/components/admin/MapSection';
import SettingsSection from '@/components/admin/SettingsSection';
import DomainWhitelistSection from '@/components/admin/DomainWhitelistSection';
import AdminNavigationManager from '@/components/admin/AdminNavigationManager';
import AdminFeaturesOrderingSection from '@/components/admin/AdminFeaturesOrderingSection';
import { UserData, StatisticsData, VisitStat } from '@/hooks/useAdminData';

interface AdminTabsContentProps {
  users: UserData[];
  statistics: StatisticsData;
  visitStats: VisitStat[];
  approvedDomains: string[];
}

const AdminTabsContent = ({
  users,
  statistics,
  visitStats,
  approvedDomains
}: AdminTabsContentProps) => {
  return (
    <>
      <TabsContent value="stats" className="space-y-4">
        <StatsSection statistics={statistics} visitStats={visitStats} />
      </TabsContent>
      
      <TabsContent value="users" className="space-y-4">
        <UsersSection users={users} />
      </TabsContent>
      
      <TabsContent value="domains" className="space-y-4">
        <DomainWhitelistSection approvedDomains={approvedDomains} />
      </TabsContent>
      
      <TabsContent value="navigation" className="space-y-4">
        <AdminNavigationManager />
      </TabsContent>
      
      <TabsContent value="features-order" className="space-y-4">
        <AdminFeaturesOrderingSection />
      </TabsContent>
      
      <TabsContent value="map" className="space-y-4">
        <MapSection />
      </TabsContent>
      
      <TabsContent value="settings" className="space-y-4">
        <SettingsSection />
      </TabsContent>
    </>
  );
};

export default AdminTabsContent;
