
import React from 'react';
import { useAppContext } from '@/context/AppContext';
import { Bell, Compass, Shield, RefreshCw } from 'lucide-react';
import UnifiedHeader from '@/components/common/UnifiedHeader';

interface AdminHeaderProps {
  pendingDomainsCount: number;
  pendingLinksCount: number;
  onRefresh: () => void;
}

const AdminHeader = ({ 
  pendingDomainsCount, 
  pendingLinksCount, 
  onRefresh 
}: AdminHeaderProps) => {
  const { language } = useAppContext();

  const badges = [];
  if (pendingDomainsCount > 0) {
    badges.push({
      text: language === 'en' 
        ? `${pendingDomainsCount} domain${pendingDomainsCount > 1 ? 's' : ''} pending` 
        : `${pendingDomainsCount} 个待审核域名`,
      variant: 'outline' as const
    });
  }
  if (pendingLinksCount > 0) {
    badges.push({
      text: language === 'en' 
        ? `${pendingLinksCount} link${pendingLinksCount > 1 ? 's' : ''} pending` 
        : `${pendingLinksCount} 个待审核链接`,
      variant: 'outline' as const
    });
  }

  const actions = [
    {
      id: 'refresh',
      label: language === 'en' ? 'Refresh' : '刷新',
      icon: RefreshCw,
      onClick: onRefresh,
      variant: 'outline' as const
    }
  ];

  return (
    <UnifiedHeader
      title={language === 'en' ? 'Admin Panel' : '管理面板'}
      description={language === 'en' 
        ? 'System administration and statistics' 
        : '系统管理和统计'}
      icon={Shield}
      variant="gradient"
      gradientFrom="from-purple-500"
      gradientTo="to-pink-500"
      badges={badges}
      actions={actions}
      layout="default"
    />
  );
};

export default AdminHeader;
