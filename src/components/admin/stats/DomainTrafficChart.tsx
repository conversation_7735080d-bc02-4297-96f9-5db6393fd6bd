
import React from 'react';
import { 
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>, 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Cell, 
  Tooltip, 
  Legend 
} from 'recharts';
import { Skeleton } from '@/components/ui/skeleton';
import { useAppContext } from '@/context/AppContext';

interface DomainTrafficChartProps {
  data: {
    name: string;
    visits: number;
  }[];
  isLoading: boolean;
}

const DomainTrafficChart: React.FC<DomainTrafficChartProps> = ({ data, isLoading }) => {
  const { language } = useAppContext();
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088FE', '#00C49F'];
  
  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Skeleton className="h-[250px] w-full" />
      </div>
    );
  }
  
  if (!data || data.length === 0) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <p className="text-muted-foreground">
          {language === 'en' 
            ? 'No domain traffic data available' 
            : '没有可用的域名流量数据'}
        </p>
      </div>
    );
  }
  
  return (
    <div className="h-full w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{ top: 10, right: 10, left: 20, bottom: 20 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="name" 
            angle={-35} 
            textAnchor="end" 
            height={60} 
            tick={{ fontSize: 11 }}
          />
          <YAxis 
            tick={{ fontSize: 11 }}
            width={30}
          />
          <Tooltip 
            formatter={(value: any) => [
              `${value} ${language === 'en' ? 'visits' : '访问量'}`, 
              language === 'en' ? 'Domain Traffic' : '域名流量'
            ]}
          />
          <Legend wrapperStyle={{ fontSize: '11px', paddingTop: '5px' }} />
          <Bar 
            dataKey="visits" 
            name={language === 'en' ? 'Domain Visits' : '域名访问量'}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default DomainTrafficChart;
