
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Eye, Users } from 'lucide-react';

interface StatsOverviewProps {
  siteStats: {
    visits: number;
    visitors: number;
    isLoading: boolean;
  };
  language: string;
}

const StatsOverview: React.FC<StatsOverviewProps> = ({ siteStats, language }) => {
  return (
    <div className="w-full grid grid-cols-2 gap-4">
      <div className="flex items-center">
        <div className="p-2 bg-primary/10 rounded-full mr-3">
          <Eye className="h-5 w-5 text-primary" />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">
            {language === 'en' ? 'Page Views (PV)' : '页面访问量 (PV)'}
          </p>
          {siteStats.isLoading ? (
            <Skeleton className="h-7 w-16" />
          ) : (
            <p className="font-semibold text-lg">{siteStats.visits.toLocaleString()}</p>
          )}
        </div>
      </div>
      
      <div className="flex items-center">
        <div className="p-2 bg-primary/10 rounded-full mr-3">
          <Users className="h-5 w-5 text-primary" />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">
            {language === 'en' ? 'Unique Visitors (UV)' : '独立访客数 (UV)'}
          </p>
          {siteStats.isLoading ? (
            <Skeleton className="h-7 w-16" />
          ) : (
            <p className="font-semibold text-lg">{siteStats.visitors.toLocaleString()}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsOverview;
