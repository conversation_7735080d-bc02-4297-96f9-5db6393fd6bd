
import React from 'react';

interface ActivePieShapeProps {
  cx: number;
  cy: number;
  innerRadius: number;
  outerRadius: number;
  startAngle: number;
  endAngle: number;
  fill: string;
  payload: {
    name: string;
    value: number;
  };
  percent: number;
  value: number;
  language: string;
  formatNumber: (num: number) => string;
}

const ActivePieShape: React.FC<ActivePieShapeProps> = (props) => {
  const { cx, cy, innerRadius, outerRadius, fill, payload, percent, value, language, formatNumber } = props;
  
  return (
    <g>
      <text x={cx} y={cy} dy={-20} textAnchor="middle" fill={fill} className="text-sm font-medium">
        {payload.name}
      </text>
      <text x={cx} y={cy} dy={10} textAnchor="middle" fill="#999" className="text-xs">
        {formatNumber(value)} ({(percent * 100).toFixed(0)}%)
      </text>
      <text x={cx} y={cy} dy={30} textAnchor="middle" fill="#999" className="text-xs">
        {language === 'en' ? 'Click for details' : '点击获取详情'}
      </text>
      <g>
        <path
          d={`M${cx},${cy}l${outerRadius},0`}
          stroke={fill}
          fill="none"
          strokeWidth={2}
        />
        <circle cx={cx} cy={cy} r={innerRadius} fill="none" />
        <circle cx={cx} cy={cy} r={outerRadius + 3} fill="none" stroke={fill} strokeWidth={2} />
      </g>
    </g>
  );
};

export default ActivePieShape;
