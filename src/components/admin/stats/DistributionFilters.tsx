
import React from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAppContext } from '@/context/AppContext';

interface DistributionFiltersProps {
  filterType: 'country';
  setFilterType: (type: 'country') => void;
}

const DistributionFilters: React.FC<DistributionFiltersProps> = ({ filterType, setFilterType }) => {
  const { language } = useAppContext();

  return (
    <div className="mb-4 flex justify-center space-x-4">
      <Tabs defaultValue="country"> {/* Wrap with Tabs component */}
        <TabsList className="mb-2">
          <TabsTrigger 
            value="country" 
            onClick={() => setFilterType('country')}
            className={`${filterType === 'country' ? 'bg-primary text-primary-foreground' : ''}`}
          >
            {language === 'en' ? 'Country' : '国家'}
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
};

export default DistributionFilters;
