
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useAppContext } from '@/context/AppContext';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';
import { format, isValid } from 'date-fns';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Loader2 } from 'lucide-react';

interface StatsDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  statType: 'users' | 'urls' | 'emails' | 'clicks';
  title: string;
}

interface DetailItem {
  id: string;
  [key: string]: any;
}

const StatsDetailDialog: React.FC<StatsDetailDialogProps> = ({
  open,
  onOpenChange,
  statType,
  title,
}) => {
  const { language } = useAppContext();
  const [items, setItems] = useState<DetailItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const itemsPerPage = 10;

  useEffect(() => {
    if (open) {
      fetchData();
    } else {
      // Reset state when dialog closes
      setCurrentPage(1);
      setSearchTerm('');
    }
  }, [open, currentPage, statType]);

  // Helper function to safely format dates
  const safeFormatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return isValid(date) ? format(date, 'yyyy-MM-dd HH:mm') : 'Invalid date';
    } catch (error) {
      console.error('Error formatting date:', dateString, error);
      return 'Invalid date';
    }
  };

  const fetchData = async () => {
    setIsLoading(true);

    try {
      let data: DetailItem[] = [];
      let count = 0;

      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      if (isUsingSupabase()) {
        // 使用 Supabase
        const { supabase } = await import('@/integrations/supabase/client');

        switch (statType) {
          case 'users':
            const { data: users, error: usersError, count: usersCount } = await supabase
              .from('user_roles')
              .select('user_id, role, created_at', { count: 'exact' })
              .range(from, to);

            if (!usersError && users) {
              data = users.map(user => ({
                id: user.user_id,
                role: user.role,
                created_at: user.created_at,
                email: `user-${user.user_id.substring(0, 8)}@example.com`,
                name: `User ${user.user_id.substring(0, 8)}`
              }));
              count = usersCount || 0;
            }
            break;

          case 'urls':
            const { data: urls, error: urlsError, count: urlsCount } = await supabase
              .from('short_urls')
              .select('*', { count: 'exact' })
              .range(from, to);

            if (!urlsError && urls) {
              data = urls;
              count = urlsCount || 0;
            }
            break;

          case 'emails':
            const { data: emails, error: emailsError, count: emailsCount } = await supabase
              .from('temp_emails')
              .select('*', { count: 'exact' })
              .range(from, to);

            if (!emailsError && emails) {
              data = emails;
              count = emailsCount || 0;
            }
            break;

          case 'clicks':
            const { data: clicks, error: clicksError, count: clicksCount } = await supabase
              .from('url_visits')
              .select('*, short_urls(short_code)', { count: 'exact' })
              .range(from, to);

            if (!clicksError && clicks) {
              data = clicks;
              count = clicksCount || 0;
            }
            break;
        }
      } else {
        // 使用 Go 后端
        try {
          switch (statType) {
            case 'users':
              const usersResponse = await apiClient.get(`/admin/users?page=${currentPage}&limit=${itemsPerPage}`);
              if (usersResponse.data) {
                data = usersResponse.data.users || [];
                count = usersResponse.data.total || 0;
              }
              break;

            case 'urls':
              const urlsResponse = await apiClient.get(`/admin/shorturls?page=${currentPage}&limit=${itemsPerPage}`);
              if (urlsResponse.data) {
                data = urlsResponse.data.urls || [];
                count = urlsResponse.data.total || 0;
              }
              break;

            case 'emails':
              const emailsResponse = await apiClient.get(`/admin/emails?page=${currentPage}&limit=${itemsPerPage}`);
              if (emailsResponse.data) {
                data = emailsResponse.data.emails || [];
                count = emailsResponse.data.total || 0;
              }
              break;

            case 'clicks':
              const clicksResponse = await apiClient.get(`/admin/visits?page=${currentPage}&limit=${itemsPerPage}`);
              if (clicksResponse.data) {
                data = clicksResponse.data.visits || [];
                count = clicksResponse.data.total || 0;
              }
              break;
          }
        } catch (error) {
          console.error('Error fetching data from Go backend:', error);
          // 使用模拟数据
          switch (statType) {
            case 'users':
              data = [
                { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'admin', created_at: new Date().toISOString() },
                { id: '2', name: 'Test User', email: '<EMAIL>', role: 'user', created_at: new Date().toISOString() }
              ];
              count = 2;
              break;
            case 'urls':
              data = [
                { id: '1', short_code: 'abc123', original_url: 'https://example.com', clicks: 10, created_at: new Date().toISOString() }
              ];
              count = 1;
              break;
            case 'emails':
              data = [
                { id: '1', email_address: '<EMAIL>', created_at: new Date().toISOString(), expires_at: new Date().toISOString() }
              ];
              count = 1;
              break;
            case 'clicks':
              data = [
                { id: '1', ip_address: '127.0.0.1', city: 'Unknown', country: 'Unknown', created_at: new Date().toISOString() }
              ];
              count = 1;
              break;
          }
        }
      }

      // If there's a search term, filter the results
      if (searchTerm) {
        data = data.filter(item => {
          // Different search criteria based on the type
          switch (statType) {
            case 'users':
              return (
                item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.role?.toLowerCase().includes(searchTerm.toLowerCase())
              );
            case 'urls':
              return (
                item.short_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.original_url?.toLowerCase().includes(searchTerm.toLowerCase())
              );
            case 'emails':
              return item.email_address?.toLowerCase().includes(searchTerm.toLowerCase());
            case 'clicks':
              return (
                item.short_urls?.short_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.ip_address?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.country?.toLowerCase().includes(searchTerm.toLowerCase())
              );
            default:
              return false;
          }
        });
        // Adjust count for filtered results
        count = data.length;
      }

      setItems(data);
      setTotalPages(Math.ceil(count / itemsPerPage));
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page on search
    fetchData();
  };

  const renderTableContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      );
    }

    if (items.length === 0) {
      return (
        <div className="text-center py-12 text-muted-foreground">
          {language === 'en' ? 'No items found' : '未找到相关数据'}
        </div>
      );
    }

    switch (statType) {
      case 'users':
        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === 'en' ? 'Name' : '姓名'}</TableHead>
                <TableHead>{language === 'en' ? 'Email' : '邮箱'}</TableHead>
                <TableHead>{language === 'en' ? 'Role' : '角色'}</TableHead>
                <TableHead>{language === 'en' ? 'Created At' : '创建时间'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge variant={user.role === 'admin' || user.role === 'super_admin' ? 'default' : 'secondary'}>
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>{safeFormatDate(user.created_at)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        );

      case 'urls':
        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === 'en' ? 'Short Code' : '短码'}</TableHead>
                <TableHead>{language === 'en' ? 'Original URL' : '原始链接'}</TableHead>
                <TableHead>{language === 'en' ? 'Clicks' : '点击数'}</TableHead>
                <TableHead>{language === 'en' ? 'Created At' : '创建时间'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((url) => (
                <TableRow key={url.id}>
                  <TableCell>{url.short_code}</TableCell>
                  <TableCell className="max-w-[300px] truncate" title={url.original_url}>
                    {url.original_url}
                  </TableCell>
                  <TableCell>{url.clicks || 0}</TableCell>
                  <TableCell>{safeFormatDate(url.created_at)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        );

      case 'emails':
        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === 'en' ? 'Email Address' : '邮箱地址'}</TableHead>
                <TableHead>{language === 'en' ? 'Created At' : '创建时间'}</TableHead>
                <TableHead>{language === 'en' ? 'Expires At' : '过期时间'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((email) => (
                <TableRow key={email.id}>
                  <TableCell>{email.email_address}</TableCell>
                  <TableCell>{safeFormatDate(email.created_at)}</TableCell>
                  <TableCell>{safeFormatDate(email.expires_at)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        );

      case 'clicks':
        return (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === 'en' ? 'URL' : '链接'}</TableHead>
                <TableHead>{language === 'en' ? 'IP Address' : 'IP地址'}</TableHead>
                <TableHead>{language === 'en' ? 'Location' : '位置'}</TableHead>
                <TableHead>{language === 'en' ? 'Click Time' : '点击时间'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((click) => (
                <TableRow key={click.id}>
                  <TableCell>
                    {click.short_urls?.short_code ?
                      `https://g2.al/${click.short_urls.short_code}` :
                      `URL #${click.short_url_id?.substring(0, 8)}`}
                  </TableCell>
                  <TableCell>{click.ip_address || 'N/A'}</TableCell>
                  <TableCell>
                    {click.city && click.country ? `${click.city}, ${click.country}` : 'N/A'}
                  </TableCell>
                  <TableCell>{safeFormatDate(click.created_at)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        );

      default:
        return null;
    }
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    // Calculate which page numbers to show
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4);
    }

    const pageNumbers = Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);

    return (
      <Pagination className="mt-4">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
            />
          </PaginationItem>

          {pageNumbers.map(pageNumber => (
            <PaginationItem key={pageNumber}>
              <PaginationLink
                isActive={pageNumber === currentPage}
                onClick={() => setCurrentPage(pageNumber)}
              >
                {pageNumber}
              </PaginationLink>
            </PaginationItem>
          ))}

          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && (
                <PaginationItem>
                  <span className="flex h-9 w-9 items-center justify-center">...</span>
                </PaginationItem>
              )}
              <PaginationItem>
                <PaginationLink onClick={() => setCurrentPage(totalPages)}>
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            </>
          )}

          <PaginationItem>
            <PaginationNext
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="my-4 flex items-center gap-2">
          <Input
            placeholder={language === 'en' ? 'Search...' : '搜索...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            className="max-w-sm"
          />
          <Button onClick={handleSearch}>
            {language === 'en' ? 'Search' : '搜索'}
          </Button>
        </div>

        <div className="max-h-[60vh] overflow-auto">
          {renderTableContent()}
        </div>

        {renderPagination()}

        {items.length > 0 && (
          <div className="mt-2 text-center text-sm text-muted-foreground">
            {language === 'en'
              ? `Showing page ${currentPage} of ${totalPages} (${items.length} items)`
              : `显示第 ${currentPage} 页，共 ${totalPages} 页（${items.length} 条记录）`}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default StatsDetailDialog;
