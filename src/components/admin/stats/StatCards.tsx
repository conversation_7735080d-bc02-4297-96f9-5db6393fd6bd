import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';

interface StatCardsProps {
  statistics: {
    totalUsers: number;
    totalUrls: number;
    totalEmails: number;
    totalClicks: number;
  };
  onStatClick: (type: 'users' | 'urls' | 'emails' | 'clicks', title: string) => void;
}

const StatCards: React.FC<StatCardsProps> = ({ statistics, onStatClick }) => {
  const { language } = useAppContext();

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card 
        className="cursor-pointer hover:shadow-md transition-shadow" 
        onClick={() => onStatClick('users', language === 'en' ? 'User Details' : '用户详情')}
      >
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            {language === 'en' ? 'Total Users' : '总用户数'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalUsers}</div>
          <p className="text-xs text-muted-foreground">
            {language === 'en' ? 'Registered users' : '注册用户'}
          </p>
        </CardContent>
      </Card>
      <Card 
        className="cursor-pointer hover:shadow-md transition-shadow" 
        onClick={() => onStatClick('clicks', language === 'en' ? 'Click Details' : '点击详情')}
      >
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            {language === 'en' ? 'Active Short URLs' : '活跃短链接'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalUrls}</div>
          <p className="text-xs text-muted-foreground">
            {language === 'en' ? 'Created URLs' : '创建的链接'}
          </p>
        </CardContent>
      </Card>
      <Card 
        className="cursor-pointer hover:shadow-md transition-shadow" 
        onClick={() => onStatClick('urls', language === 'en' ? 'URL Details' : '链接详情')}
      >
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            {language === 'en' ? 'Total Clicks' : '总点击量'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalClicks}</div>
          <p className="text-xs text-muted-foreground">
            {language === 'en' ? 'URL visits' : '链接访问量'}
          </p>
        </CardContent>
      </Card>
      <Card 
        className="cursor-pointer hover:shadow-md transition-shadow" 
        onClick={() => onStatClick('emails', language === 'en' ? 'Email Details' : '邮箱详情')}
      >
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            {language === 'en' ? 'Temporary Emails' : '临时邮箱'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalEmails}</div>
          <p className="text-xs text-muted-foreground">
            {language === 'en' ? 'Created emails' : '创建的邮箱'}
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default StatCards;
