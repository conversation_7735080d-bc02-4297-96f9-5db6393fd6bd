
import React from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  Legend, 
  ResponsiveContainer,
  <PERSON>lt<PERSON> as RechartsTooltip
} from 'recharts';
import { useAppContext } from '@/context/AppContext';
import { ChartContainer, ChartTooltipContent } from '@/components/ui/chart';
import { Skeleton } from '@/components/ui/skeleton';

export interface VisitorTimelineData {
  date: string;
  visitors: number;
  visits: number;
}

interface TimelineChartProps {
  data: VisitorTimelineData[];
  isLoading: boolean;
}

const TimelineChart: React.FC<TimelineChartProps> = ({ data, isLoading }) => {
  const { language } = useAppContext();

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Skeleton className="h-[250px] w-full" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <p className="text-muted-foreground">
          {language === 'en' ? 'No timeline data available' : '没有可用的时间线数据'}
        </p>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <ResponsiveContainer width="100%" height="100%">
        <ChartContainer
          config={{
            visitors: {
              label: language === 'en' ? 'Unique Visitors' : '独立访客',
              color: '#0088FE',
            },
            visits: {
              label: language === 'en' ? 'Total Visits' : '总访问量',
              color: '#00C49F',
            },
          }}
        >
          <LineChart
            data={data}
            margin={{ top: 10, right: 10, left: 10, bottom: 25 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tick={{ fontSize: 11 }}
              tickFormatter={(value) => {
                const date = new Date(value);
                return `${date.getMonth() + 1}/${date.getDate()}`;
              }}
              height={25}
            />
            <YAxis tick={{ fontSize: 11 }} width={30} />
            <RechartsTooltip content={<ChartTooltipContent />} />
            <Legend wrapperStyle={{ fontSize: '11px', paddingTop: '5px' }} />
            <Line 
              type="monotone" 
              dataKey="visitors" 
              name="visitors"
              stroke="var(--color-visitors, #0088FE)" 
              strokeWidth={2} 
              dot={{ r: 3 }} 
              activeDot={{ r: 7 }} 
            />
            <Line 
              type="monotone" 
              dataKey="visits" 
              name="visits"
              stroke="var(--color-visits, #00C49F)" 
              strokeWidth={2} 
              dot={{ r: 3 }} 
            />
          </LineChart>
        </ChartContainer>
      </ResponsiveContainer>
    </div>
  );
};

export default TimelineChart;
