
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import { supabase } from '@/integrations/supabase/client';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { PieChartIcon, BarChartIcon } from 'lucide-react';
import { CategoryStatItem } from './types';
import CategoryBarChart from './charts/CategoryBarChart';
import CategoryPieChart from './charts/CategoryPieChart';
import CategoryStatsState from './charts/CategoryStatsState';
import CategoryEmptyState from './charts/CategoryEmptyState';

// Colors with better harmony - a cohesive palette
const COLORS = [
  '#4f46e5', '#06b6d4', '#10b981', '#84cc16', '#eab308', 
  '#ef4444', '#8b5cf6', '#ec4899', '#0ea5e9', '#84cc16'
];

const CategoryStats: React.FC = () => {
  const { language } = useAppContext();
  const [categoryStats, setCategoryStats] = useState<CategoryStatItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');

  useEffect(() => {
    const fetchCategoryStats = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch categories
        const { data: categories, error: catError } = await supabase
          .from('nav_categories')
          .select('id, name, parent_id')
          .is('user_id', null) // Admin categories only
          .order('name');
          
        if (catError) throw catError;
        
        // For each category, count the number of links
        const statsPromises = categories.map(async (category) => {
          const { count, error: countError } = await supabase
            .from('nav_links')
            .select('*', { count: 'exact', head: true })
            .eq('category_id', category.id);
            
          if (countError) throw countError;
          
          return {
            id: category.id,
            name: category.name,
            count: count || 0,
            color: COLORS[Math.floor(Math.random() * COLORS.length)]
          };
        });
        
        const stats = await Promise.all(statsPromises);
        
        // Sort by count descending
        stats.sort((a, b) => b.count - a.count);
        
        // Assign different colors to each category
        stats.forEach((item, index) => {
          item.color = COLORS[index % COLORS.length];
        });
        
        setCategoryStats(stats);
      } catch (err) {
        console.error('Error fetching category stats:', err);
        setError(language === 'en' 
          ? 'Failed to load category statistics' 
          : '加载分类统计数据失败');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCategoryStats();
  }, [language]);
  
  if (isLoading || error) {
    return <CategoryStatsState isLoading={isLoading} error={error} />;
  }
  
  if (categoryStats.length === 0) {
    return <CategoryEmptyState />;
  }
  
  return (
    <Card className="overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2 bg-card/50">
        <CardTitle>{language === 'en' ? 'Navigation Category Analytics' : '导航分类分析'}</CardTitle>
        <Tabs value={chartType} onValueChange={(value) => setChartType(value as 'bar' | 'pie')}>
          <TabsList className="grid w-[180px] grid-cols-2">
            <TabsTrigger value="bar">
              <BarChartIcon className="h-4 w-4 mr-2" />
              {language === 'en' ? 'Bar' : '柱状图'}
            </TabsTrigger>
            <TabsTrigger value="pie">
              <PieChartIcon className="h-4 w-4 mr-2" />
              {language === 'en' ? 'Pie' : '饼状图'}
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent className="h-[350px] pt-4">
        <div className="w-full h-full">
          {chartType === 'bar' ? (
            <CategoryBarChart data={categoryStats} />
          ) : (
            <CategoryPieChart data={categoryStats} />
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CategoryStats;
