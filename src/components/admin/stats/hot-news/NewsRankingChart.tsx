
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, CartesianGrid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, Cell } from 'recharts';
import { NewsRankingChartProps } from './types';

const NewsRankingChart: React.FC<NewsRankingChartProps> = ({ data, CustomTooltip, language }) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={data.slice(0, 6)}
        layout="vertical"
        margin={{ top: 10, right: 20, left: 90, bottom: 25 }}
        barSize={20}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" horizontal={false} />
        <XAxis 
          type="number" 
          domain={[0, 'dataMax']} 
          tickLine={false}
          axisLine={{ stroke: '#eee' }}
          tick={{ fill: '#888' }}
        />
        <YAxis 
          type="category" 
          dataKey="title" 
          width={80}
          tick={{ fontSize: 10, fill: '#888' }}
          tickLine={false}
          axisLine={{ stroke: '#eee' }}
        />
        <RechartsTooltip content={CustomTooltip} />
        <Legend 
          wrapperStyle={{ bottom: 0 }}
          formatter={(value) => <span className="text-sm text-foreground/80">{value}</span>}
        />
        <Bar 
          dataKey="hot" 
          name={language === 'en' ? 'Hot Value' : '热度值'} 
          radius={[0, 4, 4, 0]}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.color}
              style={{ filter: 'drop-shadow(0px 2px 2px rgba(0, 0, 0, 0.1))' }}
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};

export default NewsRankingChart;
