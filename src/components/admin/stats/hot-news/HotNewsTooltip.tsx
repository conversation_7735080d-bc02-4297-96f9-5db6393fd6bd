
import React from 'react';
import { HotNewsTooltipProps } from '@/components/navigation/types';

const HotNewsTooltip: React.FC<HotNewsTooltipProps> = ({ active, payload, activeTab, language }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-background/95 p-3 border rounded-md shadow-md text-sm backdrop-blur-sm">
        <p className="font-semibold">{data.name || data.title}</p>
        <p className="text-foreground/80 mt-1">
          <span className="font-medium">
            {activeTab === 'platforms' 
              ? (language === 'en' ? 'News Count: ' : '新闻数量：') 
              : (language === 'en' ? 'Hot Value: ' : '热度值：')}
          </span>
          {activeTab === 'platforms' 
            ? data.newsCount 
            : data.hot?.toLocaleString()}
        </p>
        {activeTab === 'news' && data.platform && (
          <p className="text-xs text-muted-foreground mt-1">
            {data.platform}
          </p>
        )}
      </div>
    );
  }
  return null;
};

export default HotNewsTooltip;
