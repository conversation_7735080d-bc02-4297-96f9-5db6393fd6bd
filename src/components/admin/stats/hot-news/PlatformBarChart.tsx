
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, CartesianGrid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, Cell } from 'recharts';
import { HotNewsBarChartProps } from './types';

const PlatformBarChart: React.FC<HotNewsBarChartProps> = ({ data, CustomTooltip, language }) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart 
        data={data.slice(0, 6)} 
        margin={{ top: 10, right: 30, left: 20, bottom: 40 }}
        barSize={36}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
        <XAxis 
          dataKey="name" 
          angle={-45} 
          textAnchor="end" 
          height={70} 
          tick={{ fontSize: 11, fill: '#888' }}
          tickLine={false}
          axisLine={{ stroke: '#eee' }}
        />
        <YAxis 
          tickLine={false}
          axisLine={{ stroke: '#eee' }}
          tick={{ fill: '#888' }}
        />
        <RechartsTooltip content={CustomTooltip} />
        <Legend 
          wrapperStyle={{ bottom: 0 }} 
          formatter={(value) => <span className="text-sm text-foreground/80">{value}</span>}
        />
        <Bar 
          dataKey="newsCount" 
          name={language === 'en' ? 'News Count' : '新闻数量'} 
          radius={[4, 4, 0, 0]}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.color}
              style={{ filter: 'drop-shadow(0px 2px 2px rgba(0, 0, 0, 0.1))' }}
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};

export default PlatformBarChart;
