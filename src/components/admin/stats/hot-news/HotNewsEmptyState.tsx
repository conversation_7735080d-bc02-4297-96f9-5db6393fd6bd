
import React from 'react';
import { Card, CardHeader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { HotNewsEmptyStateProps } from './types';

const HotNewsEmptyState: React.FC<HotNewsEmptyStateProps> = ({ language }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? "Today's Hot List Analytics" : '今日热榜分析'}</CardTitle>
      </CardHeader>
      <CardContent className="h-80 flex items-center justify-center">
        <p className="text-muted-foreground">
          {language === 'en' 
            ? 'No hot list data available' 
            : '暂无热榜数据'}
        </p>
      </CardContent>
    </Card>
  );
};

export default HotNewsEmptyState;
