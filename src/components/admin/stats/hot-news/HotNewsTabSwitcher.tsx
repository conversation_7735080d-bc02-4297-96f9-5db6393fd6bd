
import React from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface HotNewsTabSwitcherProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  language: string;
}

const HotNewsTabSwitcher: React.FC<HotNewsTabSwitcherProps> = ({
  activeTab,
  onTabChange,
  language,
}) => {
  return (
    <Tabs defaultValue={activeTab}> {/* Wrap with Tabs component */}
      <TabsList className="mb-4 w-full justify-start">
        <TabsTrigger value="platforms" className="flex-1 max-w-[200px]">
          {language === 'en' ? 'Platform Distribution' : '平台分布'}
        </TabsTrigger>
        <TabsTrigger value="news" className="flex-1 max-w-[200px]">
          {language === 'en' ? 'Hot News Ranking' : '热门新闻排行'}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

export default HotNewsTabSwitcher;
