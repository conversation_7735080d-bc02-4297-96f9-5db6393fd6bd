
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Legend, Cell } from 'recharts';
import { HotNewsPieChartProps } from './types';
import HotNewsPieShape from './HotNewsPieShape';

const PlatformPieChart: React.FC<HotNewsPieChartProps> = ({ data, activeIndex, onPieEnter, CustomTooltip }) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          activeIndex={activeIndex}
          activeShape={(props) => <HotNewsPieShape {...props} />}
          data={data.slice(0, 6)}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={90}
          fill="#8884d8"
          dataKey="newsCount"
          onMouseEnter={onPieEnter}
          paddingAngle={2}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.color}
              style={{ filter: 'drop-shadow(0px 3px 3px rgba(0, 0, 0, 0.1))' }}
            />
          ))}
        </Pie>
        <RechartsTooltip content={CustomTooltip} />
        <Legend 
          layout="horizontal"
          verticalAlign="bottom"
          align="center"
          formatter={(value, entry: any) => (
            <span style={{ color: entry.color }} className="text-xs px-2">
              {value}
            </span>
          )}
        />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default PlatformPieChart;
