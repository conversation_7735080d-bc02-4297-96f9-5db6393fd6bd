
import React from 'react';
import { Card, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { HotNewsLoadingProps } from './types';

const HotNewsLoading: React.FC<HotNewsLoadingProps> = ({ language }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? "Today's Hot List Analytics" : '今日热榜分析'}</CardTitle>
      </CardHeader>
      <CardContent className="h-80 flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </CardContent>
    </Card>
  );
};

export default HotNewsLoading;
