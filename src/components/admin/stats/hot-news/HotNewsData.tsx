
import React, { useState, useEffect } from 'react';
import { TabsContent } from '@/components/ui/tabs';
import { fetchPlatforms, fetchNews } from '@/hooks/hot-news/api';
import { PlatformData, NewsHotData } from './types';
import PlatformBarChart from './PlatformBarChart';
import Platform<PERSON>ieChart from './PlatformPieChart';
import NewsRankingChart from './NewsRankingChart';
import HotNewsLoading from './HotNewsLoading';
import HotNewsEmptyState from './HotNewsEmptyState';

interface HotNewsDataProps {
  activeTab: string;
  chartType: 'bar' | 'pie';
  CustomTooltip: React.FC<any>;
  language: string;
  colors: string[];
}

const HotNewsData: React.FC<HotNewsDataProps> = ({ 
  activeTab, 
  chartType, 
  CustomTooltip, 
  language,
  colors
}) => {
  const [platformData, setPlatformData] = useState<PlatformData[]>([]);
  const [newsHotData, setNewsHotData] = useState<NewsHotData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeIndex, setActiveIndex] = useState(0);
  
  // Fetch hot list data
  useEffect(() => {
    const fetchHotNewsData = async () => {
      setIsLoading(true);
      try {
        // 1. Get supported platforms
        const platforms = await fetchPlatforms();
        
        // 2. Determine popular platforms to fetch data from
        const popularPlatformPaths = [
          '/baidu',          // Baidu
          '/netease-news',   // NetEase
          '/douyin',         // Douyin
          '/bilibili',       // Bilibili
          '/weibo',          // Weibo
          '/zhihu',          // Zhihu
          '/36kr',           // 36kr
          '/juejin',         // Juejin
          '/ithome'          // IT Home
        ];
        
        const featuredPlatforms = platforms.filter(p => 
          popularPlatformPaths.includes(p.path)
        );
        
        // 3. Get news data for each platform
        const allPlatformData: PlatformData[] = [];
        const allNewsHotData: NewsHotData[] = [];
        
        // Fetch data for all platforms in parallel
        const results = await Promise.allSettled(
          featuredPlatforms.map(async (platform, index) => {
            try {
              const news = await fetchNews(platform.path);
              const platformColor = colors[index % colors.length];
              
              // Add platform data
              allPlatformData.push({
                name: language === 'en' ? platform.name : platform.chineseName,
                newsCount: news.length,
                color: platformColor
              });
              
              // Add hot news data (only for news with hot values)
              news.forEach(item => {
                if (item.hot) {
                  // 安全地提取热度值
                  let hotValue = 0;
                  if (typeof item.hot === 'string') {
                    try {
                      hotValue = parseInt(item.hot.replace(/[^0-9]/g, '')) || 0;
                    } catch (err) {
                      console.warn('无法解析热度值:', item.hot);
                    }
                  }
                  
                  if (hotValue > 0) {
                    allNewsHotData.push({
                      title: item.title.length > 15 ? item.title.substring(0, 15) + '...' : item.title,
                      hot: hotValue,
                      platform: language === 'en' ? platform.name : platform.chineseName,
                      color: platformColor
                    });
                  }
                }
              });
              
              return { platform, news };
            } catch (err) {
              console.error(`Failed to fetch news for '${platform.chineseName}':`, err);
              return { platform, news: [] };
            }
          })
        );
        
        // 5. Sort data
        allPlatformData.sort((a, b) => b.newsCount - a.newsCount);
        allNewsHotData.sort((a, b) => b.hot - a.hot);
        
        setPlatformData(allPlatformData);
        setNewsHotData(allNewsHotData.slice(0, 10)); // Only take top 10 hot news
      } catch (error) {
        console.error('Failed to fetch hot list data:', error);
        // Set default data
        setPlatformData([
          { name: '百度', newsCount: 50, color: colors[0] },
          { name: '微博', newsCount: 45, color: colors[1] },
          { name: '知乎', newsCount: 40, color: colors[2] },
          { name: '抖音', newsCount: 35, color: colors[3] },
          { name: '哔哩哔哩', newsCount: 30, color: colors[4] }
        ]);
        setNewsHotData([
          { title: '热门新闻1', hot: 500000, platform: '百度', color: colors[0] },
          { title: '热门新闻2', hot: 450000, platform: '微博', color: colors[1] },
          { title: '热门新闻3', hot: 400000, platform: '知乎', color: colors[2] },
          { title: '热门新闻4', hot: 350000, platform: '抖音', color: colors[3] },
          { title: '热门新闻5', hot: 300000, platform: '哔哩哔哩', color: colors[4] }
        ]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchHotNewsData();
  }, [language, colors]);
  
  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };
  
  // Handle loading state
  if (isLoading) {
    return <HotNewsLoading language={language} />;
  }

  if (!platformData.length && !newsHotData.length) {
    return <HotNewsEmptyState language={language} />;
  }

  return (
    <>
      <TabsContent value="platforms" className="mt-0 h-full">
        {chartType === 'bar' ? (
          <PlatformBarChart 
            data={platformData} 
            CustomTooltip={CustomTooltip} 
            language={language}
          />
        ) : (
          <PlatformPieChart 
            data={platformData}
            activeIndex={activeIndex}
            onPieEnter={onPieEnter}
            CustomTooltip={CustomTooltip}
          />
        )}
      </TabsContent>
      
      <TabsContent value="news" className="mt-0 h-full">
        <NewsRankingChart 
          data={newsHotData}
          CustomTooltip={CustomTooltip}
          language={language}
        />
      </TabsContent>
    </>
  );
};

export default HotNewsData;
