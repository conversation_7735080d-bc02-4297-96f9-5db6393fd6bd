
export interface PlatformData {
  name: string;
  newsCount: number;
  color: string;
}

export interface NewsHotData {
  title: string;
  hot: number;
  platform: string;
  color: string;
}

export interface HotNewsTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: {
      name?: string;
      title?: string;
      newsCount?: number;
      hot?: number;
      platform?: string;
    };
  }>;
  activeTab: string;
  language: string;
}

export interface HotNewsPieShapeProps {
  cx: number;
  cy: number;
  innerRadius: number;
  outerRadius: number;
  startAngle: number;
  endAngle: number;
  fill: string;
  payload: {
    name: string;
  };
  percent: number;
  newsCount: number;
}

export interface HotNewsBarChartProps {
  data: PlatformData[];
  CustomTooltip: React.FC<HotNewsTooltipProps>;
  language: string;
}

export interface HotNewsPieChartProps {
  data: PlatformData[];
  activeIndex: number;
  onPieEnter: (_: any, index: number) => void;
  CustomTooltip: React.FC<HotNewsTooltipProps>;
}

export interface NewsRankingChartProps {
  data: NewsHotData[];
  CustomTooltip: React.FC<HotNewsTooltipProps>;
  language: string;
}

export interface HotNewsEmptyStateProps {
  language: string;
}

export interface HotNewsLoadingProps {
  language: string;
}
