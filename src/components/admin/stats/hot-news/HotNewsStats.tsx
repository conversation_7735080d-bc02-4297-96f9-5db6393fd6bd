
import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription
} from '@/components/ui/card';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useAppContext } from '@/context/AppContext';
import HotNewsChartSwitcher from './HotNewsChartSwitcher';
import HotNewsTabSwitcher from './HotNewsTabSwitcher';
import HotNewsData from './HotNewsData';
import HotNewsTooltip from './HotNewsTooltip';
import { COLORS } from '../constants';

const HotNewsStats: React.FC = () => {
  const { language } = useAppContext();
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');
  const [activeTab, setActiveTab] = useState('platforms');
  
  // Custom Tooltip component with memoization to prevent unnecessary renders
  const CustomTooltip = React.useMemo(() => {
    return (props: any) => (
      <HotNewsTooltip {...props} activeTab={activeTab} language={language} />
    );
  }, [activeTab, language]);
  
  return (
    <Card className="overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2 bg-card/50">
        <div>
          <CardTitle>{language === 'en' ? "Today's Hot List Analytics" : '今日热榜分析'}</CardTitle>
          <CardDescription>
            {language === 'en' 
              ? 'Platform distribution and hot news rankings' 
              : '平台分布和热门新闻排行'}
          </CardDescription>
        </div>
        <HotNewsChartSwitcher 
          chartType={chartType} 
          onChangeChartType={setChartType}
          language={language}
        />
      </CardHeader>
      
      <CardContent className="h-[350px] pt-4">
        <div className="h-full">
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <HotNewsTabSwitcher activeTab={activeTab} onTabChange={setActiveTab} language={language} />
            
            <div className="flex-1">
              <HotNewsData 
                activeTab={activeTab}
                chartType={chartType}
                CustomTooltip={CustomTooltip}
                language={language}
                colors={COLORS}
              />
            </div>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
};

export default HotNewsStats;
