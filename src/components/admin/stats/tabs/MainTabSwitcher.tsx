
import React from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TrendingUp } from 'lucide-react';

interface MainTabSwitcherProps {
  onTabChange: (value: string) => void;
  language: string;
}

const MainTabSwitcher: React.FC<MainTabSwitcherProps> = ({
  onTabChange,
  language,
}) => {
  return (
    <Tabs defaultValue="timeline"> {/* Wrap with Tabs component */}
      <TabsList className="mb-4 w-full justify-start">
        <TabsTrigger value="timeline" className="flex-1 max-w-[200px]" onClick={() => onTabChange('timeline')}>
          <TrendingUp className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Daily Traffic' : '每日流量'}
        </TabsTrigger>
        <TabsTrigger value="domains" className="flex-1 max-w-[200px]" onClick={() => onTabChange('domains')}>
          {language === 'en' ? 'Traffic by Domain' : '域名流量'}
        </TabsTrigger>
        <TabsTrigger value="pages" className="flex-1 max-w-[200px]" onClick={() => onTabChange('pages')}>
          {language === 'en' ? 'Page Popularity' : '页面热度'}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

export default MainTabSwitcher;
