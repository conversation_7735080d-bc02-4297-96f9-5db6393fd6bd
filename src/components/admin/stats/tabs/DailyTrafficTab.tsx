
import React from 'react';
import { useVisitorStats } from '@/hooks/useVisitorStats';
import TimelineBarChart from '../charts/TimelineBarChart';

interface DailyTrafficTabProps {
  language: string;
  formatNumber: (num: number) => string;
  isLoading: boolean;
}

const DailyTrafficTab: React.FC<DailyTrafficTabProps> = ({ language, formatNumber, isLoading }) => {
  const { timelineData } = useVisitorStats();
  
  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }
  
  if (!timelineData || timelineData.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">
          {language === 'en' ? 'No timeline data available' : '没有可用的时间线数据'}
        </p>
      </div>
    );
  }
  
  return (
    <div className="h-[300px]">
      <TimelineBarChart 
        data={timelineData} 
        language={language} 
        formatNumber={formatNumber} 
      />
    </div>
  );
};

export default DailyTrafficTab;
