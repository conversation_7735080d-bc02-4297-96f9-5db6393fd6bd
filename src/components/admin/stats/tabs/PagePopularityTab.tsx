
import React from 'react';
import { useVisitorStats } from '@/hooks/useVisitorStats';
import BarChartDisplay from '../charts/BarChartDisplay';
import PieChartDisplay from '../charts/PieChartDisplay';
import ActivePieShape from '../components/ActivePieShape';

interface PagePopularityTabProps {
  language: string;
  chartType: 'bar' | 'pie';
  activeIndex: number;
  setActiveIndex: (index: number) => void;
  formatNumber: (num: number) => string;
  isLoading: boolean;
}

const PagePopularityTab: React.FC<PagePopularityTabProps> = ({ 
  language, 
  chartType,
  activeIndex,
  setActiveIndex,
  formatNumber,
  isLoading
}) => {
  const { pageVisits } = useVisitorStats();
  
  // Handle pie chart segment clicks
  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };
  
  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }
  
  if (!pageVisits || pageVisits.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">
          {language === 'en' ? 'No page visit data available' : '没有可用的页面访问数据'}
        </p>
      </div>
    );
  }
  
  // Render active shape for pie chart segments
  const renderActiveShape = (props: any) => (
    <ActivePieShape {...props} language={language} formatNumber={formatNumber} />
  );
  
  return (
    <div className="h-[300px]">
      {chartType === 'bar' ? (
        <BarChartDisplay 
          data={pageVisits} 
          language={language} 
          vertical={true}
          formatNumber={formatNumber} 
        />
      ) : (
        <PieChartDisplay 
          data={pageVisits} 
          activeIndex={activeIndex}
          onPieEnter={onPieEnter} 
          renderActiveShape={renderActiveShape}
          language={language}
        />
      )}
    </div>
  );
};

export default PagePopularityTab;
