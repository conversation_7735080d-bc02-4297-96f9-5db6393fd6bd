
import React from 'react';
import { useVisitorStats } from '@/hooks/useVisitorStats';
import BarChartDisplay from '../charts/BarChartDisplay';
import PieChartDisplay from '../charts/PieChartDisplay';
import ActivePieShape from '../components/ActivePieShape';

interface DomainsTrafficTabProps {
  language: string;
  chartType: 'bar' | 'pie';
  activeIndex: number;
  setActiveIndex: (index: number) => void;
  formatNumber: (num: number) => string;
  isLoading: boolean;
}

const DomainsTrafficTab: React.FC<DomainsTrafficTabProps> = ({ 
  language, 
  chartType,
  activeIndex,
  setActiveIndex,
  formatNumber,
  isLoading
}) => {
  const { domainTrafficData } = useVisitorStats();
  
  // Handle pie chart segment clicks
  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };
  
  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }
  
  if (!domainTrafficData || domainTrafficData.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">
          {language === 'en' ? 'No domain traffic data available' : '没有可用的域名流量数据'}
        </p>
      </div>
    );
  }
  
  // Render active shape for pie chart segments
  const renderActiveShape = (props: any) => (
    <ActivePieShape {...props} language={language} formatNumber={formatNumber} />
  );
  
  return (
    <div className="h-[300px]">
      {chartType === 'bar' ? (
        <BarChartDisplay 
          data={domainTrafficData} 
          language={language} 
          vertical={true}
          formatNumber={formatNumber} 
        />
      ) : (
        <PieChartDisplay 
          data={domainTrafficData} 
          activeIndex={activeIndex}
          onPieEnter={onPieEnter} 
          renderActiveShape={renderActiveShape}
          language={language}
        />
      )}
    </div>
  );
};

export default DomainsTrafficTab;
