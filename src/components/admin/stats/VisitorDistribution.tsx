
import React, { useState } from 'react';
import { 
  Card, 
  Card<PERSON>ontent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useAppContext } from '@/context/AppContext';
import { useVisitorStats } from '@/hooks/useVisitorStats';
import TimelineChart from './TimelineChart';
import DomainTrafficChart from './DomainTrafficChart';
import ShortUrlVisitsChart from './ShortUrlVisitsChart';

const VisitorDistribution = () => {
  const { language } = useAppContext();
  const { 
    timelineData, 
    domainTrafficData,
    shortUrlVisits,
    isLoading 
  } = useVisitorStats();
  
  const [activeTab, setActiveTab] = useState('timeline');
  
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };
  
  return (
    <Card className="mt-6 overflow-hidden">
      <CardHeader>
        <CardTitle>
          {language === 'en' ? 'Visitor Distribution' : '访客分布'}
        </CardTitle>
        <CardDescription>
          {language === 'en' 
            ? 'Analyze traffic patterns over time and by domain.' 
            : '分析流量随时间和按域名的模式。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
          <div className="flex space-x-4 bg-muted rounded-md p-1 w-fit">
            <button
              onClick={() => handleTabChange('timeline')}
              className={`px-4 py-2 text-sm rounded-md transition-colors ${
                activeTab === 'timeline' 
                  ? 'bg-background text-foreground shadow-sm' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              {language === 'en' ? 'Timeline' : '时间线'}
            </button>
            <button
              onClick={() => handleTabChange('domains')}
              className={`px-4 py-2 text-sm rounded-md transition-colors ${
                activeTab === 'domains' 
                  ? 'bg-background text-foreground shadow-sm' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              {language === 'en' ? 'Domain Traffic' : '域名流量'}
            </button>
            <button
              onClick={() => handleTabChange('shorturls')}
              className={`px-4 py-2 text-sm rounded-md transition-colors ${
                activeTab === 'shorturls' 
                  ? 'bg-background text-foreground shadow-sm' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              {language === 'en' ? 'Short URL Visits' : '短链接访问量'}
            </button>
          </div>
          
          <TabsContent value="timeline" className="p-0 mt-0 h-[280px]">
            <TimelineChart data={timelineData} isLoading={isLoading} />
          </TabsContent>
          
          <TabsContent value="domains" className="p-0 mt-0 h-[280px]">
            <DomainTrafficChart data={domainTrafficData} isLoading={isLoading} />
          </TabsContent>
          
          <TabsContent value="shorturls" className="p-0 mt-0 h-[280px]">
            <ShortUrlVisitsChart data={shortUrlVisits} isLoading={isLoading} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default VisitorDistribution;
