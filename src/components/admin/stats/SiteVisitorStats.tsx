
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from '@/components/ui/card';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';

// Import components
import StatsOverview from './components/StatsOverview';
import ChartTypeSwitcher from './tabs/ChartTypeSwitcher';
import MainTabSwitcher from './tabs/MainTabSwitcher';
import DailyTrafficTab from './tabs/DailyTrafficTab';
import DomainsTrafficTab from './tabs/DomainsTrafficTab';
import PagePopularityTab from './tabs/PagePopularityTab';
import { useVisitorStats } from '@/hooks/useVisitorStats';

interface SiteVisitorStatsProps {
  language: string;
}

const SiteVisitorStats: React.FC<SiteVisitorStatsProps> = ({ language }) => {
  const [activeTab, setActiveTab] = useState('timeline');
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');
  const [activeIndex, setActiveIndex] = useState(0);
  const [siteStats, setSiteStats] = useState({
    visits: 0,
    visitors: 0,
    isLoading: true
  });

  const { isLoading } = useVisitorStats();

  // Format numbers according to locale
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat(language === 'en' ? 'en-US' : 'zh-CN').format(num);
  };

  // Fetch site PV/UV statistics
  useEffect(() => {
    const fetchSiteStats = async () => {
      try {
        // Get visits data from url_visits table
        const { data: visitsData, error } = await supabase
          .from('url_visits')
          .select('id, ip_address')
          .order('created_at', { ascending: false });

        if (!error && visitsData) {
          // Calculate total visits
          const totalVisits = visitsData.length;

          // Calculate unique visitors (by IP address)
          const uniqueIPs = new Set();
          visitsData.forEach(visit => {
            if (visit.ip_address) {
              uniqueIPs.add(visit.ip_address);
            }
          });

          const uniqueVisitors = uniqueIPs.size;

          setSiteStats({
            visits: totalVisits,
            visitors: uniqueVisitors,
            isLoading: false
          });
        } else {
          // Fallback data
          setSiteStats({
            visits: 235,
            visitors: 87,
            isLoading: false
          });
        }
      } catch (err) {
        console.error('Error fetching site statistics:', err);
        // Fallback data
        setSiteStats({
          visits: 235,
          visitors: 87,
          isLoading: false
        });
      }
    };

    fetchSiteStats();
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'en' ? 'Site Visitor Statistics' : '网站访问统计'}
          </CardTitle>
          <CardDescription>
            {language === 'en'
              ? 'Detailed visitor traffic analysis by page and time'
              : '按页面和时间的详细访问者流量分析'}
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[350px] flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2 bg-card/50">
        <div>
          <CardTitle>
            {language === 'en' ? 'Site Visitor Statistics' : '网站访问统计'}
          </CardTitle>
          <CardDescription>
            {language === 'en'
              ? 'Detailed visitor traffic analysis by page and time'
              : '按页面和时间的详细访问者流量分析'}
          </CardDescription>
        </div>
        <ChartTypeSwitcher
          chartType={chartType}
          onChangeChartType={setChartType}
          language={language}
        />
      </CardHeader>

      {/* Site PV/UV Stats Section */}
      <CardContent className="pt-4 border-b pb-4">
        <StatsOverview siteStats={siteStats} language={language} />
      </CardContent>

      <CardContent className="h-[350px] pt-4 overflow-hidden">
        <Tabs defaultValue="timeline" className="h-full">
          <MainTabSwitcher onTabChange={setActiveTab} language={language} />

          <TabsContent value="timeline" className="h-[300px] mt-0">
            <DailyTrafficTab
              language={language}
              formatNumber={formatNumber}
              isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="domains" className="h-[300px] mt-0">
            <DomainsTrafficTab
              language={language}
              chartType={chartType}
              activeIndex={activeIndex}
              setActiveIndex={setActiveIndex}
              formatNumber={formatNumber}
              isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="pages" className="h-[300px] mt-0">
            <PagePopularityTab
              language={language}
              chartType={chartType}
              activeIndex={activeIndex}
              setActiveIndex={setActiveIndex}
              formatNumber={formatNumber}
              isLoading={isLoading}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default SiteVisitorStats;
