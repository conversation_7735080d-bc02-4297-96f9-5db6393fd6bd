
import React from 'react';
import { 
  <PERSON>sponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Cell, 
  Legend,
  Tooltip as RechartsTooltip 
} from 'recharts';
import { useAppContext } from '@/context/AppContext';

interface RankingChartProps {
  visitStats: any[];
  filterType: 'country';
}

const RankingChart: React.FC<RankingChartProps> = ({ visitStats, filterType }) => {
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d'];
  const { language } = useAppContext();

  // Sort visit stats for ranking
  const sortedVisitStats = [...visitStats].sort((a, b) => b.value - a.value).slice(0, 10);

  if (!sortedVisitStats.length) {
    return (
      <div className="h-80 flex items-center justify-center">
        <p className="text-muted-foreground">
          {language === 'en' 
            ? 'No country data available' 
            : '没有可用的国家数据'}
        </p>
      </div>
    );
  }

  const renderLegend = (props: any) => {
    const { payload } = props;
    return (
      <div className="flex flex-wrap justify-center gap-x-4 gap-y-2 py-2">
        {payload.map((entry: any, index: number) => (
          <div key={`item-${index}`} className="flex items-center">
            <div 
              className="w-3 h-3 mr-1"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-xs">
              {entry.value} 
              {language === 'en' ? ' (country)' : ' (国家)'}
            </span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={sortedVisitStats}
          layout="vertical"
          margin={{ top: 5, right: 30, left: 80, bottom: 25 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis type="number" domain={[0, 'dataMax']} />
          <YAxis 
            type="category" 
            dataKey="name" 
            width={80}
            tick={{ fontSize: 12 }}
          />
          <RechartsTooltip 
            formatter={(value: any) => [
              `${value} ${language === 'en' ? 'visits' : '访问量'}`, 
              language === 'en' ? 'Visits' : '访问量'
            ]}
          />
          <Legend content={renderLegend} />
          <Bar 
            dataKey="value" 
            fill="#8884d8" 
            name={filterType}
            label={{ position: 'right', fill: '#666', fontSize: 12 }}
          >
            {sortedVisitStats.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RankingChart;
