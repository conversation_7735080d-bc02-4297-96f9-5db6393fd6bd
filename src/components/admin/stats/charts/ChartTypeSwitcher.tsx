
import React from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart2, PieChart } from 'lucide-react';

interface ChartTypeSwitcherProps {
  chartType: 'bar' | 'pie';
  onChangeChartType: (value: 'bar' | 'pie') => void;
  language: string;
}

const ChartTypeSwitcher: React.FC<ChartTypeSwitcherProps> = ({
  chartType,
  onChangeChartType,
  language,
}) => {
  return (
    <Tabs value={chartType} onValueChange={(value) => onChangeChartType(value as 'bar' | 'pie')}>
      <TabsList className="grid w-[180px] grid-cols-2">
        <TabsTrigger value="bar">
          <BarChart2 className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Bar' : '柱状图'}
        </TabsTrigger>
        <TabsTrigger value="pie">
          <PieChart className="h-4 w-4 mr-2" />
          {language === 'en' ? 'Pie' : '饼状图'}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

export default ChartTypeSwitcher;
