
import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip
} from 'recharts';
import { COLORS } from '../constants';

interface PieChartDisplayProps {
  data: Array<{ name: string; visits: number; [key: string]: any }>;
  activeIndex: number;
  onPieEnter: (_: any, index: number) => void;
  renderActiveShape: (props: any) => JSX.Element;
  language: string;
}

const PieChartDisplay: React.FC<PieChartDisplayProps> = ({
  data,
  activeIndex,
  onPieEnter,
  renderActiveShape,
  language
}) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          activeIndex={activeIndex}
          activeShape={renderActiveShape}
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={90}
          fill="#8884d8"
          dataKey="visits"
          onMouseEnter={onPieEnter}
          onClick={(_, index) => onPieEnter(_, index)}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={COLORS[index % COLORS.length]} 
              style={{ filter: 'drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.1))' }}
            />
          ))}
        </Pie>
        <Tooltip
          formatter={(value: number) => [value.toLocaleString(), language === 'en' ? 'Visits' : '访问量']}
        />
        <Legend 
          layout="horizontal"
          verticalAlign="bottom"
          align="center"
          wrapperStyle={{ fontSize: '11px', paddingTop: '5px' }}
        />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default PieChartDisplay;
