
import React from 'react';
import { Card, Card<PERSON>eader, Card<PERSON>itle, CardContent } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';

const CategoryEmptyState: React.FC = () => {
  const { language } = useAppContext();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'Category Statistics' : '分类统计'}</CardTitle>
      </CardHeader>
      <CardContent className="h-80 flex items-center justify-center">
        <p className="text-muted-foreground">
          {language === 'en' 
            ? 'No categories found' 
            : '未找到分类数据'}
        </p>
      </CardContent>
    </Card>
  );
};

export default CategoryEmptyState;
