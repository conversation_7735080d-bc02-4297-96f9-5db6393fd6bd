
import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardTitle, CardContent } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';

interface CategoryStatsStateProps {
  isLoading?: boolean;
  error?: string | null;
}

const CategoryStatsState: React.FC<CategoryStatsStateProps> = ({ isLoading, error }) => {
  const { language } = useAppContext();
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{language === 'en' ? 'Category Statistics' : '分类统计'}</CardTitle>
        </CardHeader>
        <CardContent className="h-80 flex items-center justify-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </CardContent>
      </Card>
    );
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{language === 'en' ? 'Category Statistics' : '分类统计'}</CardTitle>
        </CardHeader>
        <CardContent className="h-80 flex flex-col items-center justify-center">
          <p className="text-destructive mb-2">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="text-sm text-primary hover:underline"
          >
            {language === 'en' ? 'Reload' : '重新加载'}
          </button>
        </CardContent>
      </Card>
    );
  }
  
  return null;
};

export default CategoryStatsState;
