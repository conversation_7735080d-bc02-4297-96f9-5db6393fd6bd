
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, Cell, ResponsiveContainer } from 'recharts';
import CategoryTooltip from './CategoryTooltip';
import { CategoryStatItem } from '../types';
import { useAppContext } from '@/context/AppContext';

interface CategoryBarChartProps {
  data: CategoryStatItem[];
}

const CategoryBarChart: React.FC<CategoryBarChartProps> = ({ data }) => {
  const { language } = useAppContext();
  
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart 
        data={data.slice(0, 7)}
        margin={{ top: 10, right: 30, left: 30, bottom: 50 }}
        barSize={36}
      >
        <XAxis 
          dataKey="name" 
          angle={-45} 
          textAnchor="end" 
          height={70} 
          tick={{ fontSize: 11, fill: '#888' }} 
          tickLine={false}
          axisLine={{ stroke: '#eee' }}
        />
        <YAxis 
          tickLine={false}
          axisLine={{ stroke: '#eee' }}
          tick={{ fill: '#888' }}
        />
        <Tooltip content={<CategoryTooltip />} />
        <Legend 
          wrapperStyle={{ bottom: 0 }} 
          formatter={(value) => <span className="text-sm text-foreground/80">{value}</span>} 
        />
        <Bar 
          dataKey="count" 
          name={language === 'en' ? 'Links Count' : '链接数量'} 
          radius={[4, 4, 0, 0]}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.color} 
              style={{ filter: 'drop-shadow(0px 2px 2px rgba(0, 0, 0, 0.1))' }} 
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};

export default CategoryBarChart;
