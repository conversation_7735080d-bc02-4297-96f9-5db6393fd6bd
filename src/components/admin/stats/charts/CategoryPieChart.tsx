
import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend } from 'recharts';
import { useAppContext } from '@/context/AppContext';
import ActivePieShape from './ActivePieShape';
import CategoryTooltip from './CategoryTooltip';
import { CategoryStatItem } from '../types';

interface CategoryPieChartProps {
  data: CategoryStatItem[];
  colors?: string[]; // Add this optional colors prop
}

const CategoryPieChart: React.FC<CategoryPieChartProps> = ({ data, colors }) => {
  const { language } = useAppContext();
  const [activeIndex, setActiveIndex] = useState(0);

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          activeIndex={activeIndex}
          activeShape={(props) => <ActivePieShape {...props} />}
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={90}
          fill="#8884d8"
          dataKey="count"
          onMouseEnter={onPieEnter}
          paddingAngle={2}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} style={{ filter: 'drop-shadow(0px 3px 3px rgba(0, 0, 0, 0.1))' }} />
          ))}
        </Pie>
        <Legend 
          layout="horizontal"
          verticalAlign="bottom"
          align="center"
          formatter={(value, entry: any) => (
            <span style={{ color: entry.color }} className="text-xs px-2">
              {value}
            </span>
          )}
        />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default CategoryPieChart;
