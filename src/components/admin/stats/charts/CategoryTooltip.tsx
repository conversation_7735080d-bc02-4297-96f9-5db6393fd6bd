
import React from 'react';
import { useAppContext } from '@/context/AppContext';

interface TooltipData {
  name: string;
  count: number;
}

interface CategoryTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: TooltipData;
  }>;
}

const CategoryTooltip: React.FC<CategoryTooltipProps> = ({ active, payload }) => {
  const { language } = useAppContext();
  
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-background/95 p-3 border rounded-md shadow-md text-sm backdrop-blur-sm">
        <p className="font-semibold">{data.name}</p>
        <p className="text-foreground/80 mt-1">
          <span className="font-medium">
            {language === 'en' ? 'Links: ' : '链接数量：'}
          </span>
          {data.count}
        </p>
      </div>
    );
  }
  return null;
};

export default CategoryTooltip;
