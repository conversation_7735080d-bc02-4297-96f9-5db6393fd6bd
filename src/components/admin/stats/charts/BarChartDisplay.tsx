
import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  Cell,
  ResponsiveContainer
} from 'recharts';
import { COLORS } from '../constants';

interface BarChartDisplayProps {
  data: Array<{ name: string; visits: number; [key: string]: any }>;
  language: string;
  vertical?: boolean;
  formatNumber: (num: number) => string;
}

const BarChartDisplay: React.FC<BarChartDisplayProps> = ({
  data,
  language,
  vertical = false,
  formatNumber
}) => {
  const layout = vertical ? 'vertical' : 'horizontal';
  const margins = vertical
    ? { top: 10, right: 10, left: 80, bottom: 10 }
    : { top: 10, right: 10, left: 10, bottom: 30 };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={data}
        layout={layout}
        margin={margins}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        {vertical ? (
          <>
            <XAxis type="number" tick={{ fontSize: 11 }} />
            <YAxis 
              dataKey="name" 
              type="category" 
              tick={{ fontSize: 11 }} 
              width={75}
            />
          </>
        ) : (
          <>
            <XAxis 
              dataKey="date" 
              tick={{ fontSize: 11 }}
              tickFormatter={(value) => {
                const date = new Date(value);
                return `${date.getMonth() + 1}/${date.getDate()}`;
              }}
              height={35}
            />
            <YAxis tick={{ fontSize: 11 }} width={35} />
          </>
        )}
        <Tooltip 
          formatter={(value: number) => [formatNumber(value), language === 'en' ? 'Visits' : '访问量']}
          labelFormatter={vertical ? undefined : (label) => {
            const date = new Date(label);
            return date.toLocaleDateString(language === 'en' ? 'en-US' : 'zh-CN');
          }}
        />
        <Bar 
          dataKey="visits" 
          fill="#10b981" 
          name={language === 'en' ? 'Visits' : '访问量'} 
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};

export default BarChartDisplay;
