
import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { VisitorTimelineData } from '../TimelineChart';

interface TimelineBarChartProps {
  data: VisitorTimelineData[];
  language: string;
  formatNumber: (num: number) => string;
}

const TimelineBarChart: React.FC<TimelineBarChartProps> = ({
  data,
  language,
  formatNumber
}) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={data}
        margin={{ top: 10, right: 10, left: 10, bottom: 30 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="date" 
          tick={{ fontSize: 11 }}
          tickFormatter={(value) => {
            const date = new Date(value);
            return `${date.getMonth() + 1}/${date.getDate()}`;
          }}
          height={35}
        />
        <YA<PERSON>s tick={{ fontSize: 11 }} width={35} />
        <Tooltip 
          formatter={(value: number) => [formatNumber(value), '']}
          labelFormatter={(label) => {
            const date = new Date(label);
            return date.toLocaleDateString(language === 'en' ? 'en-US' : 'zh-CN');
          }}
        />
        <Legend wrapperStyle={{ fontSize: '11px', paddingTop: '5px' }} />
        <Bar 
          name={language === 'en' ? 'Visits (PV)' : '访问量 (PV)'} 
          dataKey="visits" 
          fill="#4f46e5" 
          barSize={20} 
        />
        <Bar 
          name={language === 'en' ? 'Unique Visitors (UV)' : '独立访客 (UV)'} 
          dataKey="visitors" 
          fill="#06b6d4" 
          barSize={20} 
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default TimelineBarChart;
