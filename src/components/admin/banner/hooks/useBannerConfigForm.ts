
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useAppContext } from '@/context/AppContext';
import { useToast } from '@/hooks/use-toast';
import { BannerConfig, BannerTextLine, DisplayStyle } from '@/hooks/useBannerConfig';
import { DEFAULT_CONFIG } from '../utils/bannerConfigConstants';
import { fetchBannerConfigFromDB, saveBannerConfigToDB } from '../services/bannerConfigService';

export const useBannerConfigForm = () => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [bannerConfig, setBannerConfig] = useState<BannerConfig>(DEFAULT_CONFIG);
  const [showPreview, setShowPreview] = useState(false);

  const form = useForm<BannerConfig>({
    defaultValues: DEFAULT_CONFIG
  });

  const fetchBannerConfig = async () => {
    try {
      setLoading(true);
      const config = await fetchBannerConfigFromDB();
      
      if (config) {
        setBannerConfig(config);
        form.reset(config);
      }
    } catch (err) {
      console.error('Error in fetchBannerConfig:', err);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' 
          ? 'Failed to load banner configuration.' 
          : '加载横幅配置失败。'
      });
    } finally {
      setLoading(false);
    }
  };

  const saveBannerConfig = async (values: BannerConfig) => {
    try {
      setSaving(true);
      
      const savedConfig = await saveBannerConfigToDB(values);
      
      if (savedConfig) {
        setBannerConfig(savedConfig);
        form.reset(savedConfig);
        toast({
          description: language === 'en' 
            ? 'Banner configuration saved successfully.' 
            : '横幅配置已成功保存。'
        });
      }
    } catch (err) {
      console.error('Error in saveBannerConfig:', err);
      toast({
        variant: 'destructive',
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' 
          ? 'An unexpected error occurred.' 
          : '发生意外错误。'
      });
    } finally {
      setSaving(false);
    }
  };

  // UI state handlers
  const togglePreview = () => setShowPreview(!showPreview);
  const handleUseThirdLineChange = (checked: boolean) => form.setValue('use_third_line', checked);
  const handleSpacingChange = (value: number[]) => form.setValue('spacing', value[0]);
  const handleAnimationSpeedChange = (value: number[]) => form.setValue('animation_speed', value[0]);
  const handleHeightChange = (value: number[]) => form.setValue('height', value[0]);
  const handleStyleChange = (style: string) => form.setValue('display_style', style as DisplayStyle);

  // Text line handlers
  const addTextLine = () => {
    const currentLines = form.getValues('lines') || [];
    form.setValue('lines', [...currentLines, { text: '', gradient: 'from-primary to-secondary' }]);
  };

  const removeTextLine = (index: number) => {
    const currentLines = form.getValues('lines') || [];
    form.setValue('lines', currentLines.filter((_, i) => i !== index));
  };

  const updateTextLine = (index: number, field: keyof BannerTextLine, value: string) => {
    const currentLines = form.getValues('lines') || [];
    const updatedLines = [...currentLines];
    updatedLines[index] = { ...updatedLines[index], [field]: value };
    form.setValue('lines', updatedLines);
  };

  return {
    form,
    loading,
    saving,
    bannerConfig,
    showPreview,
    fetchBannerConfig,
    saveBannerConfig,
    togglePreview,
    handleUseThirdLineChange,
    handleSpacingChange,
    handleAnimationSpeedChange,
    handleHeightChange,
    handleStyleChange,
    addTextLine,
    removeTextLine,
    updateTextLine
  };
};
