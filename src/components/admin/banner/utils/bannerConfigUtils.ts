
import { BannerConfig, BannerTextLine, parseBannerTextLines } from '@/hooks/useBannerConfig';

// Generate text lines from traditional fields
export const generateLinesFromFields = (config: BannerConfig): BannerTextLine[] => {
  const lines: BannerTextLine[] = [];
  
  if (config.first_text) {
    lines.push({
      text: config.first_text,
      gradient: config.first_gradient || ''
    });
  }
  
  if (config.second_text) {
    lines.push({
      text: config.second_text,
      gradient: config.second_gradient || ''
    });
  }
  
  if (config.use_third_line && config.third_text) {
    lines.push({
      text: config.third_text,
      gradient: config.third_gradient || ''
    });
  }
  
  if (config.use_third_line && config.fourth_text) {
    lines.push({
      text: config.fourth_text,
      gradient: config.fourth_gradient || ''
    });
  }
  
  return lines;
};

// Prepare banner config for Supabase by ensuring correct types
export const prepareConfigForSaving = (values: BannerConfig): any => {
  return {
    ...values,
    // Make sure lines is never undefined
    lines: values.lines || [],
    // Convert null strings to actual nulls
    third_text: values.third_text || null,
    third_gradient: values.third_gradient || null,
    fourth_text: values.fourth_text || null,
    fourth_gradient: values.fourth_gradient || null
  };
};

// Parse data received from Supabase into the correct format
export const parseConfigFromDatabase = (data: any): BannerConfig => {
  // Parse the lines data from JSONB
  const parsedLines = parseBannerTextLines(data.lines);
  
  // Initialize or convert lines data
  const configData: BannerConfig = {
    ...data,
    display_style: data.display_style || 'default',
    lines: parsedLines
  };
  
  // If there are no custom lines but we have traditional fields, convert them
  if ((!configData.lines || configData.lines.length === 0) && configData.first_text) {
    configData.lines = generateLinesFromFields(configData);
  }
  
  return configData;
};
