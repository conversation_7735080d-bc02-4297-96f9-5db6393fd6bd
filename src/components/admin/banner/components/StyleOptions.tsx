
import React from 'react';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { useAppContext } from '@/context/AppContext';
import { useFormContext } from 'react-hook-form';
import { BannerConfig } from '@/hooks/useBannerConfig';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { FormControl, FormItem, FormLabel } from '@/components/ui/form';

interface StyleOptionsProps {
  onSpacingChange: (value: number[]) => void;
  onAnimationSpeedChange: (value: number[]) => void;
  onHeightChange?: (value: number[]) => void;
  onStyleChange?: (style: string) => void;
}

const StyleOptions: React.FC<StyleOptionsProps> = ({ 
  onSpacingChange, 
  onAnimationSpeedChange,
  onHeightChange = () => {},
  onStyleChange = () => {}
}) => {
  const { language } = useAppContext();
  const { watch } = useFormContext<BannerConfig>();
  const displayStyle = watch('display_style') || 'default';

  return (
    <div className="space-y-6">
      <h3 className="font-medium text-lg">
        {language === 'en' ? 'Style Options' : '样式选项'}
      </h3>
      
      <div className="space-y-6">
        <div>
          <Label htmlFor="spacing">
            {language === 'en' ? 'Line Spacing' : '行距'}
          </Label>
          <div className="flex items-center space-x-4 mt-2">
            <span className="text-sm">1</span>
            <Slider
              id="spacing"
              min={1}
              max={5}
              step={0.5}
              value={[watch('spacing') || 2]}
              onValueChange={onSpacingChange}
            />
            <span className="text-sm">5</span>
          </div>
        </div>
        
        <div>
          <Label htmlFor="animation_speed">
            {language === 'en' ? 'Animation Speed' : '动画速度'}
          </Label>
          <div className="flex items-center space-x-4 mt-2">
            <span className="text-sm">0.5</span>
            <Slider
              id="animation_speed"
              min={0.5}
              max={2}
              step={0.1}
              value={[watch('animation_speed') || 0.8]}
              onValueChange={onAnimationSpeedChange}
            />
            <span className="text-sm">2.0</span>
          </div>
        </div>

        <div>
          <Label htmlFor="height">
            {language === 'en' ? 'Banner Height (px)' : '横幅高度 (px)'}
          </Label>
          <div className="flex items-center space-x-4 mt-2">
            <span className="text-sm">150</span>
            <Slider
              id="height"
              min={150}
              max={500}
              step={10}
              value={[watch('height') || 250]}
              onValueChange={onHeightChange}
            />
            <span className="text-sm">500</span>
            <span className="bg-muted rounded px-2 py-1 text-sm">
              {watch('height') || 250}px
            </span>
          </div>
        </div>

        <div className="space-y-3">
          <Label>
            {language === 'en' ? 'Display Style' : '显示样式'}
          </Label>
          <RadioGroup 
            value={displayStyle} 
            onValueChange={onStyleChange}
            className="grid grid-cols-2 gap-4"
          >
            <FormItem>
              <FormLabel className="flex items-center space-x-3 p-4 border rounded-md [&:has([data-state=checked])]:border-primary">
                <FormControl>
                  <RadioGroupItem value="default" />
                </FormControl>
                <span>{language === 'en' ? 'Default' : '默认'}</span>
              </FormLabel>
            </FormItem>
            <FormItem>
              <FormLabel className="flex items-center space-x-3 p-4 border rounded-md [&:has([data-state=checked])]:border-primary">
                <FormControl>
                  <RadioGroupItem value="stacked" />
                </FormControl>
                <span>{language === 'en' ? 'Stacked' : '堆叠'}</span>
              </FormLabel>
            </FormItem>
            <FormItem>
              <FormLabel className="flex items-center space-x-3 p-4 border rounded-md [&:has([data-state=checked])]:border-primary">
                <FormControl>
                  <RadioGroupItem value="inline" />
                </FormControl>
                <span>{language === 'en' ? 'Inline' : '内联'}</span>
              </FormLabel>
            </FormItem>
            <FormItem>
              <FormLabel className="flex items-center space-x-3 p-4 border rounded-md [&:has([data-state=checked])]:border-primary">
                <FormControl>
                  <RadioGroupItem value="flow" />
                </FormControl>
                <span>{language === 'en' ? 'Flow' : '流动'}</span>
              </FormLabel>
            </FormItem>
          </RadioGroup>
        </div>
      </div>
    </div>
  );
};

export default StyleOptions;
