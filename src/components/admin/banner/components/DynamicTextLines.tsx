
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { BannerConfig, BannerTextLine } from '@/hooks/useBannerConfig';

interface DynamicTextLinesProps {
  onUpdateLine: (index: number, field: keyof BannerTextLine, value: string) => void;
  onRemoveLine: (index: number) => void;
}

const DynamicTextLines: React.FC<DynamicTextLinesProps> = ({ onUpdateLine, onRemoveLine }) => {
  const { language } = useAppContext();
  const { watch } = useFormContext<BannerConfig>();
  const lines = watch('lines') || [];

  return (
    <div className="space-y-4">
      {lines.length === 0 ? (
        <div className="text-center text-muted-foreground py-4">
          {language === 'en' 
            ? 'No dynamic lines added. Lines will be generated from basic configuration.' 
            : '未添加动态行。将根据基本配置生成文本行。'}
        </div>
      ) : (
        lines.map((line, index) => (
          <div key={index} className="grid gap-4 grid-cols-1 md:grid-cols-2 border p-4 rounded relative">
            <div className="absolute -top-3 left-3 bg-background px-2 text-xs text-muted-foreground">
              {language === 'en' ? `Line ${index + 1}` : `第 ${index + 1} 行`}
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === 'en' ? 'Text' : '文本'}
              </label>
              <Input
                value={line.text}
                onChange={(e) => onUpdateLine(index, 'text', e.target.value)}
                placeholder={language === 'en' ? 'Enter text' : '输入文本'}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === 'en' ? 'Gradient' : '渐变色'}
              </label>
              <div className="flex gap-2">
                <Input
                  value={line.gradient}
                  onChange={(e) => onUpdateLine(index, 'gradient', e.target.value)}
                  placeholder={language === 'en' ? 'Tailwind gradient' : 'Tailwind渐变类'}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  onClick={() => onRemoveLine(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default DynamicTextLines;
