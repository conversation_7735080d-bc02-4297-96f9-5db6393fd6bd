
import React from 'react';
import { FormField, FormItem, FormLabel, FormDescription, FormControl } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useAppContext } from '@/context/AppContext';
import { useFormContext } from 'react-hook-form';
import { BannerConfig } from '@/hooks/useBannerConfig';

interface TextFieldPairProps {
  textField: keyof BannerConfig;
  gradientField: keyof BannerConfig;
  title: string;
}

const TextFieldPair: React.FC<TextFieldPairProps> = ({ 
  textField, 
  gradientField, 
  title 
}) => {
  const { language } = useAppContext();
  const { control } = useFormContext<BannerConfig>();

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-lg">
        {title}
      </h3>
      
      <FormField
        control={control}
        name={textField}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              {language === 'en' ? 'Text' : '文本'}
            </FormLabel>
            <FormControl>
              <Input 
                {...field} 
                value={typeof field.value === 'string' ? field.value : ''} 
              />
            </FormControl>
          </FormItem>
        )}
      />
      
      <FormField
        control={control}
        name={gradientField}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              {language === 'en' ? 'Gradient' : '渐变色'}
            </FormLabel>
            <FormDescription>
              {language === 'en' 
                ? 'Use Tailwind CSS gradient classes' 
                : '使用Tailwind CSS渐变类'}
            </FormDescription>
            <FormControl>
              <Input 
                {...field} 
                value={typeof field.value === 'string' ? field.value : ''} 
              />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
};

export default TextFieldPair;
