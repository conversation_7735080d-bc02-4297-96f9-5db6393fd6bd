
import React from 'react';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { <PERSON>Footer } from '@/components/ui/card';
import { Eye, Save, Plus, Trash2 } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { BannerConfig, BannerTextLine } from '@/hooks/useBannerConfig';
import { FormProvider, useFormContext } from 'react-hook-form';
import TextFieldPair from './TextFieldPair';
import ThirdLineToggle from './ThirdLineToggle';
import StyleOptions from './StyleOptions';
import BannerPreview from './BannerPreview';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DynamicTextLines from './DynamicTextLines';

interface BannerFormProps {
  form: any;
  saving: boolean;
  showPreview: boolean;
  onSubmit: (values: BannerConfig) => Promise<void>;
  onTogglePreview: () => void;
  onUseThirdLineChange: (checked: boolean) => void;
  onSpacingChange: (value: number[]) => void;
  onAnimationSpeedChange: (value: number[]) => void;
  onHeightChange?: (value: number[]) => void;
  onStyleChange?: (style: string) => void;
  onAddTextLine?: () => void;
  onRemoveTextLine?: (index: number) => void;
  onUpdateTextLine?: (index: number, field: keyof BannerTextLine, value: string) => void;
}

const BannerForm: React.FC<BannerFormProps> = ({ 
  form, 
  saving, 
  showPreview,
  onSubmit, 
  onTogglePreview,
  onUseThirdLineChange,
  onSpacingChange,
  onAnimationSpeedChange,
  onHeightChange = () => {},
  onStyleChange = () => {},
  onAddTextLine = () => {},
  onRemoveTextLine = () => {},
  onUpdateTextLine = () => {}
}) => {
  const { language } = useAppContext();
  
  return (
    <FormProvider {...form}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Tabs defaultValue="basic">
            <TabsList>
              <TabsTrigger value="basic">
                {language === 'en' ? 'Basic Configuration' : '基础配置'}
              </TabsTrigger>
              <TabsTrigger value="advanced">
                {language === 'en' ? 'Advanced Configuration' : '高级配置'}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="mt-4 space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <TextFieldPair 
                  textField="first_text" 
                  gradientField="first_gradient" 
                  title={language === 'en' ? 'First Line' : '第一行'}
                />
                <TextFieldPair 
                  textField="second_text" 
                  gradientField="second_gradient" 
                  title={language === 'en' ? 'Second Line' : '第二行'}
                />
              </div>

              <ThirdLineToggle onToggleChange={onUseThirdLineChange} />
              
              <StyleOptions 
                onSpacingChange={onSpacingChange} 
                onAnimationSpeedChange={onAnimationSpeedChange}
                onHeightChange={onHeightChange}
                onStyleChange={onStyleChange}
              />
            </TabsContent>
            
            <TabsContent value="advanced" className="mt-4 space-y-6">
              <div className="border rounded p-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="font-medium text-lg">
                    {language === 'en' ? 'Dynamic Text Lines' : '动态文本行'}
                  </h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onAddTextLine()}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {language === 'en' ? 'Add Line' : '添加行'}
                  </Button>
                </div>
                
                <DynamicTextLines 
                  onUpdateLine={onUpdateTextLine} 
                  onRemoveLine={onRemoveTextLine}
                />
              </div>
            </TabsContent>
          </Tabs>
          
          <div>
            <Button
              type="button"
              variant="outline"
              onClick={onTogglePreview}
            >
              <Eye className="h-4 w-4 mr-2" />
              {language === 'en' ? 'Toggle Preview' : '切换预览'}
            </Button>
          </div>
          
          <BannerPreview showPreview={showPreview} />
          
          <CardFooter className="px-0 pb-0">
            <Button 
              type="submit" 
              className="ml-auto"
              disabled={saving}
            >
              {saving && <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>}
              <Save className="h-4 w-4 mr-2" />
              {language === 'en' ? 'Save Configuration' : '保存配置'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </FormProvider>
  );
};

export default BannerForm;
