import React from 'react';
import { useAppContext } from '@/context/AppContext';
import AnimatedGradientTitle from '@/components/common/AnimatedGradientTitle';
import { BannerConfig, BannerTextLine } from '@/hooks/useBannerConfig';
import { useFormContext } from 'react-hook-form';
import { cn } from '@/lib/utils';

interface BannerPreviewProps {
  showPreview: boolean;
}

// Extended title interface that matches AnimatedGradientTitle requirements
interface ExtendedTitleFormat {
  main: string;
  subtitle: string;
  first: { text: string, gradient: string };
  second: { text: string, gradient: string };
  third?: { text: string, gradient: string };
  fourth?: { text: string, gradient: string };
}

const BannerPreview: React.FC<BannerPreviewProps> = ({ showPreview }) => {
  const { language } = useAppContext();
  const { watch } = useFormContext<BannerConfig>();
  
  const bannerConfig = watch();
  const dynamicLines = watch('lines') || [];
  const displayStyle = watch('display_style') || 'default';
  const height = watch('height') || 250;

  if (!showPreview) return null;

  // Helper function to convert old config format to title format
  const convertToTitleFormat = (): ExtendedTitleFormat => {
    // Create the base title object with required main and subtitle properties
    const titleObject: ExtendedTitleFormat = {
      main: '',
      subtitle: '',
      first: { text: '', gradient: '' },
      second: { text: '', gradient: '' }
    };
    
    // If we have dynamic lines, use them
    if (dynamicLines && dynamicLines.length > 0) {
      // Set main and subtitle from the first two lines
      titleObject.main = dynamicLines[0]?.text || '';
      titleObject.subtitle = dynamicLines[1]?.text || '';
      
      // Also set first and second for backward compatibility
      titleObject.first = dynamicLines[0] || { text: '', gradient: '' };
      titleObject.second = dynamicLines[1] || { text: '', gradient: '' };
      
      // Add optional third and fourth lines
      if (dynamicLines.length > 2) {
        titleObject.third = dynamicLines[2];
      }
      
      if (dynamicLines.length > 3) {
        titleObject.fourth = dynamicLines[3];
      }
    } else {
      // Otherwise use the traditional fields
      titleObject.main = bannerConfig.first_text || '';
      titleObject.subtitle = bannerConfig.second_text || '';
      
      titleObject.first = {
        text: bannerConfig.first_text || '',
        gradient: bannerConfig.first_gradient || ''
      };
      
      titleObject.second = {
        text: bannerConfig.second_text || '',
        gradient: bannerConfig.second_gradient || ''
      };
      
      if (bannerConfig.use_third_line && bannerConfig.third_text) {
        titleObject.third = {
          text: bannerConfig.third_text,
          gradient: bannerConfig.third_gradient || ''
        };
      }
      
      if (bannerConfig.use_third_line && bannerConfig.fourth_text) {
        titleObject.fourth = {
          text: bannerConfig.fourth_text,
          gradient: bannerConfig.fourth_gradient || ''
        };
      }
    }
    
    return titleObject;
  };

  const renderBannerContent = () => {
    switch (displayStyle) {
      case 'stacked':
        return (
          <div className={cn(`space-y-${bannerConfig.spacing || 2}`, "text-center")}>
            {(dynamicLines.length > 0 ? dynamicLines : generateDefaultLines()).map((line, index) => (
              <div 
                key={index} 
                className={cn(
                  "text-3xl md:text-4xl lg:text-5xl font-bold bg-clip-text text-transparent",
                  line.gradient
                )}
              >
                {line.text}
              </div>
            ))}
          </div>
        );
        
      case 'inline':
        return (
          <div className="flex flex-wrap justify-center gap-x-4 gap-y-2">
            {(dynamicLines.length > 0 ? dynamicLines : generateDefaultLines()).map((line, index) => (
              <div 
                key={index} 
                className={cn(
                  "text-3xl md:text-4xl lg:text-5xl font-bold bg-clip-text text-transparent",
                  line.gradient
                )}
              >
                {line.text}
              </div>
            ))}
          </div>
        );
        
      case 'flow':
        return (
          <div className="flex flex-wrap justify-center">
            {(dynamicLines.length > 0 ? dynamicLines : generateDefaultLines()).map((line, index) => (
              <React.Fragment key={index}>
                <div 
                  className={cn(
                    "text-3xl md:text-4xl lg:text-5xl font-bold bg-clip-text text-transparent px-2",
                    line.gradient
                  )}
                >
                  {line.text}
                </div>
                {index < (dynamicLines.length || 4) - 1 && (
                  <div className="text-3xl md:text-4xl lg:text-5xl font-bold px-1">•</div>
                )}
              </React.Fragment>
            ))}
          </div>
        );
      
      case 'default':
      default:
        return (
          <AnimatedGradientTitle
            title={convertToTitleFormat()}
            className={cn(`space-y-${bannerConfig.spacing || 2}`)}
          />
        );
    }
  };
  
  // Generate default lines from the traditional fields
  const generateDefaultLines = (): BannerTextLine[] => {
    const lines: BannerTextLine[] = [];
    
    if (bannerConfig.first_text) {
      lines.push({
        text: bannerConfig.first_text,
        gradient: bannerConfig.first_gradient
      });
    }
    
    if (bannerConfig.second_text) {
      lines.push({
        text: bannerConfig.second_text,
        gradient: bannerConfig.second_gradient
      });
    }
    
    if (bannerConfig.use_third_line && bannerConfig.third_text) {
      lines.push({
        text: bannerConfig.third_text,
        gradient: bannerConfig.third_gradient || ''
      });
    }
    
    if (bannerConfig.use_third_line && bannerConfig.fourth_text) {
      lines.push({
        text: bannerConfig.fourth_text,
        gradient: bannerConfig.fourth_gradient || ''
      });
    }
    
    return lines;
  };
  
  return (
    <div className="border rounded-md p-6 bg-muted/20">
      <h3 className="text-center mb-4">
        {language === 'en' ? 'Preview' : '预览'}
      </h3>
      
      <div 
        className="flex items-center justify-center border border-dashed p-4 rounded-md" 
        style={{ height: `${height}px` }}
      >
        {renderBannerContent()}
      </div>
    </div>
  );
};

export default BannerPreview;
