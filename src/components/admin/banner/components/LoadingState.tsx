
import React from 'react';
import { CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';

const LoadingState: React.FC = () => {
  const { language } = useAppContext();
  
  return (
    <>
      <CardHeader>
        <CardTitle>
          {language === 'en' ? 'Banner Text Configuration' : '横幅文本配置'}
        </CardTitle>
        <CardDescription>
          {language === 'en' 
            ? 'Loading banner configuration...' 
            : '正在加载横幅配置...'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-40 flex items-center justify-center">
          <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </CardContent>
    </>
  );
};

export default LoadingState;
