
import React from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useAppContext } from '@/context/AppContext';
import { useFormContext } from 'react-hook-form';
import { BannerConfig } from '@/hooks/useBannerConfig';
import TextFieldPair from './TextFieldPair';

interface ThirdLineToggleProps {
  onToggleChange: (checked: boolean) => void;
}

const ThirdLineToggle: React.FC<ThirdLineToggleProps> = ({ onToggleChange }) => {
  const { language } = useAppContext();
  const { watch } = useFormContext<BannerConfig>();
  const useThirdLine = watch('use_third_line');

  return (
    <div>
      <div className="flex items-center space-x-2 mb-4">
        <Switch 
          id="use-third-line"
          checked={useThirdLine}
          onCheckedChange={onToggleChange}
        />
        <Label htmlFor="use-third-line">
          {language === 'en' ? 'Use second line break' : '使用第二行分隔'}
        </Label>
      </div>
      
      {useThirdLine && (
        <div className="grid gap-6 md:grid-cols-2">
          <TextFieldPair 
            textField="third_text" 
            gradientField="third_gradient" 
            title={language === 'en' ? 'Third Line' : '第三行'}
          />
          <TextFieldPair 
            textField="fourth_text" 
            gradientField="fourth_gradient" 
            title={language === 'en' ? 'Fourth Line' : '第四行'}
          />
        </div>
      )}
    </div>
  );
};

export default ThirdLineToggle;
