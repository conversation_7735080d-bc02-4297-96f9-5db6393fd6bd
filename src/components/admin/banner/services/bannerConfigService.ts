
import { supabase } from '@/integrations/supabase/client';
import { BannerConfig } from '@/hooks/useBannerConfig';
import { parseConfigFromDatabase, prepareConfigForSaving } from '../utils/bannerConfigUtils';

export const fetchBannerConfigFromDB = async (): Promise<BannerConfig | null> => {
  try {
    const { data, error } = await supabase
      .from('banner_config')
      .select('*')
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching banner config:', error);
      throw error;
    }

    if (data) {
      return parseConfigFromDatabase(data);
    }
    
    return null;
  } catch (err) {
    console.error('Error in fetchBannerConfigFromDB:', err);
    throw err;
  }
};

export const saveBannerConfigToDB = async (values: BannerConfig): Promise<BannerConfig | null> => {
  try {
    const configToSave = prepareConfigForSaving(values);
    
    let response;
    
    if (values.id) {
      // Update existing config
      response = await supabase
        .from('banner_config')
        .update(configToSave)
        .eq('id', values.id)
        .select();
    } else {
      // Insert new config
      response = await supabase
        .from('banner_config')
        .insert(configToSave)
        .select();
    }
    
    const { data, error } = response;
    
    if (error) {
      console.error('Error saving banner config:', error);
      throw error;
    }
    
    if (data && data.length > 0) {
      return parseConfigFromDatabase(data[0]);
    }
    
    return null;
  } catch (err) {
    console.error('Error in saveBannerConfigToDB:', err);
    throw err;
  }
};
