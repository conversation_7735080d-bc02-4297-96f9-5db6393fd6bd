
import React from 'react';
import ReactMarkdown from 'react-markdown';
import { cn } from '@/lib/utils';

interface MarkdownMemoProps {
  content: string;
  className?: string;
}

export const MarkdownMemo = ({ content, className }: MarkdownMemoProps) => {
  return (
    <div className={cn("prose prose-sm dark:prose-invert max-w-none break-words",
        "prose-p:leading-relaxed prose-pre:p-0",
        "prose-code:text-primary prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded-md prose-code:font-normal",
        className
    )}>
      <ReactMarkdown>
        {content}
      </ReactMarkdown>
    </div>
  );
};
