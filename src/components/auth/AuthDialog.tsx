import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAppContext } from '@/context/AppContext';
import { authService } from '@/services/api';
import { getBackendConfig } from '@/config/backend';
import { useToast } from '@/components/ui/use-toast';
import { Mail, Lock, User, Eye, EyeOff } from 'lucide-react';
import { OAuthButtons } from '@/components/ui/oauth-buttons';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';

interface AuthDialogProps {
  isOpen: boolean;
  onClose: () => void;
  defaultMode?: 'login' | 'register' | 'reset';
}

const AuthDialog: React.FC<AuthDialogProps> = ({ 
  isOpen, 
  onClose, 
  defaultMode = 'login' 
}) => {
  const { language, fetchUser } = useAppContext();
  const { toast } = useToast();
  const [mode, setMode] = useState<'login' | 'register' | 'reset'>(defaultMode);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [username, setUsername] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showEmailNotVerified, setShowEmailNotVerified] = useState(false);
  const [isResendingEmail, setIsResendingEmail] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  // 重置表单
  const resetForm = () => {
    setEmail('');
    setPassword('');
    setConfirmPassword('');
    setUsername('');
    setError(null);
    setShowEmailNotVerified(false);
    setResendCooldown(0);
  };

  // 当模式改变时重置表单
  useEffect(() => {
    resetForm();
  }, [mode]);

  // 当对话框关闭时重置表单
  useEffect(() => {
    if (!isOpen) {
      resetForm();
      setMode(defaultMode);
    }
  }, [isOpen, defaultMode]);

  // 冷却时间倒计时
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.login({
        email: email,
        password: password,
      });

      if (response.data.token) {
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('auth_token', response.data.token);
        await fetchUser();
        window.dispatchEvent(new Event('authStateChanged'));

        toast({
          description: language === 'en' ? 'Logged in successfully!' : '登录成功！',
        });
        onClose();
      } else {
        throw new Error('未收到有效的认证令牌');
      }
    } catch (error: any) {
      let errorMessage = language === 'en'
        ? 'Login failed. Please check your credentials.'
        : '登录失败。请检查您的凭据。';

      if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
        if (errorMessage.includes('邮箱未验证') || errorMessage.includes('email not verified')) {
          setShowEmailNotVerified(true);
        }
      }

      setError(errorMessage);
      toast({
        variant: 'destructive',
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    if (password !== confirmPassword) {
      setError(language === 'en' ? 'Passwords do not match' : '密码不匹配');
      setIsLoading(false);
      return;
    }

    try {
      await authService.register({
        username,
        email,
        password,
      });

      toast({
        description: language === 'en' 
          ? 'Registration successful! Please check your email to verify your account.' 
          : '注册成功！请检查您的邮箱以验证您的账户。',
      });
      
      setMode('login');
      setPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      const errorMessage = error?.response?.data?.error || 
        (language === 'en' ? 'Registration failed' : '注册失败');
      setError(errorMessage);
      toast({
        variant: 'destructive',
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;

      const response = await fetch(`${baseURL}/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send reset email');
      }

      toast({
        description: language === 'en' 
          ? 'Password reset email sent! Please check your email.' 
          : '密码重置邮件已发送！请检查您的邮箱。',
      });
      
      setMode('login');
    } catch (error: any) {
      const errorMessage = error.message || 
        (language === 'en' ? 'Failed to send reset email' : '发送重置邮件失败');
      setError(errorMessage);
      toast({
        variant: 'destructive',
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerificationEmail = async () => {
    if (!email || resendCooldown > 0) return;

    setIsResendingEmail(true);
    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;

      const response = await fetch(`${baseURL}/auth/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send verification email');
      }

      toast({
        description: language === 'en'
          ? 'Verification email has been sent to your email address'
          : '验证邮件已发送到您的邮箱',
      });

      setResendCooldown(60);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        description: error.message || (language === 'en'
          ? 'Failed to send verification email'
          : '发送验证邮件失败'),
      });
    } finally {
      setIsResendingEmail(false);
    }
  };

  const getTitle = () => {
    switch (mode) {
      case 'login':
        return language === 'en' ? 'Login' : '登录';
      case 'register':
        return language === 'en' ? 'Register' : '注册';
      case 'reset':
        return language === 'en' ? 'Reset Password' : '重置密码';
    }
  };

  const getDescription = () => {
    switch (mode) {
      case 'login':
        return language === 'en' 
          ? 'Enter your email and password to login' 
          : '输入您的电子邮件和密码以登录';
      case 'register':
        return language === 'en' 
          ? 'Create a new account to get started' 
          : '创建新账户以开始使用';
      case 'reset':
        return language === 'en' 
          ? 'Enter your email to receive a password reset link' 
          : '输入您的邮箱以接收密码重置链接';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <p className="text-sm text-muted-foreground">{getDescription()}</p>
        </DialogHeader>

        <div className="space-y-4">
          {/* OAuth登录按钮 - 仅在登录模式显示 */}
          {mode === 'login' && (
            <>
              <OAuthButtons onSuccess={async (response) => {
                localStorage.setItem('authToken', response.token);
                localStorage.setItem('auth_token', response.token);
                await fetchUser();
                window.dispatchEvent(new Event('authStateChanged'));
                toast({
                  description: language === 'en' ? 'Logged in successfully!' : '登录成功！',
                });
                onClose();
              }} />

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    {language === 'en' ? 'Or continue with email' : '或者使用邮箱登录'}
                  </span>
                </div>
              </div>
            </>
          )}

          {/* 表单 */}
          <form onSubmit={
            mode === 'login' ? handleLogin : 
            mode === 'register' ? handleRegister : 
            handleResetPassword
          } className="space-y-4">
            
            {/* 用户名字段 - 仅注册时显示 */}
            {mode === 'register' && (
              <div className="relative">
                <User className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder={language === 'en' ? 'Username' : '用户名'}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  className="pl-10"
                />
              </div>
            )}

            {/* 邮箱字段 */}
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="email"
                placeholder={language === 'en' ? 'Email' : '电子邮件'}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="pl-10"
              />
            </div>

            {/* 密码字段 - 重置密码时不显示 */}
            {mode !== 'reset' && (
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type={showPassword ? 'text' : 'password'}
                  placeholder={language === 'en' ? 'Password' : '密码'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="pl-10 pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            )}

            {/* 确认密码字段 - 仅注册时显示 */}
            {mode === 'register' && (
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type={showPassword ? 'text' : 'password'}
                  placeholder={language === 'en' ? 'Confirm Password' : '确认密码'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="pl-10"
                />
              </div>
            )}

            {/* 错误信息 */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* 邮箱未验证提示 */}
            {showEmailNotVerified && email && mode === 'login' && (
              <Alert>
                <InfoIcon className="h-4 w-4" />
                <AlertDescription>
                  <p className="mb-2">
                    {language === 'en'
                      ? 'Your email address is not verified. Please check your email and click the verification link.'
                      : '您的邮箱尚未验证。请检查您的邮箱并点击验证链接。'}
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleResendVerificationEmail}
                    disabled={isResendingEmail || resendCooldown > 0}
                    className="w-full"
                  >
                    {isResendingEmail
                      ? language === 'en' ? 'Sending...' : '发送中...'
                      : resendCooldown > 0
                      ? `${language === 'en' ? 'Resend in' : '重新发送'} ${resendCooldown}s`
                      : language === 'en' ? 'Resend verification email' : '重新发送验证邮件'}
                  </Button>
                </AlertDescription>
              </Alert>
            )}

            {/* 提交按钮 */}
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading
                ? language === 'en' ? 'Loading...' : '加载中...'
                : mode === 'login'
                ? language === 'en' ? 'Login' : '登录'
                : mode === 'register'
                ? language === 'en' ? 'Register' : '注册'
                : language === 'en' ? 'Send Reset Email' : '发送重置邮件'}
            </Button>
          </form>

          {/* 模式切换 */}
          <div className="text-center space-y-2">
            {mode === 'login' && (
              <>
                <div>
                  <button
                    type="button"
                    onClick={() => setMode('reset')}
                    className="text-sm text-primary hover:underline"
                  >
                    {language === 'en' ? 'Forgot password?' : '忘记密码？'}
                  </button>
                </div>
                <div>
                  {language === 'en' ? "Don't have an account?" : '没有账户？'}{' '}
                  <button
                    type="button"
                    onClick={() => setMode('register')}
                    className="text-primary hover:underline"
                  >
                    {language === 'en' ? 'Register' : '注册'}
                  </button>
                </div>
              </>
            )}
            
            {mode === 'register' && (
              <div>
                {language === 'en' ? 'Already have an account?' : '已有账户？'}{' '}
                <button
                  type="button"
                  onClick={() => setMode('login')}
                  className="text-primary hover:underline"
                >
                  {language === 'en' ? 'Login' : '登录'}
                </button>
              </div>
            )}
            
            {mode === 'reset' && (
              <div>
                {language === 'en' ? 'Remember your password?' : '记起密码了？'}{' '}
                <button
                  type="button"
                  onClick={() => setMode('login')}
                  className="text-primary hover:underline"
                >
                  {language === 'en' ? 'Back to login' : '返回登录'}
                </button>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthDialog;
