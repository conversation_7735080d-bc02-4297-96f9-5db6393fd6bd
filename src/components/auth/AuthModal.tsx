import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAppContext } from "@/context/AppContext";
import { authService } from "@/services/api";
import { useToast } from "@/components/ui/use-toast";
import { Mail, Lock, User } from "lucide-react";
import { OAuthButtons } from "@/components/ui/oauth-buttons";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getBackendConfig } from "@/config/backend";

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultMode?: "login" | "register" | "forgot";
}

export const AuthModal = ({ isOpen, onClose, defaultMode = "login" }: AuthModalProps) => {
  const { language, fetchUser, t } = useAppContext();
  const { toast } = useToast();
  const [mode, setMode] = useState<"login" | "register" | "forgot">(defaultMode);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [username, setUsername] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showEmailNotVerified, setShowEmailNotVerified] = useState(false);
  const [isResendingEmail, setIsResendingEmail] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  // 重置表单
  const resetForm = () => {
    setEmail("");
    setPassword("");
    setConfirmPassword("");
    setUsername("");
    setError(null);
    setShowEmailNotVerified(false);
    setIsResendingEmail(false);
    setResendCooldown(0);
  };

  // 当模式改变时重置表单
  useEffect(() => {
    resetForm();
  }, [mode]);

  // 当模态框打开时，确保模式与defaultMode一致
  useEffect(() => {
    if (isOpen) {
      setMode(defaultMode);
    }
  }, [isOpen, defaultMode]);

  // 当模态框关闭时重置表单
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // 冷却时间倒计时
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setShowEmailNotVerified(false);

    try {
      const response = await authService.login({
        email: email,
        password: password,
      });

      // 保存token到localStorage
      if (response.data.token) {
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('auth_token', response.data.token);

        // 立即更新用户状态
        await fetchUser();

        // 触发自定义事件确保状态更新
        window.dispatchEvent(new Event('authStateChanged'));

        toast({
          description: language === "en" ? "Logged in successfully!" : "登录成功！",
        });
        onClose();
      } else {
        throw new Error('未收到有效的认证令牌');
      }
    } catch (error: unknown) {
      let errorMessage = language === "en"
        ? "Login failed. Please check your credentials."
        : "登录失败。请检查您的凭据。";

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { error?: string } } };
        errorMessage = axiosError.response?.data?.error || errorMessage;

        // 检查是否是邮箱未验证错误
        if (errorMessage.includes("邮箱未验证") || errorMessage.includes("email not verified")) {
          setShowEmailNotVerified(true);
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    // 验证密码确认
    if (password !== confirmPassword) {
      setError(language === "en" ? "Passwords do not match" : "密码不一致");
      setIsLoading(false);
      return;
    }

    try {
      const response = await authService.register({
        username,
        email,
        password,
      });

      console.log('Registration successful:', response);

      toast({
        title: language === "en" ? "Registration successful" : "注册成功",
        description: language === "en"
          ? "Verification email has been sent to your email address. Please check your email and click the verification link to complete registration."
          : "验证邮件已发送到您的邮箱，请查收并点击验证链接完成注册。",
      });

      // 切换到登录模式并预填邮箱
      setMode("login");
      setEmail(email);
      setPassword("");
      setConfirmPassword("");
      setUsername("");
    } catch (error: unknown) {
      console.error('Registration error:', error);

      let errorMessage = language === "en" ? "Registration failed. Please try again." : "注册失败，请重试";

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { error?: string } } };
        errorMessage = axiosError.response?.data?.error || errorMessage;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.forgotPassword(email);

      toast({
        title: t.resetEmailSent,
        description: t.resetEmailSentDesc,
      });

      setMode("login");
    } catch (error: unknown) {
      let errorMessage = t.failedToSendResetEmail;

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { error?: string } } };
        errorMessage = axiosError.response?.data?.error || errorMessage;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerificationEmail = async () => {
    if (!email || resendCooldown > 0) return;

    setIsResendingEmail(true);
    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;

      const response = await fetch(`${baseURL}/auth/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send verification email');
      }

      toast({
        title: language === "en" ? "Email sent" : "邮件已发送",
        description: language === "en"
          ? "Verification email has been sent to your email address"
          : "验证邮件已发送到您的邮箱",
      });

      // 设置60秒冷却时间
      setResendCooldown(60);
    } catch (error: unknown) {
      let errorMessage = language === "en"
        ? "Failed to send verification email"
        : "发送验证邮件失败";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        variant: "destructive",
        description: errorMessage,
      });
    } finally {
      setIsResendingEmail(false);
    }
  };

  const getTitle = () => {
    switch (mode) {
      case "login":
        return language === "en" ? "Login" : "登录";
      case "register":
        return language === "en" ? "Register" : "注册";
      case "forgot":
        return t.forgotPasswordTitle;
    }
  };

  const getDescription = () => {
    switch (mode) {
      case "login":
        return language === "en"
          ? "Enter your email and password to login"
          : "输入您的电子邮件和密码以登录";
      case "register":
        return language === "en"
          ? "Create a new account to get started"
          : "创建新账户以开始使用";
      case "forgot":
        return t.forgotPasswordDesc;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4">
          <form onSubmit={mode === "login" ? handleLogin : mode === "register" ? handleRegister : handleForgotPassword} className="space-y-4">
            {/* 用户名输入 - 只在注册模式显示 */}
            {mode === "register" && (
              <div className="space-y-2">
                <div className="relative">
                  <User className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder={language === "en" ? "Username" : "用户名"}
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    className="pl-10"
                  />
                </div>
              </div>
            )}

            {/* 邮箱输入 */}
            <div className="space-y-2">
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="email"
                  placeholder={language === "en" ? "Email" : "电子邮件"}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="pl-10"
                />
              </div>
            </div>

            {/* 密码输入 - 忘记密码模式不显示 */}
            {mode !== "forgot" && (
              <div className="space-y-2">
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    type="password"
                    placeholder={language === "en" ? "Password" : "密码"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="pl-10"
                  />
                </div>
              </div>
            )}

            {/* 确认密码输入 - 只在注册模式显示 */}
            {mode === "register" && (
              <div className="space-y-2">
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    type="password"
                    placeholder={language === "en" ? "Confirm Password" : "确认密码"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    className="pl-10"
                  />
                </div>
              </div>
            )}

            {/* 忘记密码链接 - 只在登录模式显示 */}
            {mode === "login" && (
              <div className="flex items-center justify-between">
                <Button
                  type="button"
                  variant="link"
                  className="px-0 text-sm"
                  onClick={() => setMode("forgot")}
                >
                  {language === 'en' ? 'Forgot password?' : '忘记密码？'}
                </Button>
              </div>
            )}

            {/* 错误信息 */}
            {error && (
              <div className="text-red-600 text-sm text-center">{error}</div>
            )}

            {/* 邮箱未验证时显示重发邮件按钮 */}
            {showEmailNotVerified && email && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-sm">
                <p className="text-yellow-800 mb-2">
                  {language === "en"
                    ? "Your email address is not verified. Please check your email and click the verification link."
                    : "您的邮箱尚未验证。请检查您的邮箱并点击验证链接。"}
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleResendVerificationEmail}
                  disabled={isResendingEmail || resendCooldown > 0}
                  className="w-full"
                >
                  {isResendingEmail
                    ? language === "en" ? "Sending..." : "发送中..."
                    : resendCooldown > 0
                    ? `${language === "en" ? "Resend in" : "重新发送"} ${resendCooldown}s`
                    : language === "en" ? "Resend verification email" : "重新发送验证邮件"}
                </Button>
              </div>
            )}

            {/* 提交按钮 */}
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading
                ? language === "en" ? "Loading..." : "加载中..."
                : mode === "login"
                ? language === "en" ? "Login" : "登录"
                : mode === "register"
                ? language === "en" ? "Register" : "注册"
                : t.sendResetEmail}
            </Button>
          </form>

          {/* OAuth登录按钮 - 只在登录和注册模式显示 */}
          {(mode === "login" || mode === "register") && (
            <>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    {language === "en" ? "Or continue with" : "或者使用"}
                  </span>
                </div>
              </div>

              <OAuthButtons onSuccess={async (response) => {
                localStorage.setItem('authToken', response.token);
                localStorage.setItem('auth_token', response.token);
                await fetchUser();
                window.dispatchEvent(new Event('authStateChanged'));
                toast({
                  description: language === "en" ? "Logged in successfully!" : "登录成功！",
                });
                onClose();
              }} />
            </>
          )}

          {/* 模式切换 */}
          <div className="text-center text-sm">
            {mode === "login" ? (
              <>
                {language === "en" ? "Don't have an account?" : "没有账户？"}
                <Button
                  type="button"
                  variant="link"
                  className="px-1"
                  onClick={() => setMode("register")}
                >
                  {language === "en" ? "Register" : "注册"}
                </Button>
              </>
            ) : mode === "register" ? (
              <>
                {language === "en" ? "Already have an account?" : "已有账户？"}
                <Button
                  type="button"
                  variant="link"
                  className="px-1"
                  onClick={() => setMode("login")}
                >
                  {language === "en" ? "Login" : "登录"}
                </Button>
              </>
            ) : (
              <>
                {language === "en" ? "Remember your password?" : "记起密码了？"}
                <Button
                  type="button"
                  variant="link"
                  className="px-1"
                  onClick={() => setMode("login")}
                >
                  {t.backToLogin}
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
