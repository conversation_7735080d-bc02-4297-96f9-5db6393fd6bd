import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppContext } from '@/context/AppContext';
import { isUsingSupabase } from '@/config/backend';
import apiClient from '@/services/api';
import Forbidden from '@/pages/Forbidden';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false
}) => {
  const location = useLocation();
  const { user, setUser } = useAppContext();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (isUsingSupabase()) {
          // 使用 Supabase 认证检查
          const { supabase } = await import('@/integrations/supabase/client');
          const { data: { session } } = await supabase.auth.getSession();

          if (session?.user) {
            setIsAuthenticated(true);

            // 检查管理员权限
            if (requireAdmin) {
              const { data: userRoles } = await supabase
                .from('user_roles')
                .select('role')
                .eq('user_id', session.user.id);

              const hasAdminRole = userRoles?.some(role =>
                role.role === 'admin' || role.role === 'super_admin'
              );
              setIsAdmin(hasAdminRole || false);
            } else {
              setIsAdmin(true); // 不需要管理员权限时，认证用户即可
            }
          } else {
            setIsAuthenticated(false);
            setIsAdmin(false);
          }
        } else {
          // 使用 Go 后端认证检查
          const token = localStorage.getItem('authToken');
          if (!token) {
            setIsAuthenticated(false);
            setIsAdmin(false);
            setIsLoading(false);
            return;
          }

          const response = await apiClient.get('/auth/me');
          const userData = response.data;

          if (userData) {
            setIsAuthenticated(true);
            setUser(userData);

            // 检查管理员权限
            if (requireAdmin) {
              setIsAdmin(userData.is_super_admin || userData.role === 'admin');
            } else {
              setIsAdmin(true); // 不需要管理员权限时，认证用户即可
            }
          } else {
            setIsAuthenticated(false);
            setIsAdmin(false);
          }
        }
      } catch (error) {
        console.error('认证检查失败:', error);
        setIsAuthenticated(false);
        setIsAdmin(false);

        // 清除无效的token
        localStorage.removeItem('authToken');
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [requireAdmin, setUser]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // 检查认证状态
  if (!isAuthenticated) {
    return <Forbidden onAuthSuccess={() => window.location.reload()} />;
  }

  // 检查管理员权限（如果需要）
  if (requireAdmin && !isAdmin) {
    return <Forbidden onAuthSuccess={() => window.location.reload()} />;
  }

  // 认证通过，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;
