
import React from 'react';
import { Link2, Mail, Compass, SlidersHorizontal } from 'lucide-react';
import { 
  Ta<PERSON>, 
  Ta<PERSON>Content, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useAppContext } from '@/context/AppContext';
import { useSubmittedLinks } from '@/hooks/useSubmittedLinks';
import { useDomainManagement } from '@/hooks/useDomainManagement';
import UrlsTabContent from './tabs/UrlsTabContent';
import EmailsTabContent from './tabs/EmailsTabContent';
import NavigationTabContent from './tabs/NavigationTabContent';
import FeaturesOrderingTab from './tabs/FeaturesOrderingTab';

interface DashboardTabsProps {
  urlData: any[];
  emailData: any[];
  approvedDomains: string[];
  userId: string;
  isLoading: boolean;
  onCopy: (text: string) => void;
  onDeleteUrl: (id: string) => void;
  onDeleteEmail: (id: string) => void;
  onCreateEmail: (domain: string) => Promise<any>;
  onUrlCreated: (newUrl: any) => void;
}

const DashboardTabs: React.FC<DashboardTabsProps> = ({
  urlData,
  emailData,
  approvedDomains,
  userId,
  isLoading,
  onCopy,
  onDeleteUrl,
  onDeleteEmail,
  onCreateEmail,
  onUrlCreated
}) => {
  const { language } = useAppContext();
  const { submittedLinks, submittedLinksCount } = useSubmittedLinks(userId);
  const { handleAddDomain } = useDomainManagement(approvedDomains);
  
  return (
    <Tabs defaultValue="urls">
      <TabsList className="mb-4">
        <TabsTrigger value="urls">
          <Link2 className="mr-2 h-4 w-4" />
          {language === 'en' ? 'Short URLs' : '短链接'}
        </TabsTrigger>
        <TabsTrigger value="emails">
          <Mail className="mr-2 h-4 w-4" />
          {language === 'en' ? 'Temporary Emails' : '临时邮箱'}
        </TabsTrigger>
        <TabsTrigger value="navigation" className="relative">
          <Compass className="mr-2 h-4 w-4" />
          {language === 'en' ? 'Navigation' : '导航'}
          {submittedLinksCount > 0 && (
            <Badge variant="secondary" className="ml-2 absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs">
              {submittedLinksCount}
            </Badge>
          )}
        </TabsTrigger>
        <TabsTrigger value="features-ordering">
          <SlidersHorizontal className="mr-2 h-4 w-4" />
          {language === 'en' ? 'Features Order' : '功能排序'}
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="urls">
        <UrlsTabContent />
      </TabsContent>
      
      <TabsContent value="emails">
        <EmailsTabContent 
          emailData={emailData}
          isLoading={isLoading}
          onCopy={onCopy}
          onDeleteEmail={onDeleteEmail}
          onCreateEmail={onCreateEmail}
        />
      </TabsContent>
      
      <TabsContent value="navigation">
        <NavigationTabContent 
          userId={userId}
          isLoading={isLoading}
          submittedLinks={submittedLinks}
        />
      </TabsContent>
      
      <TabsContent value="features-ordering">
        <FeaturesOrderingTab 
          userId={userId}
          isLoading={isLoading}
        />
      </TabsContent>
    </Tabs>
  );
};

export default DashboardTabs;
