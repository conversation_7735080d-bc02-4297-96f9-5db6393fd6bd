
import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import DomainForm from '@/components/admin/domains/DomainForm';

interface DomainSubmissionProps {
  onAddDomain: (domain: string) => Promise<boolean>;
  isLoading: boolean;
}

const DomainSubmission: React.FC<DomainSubmissionProps> = ({ onAddDomain, isLoading }) => {
  const { language } = useAppContext();

  // Wrapper function to ensure we always return a boolean
  const handleAddDomain = async (domain: string): Promise<boolean> => {
    try {
      const result = await onAddDomain(domain);
      // If the original function returns void, we'll return true
      return result !== undefined ? result : true;
    } catch (error) {
      console.error('Error adding domain:', error);
      return false;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'Submit Domain' : '提交域名'}</CardTitle>
        <CardDescription>
          {language === 'en' 
            ? 'Submit domains to the whitelist for URL shortening approval.' 
            : '提交域名到白名单以获得URL缩短的批准。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <DomainForm 
          onAddDomain={handleAddDomain} 
          isLoading={isLoading}
          showNotification={true}
        />
      </CardContent>
    </Card>
  );
};

export default DomainSubmission;
