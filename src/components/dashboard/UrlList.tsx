import React, { useState, useEffect } from "react";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Co<PERSON>, ExternalLink, QrCode, Trash2, MoreHorizontal } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import QRCode from '@/components/QRCode';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Checkbox } from "@/components/ui/checkbox";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import QRCodeDialog from "./QRCodeDialog";
import { supabase } from "@/integrations/supabase/client";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { formatDistanceToNow } from "date-fns";
import WarningDialog from "@/components/dialog/WarningDialog";

interface UrlData {
  id: string;
  original_url: string;
  short_code: string;
  clicks: number;
  created_at: string;
  expires_at: string | null;
}

interface UrlListProps {
  urlData: UrlData[];
  onCopy: (text: string) => void;
  onDelete?: (id: string) => void; // Made onDelete optional
  isLoading: boolean;
  onRefresh: () => void;
}

const UrlList: React.FC<UrlListProps> = ({ urlData, onCopy, onDelete, isLoading, onRefresh }) => {
  const { language } = useAppContext();
  const [selectedUrls, setSelectedUrls] = useState<string[]>([]);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [urlToDelete, setUrlToDelete] = useState<string | null>(null);
  const [showQRCode, setShowQRCode] = useState(false);
  const [selectedUrl, setSelectedUrl] = useState("");
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;
  
  const totalPages = Math.ceil(urlData.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentUrls = urlData.slice(indexOfFirstItem, indexOfLastItem);

  const handleDeleteConfirm = async () => {
    if (urlToDelete && onDelete) { // Added check for onDelete
      setIsDeleteLoading(true);
      await onDelete(urlToDelete);
      setIsDeleteLoading(false);
      setIsConfirmOpen(false);
      setUrlToDelete(null);
    }
  };

  // 生成短链接函数
  const generateShortUrl = (shortCode: string): string => {
    const protocol = window.location.protocol;
    const domain = window.location.hostname;
    return `${protocol}//${domain}/${shortCode}`;
  };

  // Check if url was expired
  const isExpired = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{language === 'en' ? 'URL Stats' : 'URL统计'}</CardTitle>
          <CardDescription>
            {language === 'en' ? 'Track and manage your shortened URLs.' : '跟踪和管理您的短链接。'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'URL Stats' : 'URL统计'}</CardTitle>
        <CardDescription>
          {language === 'en' ? 'Track and manage your shortened URLs.' : '跟踪和管理您的短链接。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {urlData.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              {language === 'en' ? 'You haven\'t created any short URLs yet.' : '您还没有创建任何短链接。'}
            </p>
          </div>
        ) : (
          <>
            <div className="rounded-md border overflow-x-auto">
              <table className="min-w-full divide-y divide-border">
                <thead>
                  <tr className="bg-muted/50">
                    <th className="px-4 py-3 text-left text-sm font-medium">{language === 'en' ? 'Original URL' : '原始网址'}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium">{language === 'en' ? 'Short URL' : '短网址'}</th>
                    <th className="px-4 py-3 text-center text-sm font-medium">{language === 'en' ? 'Clicks' : '点击数'}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium">{language === 'en' ? 'Created At' : '创建时间'}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium">{language === 'en' ? 'Expiration' : '过期时间'}</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">{language === 'en' ? 'Actions' : '操作'}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border">
                  {currentUrls.map((url) => {
                    const shortURL = generateShortUrl(url.short_code);
                    const expired = isExpired(url.expires_at);
                    
                    return (
                      <tr key={url.id} className={expired ? "bg-red-50" : ""}>
                        <td className="px-4 py-3 text-sm truncate max-w-[200px]" title={url.original_url}>
                          {url.original_url}
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <div className="flex items-center space-x-2">
                            <span className="truncate max-w-[150px]">{shortURL}</span>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onCopy(shortURL)}>
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{language === 'en' ? 'Copy URL' : '复制网址'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="h-8 w-8" 
                                    onClick={() => window.open(shortURL, '_blank')}
                                  >
                                    <ExternalLink className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{language === 'en' ? 'Open URL' : '打开网址'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="h-8 w-8"
                                    onClick={() => {
                                      setShowQRCode(true)
                                      setSelectedUrl(shortURL)
                                    }}
                                  >
                                    <QrCode className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{language === 'en' ? 'QR Code' : '二维码'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-center">{url.clicks || 0}</td>
                        <td className="px-4 py-3 text-sm">
                          {formatDistanceToNow(new Date(url.created_at), { addSuffix: true })}
                        </td>
                        <td className="px-4 py-3 text-sm">
                          {url.expires_at 
                            ? formatDistanceToNow(new Date(url.expires_at), { addSuffix: true })
                            : language === 'en' ? 'Never' : '永不过期'}
                        </td>
                        <td className="px-4 py-3 text-sm text-right">
                          <div className="flex justify-end">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  className="h-8 w-8 p-0 flex items-center justify-center"
                                >
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onCopy(shortURL)}>
                                  {language === 'en' ? 'Copy' : '复制'}
                                </DropdownMenuItem>
                                {onDelete && (
                                  <DropdownMenuItem onClick={() => {
                                    setUrlToDelete(url.id);
                                    setIsConfirmOpen(true);
                                  }}>
                                    {language === 'en' ? 'Delete' : '删除'}
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
            
            {totalPages > 1 && (
              <div className="mt-4 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious 
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                    
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNumber: number;
                      
                      if (totalPages <= 5) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNumber = totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }
                      
                      return (
                        <PaginationItem key={pageNumber}>
                          <PaginationLink 
                            onClick={() => setCurrentPage(pageNumber)}
                            isActive={currentPage === pageNumber}
                          >
                            {pageNumber}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}
                    
                    <PaginationItem>
                      <PaginationNext 
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
            
            <div className="mt-2 text-center text-sm text-muted-foreground">
              {language === 'en' 
                ? `Showing ${indexOfFirstItem + 1}-${Math.min(indexOfLastItem, urlData.length)} of ${urlData.length} URLs` 
                : `显示 ${indexOfFirstItem + 1}-${Math.min(indexOfLastItem, urlData.length)} 共 ${urlData.length} 个短链接`}
            </div>
          </>
        )}
      </CardContent>
      
      <QRCodeDialog 
        open={showQRCode}
        onOpenChange={setShowQRCode}
        url={selectedUrl}
        title={language === 'en' ? 'QR Code for your URL' : 'URL的二维码'}
        description={selectedUrl}
        actionText={language === 'en' ? 'Copy URL' : '复制URL'}
        onAction={() => onCopy(selectedUrl)}
      />
      
      <WarningDialog
        open={isConfirmOpen}
        onOpenChange={setIsConfirmOpen}
        title={language === 'en' ? 'Delete URL' : '删除URL'}
        description={
          language === 'en'
            ? 'Are you sure you want to delete this URL? This action cannot be undone.'
            : '确定要删除此URL吗？此操作无法撤消。'
        }
        actionText={language === 'en' ? 'Delete' : '删除'}
        cancelText={language === 'en' ? 'Cancel' : '取消'}
        isLoading={isDeleteLoading}
        onAction={handleDeleteConfirm}
      />
    </Card>
  );
};

export default UrlList;
