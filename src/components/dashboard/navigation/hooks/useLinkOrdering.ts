
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { NavLink } from '../types';

export const useLinkOrdering = (links: NavLink[], categoryId: string | null) => {
  const [isReordering, setIsReordering] = useState(false);
  const { toast } = useToast();
  const { language } = useAppContext();

  const moveLink = async (linkId: string, direction: 'up' | 'down') => {
    if (!categoryId) return;
    
    const linkIndex = links.findIndex(link => link.id === linkId);
    if (linkIndex === -1) return;
    
    const link = links[linkIndex];
    
    // Calculate adjacent link index based on direction
    const adjacentIndex = direction === 'up' 
      ? Math.max(0, linkIndex - 1) 
      : Math.min(links.length - 1, linkIndex + 1);
    
    // If there's no change in index, exit
    if (adjacentIndex === linkIndex) return;
    
    const adjacentLink = links[adjacentIndex];
    
    // Swap sort orders
    try {
      await supabase
        .from('nav_links')
        .update({ sort_order: adjacentLink.sort_order })
        .eq('id', link.id);
        
      await supabase
        .from('nav_links')
        .update({ sort_order: link.sort_order })
        .eq('id', adjacentLink.id);
        
      toast({
        description: language === 'en' ? "Link order updated." : "链接顺序已更新。",
      });
    } catch (error) {
      console.error('Error updating link order:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to update link order." : "更新链接顺序失败。",
      });
    }
  };
  
  return { isReordering, setIsReordering, moveLink };
};
