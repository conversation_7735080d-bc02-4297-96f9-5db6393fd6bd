
import React from 'react';
import { useAppContext } from '@/context/AppContext';
import { ExternalLink, Link, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface SubmittedLink {
  id: string;
  name: string;
  url: string;
  submission_status: string | null;
  created_at: string;
  nav_categories: {
    name: string;
  };
}

interface SubmittedLinksListProps {
  links: SubmittedLink[];
  isLoading: boolean;
}

const SubmittedLinksList: React.FC<SubmittedLinksListProps> = ({ links, isLoading }) => {
  const { language } = useAppContext();
  
  const getStatusBadge = (status: string | null) => {
    switch(status) {
      case 'pending':
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
            {language === 'en' ? 'Pending' : '待审核'}
          </Badge>
        );
      case 'approved':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            {language === 'en' ? 'Approved' : '已批准'}
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            {language === 'en' ? 'Rejected' : '已拒绝'}
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {language === 'en' ? 'Unknown' : '未知'}
          </Badge>
        );
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  if (links.length === 0) {
    return (
      <div className="text-center py-8 border rounded-lg bg-muted/20">
        <Link className="h-12 w-12 mx-auto text-muted-foreground" />
        <p className="mt-2 text-muted-foreground">
          {language === 'en' 
            ? "You haven't submitted any links yet" 
            : "您尚未提交任何链接"}
        </p>
        <p className="text-sm text-muted-foreground mt-1">
          {language === 'en' 
            ? "Your submitted links will appear here" 
            : "您提交的链接将显示在这里"}
        </p>
      </div>
    );
  }
  
  return (
    <div className="space-y-3 max-h-[500px] overflow-y-auto pr-1">
      {links.map((link) => (
        <div 
          key={link.id} 
          className="border rounded-lg p-3 hover:bg-muted/40 transition-colors"
        >
          <div className="flex items-start justify-between mb-2">
            <div className="font-medium line-clamp-1">{link.name}</div>
            {getStatusBadge(link.submission_status)}
          </div>
          
          <div className="text-sm text-muted-foreground mb-1 line-clamp-1">
            <a 
              href={link.url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover:text-primary flex items-center gap-1"
            >
              {link.url}
              <ExternalLink className="h-3 w-3" />
            </a>
          </div>
          
          <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
            <span>
              {language === 'en' ? 'Category: ' : '分类：'}
              {link.nav_categories?.name || '—'}
            </span>
            <span>
              {formatDate(link.created_at)}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SubmittedLinksList;
