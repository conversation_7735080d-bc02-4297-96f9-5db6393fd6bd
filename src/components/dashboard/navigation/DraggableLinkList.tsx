
import React, { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Category, NavLink } from '@/components/dashboard/navigation/types';
import { supabase } from '@/integrations/supabase/client';
import SubmitToPublicDialog from './SubmitToPublicDialog';
import LinkItem from './components/LinkItem';
import EmptyLinksList from './components/EmptyLinksList';
import { PingResult } from './components/PingButton';
import { Droppable, Draggable } from 'react-beautiful-dnd';
import { GripVertical } from 'lucide-react';

interface DraggableLinkListProps {
  links: NavLink[];
  categoryId: string;
  onDeleteLink: (linkId: string) => void;
  onEditLink: (link: NavLink) => void;
  onSubmitLinkToPublic: (linkId: string, categoryId: string) => Promise<void>;
  pingResults?: Record<string, PingResult>;
  onPingLink?: (link: NavLink) => void;
  onPingComplete?: (linkId: string, result: PingResult) => void;
}

const DraggableLinkList: React.FC<DraggableLinkListProps> = ({ 
  links, 
  categoryId,
  onDeleteLink, 
  onEditLink,
  onSubmitLinkToPublic,
  pingResults = {},
  onPingLink,
  onPingComplete
}) => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [adminCategories, setAdminCategories] = useState<Category[]>([]);
  const [isSubmitDialogOpen, setIsSubmitDialogOpen] = useState(false);
  const [selectedLinkId, setSelectedLinkId] = useState<string | null>(null);
  
  React.useEffect(() => {
    const fetchAdminCategories = async () => {
      try {
        const { data, error } = await supabase
          .from('nav_categories')
          .select('*')
          .is('user_id', null)
          .eq('is_public', true)
          .order('sort_order', { ascending: true });
          
        if (error) {
          console.error('Error fetching admin categories:', error);
          return;
        }
        
        if (data) {
          setAdminCategories(data);
        }
      } catch (error) {
        console.error('Error:', error);
      }
    };
    
    fetchAdminCategories();
  }, []);
  
  const handleSubmitToPublic = (linkId: string) => {
    setSelectedLinkId(linkId);
    setIsSubmitDialogOpen(true);
  };
  
  const handleConfirmSubmit = async (categoryId: string) => {
    if (!selectedLinkId) return;
    
    try {
      await onSubmitLinkToPublic(selectedLinkId, categoryId);
      toast({
        description: language === 'en' 
          ? "Link submitted to public directory successfully" 
          : "链接已成功提交到公共目录",
      });
    } catch (error) {
      console.error('Error submitting link to public:', error);
      toast({
        variant: "destructive",
        description: language === 'en' 
          ? "Failed to submit link to public directory" 
          : "提交链接到公共目录失败",
      });
    } finally {
      setIsSubmitDialogOpen(false);
      setSelectedLinkId(null);
    }
  };
  
  if (links.length === 0) {
    return <EmptyLinksList />;
  }
  
  // Filter links for current category
  const categoryLinks = links
    .filter(link => link.category_id === categoryId)
    .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
  
  return (
    <div className="space-y-2">
      <Droppable droppableId={categoryId} type="link">
        {(provided) => (
          <div 
            ref={provided.innerRef}
            {...provided.droppableProps}
            className="space-y-2"
          >
            {categoryLinks.map((link, index) => (
              <Draggable key={link.id} draggableId={link.id} index={index}>
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    className="relative group bg-card border rounded-md p-3 hover:shadow-sm transition-all"
                  >
                    <div 
                      {...provided.dragHandleProps}
                      className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab"
                    >
                      <GripVertical className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div className="ml-6">
                      <LinkItem
                        link={link}
                        pingResults={pingResults}
                        onDeleteLink={onDeleteLink}
                        onEditLink={onEditLink}
                        onSubmitToPublic={handleSubmitToPublic}
                        onPingLink={onPingLink}
                        onPingComplete={onPingComplete}
                        isReordering={false}
                      />
                    </div>
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
      
      <SubmitToPublicDialog 
        open={isSubmitDialogOpen}
        onOpenChange={setIsSubmitDialogOpen}
        categories={adminCategories}
        onConfirm={handleConfirmSubmit}
      />
    </div>
  );
};

export default DraggableLinkList;
