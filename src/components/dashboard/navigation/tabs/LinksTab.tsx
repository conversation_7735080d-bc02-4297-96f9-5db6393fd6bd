import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/dashboard/navigation/types';
import { getBackendConfig } from '@/config/backend';
import DraggableLinkList from '../DraggableLinkList';
import NoCategory from '../components/NoCategory';
import LinkForm from '../LinkForm';

interface LinksTabProps {
  categories: Category[];
  selectedCategory: Category | null;
  onCreateLink: (name: string, url: string, icon: string, isInternal: boolean, categoryId: string) => void;
}

const LinksTab: React.FC<LinksTabProps> = ({
  categories,
  selectedCategory,
  onCreateLink
}) => {
  const { language } = useAppContext();
  const [links, setLinks] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // 确保在分类变化时加载对应分类的链接
  useEffect(() => {
    const fetchCategoryLinks = async () => {
      if (!selectedCategory) {
        setLinks([]);
        return;
      }

      setIsLoading(true);

      try {
        const config = getBackendConfig();
        const baseURL = config.goBackend?.baseUrl;
        const token = localStorage.getItem('authToken');

        if (!token) {
          console.error('No auth token found');
          return;
        }

        const response = await fetch(`${baseURL}/navigation/links?category_id=${selectedCategory.id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setLinks(data || []);
      } catch (err) {
        console.error('Error in fetchCategoryLinks:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategoryLinks();
  }, [selectedCategory]);

  const handleEditLink = async (updatedLink) => {
    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        console.error('No auth token found');
        return;
      }

      const response = await fetch(`${baseURL}/navigation/links/${updatedLink.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: updatedLink.name,
          url: updatedLink.url,
          icon: updatedLink.icon,
          is_internal: updatedLink.is_internal
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 更新本地状态
      setLinks(links.map(link =>
        link.id === updatedLink.id ? updatedLink : link
      ));
    } catch (error) {
      console.error('Error updating link:', error);
    }
  };

  const handleDeleteLink = async (linkId) => {
    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        console.error('No auth token found');
        return;
      }

      const response = await fetch(`${baseURL}/navigation/links/${linkId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 更新本地状态
      setLinks(links.filter(link => link.id !== linkId));
    } catch (error) {
      console.error('Error deleting link:', error);
    }
  };

  const handleSubmitLinkToPublic = async (linkId, publicCategoryId) => {
    try {
      const linkToSubmit = links.find(link => link.id === linkId);
      if (!linkToSubmit) return;

      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        console.error('No auth token found');
        return;
      }

      const response = await fetch(`${baseURL}/navigation/links`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: linkToSubmit.name,
          url: linkToSubmit.url,
          icon: linkToSubmit.icon,
          is_internal: linkToSubmit.is_internal,
          category_id: publicCategoryId,
          status: 'pending',
          is_public: true
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error submitting link to public:', error);
    }
  };

  if (!selectedCategory) {
    return <NoCategory />;
  }

  return (
    <div className="grid gap-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'Add Link' : '添加链接'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <LinkForm
              categories={categories}
              selectedCategoryId={selectedCategory.id}
              onCreateLink={onCreateLink}
            />
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'Links' : '链接列表'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center p-6">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <DraggableLinkList
                links={links}
                categoryId={selectedCategory.id}
                onDeleteLink={handleDeleteLink}
                onEditLink={handleEditLink}
                onSubmitLinkToPublic={handleSubmitLinkToPublic}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LinksTab;
