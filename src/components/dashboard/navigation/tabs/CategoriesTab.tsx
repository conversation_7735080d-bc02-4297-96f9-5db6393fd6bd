
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import DraggableCategoryList from '../DraggableCategoryList';
import { Category } from '@/components/dashboard/navigation/types';

interface CategoriesTabProps {
  categories: Category[];
  selectedCategory: Category | null;
  onCreateCategory: (name: string, isPublic: boolean, parentId: string | null) => void;
  onDeleteCategory: (category: Category) => void;
  onSelectCategory: (category: Category) => void;
}

const CategoriesTab: React.FC<CategoriesTabProps> = ({
  categories,
  selectedCategory,
  onCreateCategory,
  onDeleteCategory,
  onSelectCategory
}) => {
  const { language } = useAppContext();
  const [newCategoryName, setNewCategoryName] = React.useState('');
  const [isPublic, setIsPublic] = React.useState(false);
  const [parentId, setParentId] = React.useState<string | null>(null);
  
  // Get top-level categories for parent selection
  const topLevelCategories = categories.filter(cat => !cat.parent_id);
  
  const handleCreateCategory = () => {
    if (!newCategoryName.trim()) return;
    
    onCreateCategory(newCategoryName, isPublic, parentId);
    
    // Reset form
    setNewCategoryName('');
    setIsPublic(false);
    setParentId(null);
  };
  
  return (
    <div className="grid gap-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'Add Category' : '添加分类'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="categoryParent">
                {language === 'en' ? 'Parent Category (Optional)' : '父分类（可选）'}
              </Label>
              <select
                id="categoryParent"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                value={parentId || ''}
                onChange={(e) => setParentId(e.target.value || null)}
              >
                <option value="">
                  {language === 'en' ? '-- No Parent (Top Level) --' : '-- 无父分类（顶级）--'}
                </option>
                {topLevelCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-muted-foreground">
                {language === 'en' 
                  ? "Leave empty to create a top-level category" 
                  : "留空以创建顶级分类"}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="categoryName">
                {language === 'en' ? 'Category Name' : '分类名称'}
              </Label>
              <Input
                id="categoryName"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                placeholder={language === 'en' ? 'Enter category name' : '输入分类名称'}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="categoryPublic"
                checked={isPublic}
                onCheckedChange={setIsPublic}
              />
              <Label htmlFor="categoryPublic">
                {language === 'en' ? 'Public' : '公开'}
              </Label>
            </div>
            
            <Button 
              className="w-full" 
              onClick={handleCreateCategory}
            >
              <Plus className="mr-2 h-4 w-4" />
              {language === 'en' ? 'Create Category' : '创建分类'}
            </Button>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'Categories' : '分类列表'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <DraggableCategoryList
              categories={categories}
              selectedCategoryId={selectedCategory?.id || null}
              onSelectCategory={onSelectCategory}
              onDeleteCategory={onDeleteCategory}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CategoriesTab;
