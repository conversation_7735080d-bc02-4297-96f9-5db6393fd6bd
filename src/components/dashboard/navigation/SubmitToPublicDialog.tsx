
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useAppContext } from '@/context/AppContext';
import { Category } from './types';

interface SubmitToPublicDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  categories: Category[];
  onConfirm: (categoryId: string) => Promise<void>;
}

const SubmitToPublicDialog: React.FC<SubmitToPublicDialogProps> = ({
  open,
  onOpenChange,
  categories,
  onConfirm
}) => {
  const { language } = useAppContext();
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Reset state when dialog opens
  React.useEffect(() => {
    if (open && categories.length > 0) {
      setSelectedCategoryId(categories[0].id);
    }
  }, [open, categories]);
  
  const handleSubmit = async () => {
    if (!selectedCategoryId) return;
    
    setIsSubmitting(true);
    try {
      await onConfirm(selectedCategoryId);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {language === 'en' ? 'Submit to Public Directory' : '提交到公共目录'}
          </DialogTitle>
          <DialogDescription>
            {language === 'en' 
              ? 'Submit this link to the public navigation directory. An administrator will review your submission.' 
              : '将此链接提交到公共导航目录。管理员将审核您的提交。'}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="public-category">
              {language === 'en' ? 'Select Public Category' : '选择公共分类'}
            </Label>
            <select
              id="public-category"
              className="w-full p-2 border rounded-md"
              value={selectedCategoryId}
              onChange={(e) => setSelectedCategoryId(e.target.value)}
              required
            >
              {categories.length === 0 ? (
                <option value="" disabled>
                  {language === 'en' ? 'No categories available' : '没有可用的分类'}
                </option>
              ) : (
                categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))
              )}
            </select>
          </div>
        </div>
        
        <DialogFooter className="sm:justify-end">
          <Button
            variant="secondary"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            {language === 'en' ? 'Cancel' : '取消'}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || !selectedCategoryId || categories.length === 0}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                {language === 'en' ? 'Submitting...' : '提交中...'}
              </div>
            ) : (
              language === 'en' ? 'Submit' : '提交'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SubmitToPublicDialog;
