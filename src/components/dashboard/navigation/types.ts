export interface Category {
  id: string;
  name: string;
  is_public: boolean;
  parent_id: string | null;
  sort_order: number;
  user_id?: string;
  is_admin_default?: boolean;
  created_at?: string;
  description?: string | null;
}

export interface NavLink {
  id: string;
  name: string;
  url: string;
  icon: string | null;
  is_internal: boolean;
  category_id: string;
  sort_order: number;
  description?: string | null;
  submission_status?: string;
  submitted_by?: string;
  created_at?: string;
  popularity?: number;
  clicks?: number;
}

export interface PingResult {
  status: 'pending' | 'success' | 'failed' | 'error';
  time?: number;
  message?: string;
}
