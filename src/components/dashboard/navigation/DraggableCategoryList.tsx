
import React, { useState } from 'react';
import { 
  Table, 
  TableBody,
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { 
  Trash2, 
  ChevronRight, 
  ChevronDown, 
  GripVertical 
} from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { Category } from '@/components/dashboard/navigation/types';
import { Droppable, Draggable } from 'react-beautiful-dnd';

interface DraggableCategoryListProps {
  categories: Category[];
  selectedCategoryId: string | null;
  onSelectCategory: (category: Category) => void;
  onDeleteCategory: (category: Category) => void;
}

const DraggableCategoryList: React.FC<DraggableCategoryListProps> = ({
  categories,
  selectedCategoryId,
  onSelectCategory,
  onDeleteCategory
}) => {
  const { language } = useAppContext();
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  
  // Toggle category expansion
  const toggleExpand = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };
  
  // Group categories by parent
  const topLevelCategories = categories.filter(cat => !cat.parent_id);
  const subcategoriesByParent: Record<string, Category[]> = {};
  
  categories.forEach(category => {
    if (category.parent_id) {
      if (!subcategoriesByParent[category.parent_id]) {
        subcategoriesByParent[category.parent_id] = [];
      }
      subcategoriesByParent[category.parent_id].push(category);
    }
  });
  
  if (categories.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-muted-foreground">
          {language === 'en' 
            ? 'No categories found. Create your first category!' 
            : '未找到分类。创建您的第一个分类！'}
        </p>
      </div>
    );
  }
  
  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40px]"></TableHead>
            <TableHead className="w-[40px]"></TableHead>
            <TableHead>
              {language === 'en' ? 'Name' : '名称'}
            </TableHead>
            <TableHead>
              {language === 'en' ? 'Public' : '公开'}
            </TableHead>
            <TableHead>
              {language === 'en' ? 'Type' : '类型'}
            </TableHead>
            <TableHead className="w-[100px] text-right">
              {language === 'en' ? 'Actions' : '操作'}
            </TableHead>
          </TableRow>
        </TableHeader>
        <Droppable droppableId="main-categories" type="category">
          {(provided) => (
            <TableBody
              {...provided.droppableProps}
              ref={provided.innerRef}
            >
              {topLevelCategories
                .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
                .map((category, index) => (
                <React.Fragment key={category.id}>
                  <Draggable draggableId={category.id} index={index}>
                    {(provided) => (
                      <TableRow 
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={selectedCategoryId === category.id ? "bg-muted/50" : ""}
                      >
                        <TableCell {...provided.dragHandleProps} className="cursor-grab">
                          <GripVertical className="h-4 w-4 text-muted-foreground" />
                        </TableCell>
                        <TableCell>
                          {subcategoriesByParent[category.id]?.length > 0 ? (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 p-0"
                              onClick={() => toggleExpand(category.id)}
                            >
                              {expandedCategories[category.id] ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </Button>
                          ) : (
                            <span className="w-8 block"></span>
                          )}
                        </TableCell>
                        <TableCell 
                          className="font-medium cursor-pointer"
                          onClick={() => onSelectCategory(category)}
                        >
                          {category.name}
                        </TableCell>
                        <TableCell>
                          {category.is_public ? (
                            <span className="text-green-500">
                              {language === 'en' ? 'Yes' : '是'}
                            </span>
                          ) : (
                            <span className="text-muted-foreground">
                              {language === 'en' ? 'No' : '否'}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          <span className="text-blue-500">
                            {language === 'en' ? 'Main' : '主要'}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              onDeleteCategory(category);
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    )}
                  </Draggable>
                  
                  {/* Render subcategories if this category is expanded */}
                  {expandedCategories[category.id] && (
                    <Droppable droppableId={category.id} type="category">
                      {(provided) => (
                        <TableRow>
                          <TableCell colSpan={6} className="p-0">
                            <div
                              ref={provided.innerRef}
                              {...provided.droppableProps}
                              className="pl-4 border-l-4 border-l-muted"
                            >
                              <Table>
                                <TableBody>
                                  {subcategoriesByParent[category.id]
                                    ?.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
                                    .map((subcat, index) => (
                                    <Draggable 
                                      key={subcat.id} 
                                      draggableId={subcat.id} 
                                      index={index}
                                    >
                                      {(provided) => (
                                        <TableRow
                                          ref={provided.innerRef}
                                          {...provided.draggableProps}
                                          className={selectedCategoryId === subcat.id ? "bg-muted/50" : ""}
                                        >
                                          <TableCell {...provided.dragHandleProps} className="cursor-grab">
                                            <GripVertical className="h-4 w-4 text-muted-foreground" />
                                          </TableCell>
                                          <TableCell></TableCell>
                                          <TableCell 
                                            className="font-medium cursor-pointer pl-6"
                                            onClick={() => onSelectCategory(subcat)}
                                          >
                                            {subcat.name}
                                          </TableCell>
                                          <TableCell>
                                            {subcat.is_public ? (
                                              <span className="text-green-500">
                                                {language === 'en' ? 'Yes' : '是'}
                                              </span>
                                            ) : (
                                              <span className="text-muted-foreground">
                                                {language === 'en' ? 'No' : '否'}
                                              </span>
                                            )}
                                          </TableCell>
                                          <TableCell>
                                            <span className="text-purple-500">
                                              {language === 'en' ? 'Sub' : '子类'}
                                            </span>
                                          </TableCell>
                                          <TableCell className="text-right">
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                onDeleteCategory(subcat);
                                              }}
                                            >
                                              <Trash2 className="h-4 w-4 text-destructive" />
                                              <span className="sr-only">Delete</span>
                                            </Button>
                                          </TableCell>
                                        </TableRow>
                                      )}
                                    </Draggable>
                                  ))}
                                  {provided.placeholder}
                                </TableBody>
                              </Table>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </Droppable>
                  )}
                </React.Fragment>
              ))}
              {provided.placeholder}
            </TableBody>
          )}
        </Droppable>
      </Table>
    </div>
  );
};

export default DraggableCategoryList;
