
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RotateCw } from 'lucide-react';

export interface PingResult {
  status: 'pending' | 'success' | 'failed' | 'error';
  time?: number;
  message?: string;
}

interface PingButtonProps {
  url: string;
  pingResult?: PingResult;
  onPing: () => void;
  onPingComplete?: (result: PingResult) => void;
}

const PingButton: React.FC<PingButtonProps> = ({
  url,
  pingResult,
  onPing,
  onPingComplete
}) => {
  const [isLoading, setIsLoading] = useState(false);
  
  const handlePing = async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    onPing();
    
    try {
      const start = performance.now();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
      
      const response = await fetch(url, { 
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      const end = performance.now();
      const time = Math.round(end - start);
      
      const result: PingResult = {
        status: 'success',
        time: time
      };
      
      if (onPingComplete) {
        onPingComplete(result);
      }
    } catch (error) {
      console.error('Error pinging URL:', error);
      
      const result: PingResult = {
        status: 'error',
        message: 'Connection failed'
      };
      
      if (onPingComplete) {
        onPingComplete(result);
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Button
      size="sm"
      variant="ghost"
      className="h-8 w-8 p-0"
      disabled={isLoading}
      onClick={handlePing}
    >
      <RotateCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
    </Button>
  );
};

export default PingButton;
