
import React from 'react';
import { useAppContext } from '@/context/AppContext';

const EmptyLinksList: React.FC = () => {
  const { language } = useAppContext();
  
  return (
    <div className="text-center p-6 border rounded-lg bg-muted/30">
      <p className="text-muted-foreground">
        {language === 'en' 
          ? 'No links available in this category. Add your first link!' 
          : '此分类中没有可用的链接。添加您的第一个链接！'}
      </p>
    </div>
  );
};

export default EmptyLinksList;
