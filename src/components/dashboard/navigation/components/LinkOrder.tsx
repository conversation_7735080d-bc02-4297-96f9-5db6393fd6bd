
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowUp, ArrowDown } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface LinkOrderProps {
  linkId: string;
  onMoveLink: (linkId: string, direction: 'up' | 'down') => void;
}

const LinkOrder: React.FC<LinkOrderProps> = ({ linkId, onMoveLink }) => {
  const { language } = useAppContext();
  
  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onMoveLink(linkId, 'up')}
        title={language === 'en' ? 'Move up' : '上移'}
      >
        <ArrowUp className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => onMoveLink(linkId, 'down')}
        title={language === 'en' ? 'Move down' : '下移'}
      >
        <ArrowDown className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default LinkOrder;
