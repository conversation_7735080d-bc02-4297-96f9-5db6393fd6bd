
import React from 'react';
import { NavLink, Category } from '../types';
import { Button } from '@/components/ui/button';
import { Trash2, Edit, ExternalLink, Globe } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { useAppContext } from '@/context/AppContext';
import PingButton, { PingResult } from './PingButton';
import LinkOrder from './LinkOrder';

interface LinkItemProps {
  link: NavLink;
  pingResults?: Record<string, PingResult>;
  onDeleteLink: (linkId: string) => void;
  onEditLink: (link: NavLink) => void;
  onSubmitToPublic: (linkId: string) => void;
  onPingLink?: (link: NavLink) => void;
  onPingComplete?: (linkId: string, result: PingResult) => void;
  isReordering?: boolean;
  onMoveLink?: (linkId: string, direction: 'up' | 'down') => void;
}

const LinkItem: React.FC<LinkItemProps> = ({
  link,
  pingResults = {},
  onDeleteLink,
  onEditLink,
  onSubmitToPublic,
  onPingLink,
  onPingComplete,
  isReordering = false,
  onMoveLink
}) => {
  const { language } = useAppContext();

  // Handler to adapt the onPingComplete signature
  const handlePingComplete = (result: PingResult) => {
    if (onPingComplete) {
      onPingComplete(link.id, result);
    }
  };

  return (
    <div 
      className="p-3 border rounded-lg flex flex-col gap-2 hover:bg-accent/10 transition-colors"
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          {link.icon ? (
            <img 
              src={link.icon} 
              alt="" 
              className="w-8 h-8 object-contain"
              onError={(e) => {
                e.currentTarget.src = '/placeholder.svg';
              }}
            />
          ) : (
            <div className="w-8 h-8 bg-muted rounded-md flex items-center justify-center">
              <ExternalLink className="h-4 w-4 text-muted-foreground" />
            </div>
          )}
          <div>
            <h3 className="font-medium">{link.name}</h3>
            <a 
              href={link.url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-sm text-muted-foreground hover:underline truncate max-w-xs inline-block"
            >
              {link.url}
            </a>
          </div>
        </div>
        
        <div className="flex gap-2">
          {isReordering ? (
            <LinkOrder 
              linkId={link.id} 
              onMoveLink={onMoveLink || (() => {})} 
            />
          ) : (
            <>
              {onPingLink && onPingComplete && (
                <PingButton 
                  url={link.url}
                  pingResult={pingResults[link.id]}
                  onPing={() => onPingLink(link)}
                  onPingComplete={handlePingComplete}
                />
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onSubmitToPublic(link.id)}
                title={language === 'en' ? 'Submit to public directory' : '提交到公共目录'}
              >
                <Globe className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEditLink(link)}
                title={language === 'en' ? 'Edit link' : '编辑链接'}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDeleteLink(link.id)}
                title={language === 'en' ? 'Delete link' : '删除链接'}
                className="text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </div>
      
      {!isReordering && pingResults[link.id] && !onPingComplete && (
        <div className="w-full">
          {/* This is for backward compatibility with the old implementation */}
          {/* The new implementation uses the PingButton component which has its own UI */}
          {pingResults[link.id].status === 'pending' && (
            <Progress value={50} className="w-full h-2" />
          )}
          {pingResults[link.id].status === 'success' && (
            <div className="text-xs text-green-600">
              {language === 'en' 
                ? `Response time: ${pingResults[link.id].time}ms` 
                : `响应时间: ${pingResults[link.id].time}毫秒`}
            </div>
          )}
          {pingResults[link.id].status === 'failed' && (
            <div className="text-xs text-red-500">
              {language === 'en' ? 'Connection failed' : '连接失败'}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LinkItem;
