
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { Category } from './types';

interface CategoryFormProps {
  categories: Category[];
  onCreateCategory: (name: string, isPublic: boolean, parentId: string | null) => Promise<void>;
}

const CategoryForm: React.FC<CategoryFormProps> = ({
  categories,
  onCreateCategory
}) => {
  const { language } = useAppContext();
  const [name, setName] = useState('');
  const [isPublic, setIsPublic] = useState(true);
  const [parentId, setParentId] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  
  // Only top-level categories can be parent categories
  const parentCategories = categories.filter(cat => !cat.parent_id);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      return;
    }
    
    setSubmitting(true);
    
    try {
      await onCreateCategory(name.trim(), isPublic, parentId);
      
      // Reset form
      setName('');
      setIsPublic(true);
      setParentId(null);
    } catch (error) {
      console.error('Error creating category:', error);
    } finally {
      setSubmitting(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {language === 'en' ? 'Add Category' : '添加分类'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="category-parent">
              {language === 'en' ? 'Parent Category (Optional)' : '父分类（可选）'}
            </Label>
            <select
              id="category-parent"
              className="w-full p-2 border rounded-md"
              value={parentId || ''}
              onChange={(e) => setParentId(e.target.value || null)}
            >
              <option value="">
                {language === 'en' ? '-- No Parent (Top Level) --' : '-- 无父分类（顶级）--'}
              </option>
              {parentCategories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            <p className="text-xs text-muted-foreground">
              {language === 'en' 
                ? "Leave empty to create a top-level category" 
                : "留空以创建顶级分类"}
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category-name">
              {language === 'en' ? 'Category Name' : '分类名称'}
            </Label>
            <Input
              id="category-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder={language === 'en' ? 'Enter category name' : '输入分类名称'}
              required
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="category-public"
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
            <Label htmlFor="category-public">
              {language === 'en' ? 'Public' : '公开'}
            </Label>
          </div>
          
          <Button 
            type="submit" 
            className="w-full"
            disabled={submitting || !name.trim()}
          >
            {submitting ? (
              <div className="flex items-center">
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                {language === 'en' ? 'Creating...' : '创建中...'}
              </div>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                {language === 'en' ? 'Create Category' : '创建分类'}
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default CategoryForm;
