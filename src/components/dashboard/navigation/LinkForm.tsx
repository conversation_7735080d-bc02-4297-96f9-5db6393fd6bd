
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus, Link as LinkIcon } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { Category } from './types';
import IconUploader from '@/components/navigation/IconUploader';
import { supabase } from '@/integrations/supabase/client';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from '@/components/ui/separator';

interface LinkFormProps {
  categories: Category[];
  selectedCategoryId: string;
  onCreateLink: (name: string, url: string, icon: string, isInternal: boolean, categoryId: string) => void;
}

const LinkForm: React.FC<LinkFormProps> = ({
  categories,
  selectedCategoryId,
  onCreateLink
}) => {
  const { language } = useAppContext();
  const [name, setName] = useState('');
  const [url, setUrl] = useState('');
  const [icon, setIcon] = useState('');
  const [isInternal, setIsInternal] = useState(false);
  const [parentCategoryId, setParentCategoryId] = useState('');
  const [subcategoryId, setSubcategoryId] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [loadingSubcategories, setLoadingSubcategories] = useState(false);
  const [autoFetchingIcon, setAutoFetchingIcon] = useState(false);
  
  // Get parent categories (without parent_id)
  const parentCategories = categories.filter(cat => !cat.parent_id);
  
  // Initial load
  useEffect(() => {
    // If selectedCategoryId is a child category, find its parent
    const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);
    if (selectedCategory) {
      if (selectedCategory.parent_id) {
        setParentCategoryId(selectedCategory.parent_id);
        setSubcategoryId(selectedCategory.id);
      } else {
        setParentCategoryId(selectedCategory.id);
      }
    }
  }, [selectedCategoryId, categories]);
  
  // When parent category changes, fetch subcategories
  useEffect(() => {
    if (!parentCategoryId) {
      setSubcategories([]);
      return;
    }
    
    const fetchSubcategories = async () => {
      setLoadingSubcategories(true);
      
      try {
        const { data, error } = await supabase
          .from('nav_categories')
          .select('*')
          .eq('parent_id', parentCategoryId)
          .order('sort_order', { ascending: true });
          
        if (error) {
          console.error('Error fetching subcategories:', error);
          return;
        }
        
        if (data) {
          setSubcategories(data as Category[]);
        }
      } catch (err) {
        console.error('Error fetching subcategories:', err);
      } finally {
        setLoadingSubcategories(false);
      }
    };
    
    fetchSubcategories();
  }, [parentCategoryId]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Determine which category ID to use based on selection
    const finalCategoryId = subcategoryId || parentCategoryId;
    
    if (!name.trim() || !url.trim() || !finalCategoryId) {
      return;
    }
    
    setSubmitting(true);
    
    // 如果没有设置图标，则尝试获取favicon
    if (!icon && url) {
      const favicon = getFavicon(url);
      if (favicon) {
        setIcon(favicon);
      }
    }
    
    try {
      await onCreateLink(name.trim(), url.trim(), icon, isInternal, finalCategoryId);
      
      // Reset form
      setName('');
      setUrl('');
      setIcon('');
      setIsInternal(false);
    } catch (error) {
      console.error('Error creating link:', error);
    } finally {
      setSubmitting(false);
    }
  };
  
  const handleIconSelected = (iconUrl: string) => {
    setIcon(iconUrl);
  };
  
  // 获取网站的favicon
  const getFavicon = (url: string): string => {
    try {
      const urlObj = new URL(url);
      return `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=64`;
    } catch (e) {
      return '';
    }
  };
  
  // 当URL改变时，如果没有设置图标，则自动获取favicon
  useEffect(() => {
    if (!icon && url) {
      setAutoFetchingIcon(true);
      try {
        const favicon = getFavicon(url);
        if (favicon) {
          setIcon(favicon);
        }
      } finally {
        setAutoFetchingIcon(false);
      }
    }
  }, [url]);
  
  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-xl">
          <LinkIcon className="h-5 w-5" />
          {language === 'en' ? 'Add New Link' : '添加新链接'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid sm:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="parent-category" className="text-sm font-medium">
                  {language === 'en' ? 'Main Category' : '主分类'}
                </Label>
                <Select
                  value={parentCategoryId}
                  onValueChange={(value) => {
                    setParentCategoryId(value);
                    setSubcategoryId('');
                  }}
                  required
                >
                  <SelectTrigger className="mt-1.5">
                    <SelectValue placeholder={language === 'en' ? 'Select a main category' : '选择主分类'} />
                  </SelectTrigger>
                  <SelectContent>
                    {parentCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {parentCategoryId && subcategories.length > 0 && (
                <div>
                  <Label htmlFor="subcategory" className="text-sm font-medium">
                    {language === 'en' ? 'Subcategory' : '子分类'}
                  </Label>
                  <Select
                    value={subcategoryId}
                    onValueChange={setSubcategoryId}
                    required
                  >
                    <SelectTrigger className="mt-1.5">
                      <SelectValue placeholder={language === 'en' ? 'Select subcategory' : '选择子分类'} />
                    </SelectTrigger>
                    <SelectContent>
                      {subcategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {loadingSubcategories && (
                    <div className="text-xs text-muted-foreground mt-1.5">
                      {language === 'en' ? 'Loading subcategories...' : '加载子分类中...'}
                    </div>
                  )}
                </div>
              )}
              
              <div>
                <Label htmlFor="link-name" className="text-sm font-medium">
                  {language === 'en' ? 'Link Name' : '链接名称'}
                </Label>
                <Input
                  id="link-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder={language === 'en' ? 'Enter link name' : '输入链接名称'}
                  className="mt-1.5"
                  required
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="link-url" className="text-sm font-medium">
                  {language === 'en' ? 'URL' : '链接地址'}
                </Label>
                <Input
                  id="link-url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder={language === 'en' ? 'https://example.com' : 'https://示例.com'}
                  className="mt-1.5"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="link-icon" className="text-sm font-medium">
                  {language === 'en' ? 'Icon URL' : '图标链接'}
                </Label>
                <div className="flex gap-2 items-center mt-1.5">
                  <Input
                    id="link-icon"
                    value={icon}
                    onChange={(e) => setIcon(e.target.value)}
                    placeholder={language === 'en' ? 'Auto-detected or select' : '自动检测或选择'}
                    className="flex-1"
                  />
                  <IconUploader 
                    onIconSelected={handleIconSelected} 
                    currentIconUrl={icon}
                  />
                </div>
                <div className="flex mt-2">
                  {icon ? (
                    <div className="p-2 border rounded-md w-12 h-12 flex items-center justify-center">
                      <img 
                        src={icon} 
                        alt="Icon preview" 
                        className="max-w-full max-h-full"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder.svg';
                        }}
                      />
                    </div>
                  ) : autoFetchingIcon ? (
                    <div className="text-xs text-muted-foreground flex items-center ml-1">
                      <div className="animate-spin h-3 w-3 border-2 border-primary border-t-transparent rounded-full mr-2"></div>
                      {language === 'en' ? 'Auto-detecting favicon...' : '自动检测网站图标...'}
                    </div>
                  ) : (
                    <div className="text-xs text-muted-foreground ml-1">
                      {language === 'en' ? 'Favicon will be auto-detected' : '将自动检测网站图标'}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-2 pt-1">
                <Switch
                  id="link-internal"
                  checked={isInternal}
                  onCheckedChange={setIsInternal}
                />
                <Label htmlFor="link-internal" className="text-sm font-medium">
                  {language === 'en' ? 'Internal Link' : '内部链接'}
                </Label>
              </div>
            </div>
          </div>
          
          <Separator className="my-4" />
          
          <div className="flex justify-end">
            <Button 
              type="submit" 
              className="px-8"
              disabled={submitting || (!subcategoryId && !parentCategoryId) || !name.trim() || !url.trim()}
            >
              {submitting ? (
                <div className="flex items-center">
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                  {language === 'en' ? 'Adding...' : '添加中...'}
                </div>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  {language === 'en' ? 'Add Link' : '添加链接'}
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default LinkForm;
