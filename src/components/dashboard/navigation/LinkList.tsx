
import React, { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Category, NavLink } from './types';
import { supabase } from '@/integrations/supabase/client';
import SubmitToPublicDialog from './SubmitToPublicDialog';
import LinkItem from './components/LinkItem';
import EmptyLinksList from './components/EmptyLinksList';
import { PingResult } from './components/PingButton';

interface LinkListProps {
  links: NavLink[];
  onDeleteLink: (linkId: string) => void;
  onEditLink: (link: NavLink) => void;
  onSubmitLinkToPublic: (linkId: string, categoryId: string) => Promise<void>;
  pingResults?: Record<string, PingResult>;
  onPingLink?: (link: NavLink) => void;
  onPingComplete?: (linkId: string, result: PingResult) => void;
  isReordering?: boolean;
  onMoveLink?: (linkId: string, direction: 'up' | 'down') => void;
}

const LinkList: React.FC<LinkListProps> = ({ 
  links, 
  onDeleteLink, 
  onEditLink,
  onSubmitLinkToPublic,
  pingResults = {},
  onPingLink,
  onPingComplete,
  isReordering = false,
  onMoveLink
}) => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [adminCategories, setAdminCategories] = useState<Category[]>([]);
  const [isSubmitDialogOpen, setIsSubmitDialogOpen] = useState(false);
  const [selectedLinkId, setSelectedLinkId] = useState<string | null>(null);
  
  React.useEffect(() => {
    const fetchAdminCategories = async () => {
      try {
        const { data, error } = await supabase
          .from('nav_categories')
          .select('*')
          .is('user_id', null)
          .eq('is_public', true)
          .order('sort_order', { ascending: true });
          
        if (error) {
          console.error('Error fetching admin categories:', error);
          return;
        }
        
        if (data) {
          setAdminCategories(data);
        }
      } catch (error) {
        console.error('Error:', error);
      }
    };
    
    fetchAdminCategories();
  }, []);
  
  const handleSubmitToPublic = (linkId: string) => {
    setSelectedLinkId(linkId);
    setIsSubmitDialogOpen(true);
  };
  
  const handleConfirmSubmit = async (categoryId: string) => {
    if (!selectedLinkId) return;
    
    try {
      await onSubmitLinkToPublic(selectedLinkId, categoryId);
      toast({
        description: language === 'en' 
          ? "Link submitted to public directory successfully" 
          : "链接已成功提交到公共目录",
      });
    } catch (error) {
      console.error('Error submitting link to public:', error);
      toast({
        variant: "destructive",
        description: language === 'en' 
          ? "Failed to submit link to public directory" 
          : "提交链接到公共目录失败",
      });
    } finally {
      setIsSubmitDialogOpen(false);
      setSelectedLinkId(null);
    }
  };
  
  if (links.length === 0) {
    return <EmptyLinksList />;
  }
  
  return (
    <div className="space-y-2">
      {links.map((link) => (
        <LinkItem
          key={link.id}
          link={link}
          pingResults={pingResults}
          onDeleteLink={onDeleteLink}
          onEditLink={onEditLink}
          onSubmitToPublic={handleSubmitToPublic}
          onPingLink={onPingLink}
          onPingComplete={onPingComplete}
          isReordering={isReordering}
          onMoveLink={onMoveLink}
        />
      ))}
      
      <SubmitToPublicDialog 
        open={isSubmitDialogOpen}
        onOpenChange={setIsSubmitDialogOpen}
        categories={adminCategories}
        onConfirm={handleConfirmSubmit}
      />
    </div>
  );
};

export default LinkList;
