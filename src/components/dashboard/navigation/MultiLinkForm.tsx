
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus, Trash2, ArrowDown, ArrowUp } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { Category } from './types';
import IconUploader from '@/components/navigation/IconUploader';
import { supabase } from '@/integrations/supabase/client';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';

interface LinkFormData {
  id: string;
  name: string;
  url: string;
  icon: string;
  isInternal: boolean;
}

interface MultiLinkFormProps {
  categories: Category[];
  selectedCategoryId: string;
  onCreateLink: (name: string, url: string, icon: string, isInternal: boolean, categoryId: string) => Promise<void>;
}

const MultiLinkForm: React.FC<MultiLinkFormProps> = ({
  categories,
  selectedCategoryId,
  onCreateLink
}) => {
  const { language } = useAppContext();
  const [links, setLinks] = useState<LinkFormData[]>([
    { id: crypto.randomUUID(), name: '', url: '', icon: '', isInternal: false }
  ]);
  const [parentCategoryId, setParentCategoryId] = useState('');
  const [subcategoryId, setSubcategoryId] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [loadingSubcategories, setLoadingSubcategories] = useState(false);
  const [bulkInput, setBulkInput] = useState('');
  const [showBulkInput, setShowBulkInput] = useState(false);
  
  // Get parent categories (without parent_id)
  const parentCategories = categories.filter(cat => !cat.parent_id);
  
  // Initial load
  useEffect(() => {
    // If selectedCategoryId is a child category, find its parent
    const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);
    if (selectedCategory) {
      if (selectedCategory.parent_id) {
        setParentCategoryId(selectedCategory.parent_id);
        setSubcategoryId(selectedCategory.id);
      } else {
        setParentCategoryId(selectedCategory.id);
      }
    }
  }, [selectedCategoryId, categories]);
  
  // When parent category changes, fetch subcategories
  useEffect(() => {
    if (!parentCategoryId) {
      setSubcategories([]);
      return;
    }
    
    const fetchSubcategories = async () => {
      setLoadingSubcategories(true);
      
      try {
        const { data, error } = await supabase
          .from('nav_categories')
          .select('*')
          .eq('parent_id', parentCategoryId)
          .order('sort_order', { ascending: true });
          
        if (error) {
          console.error('Error fetching subcategories:', error);
          return;
        }
        
        if (data) {
          setSubcategories(data as Category[]);
        }
      } catch (err) {
        console.error('Error fetching subcategories:', err);
      } finally {
        setLoadingSubcategories(false);
      }
    };
    
    fetchSubcategories();
  }, [parentCategoryId]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Determine which category ID to use based on selection
    const finalCategoryId = subcategoryId || parentCategoryId;
    
    if (!finalCategoryId) {
      return;
    }
    
    // Filter out empty links
    const validLinks = links.filter(link => link.name.trim() && link.url.trim());
    
    if (validLinks.length === 0) {
      return;
    }
    
    setSubmitting(true);
    
    try {
      // Submit all links sequentially
      for (const link of validLinks) {
        await onCreateLink(
          link.name.trim(), 
          link.url.trim(), 
          link.icon, 
          link.isInternal, 
          finalCategoryId
        );
      }
      
      // Reset form
      setLinks([{ id: crypto.randomUUID(), name: '', url: '', icon: '', isInternal: false }]);
      setBulkInput('');
    } catch (error) {
      console.error('Error creating links:', error);
    } finally {
      setSubmitting(false);
    }
  };
  
  const handleAddLink = () => {
    setLinks([...links, { id: crypto.randomUUID(), name: '', url: '', icon: '', isInternal: false }]);
  };
  
  const handleRemoveLink = (id: string) => {
    if (links.length > 1) {
      setLinks(links.filter(link => link.id !== id));
    }
  };
  
  const handleLinkChange = (id: string, field: keyof LinkFormData, value: string | boolean) => {
    setLinks(links.map(link => 
      link.id === id ? { ...link, [field]: value } : link
    ));
  };
  
  const handleIconSelected = (id: string, iconUrl: string) => {
    handleLinkChange(id, 'icon', iconUrl);
  };
  
  const handleMoveLink = (id: string, direction: 'up' | 'down') => {
    const index = links.findIndex(link => link.id === id);
    if (
      (direction === 'up' && index === 0) || 
      (direction === 'down' && index === links.length - 1)
    ) {
      return;
    }
    
    const newLinks = [...links];
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    [newLinks[index], newLinks[newIndex]] = [newLinks[newIndex], newLinks[index]];
    setLinks(newLinks);
  };
  
  const parseBulkInput = () => {
    if (!bulkInput.trim()) return;
    
    const lines = bulkInput.split('\n').filter(line => line.trim());
    const newLinks: LinkFormData[] = [];
    
    for (const line of lines) {
      // Try to extract name and URL
      // Assume format is either "name url" or just "url"
      const parts = line.trim().split(/\s+/);
      
      if (parts.length >= 2) {
        // Last part is URL, everything before is name
        const url = parts[parts.length - 1];
        const name = parts.slice(0, parts.length - 1).join(' ');
        
        if (url.startsWith('http://') || url.startsWith('https://')) {
          newLinks.push({
            id: crypto.randomUUID(),
            name: name || url,
            url,
            icon: '',
            isInternal: false
          });
        } else {
          // Try to interpret the whole line as a URL
          newLinks.push({
            id: crypto.randomUUID(),
            name: line.trim(),
            url: line.trim().startsWith('http') ? line.trim() : `https://${line.trim()}`,
            icon: '',
            isInternal: false
          });
        }
      } else if (parts.length === 1 && parts[0]) {
        // Just a URL
        newLinks.push({
          id: crypto.randomUUID(),
          name: parts[0],
          url: parts[0].startsWith('http') ? parts[0] : `https://${parts[0]}`,
          icon: '',
          isInternal: false
        });
      }
    }
    
    if (newLinks.length > 0) {
      setLinks(newLinks);
      setShowBulkInput(false);
    }
  };
  
  const toggleBulkInput = () => {
    if (showBulkInput && bulkInput.trim()) {
      parseBulkInput();
    } else {
      setShowBulkInput(!showBulkInput);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>{language === 'en' ? 'Add Links' : '添加链接'}</span>
          <Badge variant="outline" className="ml-2">
            {links.length} {language === 'en' ? 'links' : '链接'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="parent-category">
              {language === 'en' ? 'Main Category' : '主分类'}
            </Label>
            <select
              id="parent-category"
              className="w-full p-2 border rounded-md"
              value={parentCategoryId}
              onChange={(e) => {
                setParentCategoryId(e.target.value);
                setSubcategoryId(''); // Reset subcategory when parent changes
              }}
              required
            >
              <option value="" disabled>
                {language === 'en' ? 'Select a main category' : '选择主分类'}
              </option>
              {parentCategories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          
          {parentCategoryId && subcategories.length > 0 && (
            <div className="space-y-2">
              <Label htmlFor="subcategory">
                {language === 'en' ? 'Subcategory (Optional)' : '子分类（可选）'}
              </Label>
              <select
                id="subcategory"
                className="w-full p-2 border rounded-md"
                value={subcategoryId}
                onChange={(e) => setSubcategoryId(e.target.value)}
              >
                <option value="">
                  {language === 'en' ? '-- Use Main Category --' : '-- 使用主分类 --'}
                </option>
                {subcategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {loadingSubcategories && (
                <div className="text-sm text-muted-foreground">
                  {language === 'en' ? 'Loading subcategories...' : '加载子分类中...'}
                </div>
              )}
            </div>
          )}
          
          <div className="flex justify-between items-center">
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              onClick={toggleBulkInput}
            >
              {showBulkInput 
                ? (language === 'en' ? 'Parse Bulk Input' : '解析批量输入') 
                : (language === 'en' ? 'Bulk Input' : '批量输入')}
            </Button>
            
            <Button 
              type="button" 
              variant="ghost" 
              size="sm"
              onClick={handleAddLink}
            >
              <Plus className="h-4 w-4 mr-1" />
              {language === 'en' ? 'Add Another Link' : '添加另一个链接'}
            </Button>
          </div>
          
          {showBulkInput ? (
            <div className="space-y-2">
              <Label htmlFor="bulk-input">
                {language === 'en' 
                  ? 'Enter one link per line (format: "Name URL" or just "URL")' 
                  : '每行输入一个链接（格式："名称 URL"或仅"URL"）'}
              </Label>
              <Textarea
                id="bulk-input"
                value={bulkInput}
                onChange={(e) => setBulkInput(e.target.value)}
                rows={6}
                placeholder={language === 'en' 
                  ? 'Google https://google.com\nFacebook https://facebook.com' 
                  : '谷歌 https://google.com\n百度 https://baidu.com'}
              />
            </div>
          ) : (
            <>
              {links.map((link, index) => (
                <div key={link.id} className="p-3 border rounded-md space-y-3">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">
                      {language === 'en' ? `Link ${index + 1}` : `链接 ${index + 1}`}
                    </span>
                    <div className="flex items-center gap-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleMoveLink(link.id, 'up')}
                        disabled={index === 0}
                        className="h-6 w-6"
                      >
                        <ArrowUp className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleMoveLink(link.id, 'down')}
                        disabled={index === links.length - 1}
                        className="h-6 w-6"
                      >
                        <ArrowDown className="h-3 w-3" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveLink(link.id)}
                        disabled={links.length === 1}
                        className="h-6 w-6 text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor={`link-name-${link.id}`}>
                      {language === 'en' ? 'Name' : '名称'}
                    </Label>
                    <Input
                      id={`link-name-${link.id}`}
                      value={link.name}
                      onChange={(e) => handleLinkChange(link.id, 'name', e.target.value)}
                      placeholder={language === 'en' ? 'Enter link name' : '输入链接名称'}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor={`link-url-${link.id}`}>
                      {language === 'en' ? 'URL' : '链接'}
                    </Label>
                    <Input
                      id={`link-url-${link.id}`}
                      value={link.url}
                      onChange={(e) => handleLinkChange(link.id, 'url', e.target.value)}
                      placeholder={language === 'en' ? 'https://example.com' : 'https://示例.com'}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor={`link-icon-${link.id}`}>
                      {language === 'en' ? 'Icon' : '图标'}
                    </Label>
                    <div className="flex gap-2 items-center">
                      <Input
                        id={`link-icon-${link.id}`}
                        value={link.icon}
                        onChange={(e) => handleLinkChange(link.id, 'icon', e.target.value)}
                        placeholder={language === 'en' ? 'Icon URL or leave empty' : '图标URL或留空'}
                        className="flex-1"
                      />
                      <IconUploader 
                        onIconSelected={(iconUrl) => handleIconSelected(link.id, iconUrl)} 
                        currentIconUrl={link.icon}
                      />
                    </div>
                    {link.icon && (
                      <div className="mt-2 p-2 border rounded-md w-16 h-16 flex items-center justify-center">
                        <img 
                          src={link.icon} 
                          alt="Icon preview" 
                          className="max-w-full max-h-full"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder.svg';
                          }}
                        />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`link-internal-${link.id}`}
                      checked={link.isInternal}
                      onCheckedChange={(checked) => handleLinkChange(link.id, 'isInternal', checked)}
                    />
                    <Label htmlFor={`link-internal-${link.id}`}>
                      {language === 'en' ? 'Internal Link' : '内部链接'}
                    </Label>
                  </div>
                </div>
              ))}
            </>
          )}
          
          <Button 
            type="submit" 
            className="w-full"
            disabled={submitting || (!subcategoryId && !parentCategoryId) || 
              (showBulkInput ? !bulkInput.trim() : !links.some(link => link.name.trim() && link.url.trim()))}
          >
            {submitting ? (
              <div className="flex items-center">
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                {language === 'en' ? 'Creating...' : '创建中...'}
              </div>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                {language === 'en' ? 'Add Links' : '添加链接'}
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default MultiLinkForm;
