
import React from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface EmailFormProps {
  onCreateEmail: (domain: string) => Promise<any>;
  isLoading: boolean;
}

const EmailForm: React.FC<EmailFormProps> = ({ onCreateEmail, isLoading }) => {
  const { t, language } = useAppContext();

  const handleCreateEmail = () => {
    // Default domain or empty string as parameter
    onCreateEmail('');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'Create Email' : '创建邮箱'}</CardTitle>
        <CardDescription>
          {language === 'en' ? 'Generate a temporary email address for your needs.' : '生成临时电子邮件地址以满足您的需求。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button 
          onClick={handleCreateEmail} 
          disabled={isLoading}
        >
          <Plus className="w-4 h-4 mr-2" />
          {isLoading 
            ? (language === 'en' ? 'Creating...' : '创建中...') 
            : t('createEmail')}
        </Button>
      </CardContent>
    </Card>
  );
};

export default EmailForm;
