
import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { useAppContext } from '@/context/AppContext';
import { Compass } from 'lucide-react';
import { useNavigationManager } from '@/hooks/navigation/useNavigationManager';
import CategoriesTab from './navigation/tabs/CategoriesTab';
import LinksTab from './navigation/tabs/LinksTab';
import NavigationPreview from './NavigationPreview';
import { DragDropContext } from 'react-beautiful-dnd';

interface NavigationManagerProps {
  userId: string;
  isLoading: boolean;
}

const NavigationManager: React.FC<NavigationManagerProps> = ({ userId, isLoading }) => {
  const { language } = useAppContext();
  
  const {
    categories,
    links,
    selectedCategory,
    currentPage,
    totalPages,
    isLoading: isLinksLoading,
    setSelectedCategory,
    handlePageChange,
    handleCreateCategory,
    handleDeleteCategory,
    handleCreateLink,
    handleEditLink,
    handleDeleteLink,
    handleSubmitLinkToPublic,
    handleReorderLink,
    handleDragReorder
  } = useNavigationManager(userId);
  
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    
    const { source, destination, type } = result;
    
    // Skip if dropped in the same position
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return;
    }
    
    // Handle different types of draggable elements
    if (type === 'category') {
      const parentId = source.droppableId === 'main-categories' ? null : source.droppableId;
      handleDragReorder(source.index, destination.index, parentId, 'category');
    } else if (type === 'link') {
      const categoryId = source.droppableId;
      handleDragReorder(source.index, destination.index, categoryId, 'link');
    }
  };
  
  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center">
              <Compass className="mr-2 h-5 w-5" />
              {language === 'en' ? 'My Personal Navigation' : '我的个人导航'}
            </div>
          </CardTitle>
          <CardDescription>
            {language === 'en' 
              ? 'Create and manage your personal navigation categories and links' 
              : '创建和管理您的个人导航分类和链接'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="categories">
            <TabsList className="mb-6">
              <TabsTrigger value="categories">
                {language === 'en' ? 'Categories' : '分类'}
              </TabsTrigger>
              <TabsTrigger value="links">
                {language === 'en' ? 'Links' : '链接'}
              </TabsTrigger>
              <TabsTrigger value="preview">
                {language === 'en' ? 'Preview' : '预览'}
              </TabsTrigger>
            </TabsList>
            
            {/* Categories Tab */}
            <TabsContent value="categories" className="space-y-6">
              <CategoriesTab 
                categories={categories}
                selectedCategory={selectedCategory}
                onCreateCategory={handleCreateCategory}
                onDeleteCategory={handleDeleteCategory}
                onSelectCategory={setSelectedCategory}
              />
            </TabsContent>
            
            {/* Links Tab */}
            <TabsContent value="links" className="space-y-6">
              <LinksTab 
                categories={categories}
                selectedCategory={selectedCategory}
                onCreateLink={handleCreateLink}
              />
            </TabsContent>
            
            {/* Preview Tab */}
            <TabsContent value="preview">
              <NavigationPreview userId={userId} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </DragDropContext>
  );
};

export default NavigationManager;
