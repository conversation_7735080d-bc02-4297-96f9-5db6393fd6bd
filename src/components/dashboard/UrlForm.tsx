
import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { supabase } from '@/integrations/supabase/client';
import { extractDomain, generateRandomShortCode, calculateExpirationDate } from '@/utils/urlUtils';

interface UrlFormProps {
  userId: string | null;
  approvedDomains: string[];
  onSuccess: (newUrl: any) => void;
}

const UrlForm: React.FC<UrlFormProps> = ({ userId, approvedDomains, onSuccess }) => {
  const { t, language } = useAppContext();
  const { toast } = useToast();
  const [urlInput, setUrlInput] = useState('');
  const [expirationType, setExpirationType] = useState('day');
  const [isUrlLoading, setIsUrlLoading] = useState(false);
  const [isValidDomain, setIsValidDomain] = useState(true);

  useEffect(() => {
    if (urlInput) {
      const domain = extractDomain(urlInput);
      if (domain) {
        setIsValidDomain(approvedDomains.some(d => domain.includes(d)) || approvedDomains.length === 0);
      } else {
        setIsValidDomain(false);
      }
    } else {
      setIsValidDomain(true);
    }
  }, [urlInput, approvedDomains]);

  const handleCreateShortUrl = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUrlLoading(true);
    
    try {
      if (!urlInput) {
        toast({
          variant: "destructive",
          description: language === 'en' ? "Please enter a URL" : "请输入网址",
        });
        setIsUrlLoading(false);
        return;
      }
      
      const domain = extractDomain(urlInput);
      if (!domain || !isValidDomain) {
        toast({
          variant: "destructive",
          description: language === 'en' 
            ? "This domain is not in the approved whitelist" 
            : "该域名不在已批准的白名单中",
        });
        setIsUrlLoading(false);
        return;
      }
      
      if (!userId) {
        toast({
          variant: "destructive",
          description: language === 'en' ? "You must be logged in" : "您必须登录",
        });
        setIsUrlLoading(false);
        return;
      }
      
      let originalUrl = urlInput;
      if (!urlInput.startsWith('http://') && !urlInput.startsWith('https://')) {
        originalUrl = 'https://' + urlInput;
      }
      
      const shortCode = generateRandomShortCode();
      const expiresAt = calculateExpirationDate(expirationType);
      
      const { data, error } = await supabase
        .from('short_urls')
        .insert({
          original_url: originalUrl,
          short_code: shortCode,
          expires_at: expiresAt ? expiresAt.toISOString() : null,
          user_id: userId
        })
        .select();
      
      if (error) throw error;
      
      const newShortUrl = `https://g2.al/${shortCode}`;
      
      if (data && data[0]) {
        onSuccess({
          ...data[0],
          short_url: newShortUrl
        });
      }
      
      toast({
        description: language === 'en' ? "URL shortened successfully!" : "URL缩短成功！",
      });
      setUrlInput('');
      
    } catch (error) {
      console.error('Error creating short URL:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to create short URL" : "创建短链接失败",
      });
    } finally {
      setIsUrlLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'Create URL' : '创建网址'}</CardTitle>
        <CardDescription>
          {language === 'en' ? 'Enter a long URL to generate a short, shareable link.' : '输入长URL以生成短的、可共享的链接。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleCreateShortUrl} className="grid gap-4 sm:grid-cols-3">
          <div className="sm:col-span-2">
            <Input
              placeholder="https://example.com/long-url"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              className={`w-full ${!isValidDomain && urlInput ? "border-red-500" : ""}`}
            />
            {!isValidDomain && urlInput && (
              <div className="flex items-center mt-1 text-red-500 text-sm">
                <AlertTriangle className="h-4 w-4 mr-1" />
                {language === 'en' 
                  ? 'This domain is not in the approved whitelist' 
                  : '该域名不在已批准的白名单中'}
              </div>
            )}
          </div>
          <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:space-x-2">
            <Select
              value={expirationType}
              onValueChange={setExpirationType}
            >
              <SelectTrigger className="w-full sm:w-auto">
                <SelectValue placeholder={t('expiration')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hour">{t('hour')}</SelectItem>
                <SelectItem value="day">{t('day')}</SelectItem>
                <SelectItem value="week">{t('week')}</SelectItem>
                <SelectItem value="month">{t('month')}</SelectItem>
                <SelectItem value="permanent">{t('permanent')}</SelectItem>
              </SelectContent>
            </Select>
            <Button 
              type="submit" 
              disabled={isUrlLoading || !urlInput || !isValidDomain} 
              className="w-full sm:w-auto"
            >
              {isUrlLoading ? (language === 'en' ? 'Creating...' : '创建中...') : (language === 'en' ? 'Create' : '创建')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default UrlForm;
