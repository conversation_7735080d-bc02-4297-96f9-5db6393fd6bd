import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { useAppContext } from '@/context/AppContext';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Mail } from 'lucide-react';

interface Email {
  id: string;
  subject: string;
  body: string;
  from_address: string;
  received_at: string;
}

interface EmailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  emailAddress: string;
  emailId: string;
}

const EmailDialog: React.FC<EmailDialogProps> = ({ open, onOpenChange, emailAddress, emailId }) => {
  const { language } = useAppContext();
  const [emails, setEmails] = useState<Email[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEmails = async () => {
      if (!open || !emailId) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const { data, error } = await supabase
          .from('received_emails')
          .select('*')
          .eq('temp_email_id', emailId)
          .order('received_at', { ascending: false });
          
        if (error) throw error;
        
        setEmails(data || []);
      } catch (err) {
        console.error('Error fetching emails:', err);
        setError(language === 'en' ? 'Failed to load emails' : '加载邮件失败');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchEmails();
  }, [open, emailId, language]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Mail className="w-5 h-5 mr-2" />
            {language === 'en' ? 'Emails for ' : '电子邮件历史 '}: {emailAddress}
          </DialogTitle>
          <DialogDescription>
            {language === 'en' 
              ? 'View all received emails for this temporary email address' 
              : '查看此临时电子邮件地址收到的所有邮件'}
          </DialogDescription>
        </DialogHeader>
        
        {isLoading && (
          <div className="space-y-3">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        )}
        
        {error && (
          <div className="flex items-center justify-center p-4 text-red-500">
            <AlertCircle className="w-5 h-5 mr-2" />
            <span>{error}</span>
          </div>
        )}
        
        {!isLoading && !error && emails.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            {language === 'en' 
              ? 'No emails received yet' 
              : '尚未收到任何邮件'}
          </div>
        )}
        
        {!isLoading && !error && emails.length > 0 && (
          <div className="overflow-y-auto max-h-[400px] -mx-6 px-6">
            {emails.map((email) => (
              <div key={email.id} className="border rounded-md p-4 mb-4">
                <div className="flex justify-between text-sm text-muted-foreground mb-1">
                  <span>
                    {language === 'en' ? 'From: ' : '发件人: '}{email.from_address}
                  </span>
                  <span>
                    {new Date(email.received_at).toLocaleString()}
                  </span>
                </div>
                <div className="font-medium mb-2">{email.subject || (language === 'en' ? '(No subject)' : '(无主题)')}</div>
                <div className="text-sm border-t pt-2 whitespace-pre-wrap">
                  {email.body || (language === 'en' ? '(No content)' : '(无内容)')}
                </div>
              </div>
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EmailDialog;
