import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { <PERSON><PERSON>, Trash2, Eye } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import EmailDialog from './EmailDialog';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface EmailData {
  id: string;
  email_address: string;
  created_at: string;
  expires_at: string;
}

interface EmailListProps {
  emailData: EmailData[];
  onCopy: (text: string) => void;
  onDelete: (id: string) => void;
  onOpenEmail: (id: string) => void;
  isLoading: boolean;
}

const EmailList: React.FC<EmailListProps> = ({ emailData, onCopy, onDelete, onOpenEmail, isLoading }) => {
  const { t, language } = useAppContext();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;
  
  const totalPages = Math.ceil(emailData.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentEmails = emailData.slice(indexOfFirstItem, indexOfLastItem);

  const handleViewEmail = (id: string, address: string) => {
    onOpenEmail(id);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{language === 'en' ? 'Temp Emails' : '临时邮箱'}</CardTitle>
          <CardDescription>
            {language === 'en' ? 'Your temporary email addresses.' : '您的临时电子邮件地址。'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'Temp Emails' : '临时邮箱'}</CardTitle>
        <CardDescription>
          {language === 'en' ? 'Your temporary email addresses.' : '您的临时电子邮件地址。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {emailData.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              {language === 'en' ? 'You haven\'t created any temporary emails yet.' : '您还没有创建任何临时邮箱。'}
            </p>
          </div>
        ) : (
          <>
            <div className="rounded-md border overflow-x-auto">
              <table className="min-w-full divide-y divide-border">
                <thead>
                  <tr className="bg-muted/50">
                    <th className="px-4 py-3 text-left text-sm font-medium">{language === 'en' ? 'Email Address' : '邮箱地址'}</th>
                    <th className="px-4 py-3 text-center text-sm font-medium">{language === 'en' ? 'Messages' : '消息数'}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium">{language === 'en' ? 'Created At' : '创建时间'}</th>
                    <th className="px-4 py-3 text-left text-sm font-medium">{language === 'en' ? 'Expires At' : '过期时间'}</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">{language === 'en' ? 'Actions' : '操作'}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border">
                  {currentEmails.map((email) => (
                    <tr key={email.id} className="hover:bg-muted/50">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <span>{email.email_address}</span>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            onClick={() => onCopy(email.email_address)}
                            className="h-8 w-8"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-center">0</td>
                      <td className="px-4 py-3 text-sm">
                        {new Date(email.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {new Date(email.expires_at).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-3 text-sm text-right">
                        <div className="flex justify-end space-x-2">
                          <Button 
                            variant="secondary" 
                            size="sm"
                            onClick={() => handleViewEmail(email.id, email.email_address)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            {t('view')}
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => onDelete(email.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            {language === 'en' ? 'Delete' : '删除'}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {totalPages > 1 && (
              <div className="mt-4 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious 
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                    
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNumber: number;
                      
                      if (totalPages <= 5) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNumber = totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }
                      
                      return (
                        <PaginationItem key={pageNumber}>
                          <PaginationLink 
                            onClick={() => setCurrentPage(pageNumber)}
                            isActive={currentPage === pageNumber}
                          >
                            {pageNumber}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}
                    
                    <PaginationItem>
                      <PaginationNext 
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
            
            <div className="mt-2 text-center text-sm text-muted-foreground">
              {language === 'en' 
                ? `Showing ${indexOfFirstItem + 1}-${Math.min(indexOfLastItem, emailData.length)} of ${emailData.length} emails` 
                : `显示 ${indexOfFirstItem + 1}-${Math.min(indexOfLastItem, emailData.length)} 共 ${emailData.length} 个邮箱`}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default EmailList;
