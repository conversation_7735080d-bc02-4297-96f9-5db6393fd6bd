import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import { isUsingSupabase } from '@/config/backend';
import apiClient from '@/services/api';
import { ExternalLink, LinkIcon, Edit, GripVertical, X, Star, RefreshCw, Network } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import IconUploader from '@/components/navigation/IconUploader';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { AlertTriangle, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Category, NavLink, PingResult } from './navigation/types';

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }

  try {
    const { supabase } = await import('@/integrations/supabase/client');
    return supabase;
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

interface NavigationPreviewProps {
  userId: string;
}

const NavigationPreview: React.FC<NavigationPreviewProps> = ({ userId }) => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [mainCategories, setMainCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Record<string, Category[]>>({});
  const [activeMainCategory, setActiveMainCategory] = useState<string | null>(null);
  const [activeSubcategory, setActiveSubcategory] = useState<string | null>(null);
  const [links, setLinks] = useState<Record<string, NavLink[]>>({});
  const [pingResults, setPingResults] = useState<Record<string, PingResult>>({});
  const [isReordering, setIsReordering] = useState(false);
  const [editingLink, setEditingLink] = useState<NavLink | null>(null);
  const [editedName, setEditedName] = useState('');
  const [editedUrl, setEditedUrl] = useState('');
  const [editedIcon, setEditedIcon] = useState<string | null>('');
  const [editedIsInternal, setEditedIsInternal] = useState(false);

  // 推荐链接相关状态
  const [isRecommendDialogOpen, setIsRecommendDialogOpen] = useState(false);
  const [linkToRecommend, setLinkToRecommend] = useState<NavLink | null>(null);
  const [adminCategories, setAdminCategories] = useState<Category[]>([]);
  const [selectedAdminCategoryId, setSelectedAdminCategoryId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSorting, setIsSorting] = useState(false);

  // 获取当前活动的分类ID
  const activeCategoryId = activeSubcategory || activeMainCategory;

  // 获取管理员分类（用于推荐）
  useEffect(() => {
    const fetchAdminCategories = async () => {
      try {
        if (isUsingSupabase()) {
          // 使用 Supabase
          const supabase = await getSupabaseClient();
          const { data, error } = await supabase
            .from('nav_categories')
            .select('*')
            .is('user_id', null)
            .eq('is_public', true)
            .order('sort_order', { ascending: true });

          if (error) {
            console.error('Error fetching admin categories:', error);
            return;
          }

          if (data) {
            setAdminCategories(data);
            if (data.length > 0) {
              setSelectedAdminCategoryId(data[0].id);
            }
          }
        } else {
          // 使用 Go 后端
          const response = await apiClient.get('/navigation/categories?is_public=true&user_id=null');
          const data = response.data;

          if (data && Array.isArray(data)) {
            setAdminCategories(data);
            if (data.length > 0) {
              setSelectedAdminCategoryId(data[0].id);
            }
          }
        }
      } catch (error) {
        console.error('Error:', error);
      }
    };

    fetchAdminCategories();
  }, []);

  // 更新链接排序顺序
  const updateLinkSortOrders = async (categoryLinks: NavLink[]) => {
    setIsSorting(true);

    try {
      // 准备更新数据，确保包含所有必需的字段
      const updates = categoryLinks.map((link, index) => ({
        id: link.id,
        category_id: link.category_id,
        name: link.name,
        url: link.url,
        sort_order: index,
        // 包含其他可能需要的字段
        icon: link.icon,
        is_internal: link.is_internal
      }));

      // 批量更新数据库
      const { error } = await supabase
        .from('nav_links')
        .upsert(updates);

      if (error) {
        console.error('Error updating sort orders:', error);
        toast({
          title: language === 'zh' ? "排序更新失败" : "Failed to update sort order",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      toast({
        title: language === 'zh' ? "排序已更新" : "Sort order updated",
        variant: "default",
      });
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: language === 'zh' ? "排序更新失败" : "Failed to update sort order",
        variant: "destructive",
      });
    } finally {
      setIsSorting(false);
    }
  };

  const handlePingComplete = useCallback((linkId: string, result: PingResult) => {
    setPingResults(prev => ({
      ...prev,
      [linkId]: result
    }));
  }, []);

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination } = result;

    // 确保有有效的源和目标
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return;
    }

    const categoryId = source.droppableId;
    if (!categoryId || !links[categoryId]) return;

    // 获取当前分类的链接
    const categoryLinks = [...links[categoryId]];

    // 从源位置删除拖动的项目并插入到目标位置
    const [movedLink] = categoryLinks.splice(source.index, 1);
    categoryLinks.splice(destination.index, 0, movedLink);

    // 更新本地状态
    setLinks({
      ...links,
      [categoryId]: categoryLinks
    });

    // 更新排序值
    updateLinkSortOrders(categoryLinks);
  };

  // Fetch categories and organize them by main/sub
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);

      try {
        // 获取用户个人分类
        const { data: userData, error: userError } = await supabase
          .from('nav_categories')
          .select('*')
          .eq('user_id', userId)
          .order('sort_order', { ascending: true });

        if (userError) {
          console.error('Error fetching user categories:', userError);
          setLoading(false);
          return;
        }

        // 获取公共分类
        const { data: publicData, error: publicError } = await supabase
          .from('nav_categories')
          .select('*')
          .is('user_id', null)
          .eq('is_admin_default', true)
          .order('sort_order', { ascending: true });

        if (publicError) {
          console.error('Error fetching public categories:', publicError);
          setLoading(false);
          return;
        }

        // 将用户个人分类放在前面，推荐导航放在后面
        const allCategories = [...(userData || []), ...(publicData || [])];

        if (allCategories.length > 0) {
          // 分离主分类和子分类
          const main = allCategories.filter(c => !c.parent_id);
          const subs: Record<string, Category[]> = {};

          // 按父分类组织子分类
          allCategories.filter(c => c.parent_id).forEach(sub => {
            if (!subs[sub.parent_id!]) {
              subs[sub.parent_id!] = [];
            }
            subs[sub.parent_id!].push(sub);
          });

          setMainCategories(main);
          setSubcategories(subs);

          // 设置初始活动分类
          if (main.length > 0) {
            // 优先选择用户自己的分类（如果存在）
            const userMainCategory = main.find(c => c.user_id === userId);
            setActiveMainCategory(userMainCategory ? userMainCategory.id : main[0].id);

            // 如果有子分类，选择第一个子分类
            const categoryId = userMainCategory ? userMainCategory.id : main[0].id;
            if (subs[categoryId] && subs[categoryId].length > 0) {
              setActiveSubcategory(subs[categoryId][0].id);
            } else {
              setActiveSubcategory(null);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [userId]);

  // Fetch links for all categories
  useEffect(() => {
    const fetchAllLinks = async () => {
      const categoryIds = [
        ...mainCategories.map(c => c.id),
        ...Object.values(subcategories).flat().map(c => c.id)
      ];

      if (categoryIds.length === 0) return;

      const linksMap: Record<string, NavLink[]> = {};

      await Promise.all(
        categoryIds.map(async (categoryId) => {
          const { data, error } = await supabase
            .from('nav_links')
            .select('*')
            .eq('category_id', categoryId)
            .order('sort_order', { ascending: true });

          if (error) {
            console.error(`Error fetching links for category ${categoryId}:`, error);
            return;
          }

          if (data) {
            linksMap[categoryId] = data;
          }
        })
      );

      setLinks(linksMap);
    };

    fetchAllLinks();
  }, [mainCategories, subcategories]);

  const handleMainCategoryChange = (categoryId: string) => {
    setActiveMainCategory(categoryId);

    // If this main category has subcategories, select the first one
    if (subcategories[categoryId] && subcategories[categoryId].length > 0) {
      setActiveSubcategory(subcategories[categoryId][0].id);
    } else {
      setActiveSubcategory(null);
    }
  };

  const handlePing = async (link: NavLink) => {
    setPingResults({
      ...pingResults,
      [link.id]: { status: 'pending' }
    });

    try {
      const startTime = Date.now();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(link.url, {
        method: 'HEAD',
        signal: controller.signal,
        mode: 'no-cors'
      });

      clearTimeout(timeoutId);
      const endTime = Date.now();
      const elapsed = endTime - startTime;

      const result: PingResult = {
        status: 'success',
        time: elapsed
      };

      setPingResults({
        ...pingResults,
        [link.id]: result
      });

      return result;
    } catch (error) {
      console.error(`Error pinging ${link.url}:`, error);

      const result: PingResult = {
        status: 'error',
        message: error instanceof Error ? error.message : String(error)
      };

      setPingResults({
        ...pingResults,
        [link.id]: result
      });

      return result;
    }
  };

  // 推荐链接
  const handleRecommendLink = (link: NavLink) => {
    setLinkToRecommend(link);
    setIsRecommendDialogOpen(true);
  };

  // 提交推荐
  const handleSubmitRecommendation = async () => {
    if (!linkToRecommend || !selectedAdminCategoryId) return;

    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('nav_links')
        .insert({
          name: linkToRecommend.name,
          url: linkToRecommend.url,
          icon: linkToRecommend.icon,
          is_internal: linkToRecommend.is_internal,
          category_id: selectedAdminCategoryId,
          submission_status: 'pending',
          submitted_by: userId
        });

      if (error) {
        console.error('Error submitting link recommendation:', error);
        throw error;
      }

      toast({
        description: language === 'en'
          ? 'Link recommendation submitted successfully'
          : '链接推荐提交成功',
      });

      setIsRecommendDialogOpen(false);
      setLinkToRecommend(null);
    } catch (error) {
      console.error('Error:', error);
      toast({
        variant: 'destructive',
        description: language === 'en'
          ? 'Failed to submit link recommendation'
          : '提交链接推荐失败',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 编辑链接
  const handleEditLink = (link: NavLink) => {
    setEditingLink(link);
    setEditedName(link.name);
    setEditedUrl(link.url);
    setEditedIcon(link.icon);
    setEditedIsInternal(link.is_internal);
  };

  // 保存编辑的链接
  const handleSaveEditedLink = async () => {
    if (!editingLink) return;

    try {
      const { error } = await supabase
        .from('nav_links')
        .update({
          name: editedName,
          url: editedUrl,
          icon: editedIcon,
          is_internal: editedIsInternal
        })
        .eq('id', editingLink.id);

      if (error) throw error;

      // 更新本地状态
      if (editingLink.category_id) {
        const updatedLinks = links[editingLink.category_id]?.map(link =>
          link.id === editingLink.id
            ? { ...link, name: editedName, url: editedUrl, icon: editedIcon, is_internal: editedIsInternal }
            : link
        ) || [];

        setLinks({
          ...links,
          [editingLink.category_id]: updatedLinks
        });
      }

      toast({
        description: language === 'en'
          ? 'Link updated successfully'
          : '链接更新成功',
      });

      // 关闭对话框
      setEditingLink(null);
    } catch (error) {
      console.error('Error updating link:', error);
      toast({
        variant: 'destructive',
        description: language === 'en'
          ? 'Failed to update link'
          : '更新链接失败',
      });
    }
  };

  // 获取favicon
  const getFavicon = (url: string): string => {
    try {
      const urlObj = new URL(url);
      return `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=64`;
    } catch (e) {
      return '';
    }
  };

  // 当编辑URL改变且没有图标时，自动获取favicon
  useEffect(() => {
    if (editingLink && editedUrl && !editedIcon) {
      const favicon = getFavicon(editedUrl);
      if (favicon) {
        setEditedIcon(favicon);
      }
    }
  }, [editedUrl, editingLink]);

  // 处理图标上传回调
  const handleIconSelected = (iconUrl: string) => {
    setEditedIcon(iconUrl);
  };

  if (loading) {
    return (
      <div className="py-12 text-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
        <p className="mt-4 text-muted-foreground">
          {language === 'en' ? 'Loading preview...' : '加载预览...'}
        </p>
      </div>
    );
  }

  if (mainCategories.length === 0) {
    return (
      <div className="py-12 text-center">
        <p className="text-muted-foreground">
          {language === 'en'
            ? 'No navigation categories found. Create some categories and links to see a preview.'
            : '未找到导航分类。创建一些分类和链接以查看预览。'}
        </p>
      </div>
    );
  }

  // Determine which category's links to show
  const currentLinks = activeCategoryId ? links[activeCategoryId] || [] : [];

  return (
    <div className="border rounded-lg p-4 sm:p-6 bg-muted/20">
      <h3 className="text-xl font-semibold mb-4 sm:mb-6 text-center">
        {language === 'en' ? 'Navigation Preview' : '导航预览'}
      </h3>

      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsReordering(!isReordering)}
          disabled={isSorting}
          className="text-xs sm:text-sm flex items-center gap-2"
        >
          {isReordering ? (
            <>
              <X className="h-4 w-4" />
              {language === 'en' ? 'Done Reordering' : '完成排序'}
            </>
          ) : (
            <>
              <GripVertical className="h-4 w-4" />
              {language === 'en' ? 'Reorder Links' : '排序链接'}
            </>
          )}
        </Button>
      </div>

      {isReordering && (
        <div className="mb-4 px-4 py-2 bg-muted rounded-md text-center text-sm">
          {language === 'en'
            ? 'Drag and drop links to reorder them'
            : '拖放链接来调整它们的顺序'}
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-4 sm:gap-6">
        {/* Main categories sidebar */}
        <div className="w-full md:w-40 lg:w-48 space-y-1 sm:space-y-2">
          {mainCategories.map(category => (
            <button
              key={category.id}
              className={`w-full text-left px-3 py-1.5 text-sm rounded-md transition-colors ${
                activeMainCategory === category.id
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              }`}
              onClick={() => handleMainCategoryChange(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Content area with subcategory tabs and links */}
        <div className="flex-1">
          {activeMainCategory && (
            <>
              {/* Subcategory tabs (if any) */}
              {subcategories[activeMainCategory] && subcategories[activeMainCategory].length > 0 ? (
                <Tabs
                  value={activeSubcategory || ""}
                  onValueChange={setActiveSubcategory}
                  className="mb-4 sm:mb-6"
                >
                  <TabsList className="w-full grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
                    {subcategories[activeMainCategory].map(sub => (
                      <TabsTrigger key={sub.id} value={sub.id} className="text-xs sm:text-sm px-2 py-1">
                        {sub.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </Tabs>
              ) : null}

              {/* Links grid */}
              {currentLinks.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    {language === 'en'
                      ? 'No links in this category yet.'
                      : '此分类中还没有链接。'}
                  </p>
                </div>
              ) : (
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId={activeCategoryId || "uncategorized"} type="link">
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className={isReordering
                          ? "flex flex-col gap-2 sm:gap-3"
                          : "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-2 sm:gap-3"
                        }
                      >
                        {currentLinks.map((link, index) => (
                          <Draggable
                            key={link.id}
                            draggableId={link.id}
                            index={index}
                            isDragDisabled={!isReordering}
                          >
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`${isReordering ? 'w-full' : ''} transition-all ${
                                  snapshot.isDragging ? 'opacity-70 z-10' : ''
                                }`}
                              >
                                <Card className={`h-full transition-all duration-200 ${
                                  isReordering ? 'hover:shadow-md hover:border-primary/50 cursor-grab' : 'hover:shadow-sm'
                                }`}>
                                  <CardContent className="p-3 sm:p-4">
                                    <div className="flex flex-col items-center text-center gap-1.5">
                                      {isReordering && (
                                        <div className="self-stretch flex justify-center items-center py-1 mb-1 rounded-md">
                                          <GripVertical className="h-5 w-5 text-muted-foreground" />
                                        </div>
                                      )}

                                      <div className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full bg-muted">
                                        {link.icon ? (
                                          <img
                                            src={link.icon}
                                            alt={`${link.name} icon`}
                                            className="w-5 h-5 sm:w-6 sm:h-6 object-contain"
                                          />
                                        ) : (
                                          <LinkIcon className="w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground" />
                                        )}
                                      </div>
                                      <div className="font-medium text-sm leading-tight">{link.name}</div>
                                      <a
                                        href={link.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-500 hover:underline break-all"
                                      >
                                        {link.url.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0]}
                                      </a>
                                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                                        {link.is_internal ? (
                                          <span>{language === 'en' ? 'Internal' : '内部'}</span>
                                        ) : (
                                          <>
                                            <ExternalLink className="w-3 h-3" />
                                            <span>{language === 'en' ? 'External' : '外部'}</span>
                                          </>
                                        )}
                                      </div>

                                      {/* Action buttons */}
                                      <div className="mt-1.5 flex flex-wrap gap-1 text-xs justify-center">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7 px-2 text-xs"
                                          onClick={() => handlePing(link)}
                                          disabled={pingResults[link.id]?.status === 'pending'}
                                        >
                                          <Network className="h-3 w-3 mr-1" />
                                          {language === 'en' ? 'Ping' : '测试'}
                                        </Button>

                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7 px-2 text-xs"
                                          onClick={() => handleEditLink(link)}
                                        >
                                          <Edit className="h-3 w-3 mr-1" />
                                          {language === 'en' ? 'Edit' : '编辑'}
                                        </Button>

                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7 px-2 text-xs"
                                          onClick={() => handleRecommendLink(link)}
                                        >
                                          <Star className="h-3 w-3 mr-1" />
                                          {language === 'en' ? 'Recommend' : '推荐'}
                                        </Button>
                                      </div>

                                      {pingResults[link.id] && (
                                        <div className="mt-1 w-full">
                                          {pingResults[link.id].status === 'pending' && (
                                            <Progress value={50} className="w-full h-1" />
                                          )}
                                          {pingResults[link.id].status === 'success' && (
                                            <div className="text-xs text-green-600">
                                              {language === 'en'
                                                ? `${pingResults[link.id].time}ms`
                                                : `${pingResults[link.id].time}毫秒`}
                                            </div>
                                          )}
                                          {pingResults[link.id].status === 'failed' && (
                                            <div className="text-xs text-red-500">
                                              {language === 'en' ? 'Failed' : '失败'}
                                            </div>
                                          )}
                                          {pingResults[link.id].status === 'error' && (
                                            <div className="text-xs text-red-500">
                                              {language === 'en' ? 'Error' : '错误'}
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </CardContent>
                                </Card>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              )}
            </>
          )}
        </div>
      </div>

      {/* 编辑链接对话框 */}
      <Dialog open={!!editingLink} onOpenChange={(open) => !open && setEditingLink(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {language === 'en' ? 'Edit Link' : '编辑链接'}
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                {language === 'en' ? 'Name' : '名称'}
              </Label>
              <Input
                id="edit-name"
                value={editedName}
                onChange={(e) => setEditedName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-url" className="text-right">
                {language === 'en' ? 'URL' : '链接'}
              </Label>
              <Input
                id="edit-url"
                value={editedUrl}
                onChange={(e) => setEditedUrl(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-icon" className="text-right">
                {language === 'en' ? 'Icon' : '图标'}
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Input
                  id="edit-icon"
                  value={editedIcon || ''}
                  onChange={(e) => setEditedIcon(e.target.value)}
                  className="flex-1"
                />
                <IconUploader
                  onIconSelected={handleIconSelected}
                  currentIconUrl={editedIcon || ''}
                />
              </div>
              {editedIcon && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <div></div>
                  <div className="col-span-3">
                    <div className="p-2 border rounded-md w-16 h-16 flex items-center justify-center">
                      <img
                        src={editedIcon}
                        alt="Icon preview"
                        className="max-w-full max-h-full"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder.svg';
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-internal" className="text-right">
                {language === 'en' ? 'Internal' : '内部链接'}
              </Label>
              <div className="col-span-3 flex items-center">
                <Switch
                  id="edit-internal"
                  checked={editedIsInternal}
                  onCheckedChange={setEditedIsInternal}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">
                {language === 'en' ? 'Cancel' : '取消'}
              </Button>
            </DialogClose>
            <Button onClick={handleSaveEditedLink}>
              {language === 'en' ? 'Save Changes' : '保存更改'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 推荐链接对话框 */}
      <Dialog open={isRecommendDialogOpen} onOpenChange={setIsRecommendDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {language === 'en' ? 'Recommend Link' : '推荐链接'}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            {linkToRecommend && (
              <div className="mb-4 p-3 bg-muted rounded-md">
                <div className="flex items-center gap-2 mb-2">
                  {linkToRecommend.icon ? (
                    <img
                      src={linkToRecommend.icon}
                      alt=""
                      className="w-8 h-8 object-contain"
                    />
                  ) : (
                    <LinkIcon className="h-8 w-8 text-muted-foreground" />
                  )}
                  <div className="font-medium">{linkToRecommend.name}</div>
                </div>
                <div className="text-sm text-muted-foreground break-all">
                  {linkToRecommend.url}
                </div>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <Label htmlFor="admin-category" className="block mb-2">
                  {language === 'en' ? 'Select Category' : '选择分类'}
                </Label>
                <Select
                  value={selectedAdminCategoryId}
                  onValueChange={setSelectedAdminCategoryId}
                >
                  <SelectTrigger id="admin-category">
                    <SelectValue placeholder={language === 'en' ? 'Select category' : '选择分类'} />
                  </SelectTrigger>
                  <SelectContent>
                    {adminCategories.map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <p className="text-sm text-muted-foreground">
                {language === 'en'
                  ? 'Your recommendation will be reviewed by an administrator before being added to the public directory.'
                  : '您的推荐将由管理员审核后添加到公共目录。'}
              </p>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">
                {language === 'en' ? 'Cancel' : '取消'}
              </Button>
            </DialogClose>
            <Button
              onClick={handleSubmitRecommendation}
              disabled={isSubmitting || !selectedAdminCategoryId}
            >
              {isSubmitting
                ? (language === 'en' ? 'Submitting...' : '提交中...')
                : (language === 'en' ? 'Submit Recommendation' : '提交推荐')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NavigationPreview;
