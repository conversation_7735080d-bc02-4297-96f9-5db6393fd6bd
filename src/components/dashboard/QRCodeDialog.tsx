import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Copy } from "lucide-react";
import QRCode from '@/components/QRCode';

interface QRCodeDialogProps {
  url: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  actionText: string;
  onAction: () => void;
}

const QRCodeDialog: React.FC<QRCodeDialogProps> = ({
  url,
  open,
  onOpenChange,
  title,
  description,
  actionText,
  onAction,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center py-4">
          <QRCode url={url} size={200} className="mb-4" />
        </div>
        <DialogFooter className="sm:justify-center">
          <Button variant="outline" onClick={onAction}>
            <Copy className="h-4 w-4 mr-2" />
            {actionText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default QRCodeDialog; 