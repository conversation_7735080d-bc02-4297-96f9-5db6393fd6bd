
import React from 'react';
import { useAppContext } from '@/context/AppContext';
import { Compass } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import NavigationManager from '../NavigationManager';
import SubmittedLinksList from '../navigation/SubmittedLinksList';

interface NavigationTabContentProps {
  userId: string;
  isLoading: boolean;
  submittedLinks: any[];
}

const NavigationTabContent: React.FC<NavigationTabContentProps> = ({
  userId,
  isLoading,
  submittedLinks
}) => {
  const { language } = useAppContext();
  
  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-bold flex items-center mb-2">
          <Compass className="mr-2 h-5 w-5 text-primary" />
          {language === 'en' ? 'Navigation Management' : '导航管理'}
        </h2>
        <p className="text-sm text-muted-foreground mb-4">
          {language === 'en' 
            ? 'Manage your navigation links and view your submissions' 
            : '管理您的导航链接并查看您的提交'}
        </p>
      </div>
      
      <div className="space-y-6">
        <NavigationManager userId={userId} isLoading={isLoading} />
        
        <Card>
          <CardHeader>
            <CardTitle>
              {language === 'en' ? 'My Submissions' : '我的提交'}
            </CardTitle>
            <CardDescription>
              {language === 'en' 
                ?  'Track your submitted navigation links' 
                : '跟踪您提交的导航链接'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SubmittedLinksList links={submittedLinks} isLoading={isLoading} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default NavigationTabContent;
