
import React from 'react';
import EmailForm from '../EmailForm';
import EmailList from '../EmailList';
import EmailDialog from '../EmailDialog';

interface EmailsTabContentProps {
  emailData: any[];
  isLoading: boolean;
  onCopy: (text: string) => void;
  onDeleteEmail: (id: string) => void;
  onCreateEmail: (domain: string) => Promise<any>;
}

const EmailsTabContent: React.FC<EmailsTabContentProps> = ({
  emailData,
  isLoading,
  onCopy,
  onDeleteEmail,
  onCreateEmail
}) => {
  const [openEmailId, setOpenEmailId] = React.useState<string | null>(null);
  const [showEmailDialog, setShowEmailDialog] = React.useState(false);

  return (
    <div className="space-y-4">
      <EmailForm 
        onCreateEmail={onCreateEmail}
        isLoading={isLoading} 
      />
      <EmailList 
        emailData={emailData} 
        onCopy={onCopy} 
        onDelete={onDeleteEmail} 
        onOpenEmail={(id) => {
          setOpenEmailId(id);
          setShowEmailDialog(true);
        }} 
        isLoading={isLoading} 
      />
      
      {openEmailId && showEmailDialog && (
        <EmailDialog 
          emailId={openEmailId} 
          open={showEmailDialog}
          onOpenChange={setShowEmailDialog}
          emailAddress={emailData.find(email => email.id === openEmailId)?.email_address || ''}
        />
      )}
    </div>
  );
};

export default EmailsTabContent;
