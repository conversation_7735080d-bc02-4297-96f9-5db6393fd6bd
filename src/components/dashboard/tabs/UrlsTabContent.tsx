import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Copy, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import UrlList from '@/components/dashboard/UrlList';
import DomainList from '@/components/dashboard/DomainList';
import DomainForm from '@/components/admin/domains/DomainForm'; // Added for domain submission
import { useDomainWhitelist } from '@/hooks/domain-whitelist/useDomainWhitelist'; // Added for domain submission
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';

const UrlsTabContent = () => {
  const [urls, setUrls] = useState([]);
  // const [newUrl, setNewUrl] = useState(''); // Removed for regular users
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { language } = useAppContext();
  const { submitDomain, isLoading: isDomainSubmitting } = useDomainWhitelist(); // Added for domain submission

  const fetchUrls = useCallback(async () => {
    setIsLoading(true);
    try {
      let data: any[] = [];

      if (isUsingSupabase()) {
        // 使用 Supabase
        const { supabase } = await import('@/integrations/supabase/client');
        const { data: supabaseData, error } = await supabase
          .from('short_urls')
          .select('*');

        if (error) {
          console.error('Error fetching URLs:', error);
          toast({
            variant: 'destructive',
            description: language === 'en' ? 'Failed to fetch URLs.' : '无法获取网址。',
          });
          return;
        }

        data = supabaseData || [];
      } else {
        // 使用 Go 后端
        try {
          const response = await apiClient.get('/shorturls');
          data = Array.isArray(response.data) ? response.data : [];
        } catch (error) {
          console.error('Error fetching URLs from Go backend:', error);
          toast({
            variant: 'destructive',
            description: language === 'en' ? 'Failed to fetch URLs.' : '无法获取网址。',
          });
          data = []; // 确保即使出错也设置为空数组
        }
      }

      setUrls(data);
    } catch (error) {
      console.error('Error fetching URLs:', error);
      toast({
        variant: 'destructive',
        description: language === 'en' ? 'Failed to fetch URLs.' : '无法获取网址。',
      });
    } finally {
      setIsLoading(false);
    }
  }, [language, toast]);

  useEffect(() => {
    fetchUrls();
  }, [fetchUrls]);

  // const handleAddUrl = async () => {
  //   // Implement your add URL logic here
  //   console.log('Adding URL:', newUrl);
  //   setNewUrl('');
  // }; // Removed for regular users

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      description: language === 'en' ? 'URL copied to clipboard' : '网址已复制到剪贴板',
    });
  };

  // const handleDelete = async (id: string) => {
  //   setIsLoading(true);
  //   try {
  //     // Fix: use short_urls instead of shortened_urls
  //     const { error } = await supabase
  //       .from('short_urls')
  //       .delete()
  //       .eq('id', id);

  //     if (error) {
  //       console.error('Error deleting URL:', error);
  //       toast({
  //         variant: 'destructive',
  //         description: language === 'en' ? 'Failed to delete URL.' : '无法删除网址。',
  //       });
  //       return;
  //     }

  //     setUrls(urls.filter((url) => url.id !== id));
  //     toast({
  //       description: language === 'en' ? 'URL deleted successfully.' : '网址已成功删除。',
  //     });
  //   } catch (error) {
  //     console.error('Error deleting URL:', error);
  //     toast({
  //       variant: 'destructive',
  //       description: language === 'en' ? 'Failed to delete URL.' : '无法删除网址。',
  //     });
  //   } finally {
  //     setIsLoading(false);
  //   }
  // }; // Removed for regular users

  // Add a handleRefresh function
  const handleRefresh = async () => {
    await fetchUrls();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{language === 'en' ? 'Submit Domain for Whitelist' : '提交域名到白名单'}</CardTitle>
        </CardHeader>
        <CardContent>
          <DomainForm
            onAddDomain={submitDomain}
            isLoading={isDomainSubmitting}
            showNotification={true}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{language === 'en' ? 'Your URLs' : '您的网址'}</CardTitle>
        </CardHeader>
        <CardContent>
          <UrlList
            urlData={urls}
            onCopy={handleCopy}
            // onDelete={handleDelete} // Removed for regular users
            isLoading={isLoading}
            onRefresh={handleRefresh}
          />
        </CardContent>
      </Card>

      {/* 域名白名单 */}
      <DomainList />
    </div>
  );
};

export default UrlsTabContent;
