import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Save } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { DragDropContext } from 'react-beautiful-dnd';
import { useFeatureOrdering, FeatureComponent } from '@/hooks/useFeatureOrdering';
import FeaturesHeader from '../features/FeaturesHeader';
import FeaturesList from '../features/FeaturesList';

interface FeaturesOrderingTabProps {
  userId: string;
  isLoading?: boolean;
}

const FeaturesOrderingTab: React.FC<FeaturesOrderingTabProps> = ({ userId, isLoading: externalLoading }) => {
  const { language } = useAppContext();
  const { toast } = useToast();

  const initialComponents: FeatureComponent[] = [
    {
      id: 'url-shortener',
      name: { en: 'URL Shortener', zh: '缩短网址' },
      icon: 'link',
      visible: true
    },
    {
      id: 'temp-email',
      name: { en: 'Temporary Email', zh: '临时邮箱' },
      icon: 'mail',
      visible: true
    },
    {
      id: 'navigation',
      name: { en: 'My Navigation', zh: '我的导航' },
      icon: 'compass',
      visible: true
    },
    {
      id: 'features-order',
      name: { en: 'Dashboard Layout', zh: '控制台布局' },
      icon: 'layout',
      visible: true
    }
  ];

  const { 
    components, 
    setComponents,
    saving, 
    loading, 
    saveConfiguration 
  } = useFeatureOrdering(userId, 'dashboard', initialComponents);

  const handleDragEnd = (result) => {
    if (!result.destination) return;
    
    if (
      result.destination.droppableId === result.source.droppableId &&
      result.destination.index === result.source.index
    ) {
      return;
    }
    
    const items = Array.from(components);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    setComponents(items);
    
    toast({
      description: language === 'en' 
        ? 'Order updated. Remember to save your changes.'
        : '顺序已更新。记得保存您的更改。',
      duration: 2000,
    });
  };

  const handleVisibilityToggle = (id: string) => {
    setComponents(prevComponents => 
      prevComponents.map(component => 
        component.id === id 
          ? { ...component, visible: !component.visible } 
          : component
      )
    );
    
    toast({
      description: language === 'en' 
        ? 'Visibility updated. Remember to save your changes.'
        : '可见性已更新。记得保存您的更改。',
      duration: 2000,
    });
  };

  const resetToDefault = () => {
    setComponents(initialComponents);
    toast({
      description: language === 'en' 
        ? 'Reset to default settings. Remember to save your changes.'
        : '已重置为默认设置。记得保存您的更改。',
      duration: 2000,
    });
  };

  const isComponentLoading = externalLoading || loading;

  if (isComponentLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {language === 'en' ? 'Dashboard Layout' : '控制台布局'}
        </CardTitle>
        <CardDescription>
          {language === 'en' 
            ? 'Customize which features are visible and in what order they appear in your dashboard.'
            : '自定义在您的控制台中显示哪些功能及其显示顺序。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <FeaturesHeader
            language={language}
            onReset={resetToDefault}
            isSaving={saving}
          />
          <DragDropContext onDragEnd={handleDragEnd}>
            <FeaturesList
              components={components}
              onVisibilityToggle={handleVisibilityToggle}
              language={language}
            />
          </DragDropContext>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button 
          onClick={saveConfiguration} 
          disabled={saving}
        >
          {saving && <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>}
          <Save className="mr-2 h-4 w-4" />
          {language === 'en' ? 'Save Layout' : '保存布局'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default FeaturesOrderingTab;
