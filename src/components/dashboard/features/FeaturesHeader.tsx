
import React from 'react';
import { Button } from '@/components/ui/button';
import { Info } from 'lucide-react';

interface FeaturesHeaderProps {
  language: string;
  onReset: () => void;
  isSaving: boolean;
}

const FeaturesHeader: React.FC<FeaturesHeaderProps> = ({
  language,
  onReset,
  isSaving,
}) => {
  return (
    <>
      <div className="flex justify-between mb-6">
        <div>
          <h3 className="text-lg font-medium">
            {language === 'en' ? 'Arrange Items' : '排列项目'}
          </h3>
          <p className="text-sm text-muted-foreground">
            {language === 'en' 
              ? 'Drag and drop to change the order of features in your dashboard.'
              : '拖放以更改控制台中功能的顺序。'}
          </p>
        </div>
        <Button 
          variant="outline" 
          onClick={onReset}
          disabled={isSaving}
        >
          {language === 'en' ? 'Reset to Default' : '重置为默认设置'}
        </Button>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4 flex items-center text-sm text-blue-700">
        <Info className="h-5 w-5 mr-2 text-blue-500" />
        <p>
          {language === 'en' 
            ? 'Drag and drop items to reorder them. Don\'t forget to save your changes.' 
            : '拖放项目以重新排序。记得保存您的更改。'}
        </p>
      </div>
    </>
  );
};

export default FeaturesHeader;
