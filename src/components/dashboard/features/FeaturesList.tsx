import React from 'react';
import { Droppable } from 'react-beautiful-dnd';
import { FeatureComponent } from '@/hooks/useFeatureOrdering';
import FeatureItem from './FeatureItem';

interface FeaturesListProps {
  components: FeatureComponent[];
  onVisibilityToggle: (id: string) => void;
  language: string;
}

const FeaturesList: React.FC<FeaturesListProps> = ({
  components,
  onVisibilityToggle,
  language,
}) => {
  return (
    <Droppable 
      droppableId="dashboard-features"
      type="features"
    >
      {(provided, snapshot) => (
        <ul 
          className={`space-y-2 min-h-[200px] transition-colors duration-200 rounded-md p-1 ${
            snapshot.isDraggingOver ? 'bg-muted/50 border border-dashed border-primary/50' : ''
          }`}
          {...provided.droppableProps}
          ref={provided.innerRef}
        >
          {components.map((component, index) => (
            <FeatureItem
              key={component.id}
              component={component}
              index={index}
              onVisibilityToggle={onVisibilityToggle}
              language={language}
            />
          ))}
          {provided.placeholder}
        </ul>
      )}
    </Droppable>
  );
};

export default FeaturesList;
