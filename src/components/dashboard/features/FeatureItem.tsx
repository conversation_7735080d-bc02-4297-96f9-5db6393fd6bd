import React from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { GripVertical } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FeatureComponent } from '@/hooks/useFeatureOrdering';

interface FeatureItemProps {
  component: FeatureComponent;
  index: number;
  onVisibilityToggle: (id: string) => void;
  language: string;
}

const FeatureItem: React.FC<FeatureItemProps> = ({
  component,
  index,
  onVisibilityToggle,
  language,
}) => {
  return (
    <Draggable 
      key={component.id} 
      draggableId={component.id} 
      index={index}
      isDragDisabled={false}
    >
      {(provided, snapshot) => (
        <li 
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`flex items-center p-3 bg-card rounded-md border 
            ${snapshot.isDragging 
              ? 'border-primary shadow-lg' 
              : 'border-border hover:border-muted-foreground/30'} 
            transition-all ${snapshot.isDragging ? 'z-50 opacity-90' : ''}`}
          style={{
            ...provided.draggableProps.style,
            ...(snapshot.isDragging ? { zIndex: 9999 } : {})
          }}
        >
          <div 
            className={`mr-3 p-1 rounded-md ${snapshot.isDragging ? 'bg-primary/20' : 'bg-muted/50'} cursor-grab active:cursor-grabbing`} 
            {...provided.dragHandleProps}
          >
            <GripVertical className={`h-5 w-5 ${snapshot.isDragging ? 'text-primary' : 'text-muted-foreground'}`} />
          </div>
          <div className="flex-1">
            <p className="font-medium">
              {language === 'en' ? component.name.en : component.name.zh}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox 
              id={`dashboard-visibility-${component.id}`}
              checked={component.visible}
              onCheckedChange={() => onVisibilityToggle(component.id)}
            />
            <Label htmlFor={`dashboard-visibility-${component.id}`} className="cursor-pointer mr-4">
              {language === 'en' ? 'Visible' : '显示'}
            </Label>
          </div>
        </li>
      )}
    </Draggable>
  );
};

export default FeatureItem;
