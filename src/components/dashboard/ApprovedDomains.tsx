
import React from 'react';
import { <PERSON>, <PERSON>Header, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Globe } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { Skeleton } from '@/components/ui/skeleton';

interface ApprovedDomainsProps {
  domains: string[];
  isLoading: boolean;
}

const ApprovedDomains: React.FC<ApprovedDomainsProps> = ({ domains, isLoading }) => {
  const { language } = useAppContext();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{language === 'en' ? 'Approved Domains' : '已批准的域名'}</CardTitle>
          <CardDescription>
            {language === 'en' 
              ? 'List of approved domains for URL shortening.' 
              : '用于URL缩短的已批准域名列表。'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'en' ? 'Approved Domains' : '已批准的域名'}</CardTitle>
        <CardDescription>
          {language === 'en' 
            ? 'List of approved domains for URL shortening.' 
            : '用于URL缩短的已批准域名列表。'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {domains.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              {language === 'en' ? 'No approved domains found.' : '未找到已批准的域名。'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {domains.map((domain, index) => (
              <div 
                key={index}
                className="flex items-center rounded-md border bg-card p-2.5 text-sm"
              >
                <div className="bg-primary/10 text-primary rounded-full p-1 mr-2">
                  <Globe className="h-3.5 w-3.5" />
                </div>
                <span className="truncate font-medium">
                  {domain}
                </span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ApprovedDomains;
