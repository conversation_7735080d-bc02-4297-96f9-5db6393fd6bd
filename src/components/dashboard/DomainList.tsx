import React from 'react';
import { useDomainWhitelist } from '@/hooks/domain-whitelist/useDomainWhitelist';
import { useAppContext } from '@/context/AppContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Globe, Trash2, ExternalLink } from 'lucide-react';
import { Badge } from '@/components/ui/badge'; // Import Badge for compact display
import { Skeleton } from '@/components/ui/skeleton';

interface DomainListProps {
  showDelete?: boolean;
}

const DomainList: React.FC<DomainListProps> = ({ showDelete = false }) => {
  const { language } = useAppContext();
  const { 
    domains, 
    isLoading, 
    deleteDomain 
  } = useDomainWhitelist();

  // 处理删除域名
  const handleDelete = async (id: string) => {
    await deleteDomain(id);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5 text-primary" />
          {language === 'en' ? 'Domain Whitelist' : '域名白名单'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center gap-2">
                <Skeleton className="h-8 w-full" />
              </div>
            ))}
          </div>
        ) : domains.length === 0 ? (
          <div className="text-center p-4 border rounded-md">
            <p className="text-muted-foreground">
              {language === 'en' 
                ? 'No domains in whitelist yet' 
                : '白名单中尚无域名'}
            </p>
          </div>
        ) : (
          <div className="flex flex-wrap gap-2">
            {domains.map((domain) => (
              <Badge key={domain.id} variant="secondary" className="flex items-center gap-1.5 pr-1 pl-2 py-1 text-sm">
                <Globe className="h-3.5 w-3.5 text-muted-foreground" />
                <span>{domain.domain}</span>
                <a 
                  href={`https://${domain.domain}`} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-600"
                  onClick={(e) => e.stopPropagation()} // Prevent card click if any
                >
                  <ExternalLink className="h-3 w-3" />
                </a>
                {showDelete && (
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card click if any
                      handleDelete(domain.id);
                    }}
                    className="h-5 w-5 text-destructive hover:text-destructive hover:bg-destructive/10 ml-1 p-0.5 rounded-full"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                )}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DomainList;