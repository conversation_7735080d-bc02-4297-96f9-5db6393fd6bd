
export function generateRandomShortCode(length = 6) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

export function extractDomain(url: string) {
  try {
    // Add protocol if missing
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    
    const urlObj = new URL(url);
    return urlObj.hostname.replace(/^www\./, '');
  } catch (e) {
    return null;
  }
}

// Function to calculate expiration date
export function calculateExpirationDate(expirationType: string): Date | null {
  const now = new Date();
  
  switch (expirationType) {
    case 'hour':
      return new Date(now.getTime() + 60 * 60 * 1000);
    case 'day':
      return new Date(now.getTime() + 24 * 60 * 60 * 1000);
    case 'week':
      return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    case 'month':
      return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    case 'permanent':
      return null;
    default:
      return new Date(now.getTime() + 24 * 60 * 60 * 1000); // Default to 1 day
  }
}
