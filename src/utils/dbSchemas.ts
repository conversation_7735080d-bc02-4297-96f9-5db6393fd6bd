
// This file contains SQL statements that should be executed to set up the required database tables
// Run these in the Supabase SQL editor

/*
-- Create email_domains table
CREATE TABLE IF NOT EXISTS public.email_domains (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    domain TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add initial default domain (should be run after table creation)
INSERT INTO public.email_domains (domain)
VALUES ('g2.al')
ON CONFLICT (domain) DO NOTHING;

-- Add RLS policies for email_domains
ALTER TABLE public.email_domains ENABLE ROW LEVEL SECURITY;

-- Only allow admins to manage domains
CREATE POLICY "Allow admins to manage email domains" ON public.email_domains
    USING (EXISTS (SELECT 1 FROM user_roles WHERE user_id = auth.uid() AND role = 'admin'));
    
-- Everyone can view domains
CREATE POLICY "Allow everyone to view email domains" ON public.email_domains
    FOR SELECT
    USING (true);
*/
