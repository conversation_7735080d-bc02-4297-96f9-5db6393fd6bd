
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link2 } from "lucide-react";
import { useAppContext } from "@/context/AppContext";
import UrlForm from "./url-shortener/UrlForm";
import UrlResult from "./url-shortener/UrlResult";
import { useUrlShortener } from "@/hooks/url-shortener/useUrlShortener";

interface UrlShortenerProps {
  approvedDomains: string[];
}

const UrlShortener: React.FC<UrlShortenerProps> = ({ approvedDomains }) => {
  const { language } = useAppContext();
  const {
    url,
    setUrl,
    isLoading,
    shortUrl,
    showQRCode,
    expirationType,
    setExpirationType,
    handleShortenUrl,
    user
  } = useUrlShortener(approvedDomains);

  return (
    <Card className="overflow-hidden border-2 border-primary/10 shadow-md">
      <CardHeader className="bg-gradient-to-r from-brand-50 to-accent1-50 dark:from-brand-950/30 dark:to-accent1-950/30">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
            <Link2 className="h-5 w-5 text-primary" />
          </div>
          <div className="text-left">
            <CardTitle>{language === 'en' ? 'Shorten a URL' : '缩短网址'}</CardTitle>
            <CardDescription>
              {language === 'en' 
                ? 'Paste a long URL to create a shorter, more manageable link.'
                : '粘贴长URL以创建更短、更易管理的链接。'}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <UrlForm 
          url={url}
          setUrl={setUrl}
          expirationType={expirationType}
          setExpirationType={setExpirationType}
          isLoading={isLoading}
          onSubmit={handleShortenUrl}
          approvedDomains={approvedDomains}
          user={user}
        />
        
        {shortUrl && (
          <UrlResult 
            shortUrl={shortUrl}
            showQRCode={showQRCode}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default UrlShortener;
