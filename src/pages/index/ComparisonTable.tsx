import { Check } from "lucide-react";
import { useAppContext } from "@/context/AppContext";

const ComparisonTable = () => {
  const { t, language } = useAppContext();
  
  return (
    <div className="mt-12 overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            <th className="py-4 px-6 text-center font-medium">{t('feature')}</th>
            <th className="py-4 px-6 text-center font-medium">{t('guestUsers')}</th>
            <th className="py-4 px-6 text-center font-medium text-primary">{t('registeredUsers')}</th>
          </tr>
        </thead>
        <tbody>
          {/* 短链功能 */}
          <tr className="border-b bg-muted/20">
            <td className="py-4 px-6 font-medium" colSpan={3}>
              {language === 'en' ? 'URL Shortening' : 'URL短链'}
            </td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('createShortUrls')}</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('permanentUrls')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('submitDomains')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('trackClicks')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          
          {/* 临时邮箱功能 */}
          <tr className="border-b bg-muted/20">
            <td className="py-4 px-6 font-medium" colSpan={3}>
              {language === 'en' ? 'Temporary Email' : '临时邮箱'}
            </td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('tempEmails')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('emailsUpTo30Days')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          
          {/* 导航站功能 */}
          <tr className="border-b bg-muted/20">
            <td className="py-4 px-6 font-medium" colSpan={3}>
              {language === 'en' ? 'Navigation Directory' : '导航站'}
            </td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('browseCategories') || (language === 'en' ? 'Browse Categorized Resources' : '浏览分类资源')}</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('customCategories') || (language === 'en' ? 'Create Custom Categories' : '创建自定义分类')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('privateCollections') || (language === 'en' ? 'Private Collections' : '私人收藏')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('submitLinks') || (language === 'en' ? 'Submit New Links' : '提交新链接')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          
          {/* 今日热榜功能 */}
          <tr className="border-b bg-muted/20">
            <td className="py-4 px-6 font-medium" colSpan={3}>
              {language === 'en' ? "Today's Hot List" : '今日热榜'}
            </td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('viewHotLists') || (language === 'en' ? 'View Trending Topics' : '查看热门话题')}</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('multiPlatforms') || (language === 'en' ? 'Multiple Platform Aggregation' : '多平台聚合')}</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('categoryFiltering') || (language === 'en' ? 'Category Filtering' : '分类筛选')}</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
          <tr className="border-b">
            <td className="py-4 px-6">{t('saveHotTopics') || (language === 'en' ? 'Save Favorite Topics' : '保存喜爱的话题')}</td>
            <td className="py-4 px-6 text-center">-</td>
            <td className="py-4 px-6 text-center"><Check className="h-5 w-5 text-green-500 mx-auto" /></td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default ComparisonTable;
