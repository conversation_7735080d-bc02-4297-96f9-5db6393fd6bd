import {useAppContext} from "@/context/AppContext";
import FeaturesTabs from "./FeaturesTabs";
import ComparisonTable from "./ComparisonTable";
import {ArrowRight} from "lucide-react";
import {Button} from "@/components/ui/button";
import {Link} from "react-router-dom";

const FeaturesSection = () => {
    const {t, language} = useAppContext();

    return (
        <section id="features" className="w-full py-16 md:py-24 lg:py-32 bg-gradient-to-b from-background to-muted/40">
            <div className="container px-4 md:px-6">
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                    <div className="inline-block px-3 py-1 text-sm rounded-full bg-primary/10 text-primary mb-4">
                        {language === 'en' ? 'Powerful Features' : '强大功能'}
                    </div>
                    <div className="space-y-2">
                        <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-brand-600">
                            {t('features')}
                        </h2>
                        <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                            {t('featuresDesc')}
                        </p>
                    </div>
                </div>

            </div>
        </section>
    );
};

export default FeaturesSection;
