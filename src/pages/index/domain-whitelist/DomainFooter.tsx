
import { useAppContext } from "@/context/AppContext";

interface DomainFooterProps {
  filteredCount: number;
  totalCount: number;
}

const DomainFooter: React.FC<DomainFooterProps> = ({ filteredCount, totalCount }) => {
  const { language } = useAppContext();

  return (
    <div className="mt-4 text-center text-sm text-muted-foreground">
      {language === 'en' 
        ? `Showing ${filteredCount} of ${totalCount} domains`
        : `显示 ${filteredCount} 个域名（共 ${totalCount} 个）`}
    </div>
  );
};

export default DomainFooter;
