
import { Globe, CheckCircle } from "lucide-react";

interface DomainsGridProps {
  domains: string[];
}

const DomainsGrid: React.FC<DomainsGridProps> = ({ domains }) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
      {domains.map((domain, index) => (
        <div 
          key={index}
          className="flex items-center rounded-md border bg-card p-2.5 text-sm"
        >
          <div className="bg-primary/10 text-primary rounded-full p-1 mr-2">
            <Globe className="h-3.5 w-3.5" />
          </div>
          <span className="truncate font-medium">
            {domain}
          </span>
          <CheckCircle className="h-3.5 w-3.5 ml-auto text-green-500" />
        </div>
      ))}
    </div>
  );
};

export default DomainsGrid;
