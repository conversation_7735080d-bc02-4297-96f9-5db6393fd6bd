
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { useAppContext } from "@/context/AppContext";

interface DomainSearchProps {
  searchDomain: string;
  setSearchDomain: (value: string) => void;
  whitelistView: "grid" | "table";
  setWhitelistView: (view: "grid" | "table") => void;
}

const DomainSearch: React.FC<DomainSearchProps> = ({ 
  searchDomain, 
  setSearchDomain, 
  whitelistView, 
  setWhitelistView 
}) => {
  const { language } = useAppContext();

  return (
    <div className="mb-6 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
      <div className="relative w-full sm:max-w-xs">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input 
          placeholder={language === 'en' ? 'Search domains...' : '搜索域名...'}
          className="pl-10"
          value={searchDomain}
          onChange={(e) => setSearchDomain(e.target.value)}
        />
      </div>
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">
          {language === 'en' ? 'View:' : '视图:'}
        </span>
        <Button 
          variant={whitelistView === 'grid' ? 'default' : 'outline'} 
          size="icon" 
          className="h-8 w-8"
          onClick={() => setWhitelistView('grid')}
        >
          <div className="grid grid-cols-2 gap-0.5">
            <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
            <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
            <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
            <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
          </div>
        </Button>
        <Button 
          variant={whitelistView === 'table' ? 'default' : 'outline'} 
          size="icon" 
          className="h-8 w-8"
          onClick={() => setWhitelistView('table')}
        >
          <div className="flex flex-col items-center justify-center gap-0.5">
            <div className="h-1 w-4 rounded-sm bg-current"></div>
            <div className="h-1 w-4 rounded-sm bg-current"></div>
            <div className="h-1 w-4 rounded-sm bg-current"></div>
          </div>
        </Button>
      </div>
    </div>
  );
};

export default DomainSearch;
