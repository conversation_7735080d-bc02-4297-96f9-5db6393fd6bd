
import { Globe, CheckCircle } from "lucide-react";
import { useAppContext } from "@/context/AppContext";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";

interface DomainsTableProps {
  domains: string[];
}

const DomainsTable: React.FC<DomainsTableProps> = ({ domains }) => {
  const { language } = useAppContext();

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">#</TableHead>
            <TableHead>{language === 'en' ? 'Domain' : '域名'}</TableHead>
            <TableHead className="text-right">{language === 'en' ? 'Status' : '状态'}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {domains.map((domain, index) => (
            <TableRow key={index}>
              <TableCell className="font-medium">{index + 1}</TableCell>
              <TableCell className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                {domain}
              </TableCell>
              <TableCell className="text-right">
                <div className="inline-flex items-center px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  {language === 'en' ? 'Approved' : '已批准'}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default DomainsTable;
