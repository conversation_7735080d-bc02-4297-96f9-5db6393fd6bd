
import { Globe } from "lucide-react";
import { useAppContext } from "@/context/AppContext";

const EmptyDomainState: React.FC = () => {
  const { language } = useAppContext();

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="bg-muted rounded-full p-3 mb-4">
        <Globe className="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium">
        {language === 'en' ? 'No domains found' : '未找到域名'}
      </h3>
      <p className="text-sm text-muted-foreground mt-1">
        {language === 'en' 
          ? 'Try adjusting your search or check back later.'
          : '尝试调整您的搜索或稍后再查看。'}
      </p>
    </div>
  );
};

export default EmptyDomainState;
