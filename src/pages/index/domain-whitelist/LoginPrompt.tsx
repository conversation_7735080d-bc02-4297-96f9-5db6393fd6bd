
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { LogIn } from "lucide-react";
import { useAppContext } from "@/context/AppContext";

const LoginPrompt: React.FC = () => {
  const { language } = useAppContext();

  return (
    <div className="bg-muted p-4 rounded-md mb-6 flex items-center justify-between">
      <div>
        <h3 className="font-medium mb-1">
          {language === 'en' 
            ? 'Login to submit new domains' 
            : '登录以提交新域名'}
        </h3>
        <p className="text-sm text-muted-foreground">
          {language === 'en' 
            ? 'You need to be logged in to submit domains for approval.' 
            : '您需要登录才能提交域名以获得批准。'}
        </p>
      </div>
      <Button asChild variant="outline">
        <Link to="/login" className="flex items-center gap-2">
          <LogIn className="h-4 w-4" />
          {language === 'en' ? 'Login' : '登录'}
        </Link>
      </Button>
    </div>
  );
};

export default LoginPrompt;
