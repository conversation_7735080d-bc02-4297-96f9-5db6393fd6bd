import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Globe } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAppContext } from "@/context/AppContext";
import { getCurrentUser } from "@/services/authService";
import { submitDomainForApproval } from "@/services/domainWhitelistService";

interface DomainFormProps {
  approvedDomains: string[];
  isUserLoggedIn: boolean;
}

const DomainForm: React.FC<DomainFormProps> = ({ approvedDomains, isUserLoggedIn }) => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [newDomain, setNewDomain] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Function to extract main domain from subdomain
  const extractMainDomain = (domain: string): string => {
    // First clean the domain (remove protocol, www, path, etc.)
    let cleanDomain = domain.trim();
    
    // Remove protocol if present
    cleanDomain = cleanDomain.replace(/^(https?:\/\/)?(www\.)?/i, '');
    
    // Remove path, query string, and fragment
    cleanDomain = cleanDomain.split('/')[0];
    
    // Check if it's likely an IP address (simple check)
    if (/^\d+\.\d+\.\d+\.\d+$/.test(cleanDomain)) {
      return cleanDomain;
    }
    
    // Extract main domain (e.g., example.com from sub.example.com)
    const parts = cleanDomain.split('.');
    if (parts.length > 2) {
      // Handle special cases like co.uk, com.au, etc.
      const secondLevelDomains = ['co', 'com', 'net', 'org', 'ac', 'edu', 'gov'];
      
      if (parts.length >= 3 && secondLevelDomains.includes(parts[parts.length - 2])) {
        // Return last three parts for domains like example.co.uk
        return parts.slice(-3).join('.');
      } else {
        // Standard case - return last two parts for domains like sub.example.com
        return parts.slice(-2).join('.');
      }
    }
    
    // It's already a main domain
    return cleanDomain;
  };

  const handleSubmitDomain = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newDomain) return;
    
    setIsSubmitting(true);
    try {
      // Extract main domain
      const mainDomain = extractMainDomain(newDomain.trim());
      
      if (approvedDomains.some(d => d.toLowerCase() === mainDomain.toLowerCase())) {
        toast({
          variant: "destructive",
          description: language === 'en' 
            ? `Domain ${mainDomain} already exists in whitelist` 
            : `域名 ${mainDomain} 已存在于白名单中`,
        });
        setIsSubmitting(false);
        return;
      }
      
      const user = await getCurrentUser();
      
      await submitDomainForApproval(mainDomain, user?.id?.toString());
      
      setNewDomain("");
      
      const message = language === 'en' 
        ? `Domain ${mainDomain} submitted for approval` 
        : `域名 ${mainDomain} 已提交等待审核`;
        
      toast({ description: message });
      
      if (newDomain.trim() !== mainDomain) {
        toast({
          description: language === 'en' 
            ? `Submitted main domain (${mainDomain}) instead of subdomain` 
            : `已提交主域名 (${mainDomain}) 而非子域名`,
        });
      }
    } catch (error) {
      console.error('Error submitting domain:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to submit domain" : "提交域名失败",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return isUserLoggedIn ? (
    <form onSubmit={handleSubmitDomain} className="mb-6">
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="relative flex-1">
          <Globe className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={language === 'en' 
              ? "Submit a domain for approval (e.g., example.com)" 
              : "提交域名以获得批准（例如，example.com）"}
            value={newDomain}
            onChange={(e) => setNewDomain(e.target.value)}
            className="pl-10"
            disabled={isSubmitting}
          />
        </div>
        <Button 
          type="submit" 
          className="whitespace-nowrap"
          disabled={isSubmitting || !newDomain}
        >
          {isSubmitting
            ? (language === 'en' ? 'Submitting...' : '提交中...')
            : (language === 'en' ? 'Submit Domain' : '提交域名')}
        </Button>
      </div>
      <p className="text-xs text-muted-foreground mt-2">
        {language === 'en' 
          ? 'Your submission will be reviewed by an administrator before being added to the whitelist.'
          : '您的提交将在添加到白名单之前由管理员审核。'}
      </p>
    </form>
  ) : null;
};

export default DomainForm;
