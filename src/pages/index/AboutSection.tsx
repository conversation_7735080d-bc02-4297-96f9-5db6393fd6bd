
import { ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAppContext } from "@/context/AppContext";
import { Link } from "react-router-dom";

const AboutSection = () => {
  const { t } = useAppContext();
  
  return (
    <section id="about" className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-muted/30 to-background">
      <div className="container px-4 md:px-6">
        <div className="grid gap-10 lg:grid-cols-2">
          <div className="flex flex-col justify-center space-y-4">
            <div className="space-y-2">
              <div className="inline-block p-2 bg-primary/10 rounded-full mb-2">
                <ExternalLink className="h-5 w-5 text-primary" />
              </div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">{t('aboutUs')}</h2>
              <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t('aboutUsDesc')}
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-bold">{t('ourMission')}</h3>
              <p className="max-w-[600px] text-muted-foreground md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                {t('ourMissionDesc')}
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-bold">{t('privacyFocus')}</h3>
              <p className="max-w-[600px] text-muted-foreground md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                {t('privacyFocusDesc')}
              </p>
            </div>
            <div className="flex gap-4">
              <Button asChild variant="outline" className="rounded-full shadow-sm border border-primary/20 hover:bg-primary/5">
                <a href="https://github.com" target="_blank" rel="noopener noreferrer">
                  GitHub <ExternalLink className="ml-2 h-4 w-4" />
                </a>
              </Button>
              <Button asChild variant="outline" className="rounded-full shadow-sm border border-primary/20 hover:bg-primary/5">
                <Link to="/about">
                  {t('learnMore')}
                </Link>
              </Button>
            </div>
          </div>
          <div className="flex items-center justify-center">
            <div className="relative h-[400px] w-full overflow-hidden rounded-xl bg-gradient-to-br from-background to-muted/50 border shadow-sm">
              <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                <div className="max-w-sm space-y-2 text-center">
                  <div className="inline-block p-2 bg-primary/10 rounded-full mb-4">
                    <ExternalLink className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-2xl font-bold">{t('joinUs')}</h3>
                  <p className="text-sm">
                    {t('joinUsDesc')}
                  </p>
                  <Button asChild className="mt-4 bg-gradient-to-r from-brand-500 to-accent1-500 hover:from-brand-600 hover:to-accent1-600">
                    <Link to="/register">
                      {t('signUp')}
                    </Link>
                  </Button>
                </div>
              </div>
              
              {/* Decorative Elements */}
              <div className="absolute -top-16 -right-16 w-32 h-32 bg-primary/5 rounded-full blur-xl"></div>
              <div className="absolute -bottom-16 -left-16 w-40 h-40 bg-accent1-500/5 rounded-full blur-xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
