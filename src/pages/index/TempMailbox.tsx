import { useState, useEffect } from "react";
import { useAppContext } from "@/context/AppContext";
import { Mail, Copy, Eye, Clock, RefreshCw, AlertCircle } from "lucide-react";
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";

interface Email {
  id: string;
  from: string;
  subject: string;
  body: string;
  received_at: string;
  read: boolean;
}

const TempMailbox: React.FC = () => {
  const { language, user } = useAppContext();
  const { toast } = useToast();

  // 临时邮箱地址
  const [tempEmail, setTempEmail] = useState<string>("");
  const [generatingEmail, setGeneratingEmail] = useState(false);

  // 邮件列表状态
  const [emails, setEmails] = useState<Email[]>([]);
  const [loadingEmails, setLoadingEmails] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);

  // 错误状态
  const [error, setError] = useState<string | null>(null);

  // 对话框状态
  const [dialogOpen, setDialogOpen] = useState(false);

  // 从localStorage加载临时邮箱地址
  useEffect(() => {
    const savedEmail = localStorage.getItem("temp_email");
    if (savedEmail) {
      setTempEmail(savedEmail);
    }
  }, []);

  // 生成临时邮箱地址
  const generateTempEmail = async () => {
    if (tempEmail) {
      toast({
        title: language === "en" ? "Email already generated" : "邮箱已生成",
        description: language === "en"
          ? "You already have a temporary email address"
          : "您已经有一个临时邮箱地址",
      });
      return;
    }

    setGeneratingEmail(true);
    setError(null);

    try {
      // 生成随机字符串作为邮箱前缀
      const randomChars = Math.random().toString(36).substring(2, 10);
      const emailDomain = "example-temp-mail.com"; // 示例域名，实际项目中应替换为真实的临时邮箱域名
      const email = `${randomChars}@${emailDomain}`;

      // 实际项目中，这里应该调用后端API来创建临时邮箱
      // 由于这是前端模拟，我们直接设置并保存
      setTempEmail(email);
      localStorage.setItem("temp_email", email);

      toast({
        title: language === "en" ? "Email Generated" : "邮箱已生成",
        description: language === "en"
          ? "Your temporary email has been generated"
          : "您的临时邮箱已生成",
      });

      // 自动获取邮件
      fetchEmails(email);
    } catch (error) {
      console.error("生成临时邮箱失败:", error);
      setError(language === "en"
        ? "Failed to generate temporary email"
        : "生成临时邮箱失败");
    } finally {
      setGeneratingEmail(false);
    }
  };

  // 复制临时邮箱地址到剪贴板
  const copyEmailToClipboard = () => {
    if (!tempEmail) return;

    navigator.clipboard.writeText(tempEmail).then(() => {
      toast({
        title: language === "en" ? "Copied to clipboard" : "已复制到剪贴板",
        description: language === "en"
          ? "Email address copied to clipboard"
          : "邮箱地址已复制到剪贴板",
      });
    }).catch(err => {
      console.error("复制到剪贴板失败:", err);
      toast({
        variant: "destructive",
        title: language === "en" ? "Copy failed" : "复制失败",
        description: language === "en"
          ? "Failed to copy email address"
          : "复制邮箱地址失败",
      });
    });
  };

  // 获取邮件列表（模拟数据）
  const fetchEmails = async (email: string = tempEmail) => {
    if (!email) return;

    setLoadingEmails(true);
    setError(null);

    try {
      // 实际项目中，这里应该调用API获取真实的邮件列表
      // 这里为了演示，使用模拟数据
      setTimeout(() => {
        const mockEmails: Email[] = [
          {
            id: "1",
            from: "<EMAIL>",
            subject: "欢迎使用临时邮箱",
            body: "<p>感谢您使用我们的临时邮箱服务！</p><p>此邮箱将为您提供临时的电子邮件地址，用于接收验证邮件、注册确认等。</p><p>邮箱有效期为24小时，请在此期间完成您的操作。</p>",
            received_at: new Date().toISOString(),
            read: false
          },
          {
            id: "2",
            from: "<EMAIL>",
            subject: "每周科技更新",
            body: "<h2>本周热门科技新闻</h2><ul><li>苹果发布新款iPhone</li><li>谷歌推出新的AI工具</li><li>特斯拉宣布新能源电池技术突破</li></ul>",
            received_at: new Date(Date.now() - 3600000).toISOString(), // 1小时前
            read: false
          }
        ];

        setEmails(mockEmails);
        setLoadingEmails(false);
      }, 1000); // 模拟网络请求延迟
    } catch (error) {
      console.error("获取邮件失败:", error);
      setError(language === "en"
        ? "Failed to fetch emails"
        : "获取邮件失败");
      setLoadingEmails(false);
    }
  };

  // 清除临时邮箱
  const clearTempEmail = () => {
    const confirmed = window.confirm(
      language === "en"
        ? "Are you sure you want to delete this temporary email? This action cannot be undone."
        : "您确定要删除这个临时邮箱吗？此操作无法撤销。"
    );

    if (confirmed) {
      localStorage.removeItem("temp_email");
      setTempEmail("");
      setEmails([]);
      setSelectedEmail(null);

      toast({
        title: language === "en" ? "Email Deleted" : "邮箱已删除",
        description: language === "en"
          ? "Your temporary email has been deleted"
          : "您的临时邮箱已删除",
      });
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(language === "en" ? "en-US" : "zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(date);
  };

  // 查看邮件详情
  const viewEmail = (email: Email) => {
    setSelectedEmail(email);

    // 标记为已读
    const updatedEmails = emails.map(e =>
      e.id === email.id ? { ...e, read: true } : e
    );
    setEmails(updatedEmails);

    // 打开对话框
    setDialogOpen(true);
  };

  return (
    <Card className="border-2 border-accent1-100/50 shadow-md dark:border-accent1-800/30">
      <CardHeader className="bg-gradient-to-r from-brand-50 to-accent1-50 dark:from-brand-950/30 dark:to-accent1-950/30">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-accent1-100 dark:bg-accent1-800/50">
            <Mail className="h-5 w-5 text-accent1-600 dark:text-accent1-400" />
          </div>
          <div className="text-left">
            <CardTitle>{language === "en" ? "Temporary Email" : "临时邮箱"}</CardTitle>
            <CardDescription>
              {language === "en"
                ? "Secure, temporary email for your privacy"
                : "安全、临时的电子邮箱保护您的隐私"}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{language === "en" ? "Error" : "错误"}</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!tempEmail ? (
          <div className="text-center py-4">
            <div className="mx-auto mb-4 bg-muted/30 p-6 rounded-full inline-flex">
              <Mail className="h-10 w-10 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {language === "en" ? "No Temporary Email" : "未创建临时邮箱"}
            </h3>
            <p className="text-muted-foreground mb-4 max-w-md mx-auto">
              {language === "en"
                ? "Generate a temporary email address to receive messages without using your personal email."
                : "生成一个临时邮箱地址，无需使用您的个人邮箱即可接收消息。"}
            </p>
            <Button
              onClick={generateTempEmail}
              disabled={generatingEmail}
              className="mt-2"
            >
              {generatingEmail && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
              {language === "en" ? "Generate Email" : "生成邮箱"}
            </Button>
          </div>
        ) : (
          <div>
            <div className="mb-6 border bg-muted/20 rounded-lg p-4">
              <Label className="text-xs text-muted-foreground mb-1 block">
                {language === "en" ? "Your temporary email address" : "您的临时邮箱地址"}
              </Label>
              <div className="flex items-center gap-2">
                <Input
                  value={tempEmail}
                  readOnly
                  className="font-mono bg-background"
                />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={copyEmailToClipboard}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{language === "en" ? "Copy to clipboard" : "复制到剪贴板"}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={() => fetchEmails()}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      {language === "en" ? "View Inbox" : "查看收件箱"}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>
                        {language === "en" ? "Temporary Email Inbox" : "临时邮箱收件箱"}
                      </DialogTitle>
                      <DialogDescription>
                        {language === "en"
                          ? `Inbox for ${tempEmail}`
                          : `${tempEmail} 的收件箱`}
                      </DialogDescription>
                    </DialogHeader>

                    <div className="flex h-[400px] border rounded-md">
                      {/* 左侧邮件列表 */}
                      <div className="w-1/3 border-r overflow-hidden">
                        <div className="p-3 border-b flex justify-between items-center">
                          <span className="text-sm font-medium">
                            {language === "en" ? "Inbox" : "收件箱"}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => fetchEmails()}
                            disabled={loadingEmails}
                          >
                            <RefreshCw className={`h-4 w-4 ${loadingEmails ? 'animate-spin' : ''}`} />
                          </Button>
                        </div>
                        <ScrollArea className="h-[355px]">
                          {loadingEmails ? (
                            <div className="p-4 space-y-4">
                              {[1, 2, 3].map(i => (
                                <div key={i} className="flex flex-col gap-2">
                                  <Skeleton className="h-4 w-3/4" />
                                  <Skeleton className="h-3 w-1/2" />
                                </div>
                              ))}
                            </div>
                          ) : emails.length === 0 ? (
                            <div className="p-4 text-center text-muted-foreground text-sm">
                              {language === "en"
                                ? "No emails yet. Emails will appear here when received."
                                : "尚无邮件。收到的邮件将显示在这里。"}
                            </div>
                          ) : (
                            <div>
                              {emails.map(email => (
                                <div
                                  key={email.id}
                                  className={`p-3 border-b cursor-pointer hover:bg-muted/30 transition-colors ${
                                    selectedEmail?.id === email.id ? 'bg-muted/40' : ''
                                  } ${!email.read ? 'font-medium' : ''}`}
                                  onClick={() => viewEmail(email)}
                                >
                                  <div className="flex justify-between items-center">
                                    <div className="truncate text-sm">
                                      {email.from}
                                    </div>
                                    {!email.read && (
                                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                                    )}
                                  </div>
                                  <div className="text-sm font-medium truncate">
                                    {email.subject}
                                  </div>
                                  <div className="text-xs text-muted-foreground flex items-center mt-1">
                                    <Clock className="h-3 w-3 mr-1" />
                                    {formatDate(email.received_at)}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </ScrollArea>
                      </div>

                      {/* 右侧邮件内容 */}
                      <div className="w-2/3 overflow-hidden">
                        {selectedEmail ? (
                          <div className="flex flex-col h-full">
                            <div className="p-4 border-b">
                              <h3 className="font-medium">
                                {selectedEmail.subject}
                              </h3>
                              <div className="flex justify-between text-sm mt-1">
                                <span className="text-muted-foreground">
                                  {language === "en" ? "From: " : "发件人："}{selectedEmail.from}
                                </span>
                                <span className="text-muted-foreground">
                                  {formatDate(selectedEmail.received_at)}
                                </span>
                              </div>
                            </div>
                            <ScrollArea className="flex-1 p-4">
                              <div
                                className="prose dark:prose-invert max-w-none"
                                dangerouslySetInnerHTML={{ __html: selectedEmail.body }}
                              />
                            </ScrollArea>
                          </div>
                        ) : (
                          <div className="flex items-center justify-center h-full text-muted-foreground">
                            {language === "en"
                              ? "Select an email to view its contents"
                              : "选择一封邮件以查看其内容"}
                          </div>
                        )}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
              <div className="mt-3 flex justify-between items-center">
                <div className="text-xs text-muted-foreground flex items-center">
                  <Clock className="h-3.5 w-3.5 mr-1" />
                  {language === "en" ? "Valid for 24 hours" : "有效期为24小时"}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-destructive hover:text-destructive/80"
                  onClick={clearTempEmail}
                >
                  {language === "en" ? "Delete Email" : "删除邮箱"}
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">
                {language === "en" ? "Recent Messages" : "最近消息"}
              </h3>
              {loadingEmails ? (
                <div className="space-y-3">
                  {[1, 2].map(i => (
                    <div key={i} className="flex items-center gap-2 animate-pulse">
                      <Skeleton className="h-6 w-6 rounded-full" />
                      <div className="space-y-1.5 flex-1">
                        <Skeleton className="h-3.5 w-1/3" />
                        <Skeleton className="h-2.5 w-2/3" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : emails.length === 0 ? (
                <div className="text-center py-3 text-muted-foreground text-sm border rounded-lg">
                  {language === "en"
                    ? "No messages yet. Check back later."
                    : "暂无消息。请稍后再查看。"}
                </div>
              ) : (
                <div className="space-y-2">
                  {emails.slice(0, 2).map(email => (
                    <div
                      key={email.id}
                      className="p-2.5 border rounded-lg hover:bg-muted/20 transition-colors cursor-pointer"
                      onClick={() => viewEmail(email)}
                    >
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{email.from}</span>
                        {!email.read && (
                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        )}
                      </div>
                      <p className="text-sm truncate">{email.subject}</p>
                      <div className="text-xs text-muted-foreground mt-1">
                        {formatDate(email.received_at)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex justify-between px-6 py-4 border-t bg-muted/10">
        <div className="text-xs text-muted-foreground">
          {language === "en"
            ? "Emails are automatically deleted after 24 hours"
            : "邮件在24小时后自动删除"}
        </div>
        {emails.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDialogOpen(true)}
          >
            {language === "en" ? "View All" : "查看全部"}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default TempMailbox;