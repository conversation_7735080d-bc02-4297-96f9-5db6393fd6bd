import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Globe } from "lucide-react";
import { useAppContext } from "@/context/AppContext";
import { getCurrentUser } from "@/services/authService";
import DomainForm from "./domain-whitelist/DomainForm";
import DomainSearch from "./domain-whitelist/DomainSearch";
import DomainsGrid from "./domain-whitelist/DomainsGrid";
import DomainsTable from "./domain-whitelist/DomainsTable";
import EmptyDomainState from "./domain-whitelist/EmptyDomainState";
import DomainFooter from "./domain-whitelist/DomainFooter";
import LoginPrompt from "./domain-whitelist/LoginPrompt";

interface DomainWhitelistProps {
  approvedDomains: string[];
}

const DomainWhitelist: React.FC<DomainWhitelistProps> = ({ approvedDomains }) => {
  const { language } = useAppContext();
  const [searchDomain, setSearchDomain] = useState("");
  const [filteredDomains, setFilteredDomains] = useState<string[]>(approvedDomains);
  const [whitelistView, setWhitelistView] = useState<"grid" | "table">("grid");
  const [user, setUser] = useState<any>(null);
  
  useEffect(() => {
    const checkUser = async () => {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
    };
    
    checkUser();
  }, []);
  
  useEffect(() => {
    if (searchDomain) {
      setFilteredDomains(
        approvedDomains.filter(domain => 
          domain.toLowerCase().includes(searchDomain.toLowerCase())
        )
      );
    } else {
      setFilteredDomains(approvedDomains);
    }
  }, [searchDomain, approvedDomains]);
  
  return (
    <Card className="border-2 border-primary/10 shadow-md">
      <CardHeader className="bg-gradient-to-r from-accent1-50 to-brand-50 dark:from-accent1-950/30 dark:to-brand-950/30">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
            <Globe className="h-5 w-5 text-primary" />
          </div>
          <div className="text-left">
            <CardTitle>{language === 'en' ? 'Allowed Domains' : '允许的域名'}</CardTitle>
            <CardDescription>
              {language === 'en' 
                ? 'List of domains that are approved for URL shortening'
                : '已批准用于URL缩短的域名列表'}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {user ? (
          <DomainForm approvedDomains={approvedDomains} isUserLoggedIn={!!user} />
        ) : (
          <LoginPrompt />
        )}
        
        <DomainSearch 
          searchDomain={searchDomain}
          setSearchDomain={setSearchDomain}
          whitelistView={whitelistView}
          setWhitelistView={setWhitelistView}
        />
        
        {filteredDomains.length === 0 ? (
          <EmptyDomainState />
        ) : whitelistView === 'grid' ? (
          <DomainsGrid domains={filteredDomains} />
        ) : (
          <DomainsTable domains={filteredDomains} />
        )}
        
        <DomainFooter 
          filteredCount={filteredDomains.length} 
          totalCount={approvedDomains.length} 
        />
      </CardContent>
    </Card>
  );
};

export default DomainWhitelist;
