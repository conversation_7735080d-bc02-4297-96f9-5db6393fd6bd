
import { ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAppContext } from "@/context/AppContext";

const CtaSection = () => {
  const { t } = useAppContext();
  
  return (
    <section className="w-full py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              {t('readyToStart')}
            </h2>
            <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              {t('joinThousands')}
            </p>
          </div>
          <div className="space-x-4">
            <Button asChild size="lg" className="bg-gradient-to-r from-brand-500 to-accent1-500 hover:from-brand-600 hover:to-accent1-600">
              <a href="/register">
                {t('register')} <ArrowRight className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CtaSection;
