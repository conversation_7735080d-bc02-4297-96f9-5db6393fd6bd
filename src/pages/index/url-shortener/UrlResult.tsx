
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useAppContext } from "@/context/AppContext";
import { Check, Copy, Download, ExternalLink } from "lucide-react";
import QRCode from "@/components/QRCode";
import { useToast } from "@/components/ui/use-toast";

interface UrlResultProps {
  shortUrl: string;
  showQRCode: boolean;
}

const UrlResult: React.FC<UrlResultProps> = ({ shortUrl, showQRCode }) => {
  const { t, language } = useAppContext();
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const handleCopy = () => {
    navigator.clipboard.writeText(shortUrl);
    setCopied(true);
    
    toast({
      description: language === 'en' ? "Copied to clipboard!" : "已复制到剪贴板！",
    });
    
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  const handleDownloadQR = () => {
    const canvas = document.querySelector('canvas');
    if (!canvas) return;
    
    // Create a temporary link element
    const link = document.createElement('a');
    link.download = 'qrcode.png';
    link.href = canvas.toDataURL('image/png');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      description: language === 'en' ? "QR code downloaded!" : "二维码已下载！",
    });
  };

  if (!shortUrl) return null;

  return (
    <div className="mt-8 space-y-6 bg-muted/30 p-4 rounded-lg">
      <div className="flex flex-col gap-4">
        <h3 className="text-lg font-medium">{t('yourShortUrl')}</h3>
        <div className="flex items-center gap-2 rounded-md border bg-background p-3">
          <span className="flex-1 truncate font-medium text-primary">{shortUrl}</span>
          <Button 
            size="sm" 
            variant="outline"
            onClick={handleCopy}
            className="flex-shrink-0"
          >
            {copied ? <Check size={16} /> : <Copy size={16} />}
            <span className="ml-1">
              {copied 
                ? (language === 'en' ? 'Copied' : '已复制') 
                : (language === 'en' ? 'Copy' : '复制')}
            </span>
          </Button>
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => window.open(shortUrl, '_blank')}
            className="flex-shrink-0"
          >
            <ExternalLink size={16} />
            <span className="ml-1">
              {language === 'en' ? 'Open' : '打开'}
            </span>
          </Button>
        </div>
      </div>
      
      {showQRCode && (
        <div className="flex flex-col items-center py-4">
          <h3 className="text-lg font-medium mb-3">{t('qrCode')}</h3>
          <p className="text-sm text-muted-foreground mb-4">{t('scanToVisit')}</p>
          <QRCode url={shortUrl} />
          <Button 
            onClick={handleDownloadQR} 
            variant="outline" 
            className="mt-4"
          >
            <Download size={16} className="mr-2" />
            {language === 'en' ? 'Download QR Code' : '下载二维码'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default UrlResult;
