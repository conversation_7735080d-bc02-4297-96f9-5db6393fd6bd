
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAppContext } from "@/context/AppContext";
import { extractDomain } from "@/utils/urlUtils";

interface UrlFormProps {
  url: string;
  setUrl: (url: string) => void;
  expirationType: string;
  setExpirationType: (type: string) => void;
  isLoading: boolean;
  onSubmit: (e: React.FormEvent) => void;
  approvedDomains: string[];
  user: any;
}

const UrlForm: React.FC<UrlFormProps> = ({
  url,
  setUrl,
  expirationType,
  setExpirationType,
  isLoading,
  onSubmit,
  approvedDomains,
  user
}) => {
  const { t, language } = useAppContext();
  const [isValidDomain, setIsValidDomain] = useState(true);

  // Validate domain when URL changes
  useEffect(() => {
    if (url) {
      const domain = extractDomain(url);
      if (domain) {
        setIsValidDomain(approvedDomains.some(d => domain.includes(d)) || approvedDomains.length === 0);
      } else {
        setIsValidDomain(false);
      }
    } else {
      setIsValidDomain(true);
    }
  }, [url, approvedDomains]);

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="flex w-full flex-col gap-2">
        <div className="relative">
          <Input
            placeholder={language === 'en' ? 'https://example.com/long-url' : 'https://example.com/长网址'}
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className={`pr-10 ${!isValidDomain && url ? "border-red-500" : ""}`}
          />
          {url && isValidDomain && (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="absolute right-3 top-1/2 h-5 w-5 -translate-y-1/2 text-green-500"
            >
              <polyline points="20 6 9 17 4 12" />
            </svg>
          )}
        </div>
        
        {!isValidDomain && url && (
          <p className="text-sm text-red-500">
            {language === 'en' 
              ? 'This domain is not in the approved whitelist.' 
              : '该域名不在已批准的白名单中。'}
          </p>
        )}
        
        <div className="grid gap-4 sm:grid-cols-3">
          <div className="sm:col-span-2">
            <label htmlFor="expiration" className="text-sm font-medium mb-1.5 block">
              {t('expirationTime')}
            </label>
            <Select
              value={expirationType}
              onValueChange={setExpirationType}
            >
              <SelectTrigger id="expiration" className="w-full">
                <SelectValue placeholder={t('selectExpiration')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hour">{t('hour')}</SelectItem>
                <SelectItem value="day">{t('day')}</SelectItem>
                <SelectItem value="week">{t('week')}</SelectItem>
                <SelectItem value="month">{t('month')}</SelectItem>
                <SelectItem value="permanent" disabled={!user}>
                  {user ? t('permanent') : t('permanentRequiresLogin')}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-end">
            <Button 
              type="submit" 
              disabled={isLoading || (url && !isValidDomain)}
              className="w-full h-10"
            >
              {isLoading 
                ? (language === 'en' ? 'Shortening...' : '缩短中...') 
                : t('shortenUrl')}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
};

export default UrlForm;
