
import { Check, Link2, Mail } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useAppContext } from "@/context/AppContext";

const FeaturesTabs = () => {
  const { t, language } = useAppContext();
  
  return (
    <Tabs defaultValue="url" className="w-full max-w-4xl mx-auto mt-12">
      <TabsList className="grid w-full grid-cols-2 mb-8">
        <TabsTrigger value="url">
          <Link2 className="mr-2 h-4 w-4" />
          {t('urlShortener')}
        </TabsTrigger>
        <TabsTrigger value="email">
          <Mail className="mr-2 h-4 w-4" />
          {t('tempEmail')}
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="url" className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('urlFeature1Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('urlFeature1Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('urlFeature2Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('urlFeature2Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('urlFeature3Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('urlFeature3Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('urlFeature4Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('urlFeature4Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('urlFeature5Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('urlFeature5Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('urlFeature6Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('urlFeature6Desc')}</p>
            </div>
          </div>
        </div>
        
        <div className="p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">{t('urlExpiration')}</h4>
          <p className="text-sm text-muted-foreground mb-4">{t('urlExpirationDesc')}</p>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-background rounded border">
              <p className="text-sm font-medium">{t('unregisteredUsers')}</p>
              <ul className="mt-2 text-xs space-y-1 text-muted-foreground">
                <li>{t('urlExpirationOption1')}</li>
                <li>{t('urlExpirationOption2')}</li>
                <li>{t('urlExpirationOption3')}</li>
                <li>{t('urlExpirationOption4')}</li>
              </ul>
            </div>
            <div className="p-3 bg-background rounded border border-primary/50">
              <p className="text-sm font-medium">{t('registeredUsers')}</p>
              <ul className="mt-2 text-xs space-y-1 text-muted-foreground">
                <li>{t('urlExpirationOption1')}</li>
                <li>{t('urlExpirationOption2')}</li>
                <li>{t('urlExpirationOption3')}</li>
                <li>{t('urlExpirationOption4')}</li>
                <li className="font-medium text-primary">{t('urlExpirationOption5')}</li>
              </ul>
            </div>
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="email" className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('emailFeature1Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('emailFeature1Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('emailFeature2Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('emailFeature2Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('emailFeature3Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('emailFeature3Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('emailFeature4Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('emailFeature4Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('emailFeature5Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('emailFeature5Desc')}</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Check className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium">{t('emailFeature6Title')}</h4>
              <p className="text-sm text-muted-foreground">{t('emailFeature6Desc')}</p>
            </div>
          </div>
        </div>
        
        <div className="p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">{t('emailExpiration')}</h4>
          <p className="text-sm text-muted-foreground mb-4">{t('emailExpirationDesc')}</p>
          <div className="grid grid-cols-1 gap-4">
            <div className="p-3 bg-background rounded border border-primary/50">
              <p className="text-sm font-medium">{t('tempEmailOptions')}</p>
              <ul className="mt-2 text-xs space-y-1 text-muted-foreground">
                <li>{t('emailExpirationOption1')}</li>
                <li>{t('emailExpirationOption2')}</li>
                <li>{t('emailExpirationOption3')}</li>
                <li className="font-medium text-primary">{t('maxDaysLimit')}</li>
              </ul>
            </div>
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default FeaturesTabs;
