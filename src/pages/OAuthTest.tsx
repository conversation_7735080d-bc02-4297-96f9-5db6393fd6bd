import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { authService } from '@/services/api';
import { useToast } from '@/components/ui/use-toast';

interface OAuthProvider {
  id: string;
  provider_name: string;
  client_id: string;
  redirect_url: string;
  scopes: string[];
  enabled: boolean;
}

const OAuthTest: React.FC = () => {
  const [providers, setProviders] = useState<OAuthProvider[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    try {
      const response = await authService.getOAuthProviders();
      setProviders(response.data.providers || []);
    } catch (error) {
      console.error('Failed to load OAuth providers:', error);
      toast({
        variant: "destructive",
        title: "错误",
        description: "加载OAuth提供商失败",
      });
    }
  };

  const handleOAuthLogin = async (provider: OAuthProvider) => {
    setLoading(true);
    try {
      // 生成随机状态值
      const state = Math.random().toString(36).substring(2, 15);
      localStorage.setItem('oauth_state', state);

      // 获取OAuth认证URL
      const response = await authService.getOAuthAuthURL(provider.provider_name, state);
      const authURL = response.data.auth_url;

      // 由于重定向URL配置问题，我们暂时修改URL指向前端
      const correctedURL = authURL.replace(
        'http://localhost:8080/auth/',
        'http://localhost:3000/oauth/callback/'
      );

      console.log('Original auth URL:', authURL);
      console.log('Corrected auth URL:', correctedURL);

      // 重定向到OAuth提供商
      window.location.href = correctedURL;
    } catch (error) {
      console.error('OAuth login failed:', error);
      toast({
        variant: "destructive",
        title: "错误",
        description: "OAuth登录失败",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>OAuth 第三方登录测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">可用的OAuth提供商</h3>
              {providers.length === 0 ? (
                <p className="text-muted-foreground">正在加载OAuth提供商...</p>
              ) : (
                providers.map((provider) => (
                  <div key={provider.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium capitalize">{provider.provider_name}</h4>
                        <p className="text-sm text-muted-foreground">
                          客户端ID: {provider.client_id}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          重定向URL: {provider.redirect_url}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          作用域: {provider.scopes.join(', ')}
                        </p>
                        <p className="text-sm">
                          状态: {provider.enabled ? '启用' : '禁用'}
                        </p>
                      </div>
                      <Button
                        onClick={() => handleOAuthLogin(provider)}
                        disabled={!provider.enabled || loading}
                        className="ml-4"
                      >
                        使用{provider.provider_name}登录
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>

            <div className="mt-6 p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">测试说明</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 这是OAuth第三方登录的测试页面</li>
                <li>• 由于配置限制，当前使用的是测试客户端ID</li>
                <li>• 实际使用时需要配置真实的OAuth应用凭据</li>
                <li>• 回调URL会自动调整为前端地址</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OAuthTest; 