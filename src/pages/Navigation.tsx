
import React from 'react';
import { Outlet } from 'react-router-dom';
import NavigationHub from '@/components/navigation/NavigationHub';
import { DragDropContext } from 'react-beautiful-dnd';
import { useAppContext } from '@/context/AppContext';
import UnifiedHeader from '@/components/common/UnifiedHeader';
import { Compass } from 'lucide-react';
import PageWrapper from '@/components/layout/PageWrapper';

const Navigation = () => {
  const { language } = useAppContext();
  
  const headerContent = (
    <UnifiedHeader
      title={language === 'zh' ? '导航中心' : 'Navigation Hub'}
      description={language === 'zh' ? '发现和收藏实用的网站与工具' : 'Discover and bookmark useful websites and tools'}
      icon={Compass}
      variant="gradient"
      gradientFrom="from-emerald-500"
      gradientTo="to-teal-500"
      layout="default"
    />
  );

  return (
    <PageWrapper headerContent={headerContent}>
      <DragDropContext onDragEnd={() => {}}>
        <NavigationHub />
      </DragDropContext>
    </PageWrapper>
  );
};

export default Navigation;
