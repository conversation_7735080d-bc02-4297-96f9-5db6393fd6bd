import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useAppContext } from '@/context/AppContext';
import { Loader2, Lock, LogIn, Mail, User as UserIcon, Github, Upload, Camera, X, Key, Shield, AlertTriangle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import apiClient from '@/services/api';
import * as authService from '@/services/authService';

const passwordSchema = z.object({
  currentPassword: z.string().min(6, {
    message: "Current password must be at least 6 characters",
  }),
  newPassword: z.string().min(8, {
    message: "New password must be at least 8 characters",
  }).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: "Password must contain uppercase, lowercase, number and special character",
  }),
  confirmPassword: z.string().min(8, {
    message: "Confirm password must be at least 8 characters",
  }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const Profile = () => {
  const { t, language, user, isAuthReady, fetchUser } = useAppContext();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [emailVerificationSending, setEmailVerificationSending] = useState(false);
  const [isAvatarDialogOpen, setIsAvatarDialogOpen] = useState(false);
  const [oauthProviders, setOauthProviders] = useState<string[]>([]);
  const [connectedProviders, setConnectedProviders] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<z.infer<typeof passwordSchema>>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    const checkUser = async () => {
      if (!isAuthReady) return;

      if (!user) {
        navigate('/login');
        return;
      }

      setLoading(false);
      fetchOAuthProviders();
      fetchConnectedProviders();
    };

    checkUser();
  }, [isAuthReady, user, navigate]);

  // 获取启用的OAuth提供商
  const fetchOAuthProviders = async () => {
    try {
      const response = await apiClient.get('/auth/oauth/providers');
      if (response.data && response.data.providers) {
        const enabledProviders = response.data.providers
          .filter((p: { enabled: boolean }) => p.enabled)
          .map((p: { provider_name: string }) => p.provider_name);
        setOauthProviders(enabledProviders);
      }
    } catch (error) {
      console.error('Failed to fetch OAuth providers:', error);
    }
  };

  // 获取已连接的OAuth账号
  const fetchConnectedProviders = async () => {
    try {
      const response = await apiClient.get('/auth/oauth/accounts');
      if (response.data && response.data.accounts) {
        const connectedProviders = response.data.accounts.map((account: { provider_name: string }) => account.provider_name);
        setConnectedProviders(connectedProviders);
      }
    } catch (error) {
      console.error('Failed to fetch connected providers:', error);
    }
  };

  const handlePasswordChange = async (values: z.infer<typeof passwordSchema>) => {
    try {
      setLoading(true);

      const response = await apiClient.post('/auth/change-password', {
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
      });

      if (response.data.error) {
        throw new Error(response.data.error);
      }

      toast({
        title: language === 'en' ? "Password updated" : "密码已更新",
        description: language === 'en'
          ? "Your password has been updated successfully"
          : "您的密码已成功更新",
      });

      form.reset();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: language === 'en' ? "Error" : "错误",
        description: errorMessage || (language === 'en'
          ? "Failed to update password"
          : "密码更新失败"),
      });
    } finally {
      setLoading(false);
    }
  };

  // 发送邮箱验证邮件
  const handleSendVerificationEmail = async () => {
    try {
      setEmailVerificationSending(true);
      await authService.sendVerificationEmail();

      toast({
        title: language === 'en' ? "Email sent" : "邮件已发送",
        description: language === 'en'
          ? "Verification email has been sent to your email address"
          : "验证邮件已发送到您的邮箱",
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: language === 'en' ? "Error" : "错误",
        description: errorMessage || (language === 'en'
          ? "Failed to send verification email"
          : "发送验证邮件失败"),
      });
    } finally {
      setEmailVerificationSending(false);
    }
  };

  // Generate user initials from username or email
  const getUserInitials = () => {
    if (!user) return '?';
    if (user.username) {
      return user.username.substring(0, 2).toUpperCase();
    }
    return user.email.substring(0, 2).toUpperCase();
  };

  // 连接OAuth账号
  const connectProvider = async (provider: string) => {
    try {
      const response = await apiClient.get(`/auth/oauth/${provider}/auth`);
      if (response.data && response.data.auth_url) {
        window.location.href = response.data.auth_url;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: language === 'en' ? "Error" : "错误",
        description: errorMessage || (language === 'en'
          ? "Failed to connect account"
          : "连接账户失败"),
      });
    }
  };

  // 断开OAuth连接
  const disconnectProvider = async (provider: string) => {
    try {
      await apiClient.delete(`/auth/oauth/${provider}`);
      setConnectedProviders(prev => prev.filter(p => p !== provider));

      toast({
        title: language === 'en' ? "Disconnected" : "已断开连接",
        description: language === 'en'
          ? `${provider} account has been disconnected`
          : `已断开 ${provider} 账号连接`,
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: language === 'en' ? "Error" : "错误",
        description: errorMessage || (language === 'en'
          ? "Failed to disconnect account"
          : "断开连接失败"),
      });
    }
  };

  // 打开文件选择器
  const openFileSelector = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理头像上传
  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    try {
      setAvatarLoading(true);

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        throw new Error(language === 'en' ? 'Please select an image file' : '请选择图片文件');
      }

      // 验证文件大小 (2MB限制)
      if (file.size > 2 * 1024 * 1024) {
        throw new Error(language === 'en' ? 'Image size should be less than 2MB' : '图片大小应小于2MB');
      }

      // 创建FormData上传文件
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await apiClient.post('/upload/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.error) {
        throw new Error(response.data.error);
      }

      // 刷新用户信息
      await fetchUser();

      toast({
        title: language === 'en' ? 'Avatar updated' : '头像已更新',
        description: language === 'en' ? 'Your avatar has been updated successfully' : '您的头像已成功更新'
      });

      setIsAvatarDialogOpen(false);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: language === 'en' ? "Error" : "错误",
        description: errorMessage || (language === 'en'
          ? "Failed to update avatar"
          : "头像更新失败"),
      });
    } finally {
      setAvatarLoading(false);
      // 清除文件输入，以便再次选择同一文件
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // 移除头像
  const handleRemoveAvatar = async () => {
    if (!user) return;

    try {
      setAvatarLoading(true);

      const response = await apiClient.delete('/auth/remove-avatar');

      if (response.data.error) {
        throw new Error(response.data.error);
      }

      // 刷新用户信息
      await fetchUser();

      toast({
        title: language === 'en' ? 'Avatar removed' : '头像已移除',
        description: language === 'en' ? 'Your avatar has been removed' : '您的头像已被移除'
      });

      setIsAvatarDialogOpen(false);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: language === 'en' ? "Error" : "错误",
        description: errorMessage || (language === 'en'
          ? "Failed to remove avatar"
          : "头像移除失败"),
      });
    } finally {
      setAvatarLoading(false);
    }
  };

  // 获取OAuth提供商的图标
  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'github':
        return <Github className="h-5 w-5" />;
      case 'google':
        return <Mail className="h-5 w-5" />;
      default:
        return <Key className="h-5 w-5" />;
    }
  };

  // 获取OAuth提供商的颜色
  const getProviderColor = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'github':
        return 'bg-gray-800 text-white hover:bg-gray-700';
      case 'google':
        return 'bg-red-500 text-white hover:bg-red-600';
      default:
        return 'bg-primary text-primary-foreground hover:bg-primary/90';
    }
  };

  if (loading && !user) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 flex flex-col gap-2">
          <h1 className="text-3xl font-bold">
            {language === 'en' ? 'Your Profile' : '个人资料'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'en'
              ? 'Manage your account settings and preferences'
              : '管理您的账户设置和偏好'}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle>{language === 'en' ? 'Account' : '账户'}</CardTitle>
              <CardDescription>
                {language === 'en'
                  ? 'Your account information'
                  : '您的账户信息'}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center text-center">
              <Dialog open={isAvatarDialogOpen} onOpenChange={setIsAvatarDialogOpen}>
                <DialogTrigger asChild>
                  <div className="relative cursor-pointer group">
                    <Avatar className="h-24 w-24 mb-4 border-2 border-transparent group-hover:border-primary">
                      <AvatarImage src={user?.avatar_url || undefined} />
                      <AvatarFallback className="text-xl">{getUserInitials()}</AvatarFallback>
                    </Avatar>
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-full flex items-center justify-center text-transparent group-hover:text-white transition-all">
                      <Camera className="h-6 w-6 opacity-0 group-hover:opacity-100" />
                    </div>
                  </div>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      {language === 'en' ? 'Upload Profile Picture' : '上传头像'}
                    </DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="flex items-center justify-center my-4">
                      <Avatar className="h-32 w-32 border-2 border-primary/20">
                        <AvatarImage src={user?.avatar_url || undefined} />
                        <AvatarFallback className="text-2xl">{getUserInitials()}</AvatarFallback>
                      </Avatar>
                    </div>
                    <div className="flex items-center justify-center gap-3">
                      <Button
                        variant="outline"
                        onClick={openFileSelector}
                        disabled={avatarLoading}
                        className="flex items-center gap-2"
                      >
                        {avatarLoading ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Upload className="h-4 w-4 mr-2" />
                        )}
                        {language === 'en' ? 'Select Image' : '选择图片'}
                      </Button>
                      {user?.avatar_url && (
                        <Button
                          variant="destructive"
                          onClick={handleRemoveAvatar}
                          disabled={avatarLoading}
                          className="flex items-center gap-2"
                        >
                          <X className="h-4 w-4 mr-2" />
                          {language === 'en' ? 'Remove' : '移除'}
                        </Button>
                      )}
                      <input
                        type="file"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/*"
                        onChange={handleAvatarUpload}
                      />
                    </div>
                    <p className="text-sm text-muted-foreground text-center">
                      {language === 'en'
                        ? 'Upload a square image for best results. Maximum file size: 2MB.'
                        : '上传正方形图片效果最佳。文件大小上限：2MB。'}
                    </p>
                  </div>
                </DialogContent>
              </Dialog>
              <h3 className="font-medium text-lg mb-1">{user?.username || user?.email}</h3>
              <p className="text-sm text-muted-foreground mb-2">{user?.email}</p>
              <div className="flex items-center gap-2 mb-4">
                {user?.is_super_admin && (
                  <Badge variant="default" className="text-xs">
                    <Shield className="h-3 w-3 mr-1" />
                    {language === 'en' ? 'Super Admin' : '超级管理员'}
                  </Badge>
                )}
                {user?.email_verified ? (
                  <Badge variant="secondary" className="text-xs">
                    {language === 'en' ? 'Email Verified' : '邮箱已验证'}
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="text-xs">
                    {language === 'en' ? 'Email Not Verified' : '邮箱未验证'}
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                {language === 'en' ? 'Member since' : '注册时间'}: {' '}
                {new Date(user?.created_at || '').toLocaleDateString()}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsAvatarDialogOpen(true)}
                className="text-sm"
              >
                <Camera className="h-4 w-4 mr-2" />
                {language === 'en' ? 'Change Picture' : '更换头像'}
              </Button>
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <Tabs defaultValue="password">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>{language === 'en' ? 'Settings' : '设置'}</CardTitle>
                  <TabsList>
                    <TabsTrigger value="password">
                      <Lock className="h-4 w-4 mr-2" />
                      {language === 'en' ? 'Password' : '密码'}
                    </TabsTrigger>
                    <TabsTrigger value="connections">
                      <LogIn className="h-4 w-4 mr-2" />
                      {language === 'en' ? 'Connections' : '连接'}
                    </TabsTrigger>
                  </TabsList>
                </div>
                <CardDescription>
                  {language === 'en'
                    ? 'Manage your security settings and connections'
                    : '管理您的安全设置和连接'}
                </CardDescription>
              </CardHeader>

              <CardContent>
                <TabsContent value="password">
                  {/* 邮箱验证提醒 */}
                  {!user?.email_verified && (
                    <Alert className="mb-4">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription className="flex items-center justify-between">
                        <span>
                          {language === 'en'
                            ? 'Your email is not verified. Please verify your email to access all features.'
                            : '您的邮箱尚未验证。请验证邮箱以访问所有功能。'}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleSendVerificationEmail}
                          disabled={emailVerificationSending}
                        >
                          {emailVerificationSending ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            language === 'en' ? 'Send Verification Email' : '发送验证邮件'
                          )}
                        </Button>
                      </AlertDescription>
                    </Alert>
                  )}

                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(handlePasswordChange)} className="space-y-4">
                      <FormField
                        control={form.control}
                        name="currentPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {language === 'en' ? 'Current Password' : '当前密码'}
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder={language === 'en' ? 'Enter current password' : '输入当前密码'}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {language === 'en' ? 'New Password' : '新密码'}
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder={language === 'en' ? 'Enter new password' : '输入新密码'}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {language === 'en' ? 'Confirm New Password' : '确认新密码'}
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder={language === 'en' ? 'Confirm new password' : '确认新密码'}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button type="submit" disabled={loading} className="w-full">
                        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {language === 'en' ? 'Update Password' : '更新密码'}
                      </Button>
                    </form>
                  </Form>
                </TabsContent>

                <TabsContent value="connections">
                  <div className="space-y-4">
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-100 text-red-600">
                            <Mail className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-medium">
                              {language === 'en' ? 'Email Address' : '电子邮箱'}
                            </h4>
                            <p className="text-sm text-muted-foreground">{user?.email}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {user?.email_verified ? (
                            <Badge variant="secondary">
                              {language === 'en' ? 'Verified' : '已验证'}
                            </Badge>
                          ) : (
                            <Badge variant="outline">
                              {language === 'en' ? 'Unverified' : '未验证'}
                            </Badge>
                          )}
                          <Button variant="outline" disabled>
                            {language === 'en' ? 'Primary' : '主要'}
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* OAuth连接 */}
                    {oauthProviders.map((provider) => {
                      const isConnected = connectedProviders.includes(provider);
                      return (
                        <div key={provider} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                                isConnected ? getProviderColor(provider) : 'bg-muted text-muted-foreground'
                              }`}>
                                {getProviderIcon(provider)}
                              </div>
                              <div>
                                <h4 className="font-medium capitalize">{provider}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {isConnected
                                    ? (language === 'en' ? 'Connected' : '已连接')
                                    : (language === 'en' ? 'Not connected' : '未连接')}
                                </p>
                              </div>
                            </div>
                            {isConnected ? (
                              <Button
                                variant="outline"
                                onClick={() => disconnectProvider(provider)}
                                className="text-destructive hover:text-destructive"
                              >
                                {language === 'en' ? 'Disconnect' : '断开连接'}
                              </Button>
                            ) : (
                              <Button
                                variant="outline"
                                onClick={() => connectProvider(provider)}
                              >
                                {language === 'en' ? 'Connect' : '连接'}
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}

                    {oauthProviders.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>
                          {language === 'en'
                            ? 'No OAuth providers available. Contact your administrator to enable third-party login options.'
                            : '没有可用的第三方登录选项。请联系管理员启用第三方登录。'}
                        </p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </CardContent>
            </Tabs>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Profile;
