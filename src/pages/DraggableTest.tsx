
import React from 'react';
import AnimatedGradientTitle from '@/components/common/AnimatedGradientTitle';

const DraggableTest: React.FC = () => {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Draggable Components Test</h1>
      
      <div className="p-8 border rounded-lg mb-8">
        <AnimatedGradientTitle 
          title={{
            main: "Main Title",
            subtitle: "This is a subtitle",
            first: { text: "First line", gradient: "from-primary to-secondary" },
            second: { text: "Second line", gradient: "from-accent1-400 to-accent2-400" }
          }}
        />
      </div>
    </div>
  );
};

export default DraggableTest;
