import React from "react";
import { 
  Popover, 
  PopoverTrigger, 
  PopoverContent 
} from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";

const PopoverTest: React.FC = () => {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="p-6 border rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold mb-4">Popover测试</h1>
        
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline">点击测试Popover</Button>
          </PopoverTrigger>
          <PopoverContent className="p-4 w-80">
            <div className="text-sm">
              <p>这是一个Popover内容测试。</p>
              <p>如果你看到此内容，说明Popover正常工作。</p>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};

export default PopoverTest; 