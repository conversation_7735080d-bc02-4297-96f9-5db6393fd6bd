import React from 'react';
import { useAppContext } from '@/context/AppContext';
import { History } from 'lucide-react';
import { useOnlineTools } from './OnlineToolsContext';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { format } from 'date-fns';

const OnlineToolsHeader = () => {
  const { language } = useAppContext();
  const { history, clearHistory } = useOnlineTools();

  return (
    <div className="flex items-center">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 border-white/70 text-white hover:bg-white/20 hover:border-white/90 shadow-sm bg-white/10 font-medium"
          >
            <History className="h-4 w-4" />
            {language === 'zh' ? '使用历史' : 'History'}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96 p-0" align="start">
          <div className="p-4 border-b">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">
                {language === 'zh' ? '最近使用记录' : 'Recent Activity'}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearHistory}
                disabled={history.length === 0}
              >
                <Trash2 className="h-4 w-4 text-muted-foreground" />
              </Button>
            </div>
          </div>
          <div className="max-h-80 overflow-auto">
            {history.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">
                {language === 'zh' ? '暂无使用记录' : 'No history yet'}
              </div>
            ) : (
              <div className="divide-y">
                {history.map(item => (
                  <div key={item.id} className="p-3 text-sm">
                    <div className="flex justify-between">
                      <span className="font-medium">{item.tool}</span>
                      <span className="text-xs text-muted-foreground">
                        {format(item.timestamp, 'yyyy-MM-dd HH:mm:ss')}
                      </span>
                    </div>
                    <div className="text-xs truncate mt-1 text-muted-foreground">
                      {language === 'zh' ? '输入: ' : 'Input: '}
                      {item.input.length > 40 ? `${item.input.slice(0, 40)}...` : item.input}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default OnlineToolsHeader;
