
import React, { createContext, useContext, useState } from 'react';

interface OnlineToolsContextType {
  history: HistoryItem[];
  addToHistory: (tool: string, input: string, output: string) => void;
  clearHistory: () => void;
}

interface HistoryItem {
  id: string;
  timestamp: Date;
  tool: string;
  input: string;
  output: string;
}

const OnlineToolsContext = createContext<OnlineToolsContextType | undefined>(undefined);

export const OnlineToolsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [history, setHistory] = useState<HistoryItem[]>([]);

  const addToHistory = (tool: string, input: string, output: string) => {
    const newItem = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      tool,
      input,
      output
    };
    
    setHistory(prev => [newItem, ...prev].slice(0, 50)); // Keep last 50 items
  };

  const clearHistory = () => {
    setHistory([]);
  };

  return (
    <OnlineToolsContext.Provider value={{ history, addToHistory, clearHistory }}>
      {children}
    </OnlineToolsContext.Provider>
  );
};

export const useOnlineTools = () => {
  const context = useContext(OnlineToolsContext);
  if (context === undefined) {
    throw new Error('useOnlineTools must be used within an OnlineToolsProvider');
  }
  return context;
};
