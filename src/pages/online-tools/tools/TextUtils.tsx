import React, { useState, useEffect } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Co<PERSON>, ClipboardCheck, FileText } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

const TextUtils = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [activeTab, setActiveTab] = useState('case-converter');
  const [operationType, setOperationType] = useState('uppercase');
  const [textStats, setTextStats] = useState({ 
    chars: 0, 
    words: 0, 
    lines: 0, 
    sentences: 0 
  });
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (inputText) {
      updateTextStats(inputText);
    }
  }, [inputText]);

  const updateTextStats = (text: string) => {
    const chars = text.length;
    const words = text.trim() ? text.trim().split(/\s+/).length : 0;
    const lines = text.split('\n').length;
    // Simple sentence count (not perfect)
    const sentences = text.split(/[.!?]+\s*/g).filter(Boolean).length;
    
    setTextStats({ chars, words, lines, sentences });
  };

  const processText = () => {
    try {
      if (!inputText.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入文本' : 'Please enter text',
        });
        setOutputText('');
        return;
      }

      let result = '';
      let opName = '';
      let error: Error | null = null;
      
      switch (activeTab) {
        case 'case-converter':
          opName = language === 'zh' ? '大小写转换' : 'Case Conversion';
          switch (operationType) {
            case 'uppercase':
              result = inputText.toUpperCase();
              opName = language === 'zh' ? '转大写' : 'To Uppercase';
              break;
            case 'lowercase':
              result = inputText.toLowerCase();
              opName = language === 'zh' ? '转小写' : 'To Lowercase';
              break;
            case 'capitalize':
              result = inputText
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
              opName = language === 'zh' ? '首字母大写' : 'Capitalize';
              break;
            case 'title-case':
              result = inputText
                .split(' ')
                .map(word => {
                  const lowercaseExceptions = ['a', 'an', 'the', 'and', 'but', 'or', 'for', 'nor', 'on', 'at', 'to', 'from', 'by', 'in', 'of', 'with'];
                  if (lowercaseExceptions.includes(word.toLowerCase()) && result.length > 0) {
                    return word.toLowerCase();
                  }
                  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                })
                .join(' ');
              
              // Always capitalize the first word
              if (result.length > 0) {
                result = result.charAt(0).toUpperCase() + result.slice(1);
              }
              opName = language === 'zh' ? '标题格式' : 'Title Case';
              break;
            default:
              result = inputText;
          }
          break;
        
        case 'text-operations':
          opName = language === 'zh' ? '文本操作' : 'Text Operations';
          switch (operationType) {
            case 'reverse':
              result = inputText.split('').reverse().join('');
              opName = language === 'zh' ? '文本反转' : 'Reverse Text';
              break;
            case 'remove-spaces':
              result = inputText.replace(/\s+/g, '');
              opName = language === 'zh' ? '移除空格' : 'Remove Spaces';
              break;
            case 'trim-lines':
              result = inputText
                .split('\n')
                .map(line => line.trim())
                .join('\n');
              opName = language === 'zh' ? '修剪行' : 'Trim Lines';
              break;
            case 'remove-dupe-lines':
              result = [...new Set(inputText.split('\n'))].join('\n');
              opName = language === 'zh' ? '移除重复行' : 'Remove Duplicate Lines';
              break;
            case 'sort-lines':
              result = inputText
                .split('\n')
                .filter(line => line.trim())
                .sort()
                .join('\n');
              opName = language === 'zh' ? '行排序' : 'Sort Lines';
              break;
            case 'sort-lines-reverse':
              result = inputText
                .split('\n')
                .filter(line => line.trim())
                .sort()
                .reverse()
                .join('\n');
              opName = language === 'zh' ? '行逆序排序' : 'Sort Lines (Reverse)';
              break;
            case 'shuffle-lines':
              const lines = inputText.split('\n').filter(line => line.trim());
              for (let i = lines.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [lines[i], lines[j]] = [lines[j], lines[i]];
              }
              result = lines.join('\n');
              opName = language === 'zh' ? '行随机排列' : 'Shuffle Lines';
              break;
            default:
              result = inputText;
          }
          break;
          
        case 'whitespace':
          opName = language === 'zh' ? '空白处理' : 'Whitespace';
          switch (operationType) {
            case 'tabs-to-spaces':
              result = inputText.replace(/\t/g, '    ');
              opName = language === 'zh' ? '制表符转空格' : 'Tabs to Spaces';
              break;
            case 'spaces-to-tabs':
              result = inputText.replace(/    /g, '\t');
              opName = language === 'zh' ? '空格转制表符' : 'Spaces to Tabs';
              break;
            case 'remove-empty-lines':
              result = inputText
                .split('\n')
                .filter(line => line.trim())
                .join('\n');
              opName = language === 'zh' ? '移除空行' : 'Remove Empty Lines';
              break;
            case 'normalize-spaces':
              result = inputText.replace(/\s+/g, ' ');
              opName = language === 'zh' ? '标准化空格' : 'Normalize Spaces';
              break;
            default:
              result = inputText;
          }
          break;
          
        case 'encoding': {
          opName = language === 'zh' ? '编码' : 'Encoding';
          switch (operationType) {
            case 'uri-encode':
              result = encodeURIComponent(inputText);
              opName = language === 'zh' ? 'URI编码' : 'URI Encode';
              break;
            case 'uri-decode':
              try {
                result = decodeURIComponent(inputText);
                opName = language === 'zh' ? 'URI解码' : 'URI Decode';
              } catch (e) {
                error = new Error(language === 'zh' ? 'URI解码失败，请检查输入' : 'URI decode failed, please check input');
                throw error;
              }
              break;
            case 'html-encode':
              result = inputText
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');
              opName = language === 'zh' ? 'HTML编码' : 'HTML Encode';
              break;
            case 'html-decode':
              result = inputText
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#039;/g, "'");
              opName = language === 'zh' ? 'HTML解码' : 'HTML Decode';
              break;
            case 'base64-encode':
              try {
                // 使用更安全的方法处理Unicode字符
                result = btoa(unescape(encodeURIComponent(inputText)));
                opName = language === 'zh' ? 'Base64编码' : 'Base64 Encode';
              } catch (e) {
                error = new Error(language === 'zh' ? 'Base64编码失败，请检查输入' : 'Base64 encode failed, please check input');
                throw error;
              }
              break;
            case 'base64-decode':
              try {
                // 使用更安全的方法处理Unicode字符
                result = decodeURIComponent(escape(atob(inputText)));
                opName = language === 'zh' ? 'Base64解码' : 'Base64 Decode';
              } catch (e) {
                error = new Error(language === 'zh' ? 'Base64解码失败，请检查输入' : 'Base64 decode failed, please check input');
                throw error;
              }
              break;
            default:
              result = inputText;
          }
          break;
        }
          
        default:
          result = inputText;
          opName = language === 'zh' ? '未知操作' : 'Unknown Operation';
      }
      
      setOutputText(result);
      
      // Add to history
      addToHistory(
        opName,
        inputText.substring(0, 50) + (inputText.length > 50 ? '...' : ''),
        result.substring(0, 50) + (result.length > 50 ? '...' : '')
      );
    } catch (error: unknown) {
      console.error('Text processing error:', error);
      const errorMessage = error instanceof Error 
        ? error.message 
        : (language === 'zh' ? '处理文本时出错' : 'Error processing text');
      
      toast({
        variant: "destructive",
        description: errorMessage,
      });
      setOutputText('');
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(outputText);
    setCopied(true);
    toast({
      description: language === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
    });
    setTimeout(() => setCopied(false), 2000);
  };

  // Tab内容渲染
  const renderOperations = () => {
    switch (activeTab) {
      case 'case-converter':
        return (
          <Tabs value={operationType} onValueChange={setOperationType} className="w-full">
            <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
              <TabsTrigger value="uppercase">
                {language === 'zh' ? '大写' : 'UPPERCASE'}
              </TabsTrigger>
              <TabsTrigger value="lowercase">
                {language === 'zh' ? '小写' : 'lowercase'}
              </TabsTrigger>
              <TabsTrigger value="capitalize">
                {language === 'zh' ? '首字母大写' : 'Capitalize'}
              </TabsTrigger>
              <TabsTrigger value="title-case">
                {language === 'zh' ? '标题格式' : 'Title Case'}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        );
        
      case 'text-operations':
        return (
          <Tabs value={operationType} onValueChange={setOperationType} className="w-full">
            <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-7">
              <TabsTrigger value="reverse">
                {language === 'zh' ? '反转' : 'Reverse'}
              </TabsTrigger>
              <TabsTrigger value="remove-spaces">
                {language === 'zh' ? '删空格' : 'No Spaces'}
              </TabsTrigger>
              <TabsTrigger value="trim-lines">
                {language === 'zh' ? '修剪行' : 'Trim Lines'}
              </TabsTrigger>
              <TabsTrigger value="remove-dupe-lines">
                {language === 'zh' ? '去重行' : 'Unique Lines'}
              </TabsTrigger>
              <TabsTrigger value="sort-lines">
                {language === 'zh' ? '排序' : 'Sort'}
              </TabsTrigger>
              <TabsTrigger value="sort-lines-reverse">
                {language === 'zh' ? '逆序' : 'Rev Sort'}
              </TabsTrigger>
              <TabsTrigger value="shuffle-lines">
                {language === 'zh' ? '随机' : 'Shuffle'}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        );
        
      case 'whitespace':
        return (
          <Tabs value={operationType} onValueChange={setOperationType} className="w-full">
            <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
              <TabsTrigger value="tabs-to-spaces">
                {language === 'zh' ? '制表→空格' : 'Tabs→Spaces'}
              </TabsTrigger>
              <TabsTrigger value="spaces-to-tabs">
                {language === 'zh' ? '空格→制表' : 'Spaces→Tabs'}
              </TabsTrigger>
              <TabsTrigger value="remove-empty-lines">
                {language === 'zh' ? '删空行' : 'No Empty Lines'}
              </TabsTrigger>
              <TabsTrigger value="normalize-spaces">
                {language === 'zh' ? '标准化空格' : 'Normalize Spaces'}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        );
        
      case 'encoding':
        return (
          <Tabs value={operationType} onValueChange={setOperationType} className="w-full">
            <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-6">
              <TabsTrigger value="uri-encode">
                {language === 'zh' ? 'URI编码' : 'URI Encode'}
              </TabsTrigger>
              <TabsTrigger value="uri-decode">
                {language === 'zh' ? 'URI解码' : 'URI Decode'}
              </TabsTrigger>
              <TabsTrigger value="html-encode">
                {language === 'zh' ? 'HTML编码' : 'HTML Encode'}
              </TabsTrigger>
              <TabsTrigger value="html-decode">
                {language === 'zh' ? 'HTML解码' : 'HTML Decode'}
              </TabsTrigger>
              <TabsTrigger value="base64-encode">
                {language === 'zh' ? 'Base64编码' : 'Base64 Encode'}
              </TabsTrigger>
              <TabsTrigger value="base64-decode">
                {language === 'zh' ? 'Base64解码' : 'Base64 Decode'}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        );
        
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <FileText className="mr-2 h-5 w-5" />
          {language === 'zh' ? '文本工具集' : 'Text Utilities'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '处理和转换文本的多种实用工具' 
            : 'Multiple utilities for processing and transforming text'}
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
          <TabsTrigger value="case-converter">
            {language === 'zh' ? '大小写转换' : 'Case Converter'}
          </TabsTrigger>
          <TabsTrigger value="text-operations">
            {language === 'zh' ? '文本操作' : 'Text Operations'}
          </TabsTrigger>
          <TabsTrigger value="whitespace">
            {language === 'zh' ? '空白处理' : 'Whitespace'}
          </TabsTrigger>
          <TabsTrigger value="encoding">
            {language === 'zh' ? '编码' : 'Encoding'}
          </TabsTrigger>
        </TabsList>
      </Tabs>
      
      <div className="space-y-2">
        {renderOperations()}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium">
              {language === 'zh' ? '输入文本' : 'Input Text'}
            </label>
            <div className="text-xs text-muted-foreground">
              {language === 'zh' 
                ? `字符: ${textStats.chars} | 单词: ${textStats.words} | 行: ${textStats.lines}`
                : `Chars: ${textStats.chars} | Words: ${textStats.words} | Lines: ${textStats.lines}`}
            </div>
          </div>
          <Textarea
            placeholder={language === 'zh' ? '在此输入文本...' : 'Enter text here...'}
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="h-64"
          />
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium">
              {language === 'zh' ? '输出文本' : 'Output Text'}
            </label>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={copyToClipboard}
              disabled={!outputText}
            >
              {copied ? <ClipboardCheck className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
              {language === 'zh' ? '复制' : 'Copy'}
            </Button>
          </div>
          <Textarea
            value={outputText}
            readOnly
            className="h-64"
            placeholder={language === 'zh' ? '处理后的文本将显示在此处...' : 'Processed text will appear here...'}
          />
        </div>
      </div>
      
      <Button onClick={processText} className="w-full">
        {language === 'zh' ? '处理文本' : 'Process Text'}
      </Button>
    </div>
  );
};

export default TextUtils;
