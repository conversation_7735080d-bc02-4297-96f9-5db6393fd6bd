
import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Key, Copy, ClipboardCheck } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent } from '@/components/ui/card';

const JwtDecoder = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [jwtToken, setJwtToken] = useState('');
  const [decodedHeader, setDecodedHeader] = useState('');
  const [decodedPayload, setDecodedPayload] = useState('');
  const [copied, setCopied] = useState(false);

  const decodeJwt = () => {
    try {
      if (!jwtToken.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入JWT令牌' : 'Please enter a JWT token',
        });
        return;
      }

      const parts = jwtToken.split('.');
      if (parts.length !== 3) {
        throw new Error(language === 'zh' ? '无效的JWT格式' : 'Invalid JWT format');
      }

      // Decode header
      const headerBase64 = parts[0];
      const headerStr = atob(headerBase64.replace(/-/g, '+').replace(/_/g, '/'));
      const headerObj = JSON.parse(headerStr);
      const formattedHeader = JSON.stringify(headerObj, null, 2);
      setDecodedHeader(formattedHeader);

      // Decode payload
      const payloadBase64 = parts[1];
      const payloadStr = atob(payloadBase64.replace(/-/g, '+').replace(/_/g, '/'));
      const payloadObj = JSON.parse(payloadStr);
      const formattedPayload = JSON.stringify(payloadObj, null, 2);
      setDecodedPayload(formattedPayload);

      // Add to history
      addToHistory(
        language === 'zh' ? 'JWT解码' : 'JWT Decoder',
        jwtToken,
        `Header: ${formattedHeader}\n\nPayload: ${formattedPayload}`
      );
    } catch (error) {
      console.error('JWT decode error:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '解码失败: JWT格式错误' : 'Decoding failed: Invalid JWT format',
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    toast({
      description: language === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
    });
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <Key className="mr-2 h-5 w-5" />
          {language === 'zh' ? 'JWT解码工具' : 'JWT Decoder'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '解码JWT令牌，查看其中的头部和有效载荷信息' 
            : 'Decode JWT tokens to view header and payload information'}
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          {language === 'zh' ? '输入JWT令牌' : 'Enter JWT Token'}
        </label>
        <Textarea
          placeholder={language === 'zh' ? '粘贴JWT令牌...' : 'Paste JWT token here...'}
          value={jwtToken}
          onChange={(e) => setJwtToken(e.target.value)}
          className="font-mono"
          rows={3}
        />
      </div>

      <Button onClick={decodeJwt} className="w-full">
        {language === 'zh' ? '解码' : 'Decode'}
      </Button>

      {(decodedHeader || decodedPayload) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-4 space-y-2">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold">
                  {language === 'zh' ? '头部 (Header)' : 'Header'}
                </h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => copyToClipboard(decodedHeader)}
                >
                  {copied ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <pre className="bg-muted p-2 rounded overflow-x-auto text-xs">
                {decodedHeader}
              </pre>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 space-y-2">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold">
                  {language === 'zh' ? '有效载荷 (Payload)' : 'Payload'}
                </h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => copyToClipboard(decodedPayload)}
                >
                  {copied ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <pre className="bg-muted p-2 rounded overflow-x-auto text-xs">
                {decodedPayload}
              </pre>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default JwtDecoder;
