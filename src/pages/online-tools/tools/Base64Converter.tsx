
import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Co<PERSON>, ClipboardCheck, FileText } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

const Base64Converter = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [mode, setMode] = useState<'encode' | 'decode'>('encode');
  const [urlSafe, setUrlSafe] = useState(false);
  const [copied, setCopied] = useState(false);

  const convertBase64 = () => {
    try {
      if (!input.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入要转换的内容' : 'Please enter content to convert',
        });
        setOutput('');
        return;
      }

      let result = '';
      if (mode === 'encode') {
        // Encode to Base64
        result = btoa(input);
        if (urlSafe) {
          result = result.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        }
      } else {
        // Decode from Base64
        try {
          let processedInput = input;
          if (urlSafe) {
            // Restore standard base64 for decoding
            processedInput = processedInput.replace(/-/g, '+').replace(/_/g, '/');
            // Add padding if necessary
            while (processedInput.length % 4 !== 0) {
              processedInput += '=';
            }
          }
          result = atob(processedInput);
        } catch (error) {
          throw new Error(language === 'zh' ? '无效的Base64编码' : 'Invalid Base64 encoding');
        }
      }
      
      setOutput(result);
      
      // Add to history
      addToHistory(
        mode === 'encode'
          ? (language === 'zh' ? 'Base64编码' : 'Base64 Encode')
          : (language === 'zh' ? 'Base64解码' : 'Base64 Decode'),
        input.substring(0, 100) + (input.length > 100 ? '...' : ''),
        result.substring(0, 100) + (result.length > 100 ? '...' : '')
      );
    } catch (error) {
      console.error('Base64 conversion error:', error);
      toast({
        variant: "destructive",
        description: error instanceof Error ? error.message : (language === 'zh' ? 'Base64转换错误' : 'Base64 conversion error'),
      });
      setOutput('');
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(output);
    setCopied(true);
    toast({
      description: language === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
    });
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <FileText className="mr-2 h-5 w-5" />
          {language === 'zh' ? 'Base64转换工具' : 'Base64 Converter'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '在文本和Base64编码之间进行转换' 
            : 'Convert between plain text and Base64 encoding'}
        </p>
      </div>

      <Tabs value={mode} onValueChange={(value) => setMode(value as 'encode' | 'decode')} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="encode">
            {language === 'zh' ? '编码' : 'Encode'}
          </TabsTrigger>
          <TabsTrigger value="decode">
            {language === 'zh' ? '解码' : 'Decode'}
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <label className="block text-sm font-medium mb-2">
            {mode === 'encode'
              ? (language === 'zh' ? '输入文本' : 'Input Text')
              : (language === 'zh' ? '输入Base64' : 'Input Base64')}
          </label>
          <Textarea
            placeholder={
              mode === 'encode'
                ? (language === 'zh' ? '输入要编码的文本...' : 'Enter text to encode...')
                : (language === 'zh' ? '输入要解码的Base64...' : 'Enter Base64 to decode...')
            }
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="h-40"
          />
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between mb-2">
            <label className="block text-sm font-medium">
              {mode === 'encode'
                ? (language === 'zh' ? '输出Base64' : 'Output Base64')
                : (language === 'zh' ? '输出文本' : 'Output Text')}
            </label>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={copyToClipboard}
              disabled={!output}
            >
              {copied ? <ClipboardCheck className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
              {language === 'zh' ? '复制' : 'Copy'}
            </Button>
          </div>
          <Textarea
            value={output}
            readOnly
            className="h-40"
            placeholder={
              mode === 'encode'
                ? (language === 'zh' ? 'Base64编码将显示在此处...' : 'Base64 encoding will appear here...')
                : (language === 'zh' ? '解码文本将显示在此处...' : 'Decoded text will appear here...')
            }
          />
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Switch 
            id="url-safe" 
            checked={urlSafe} 
            onCheckedChange={setUrlSafe}
          />
          <Label htmlFor="url-safe" className="text-sm">
            {language === 'zh' ? 'URL安全模式 (替换 +, /, =)' : 'URL-safe mode (replace +, /, =)'}
          </Label>
        </div>
        
        <Button onClick={convertBase64}>
          {mode === 'encode'
            ? (language === 'zh' ? '编码' : 'Encode')
            : (language === 'zh' ? '解码' : 'Decode')}
        </Button>
      </div>
    </div>
  );
};

export default Base64Converter;
