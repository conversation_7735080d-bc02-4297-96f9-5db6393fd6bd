
import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { useToast } from '@/components/ui/use-toast';
import { User } from 'lucide-react';

// Import components, types, and utilities from refactored files
import { Name, NationalityCode, GenderCode } from './name-generator/types';
import { generateNames } from './name-generator/utils';
import { middleNames } from './name-generator/nameData';
import GeneratorControls from './name-generator/GeneratorControls';
import NameList from './name-generator/NameList';

const NameGenerator = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [generatedNames, setGeneratedNames] = useState<Name[]>([]);
  const [gender, setGender] = useState<GenderCode>('random');
  const [count, setCount] = useState(5);
  const [nationality, setNationality] = useState<NationalityCode>('random');
  const [includeMiddleName, setIncludeMiddleName] = useState(false);

  const handleGenerateNames = () => {
    try {
      const names = generateNames(count, gender, nationality, includeMiddleName, middleNames);
      setGeneratedNames(names);
      
      // Add to history
      addToHistory(
        language === 'zh' ? '姓名生成器' : 'Name Generator',
        `Gender: ${gender}, Nationality: ${nationality}, Count: ${count}`,
        names.map(n => `${n.firstName} ${n.lastName} (${n.gender})`).join(', ')
      );
    } catch (error) {
      console.error('Name generation error:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '生成姓名时出错' : 'Error generating names',
      });
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <User className="mr-2 h-5 w-5" />
          {language === 'zh' ? '姓名生成工具' : 'Name Generator'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '生成随机人名，可以选择性别与国籍' 
            : 'Generate random person names with selectable gender and nationality'}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <GeneratorControls
          language={language}
          gender={gender}
          setGender={setGender}
          count={count}
          setCount={setCount}
          nationality={nationality}
          setNationality={setNationality}
          includeMiddleName={includeMiddleName}
          setIncludeMiddleName={setIncludeMiddleName}
          onGenerate={handleGenerateNames}
        />
        
        <NameList
          language={language}
          generatedNames={generatedNames}
        />
      </div>
    </div>
  );
};

export default NameGenerator;
