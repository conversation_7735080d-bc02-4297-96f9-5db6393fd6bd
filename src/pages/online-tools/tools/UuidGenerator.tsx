
import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Copy, ClipboardCheck, RefreshCw, FilePlus } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { v1 as uuidv1, v4 as uuidv4, v5 as uuidv5, v3 as uuidv3 } from 'uuid';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';

const UuidGenerator = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [uuidType, setUuidType] = useState('v4');
  const [generatedUuids, setGeneratedUuids] = useState<string[]>([]);
  const [count, setCount] = useState(1);
  const [namespace, setNamespace] = useState('');
  const [name, setName] = useState('');
  const [copied, setCopied] = useState<Record<number, boolean>>({});

  const generateUuid = () => {
    try {
      const results: string[] = [];
      const actualCount = Math.min(100, Math.max(1, count));
      
      for (let i = 0; i < actualCount; i++) {
        let uuid: string;
        
        switch (uuidType) {
          case 'v1':
            uuid = uuidv1();
            break;
          case 'v3':
            if (!namespace || !name) {
              throw new Error(language === 'zh' ? '生成UUID v3需要命名空间和名称' : 'Namespace and name are required for UUID v3');
            }
            uuid = uuidv3(name, namespace);
            break;
          case 'v5':
            if (!namespace || !name) {
              throw new Error(language === 'zh' ? '生成UUID v5需要命名空间和名称' : 'Namespace and name are required for UUID v5');
            }
            uuid = uuidv5(name, namespace);
            break;
          case 'v4':
          default:
            uuid = uuidv4();
        }
        
        results.push(uuid);
      }
      
      setGeneratedUuids(results);
      
      // Add to history
      addToHistory(
        language === 'zh' ? `UUID生成 (${uuidType})` : `UUID Generator (${uuidType})`,
        `Type: ${uuidType}, Count: ${actualCount}${name ? `, Name: ${name}` : ''}`,
        results.join('\n')
      );
    } catch (error) {
      console.error('UUID generation error:', error);
      toast({
        variant: "destructive",
        description: error instanceof Error ? error.message : (language === 'zh' ? '生成UUID时出错' : 'Error generating UUID'),
      });
    }
  };

  const copyToClipboard = (text: string, index: number) => {
    navigator.clipboard.writeText(text);
    setCopied({ ...copied, [index]: true });
    toast({
      description: language === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
    });
    setTimeout(() => setCopied(prevState => ({ ...prevState, [index]: false })), 2000);
  };

  const copyAll = () => {
    navigator.clipboard.writeText(generatedUuids.join('\n'));
    toast({
      description: language === 'zh' ? '已复制所有UUID到剪贴板' : 'Copied all UUIDs to clipboard',
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <FilePlus className="mr-2 h-5 w-5" />
          {language === 'zh' ? 'UUID生成工具' : 'UUID Generator'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '生成各种版本的UUID标识符' 
            : 'Generate various versions of Universally Unique Identifiers (UUIDs)'}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="uuid-type" className="mb-2 block">
            {language === 'zh' ? 'UUID版本' : 'UUID Version'}
          </Label>
          <Select value={uuidType} onValueChange={setUuidType}>
            <SelectTrigger id="uuid-type">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="v1">UUID v1 (timestamp-based)</SelectItem>
              <SelectItem value="v4">UUID v4 (random)</SelectItem>
              <SelectItem value="v3">UUID v3 (namespace, MD5)</SelectItem>
              <SelectItem value="v5">UUID v5 (namespace, SHA-1)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="uuid-count" className="mb-2 block">
            {language === 'zh' ? '生成数量' : 'Count'}
          </Label>
          <Input 
            id="uuid-count"
            type="number"
            min="1"
            max="100"
            value={count}
            onChange={(e) => setCount(parseInt(e.target.value) || 1)}
          />
        </div>
      </div>
      
      {(uuidType === 'v3' || uuidType === 'v5') && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="namespace" className="mb-2 block">
              {language === 'zh' ? '命名空间' : 'Namespace'}
            </Label>
            <Input 
              id="namespace"
              value={namespace}
              placeholder={language === 'zh' ? '输入UUID命名空间' : 'Enter namespace UUID'}
              onChange={(e) => setNamespace(e.target.value)}
            />
          </div>
          
          <div>
            <Label htmlFor="name" className="mb-2 block">
              {language === 'zh' ? '名称' : 'Name'}
            </Label>
            <Input 
              id="name"
              value={name}
              placeholder={language === 'zh' ? '输入名称' : 'Enter name'}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
        </div>
      )}

      <div className="flex gap-2">
        <Button onClick={generateUuid} className="flex-1">
          <RefreshCw className="mr-2 h-4 w-4" />
          {language === 'zh' ? '生成UUID' : 'Generate UUID'}
        </Button>
        
        {generatedUuids.length > 0 && (
          <Button variant="outline" onClick={copyAll}>
            <Copy className="mr-2 h-4 w-4" />
            {language === 'zh' ? '复制全部' : 'Copy All'}
          </Button>
        )}
      </div>

      {generatedUuids.length > 0 && (
        <div className="border rounded-md">
          <div className="px-4 py-2 bg-muted border-b flex items-center justify-between">
            <span className="font-medium">
              {language === 'zh' ? '生成的UUID' : 'Generated UUIDs'}
            </span>
            <Badge variant="outline">
              {language === 'zh' ? `${generatedUuids.length} 个` : `${generatedUuids.length} UUIDs`}
            </Badge>
          </div>
          <div className="max-h-80 overflow-y-auto">
            <ul className="divide-y">
              {generatedUuids.map((uuid, index) => (
                <li key={index} className="px-4 py-2 flex justify-between items-center">
                  <code className="text-sm font-mono">{uuid}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(uuid, index)}
                  >
                    {copied[index] ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default UuidGenerator;
