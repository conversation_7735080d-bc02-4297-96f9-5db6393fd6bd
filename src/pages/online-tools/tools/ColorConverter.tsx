
import React, { useState, useEffect } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Co<PERSON>, ClipboardCheck } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const ColorConverter = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [inputColor, setInputColor] = useState('#3b82f6');
  const [colorResults, setColorResults] = useState({
    hex: '#3b82f6',
    rgb: 'rgb(59, 130, 246)',
    hsl: 'hsl(217, 91%, 60%)',
    cmyk: 'cmyk(76%, 47%, 0%, 4%)',
    name: 'Royal Blue'
  });
  const [copied, setCopied] = useState<Record<string, boolean>>({});

  // Update color values when input changes
  useEffect(() => {
    convertColor(inputColor);
  }, [inputColor]);

  const convertColor = (color: string) => {
    try {
      // Parse the color
      let r = 0, g = 0, b = 0;
      
      // Check if it's a valid hex color
      if (/^#?([a-f\d]{3}|[a-f\d]{6})$/i.test(color)) {
        // Convert shorthand hex to full hex
        const hex = color.replace(/^#/, '');
        const fullHex = hex.length === 3 
          ? '#' + hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2]
          : '#' + hex;
        
        // Extract RGB values
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(fullHex);
        if (result) {
          r = parseInt(result[1], 16);
          g = parseInt(result[2], 16);
          b = parseInt(result[3], 16);
        }
      } 
      // Check if it's RGB format
      else if (/^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i.test(color)) {
        const result = /^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i.exec(color);
        if (result) {
          r = parseInt(result[1]);
          g = parseInt(result[2]);
          b = parseInt(result[3]);
        }
      }
      // If we have valid RGB values, convert to other formats
      if (r >= 0 && r <= 255 && g >= 0 && g <= 255 && b >= 0 && b <= 255) {
        // HEX
        const hex = '#' + ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0');
        
        // RGB
        const rgb = `rgb(${r}, ${g}, ${b})`;
        
        // HSL
        const rNorm = r / 255;
        const gNorm = g / 255;
        const bNorm = b / 255;
        const cMax = Math.max(rNorm, gNorm, bNorm);
        const cMin = Math.min(rNorm, gNorm, bNorm);
        const delta = cMax - cMin;
        
        let h = 0, s = 0, l = (cMax + cMin) / 2;
        
        if (delta !== 0) {
          s = delta / (1 - Math.abs(2 * l - 1));
          if (cMax === rNorm) {
            h = ((gNorm - bNorm) / delta) % 6;
          } else if (cMax === gNorm) {
            h = (bNorm - rNorm) / delta + 2;
          } else {
            h = (rNorm - gNorm) / delta + 4;
          }
          h *= 60;
          if (h < 0) h += 360;
        }
        
        const hsl = `hsl(${Math.round(h)}, ${Math.round(s * 100)}%, ${Math.round(l * 100)}%)`;
        
        // CMYK
        let k = 1 - Math.max(rNorm, gNorm, bNorm);
        let c = (1 - rNorm - k) / (1 - k) || 0;
        let m = (1 - gNorm - k) / (1 - k) || 0;
        let y = (1 - bNorm - k) / (1 - k) || 0;
        
        const cmyk = `cmyk(${Math.round(c * 100)}%, ${Math.round(m * 100)}%, ${Math.round(y * 100)}%, ${Math.round(k * 100)}%)`;
        
        // Approximate color name (very basic)
        const colorName = getApproximateColorName(r, g, b);
        
        setColorResults({
          hex,
          rgb,
          hsl,
          cmyk,
          name: colorName
        });
        
        // Add to history
        addToHistory(
          language === 'zh' ? '颜色转换' : 'Color Converter',
          inputColor,
          `HEX: ${hex}, RGB: ${rgb}, HSL: ${hsl}`
        );
      } else {
        // Invalid color
        setColorResults({
          hex: '#000000',
          rgb: 'rgb(0, 0, 0)',
          hsl: 'hsl(0, 0%, 0%)',
          cmyk: 'cmyk(0%, 0%, 0%, 100%)',
          name: 'Invalid Color'
        });
      }
    } catch (error) {
      console.error('Color conversion error:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '颜色格式无效' : 'Invalid color format',
      });
    }
  };

  // Very basic color name approximation (not accurate)
  const getApproximateColorName = (r: number, g: number, b: number): string => {
    const colorMap = [
      { name: 'Black', r: 0, g: 0, b: 0 },
      { name: 'White', r: 255, g: 255, b: 255 },
      { name: 'Red', r: 255, g: 0, b: 0 },
      { name: 'Green', r: 0, g: 255, b: 0 },
      { name: 'Blue', r: 0, g: 0, b: 255 },
      { name: 'Yellow', r: 255, g: 255, b: 0 },
      { name: 'Cyan', r: 0, g: 255, b: 255 },
      { name: 'Magenta', r: 255, g: 0, b: 255 },
      { name: 'Silver', r: 192, g: 192, b: 192 },
      { name: 'Gray', r: 128, g: 128, b: 128 },
      { name: 'Maroon', r: 128, g: 0, b: 0 },
      { name: 'Olive', r: 128, g: 128, b: 0 },
      { name: 'Navy', r: 0, g: 0, b: 128 },
      { name: 'Purple', r: 128, g: 0, b: 128 },
      { name: 'Teal', r: 0, g: 128, b: 128 },
      { name: 'Orange', r: 255, g: 165, b: 0 },
      { name: 'Royal Blue', r: 65, g: 105, b: 225 },
      { name: 'Forest Green', r: 34, g: 139, b: 34 },
      { name: 'Gold', r: 255, g: 215, b: 0 },
      { name: 'Hot Pink', r: 255, g: 105, b: 180 },
    ];
    
    let closestColor = colorMap[0];
    let closestDistance = 255 * 3;
    
    for (const color of colorMap) {
      const distance = Math.sqrt(
        Math.pow(r - color.r, 2) + 
        Math.pow(g - color.g, 2) + 
        Math.pow(b - color.b, 2)
      );
      
      if (distance < closestDistance) {
        closestDistance = distance;
        closestColor = color;
      }
    }
    
    return closestColor.name;
  };

  const copyToClipboard = (text: string, format: string) => {
    navigator.clipboard.writeText(text);
    setCopied({ ...copied, [format]: true });
    toast({
      description: `${language === 'zh' ? '已复制 ' : 'Copied '} ${format.toUpperCase()}`,
    });
    setTimeout(() => setCopied(prevState => ({ ...prevState, [format]: false })), 2000);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <span className="w-5 h-5 mr-2 rounded-full border" style={{ backgroundColor: colorResults.hex }}></span>
          {language === 'zh' ? '颜色转换工具' : 'Color Converter'}
        </h2>
        <p className="text-muted-foreground">
          {language ===  'zh' 
            ? '在不同颜色格式之间进行转换 (HEX, RGB, HSL, CMYK)' 
            : 'Convert between different color formats (HEX, RGB, HSL, CMYK)'}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
        <div className="space-y-2">
          <Label htmlFor="color-input" className="block">
            {language === 'zh' ? '输入颜色' : 'Input Color'}
          </Label>
          <Input
            id="color-input"
            type="text"
            value={inputColor}
            onChange={(e) => setInputColor(e.target.value)}
            placeholder="#RRGGBB or rgb(r,g,b)"
            className="font-mono"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="color-picker" className="block">
            {language === 'zh' ? '颜色选择器' : 'Color Picker'}
          </Label>
          <div className="flex items-center gap-2">
            <Input
              id="color-picker"
              type="color"
              value={colorResults.hex}
              onChange={(e) => setInputColor(e.target.value)}
              className="w-12 h-12 p-1 cursor-pointer"
            />
            <div className="flex-1 h-12 rounded-md border" style={{ backgroundColor: colorResults.hex }}></div>
          </div>
        </div>
        
        <div className="flex items-end h-full">
          <Button onClick={() => convertColor(inputColor)} className="w-full">
            {language === 'zh' ? '转换' : 'Convert'}
          </Button>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="whitespace-nowrap">{language === 'zh' ? '格式' : 'Format'}</TableHead>
              <TableHead className="whitespace-nowrap">{language === 'zh' ? '值' : 'Value'}</TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell className="font-medium">HEX</TableCell>
              <TableCell className="font-mono">{colorResults.hex}</TableCell>
              <TableCell>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => copyToClipboard(colorResults.hex, 'hex')}
                >
                  {copied['hex'] ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">RGB</TableCell>
              <TableCell className="font-mono">{colorResults.rgb}</TableCell>
              <TableCell>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => copyToClipboard(colorResults.rgb, 'rgb')}
                >
                  {copied['rgb'] ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">HSL</TableCell>
              <TableCell className="font-mono">{colorResults.hsl}</TableCell>
              <TableCell>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => copyToClipboard(colorResults.hsl, 'hsl')}
                >
                  {copied['hsl'] ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">CMYK</TableCell>
              <TableCell className="font-mono">{colorResults.cmyk}</TableCell>
              <TableCell>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => copyToClipboard(colorResults.cmyk, 'cmyk')}
                >
                  {copied['cmyk'] ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">{language === 'zh' ? '颜色名称' : 'Color Name'}</TableCell>
              <TableCell>{colorResults.name}</TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-8 gap-2">
        {['#FF0000', '#FF7F00', '#FFFF00', '#00FF00', '#0000FF', '#4B0082', '#8B00FF', '#000000']
          .map((color, index) => (
            <Button
              key={index}
              variant="outline"
              style={{ backgroundColor: color }}
              className="h-10 w-full"
              onClick={() => setInputColor(color)}
              title={color}
            />
          ))}
      </div>
    </div>
  );
};

export default ColorConverter;
