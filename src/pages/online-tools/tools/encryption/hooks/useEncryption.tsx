
import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { 
  EncryptionMode, 
  EncryptionType, 
  SymmetricAlgorithm, 
  AsymmetricAlgorithm, 
  AESMode, 
  AESPadding, 
  KeySize 
} from '../types';
import { xorCipher, simulateAsymmetricCrypto, generateKeyPair } from '../utils';

interface UseEncryptionProps {
  language: string;
  addToHistory: (tool: string, input: string, output: string) => void;
}

export const useEncryption = ({ language, addToHistory }: UseEncryptionProps) => {
  const { toast } = useToast();
  
  // Basic state
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [key, setKey] = useState('');
  const [mode, setMode] = useState<EncryptionMode>('encrypt');
  const [copied, setCopied] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  // Advanced encryption state
  const [encryptionType, setEncryptionType] = useState<EncryptionType>('symmetric');
  const [symmetricAlgorithm, setSymmetricAlgorithm] = useState<SymmetricAlgorithm>('aes');
  const [asymmetricAlgorithm, setAsymmetricAlgorithm] = useState<AsymmetricAlgorithm>('rsa');
  const [aesMode, setAesMode] = useState<AESMode>('cbc');
  const [aesPadding, setAesPadding] = useState<AESPadding>('pkcs7');
  const [keySize, setKeySize] = useState<KeySize>('256');
  const [iv, setIv] = useState('');
  const [publicKey, setPublicKey] = useState('');
  const [privateKey, setPrivateKey] = useState('');
  const [useIv, setUseIv] = useState(true);

  const performCrypto = async () => {
    if (!input.trim()) {
      toast({
        variant: "destructive",
        description: language === 'zh' ? '请输入要处理的文本' : 'Please enter text to process',
      });
      return;
    }

    if (encryptionType === 'symmetric' && !key.trim() && symmetricAlgorithm !== 'xor') {
      toast({
        variant: "destructive",
        description: language === 'zh' ? '请输入密钥' : 'Please enter a key',
      });
      return;
    }
    
    if (encryptionType === 'asymmetric') {
      if (mode === 'encrypt' && !publicKey.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入公钥' : 'Please enter a public key',
        });
        return;
      }
      if (mode === 'decrypt' && !privateKey.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入私钥' : 'Please enter a private key',
        });
        return;
      }
    }

    try {
      let result = '';
      let operationDetails = '';
      
      if (encryptionType === 'symmetric') {
        // Handle symmetric encryption
        if (symmetricAlgorithm === 'xor') {
          result = xorCipher(input, key);
          operationDetails = `XOR Cipher, Key: ${key.substring(0, 3)}...`;
        } else {
          // In a real implementation, you would use a proper crypto library
          // For now, just simulate different algorithms with the XOR cipher
          result = xorCipher(input, key);
          operationDetails = `${symmetricAlgorithm.toUpperCase()}, Mode: ${aesMode}, Padding: ${aesPadding}, Key Size: ${keySize}`;
        }
      } else {
        // Handle asymmetric encryption (simulation)
        result = simulateAsymmetricCrypto(input, asymmetricAlgorithm, mode);
        operationDetails = `${asymmetricAlgorithm.toUpperCase()}`;
      }
      
      setOutput(result);
      
      // Add to history
      addToHistory(
        mode === 'encrypt'
          ? (language === 'zh' ? '加密' : 'Encryption')
          : (language === 'zh' ? '解密' : 'Decryption'),
        `${operationDetails} - ${input.substring(0, 20)}${input.length > 20 ? '...' : ''}`,
        `${result.substring(0, 20)}${result.length > 20 ? '...' : ''}`
      );
    } catch (error) {
      console.error('Cryptography error:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '处理过程中发生错误' : 'Error during processing',
      });
    }
  };

  const handleGenerateKeyPair = () => {
    const keys = generateKeyPair();
    setPublicKey(keys.publicKey);
    setPrivateKey(keys.privateKey);
    
    toast({
      description: language === 'zh' ? '已生成密钥对（演示用）' : 'Key pair generated (Demo)',
    });
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(output);
    setCopied(true);
    toast({
      description: language === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
    });
    setTimeout(() => setCopied(false), 2000);
  };

  return {
    // Basic state
    input, setInput,
    output, setOutput,
    key, setKey,
    mode, setMode,
    copied, setCopied,
    showPassword, setShowPassword,
    
    // Advanced encryption state
    encryptionType, setEncryptionType,
    symmetricAlgorithm, setSymmetricAlgorithm,
    asymmetricAlgorithm, setAsymmetricAlgorithm,
    aesMode, setAesMode,
    aesPadding, setAesPadding,
    keySize, setKeySize,
    iv, setIv,
    publicKey, setPublicKey,
    privateKey, setPrivateKey,
    useIv, setUseIv,
    
    // Actions
    performCrypto,
    handleGenerateKeyPair,
    copyToClipboard
  };
};
