
import React from 'react';
import EncryptionBody from './components/EncryptionBody';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../../OnlineToolsContext';

// Define the props interface for this component
interface EncryptionContentProps {
  language: string;
  addToHistory: (tool: string, input: string, output: string) => void;
}

const EncryptionContent: React.FC<EncryptionContentProps> = ({ language, addToHistory }) => {
  return <EncryptionBody language={language} addToHistory={addToHistory} />;
};

export default EncryptionContent;
