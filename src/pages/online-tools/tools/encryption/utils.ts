
// Simple XOR cipher for demonstration (not secure)
export const xorCipher = (text: string, key: string): string => {
  if (!key) key = 'default'; // Default key for demo
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return result;
};

// Simulate asymmetric crypto (not real encryption)
export const simulateAsymmetricCrypto = (
  text: string, 
  algorithm: string, 
  mode: 'encrypt' | 'decrypt'
): string => {
  // This is just for UI demonstration - not real encryption
  // In a real app, you would use a proper crypto library
  let prefix = '';
  switch(algorithm) {
    case 'rsa': prefix = 'RSA-'; break;
    case 'ecc': prefix = 'ECC-'; break;
    case 'dsa': prefix = 'DSA-'; break;
  }
  
  // Simple character manipulation to simulate encryption/decryption
  if (mode === 'encrypt') {
    return prefix + btoa(text).split('').reverse().join('');
  } else {
    const processed = text.replace(new RegExp(`^${prefix}`), '');
    try {
      return atob(processed.split('').reverse().join(''));
    } catch(e) {
      return '解密失败，无效的输入 / Decryption failed, invalid input';
    }
  }
};

export const generateKeyPair = (): { publicKey: string, privateKey: string } => {
  // In a real app, you would use a proper crypto library
  // This is just for UI demonstration
  return {
    publicKey: 'demo-public-key-' + Math.random().toString(36).substring(2, 10),
    privateKey: 'demo-private-key-' + Math.random().toString(36).substring(2, 10)
  };
};
