
import React from 'react';
import { Lock, Unlock, Shield } from 'lucide-react';
import { EncryptionMode } from '../types';
import PageHeader from '@/components/common/PageHeader';

interface EncryptionHeaderProps {
  language: string;
  mode: EncryptionMode;
}

const EncryptionHeader: React.FC<EncryptionHeaderProps> = ({ language, mode }) => {
  return (
    <div className="mb-6">
      <PageHeader
        title={language === 'zh' ? '加密/解密工具' : 'Encryption/Decryption Tool'}
        subtitle={language === 'zh' 
          ? '加密或解密文本数据，保护您的敏感信息' 
          : 'Encrypt or decrypt text data to protect your sensitive information'}
        icon={Shield}
        colorScheme="info"
        className="rounded-lg"
      />
      
      <div className="text-xs text-amber-600 dark:text-amber-400 mt-4 bg-amber-50 dark:bg-amber-900/30 p-3 rounded-lg border border-amber-200 dark:border-amber-800">
        {language === 'zh' 
          ? '注意：这是一个演示工具，仅用于教育目的。请勿用于加密敏感数据。' 
          : 'Note: This is a demonstration tool intended for educational purposes only. Do not use it to encrypt sensitive data.'}
      </div>
    </div>
  );
};

export default EncryptionHeader;
