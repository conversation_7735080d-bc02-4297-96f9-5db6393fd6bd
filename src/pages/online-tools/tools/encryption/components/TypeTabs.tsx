
import React from 'react';
import { Key, ShieldCheck } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { EncryptionType } from '../types';

interface TypeTabsProps {
  language: string;
  encryptionType: EncryptionType;
  onTypeChange: (type: EncryptionType) => void;
}

const TypeTabs: React.FC<TypeTabsProps> = ({ language, encryptionType, onTypeChange }) => {
  return (
    <div className="mb-6">
      <h3 className="text-sm font-medium mb-2">
        {language === 'zh' ? '加密类型' : 'Encryption Type'}
      </h3>
      <Tabs value={encryptionType} onValueChange={(value) => onTypeChange(value as EncryptionType)}>
        <TabsList className="w-full">
          <TabsTrigger value="symmetric" className="flex-1">
            <Key className="mr-2 h-4 w-4" />
            {language === 'zh' ? '对称加密' : 'Symmetric'}
          </TabsTrigger>
          <TabsTrigger value="asymmetric" className="flex-1">
            <ShieldCheck className="mr-2 h-4 w-4" />
            {language === 'zh' ? '非对称加密' : 'Asymmetric'}
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
};

export default TypeTabs;
