
import React from 'react';
import { Lock, Unlock } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EncryptionMode } from '../types';

interface ModeTabsProps {
  language: string;
  mode: EncryptionMode;
  onModeChange: (mode: EncryptionMode) => void;
}

const ModeTabs: React.FC<ModeTabsProps> = ({ language, mode, onModeChange }) => {
  return (
    <Tabs value={mode} onValueChange={(value) => onModeChange(value as EncryptionMode)} className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="encrypt" className="flex items-center">
          <Lock className="mr-2 h-4 w-4" />
          {language === 'zh' ? '加密' : 'Encrypt'}
        </TabsTrigger>
        <TabsTrigger value="decrypt" className="flex items-center">
          <Unlock className="mr-2 h-4 w-4" />
          {language === 'zh' ? '解密' : 'Decrypt'}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

export default ModeTabs;
