
import React from 'react';
import { Tabs, TabsContent } from "@/components/ui/tabs";

import ModeTabs from './ModeTabs';
import TypeTabs from './TypeTabs';
import SymmetricEncryptionOptions from './SymmetricEncryptionOptions';
import AsymmetricEncryptionOptions from './AsymmetricEncryptionOptions';
import TextIOSection from './TextIOSection';
import EncryptionHeader from './EncryptionHeader';
import { EncryptionMode, EncryptionType } from '../types';
import { useEncryption } from '../hooks/useEncryption';

interface EncryptionBodyProps {
  language: string;
  addToHistory: (tool: string, input: string, output: string) => void;
}

const EncryptionBody: React.FC<EncryptionBodyProps> = ({ language, addToHistory }) => {
  const { 
    input, setInput,
    output, setOutput,
    key, setKey,
    mode, setMode,
    copied, setCopied,
    showPassword, setShowPassword,
    encryptionType, setEncryptionType,
    symmetricAlgorithm, setSymmetricAlgorithm,
    asymmetricAlgorithm, setAsymmetricAlgorithm,
    aesMode, setAesMode,
    aesPadding, setAesPadding,
    keySize, setKeySize,
    iv, setIv,
    publicKey, setPublicKey,
    privateKey, setPrivateKey,
    useIv, setUseIv,
    performCrypto,
    handleGenerateKeyPair,
    copyToClipboard
  } = useEncryption({ language, addToHistory });

  return (
    <div className="space-y-6">
      <EncryptionHeader language={language} mode={mode} />
      
      <ModeTabs 
        language={language} 
        mode={mode} 
        onModeChange={(newMode) => setMode(newMode)} 
      />
      
      <TypeTabs 
        language={language} 
        encryptionType={encryptionType} 
        onTypeChange={(newType) => setEncryptionType(newType)} 
      />
      
      <Tabs value={encryptionType} onValueChange={(value) => setEncryptionType(value as EncryptionType)}>
        <TabsContent value="symmetric">
          <SymmetricEncryptionOptions 
            language={language}
            symmetricAlgorithm={symmetricAlgorithm}
            setSymmetricAlgorithm={setSymmetricAlgorithm}
            aesMode={aesMode}
            setAesMode={setAesMode}
            aesPadding={aesPadding}
            setAesPadding={setAesPadding}
            keySize={keySize}
            setKeySize={setKeySize}
            key={key}
            setKey={setKey}
            iv={iv}
            setIv={setIv}
            showPassword={showPassword}
            setShowPassword={setShowPassword}
            useIv={useIv}
            setUseIv={setUseIv}
          />
        </TabsContent>
        
        <TabsContent value="asymmetric">
          <AsymmetricEncryptionOptions 
            language={language}
            asymmetricAlgorithm={asymmetricAlgorithm}
            setAsymmetricAlgorithm={setAsymmetricAlgorithm}
            publicKey={publicKey}
            setPublicKey={setPublicKey}
            privateKey={privateKey}
            setPrivateKey={setPrivateKey}
            mode={mode}
            onGenerateKeyPair={handleGenerateKeyPair}
          />
        </TabsContent>
      </Tabs>
      
      <TextIOSection
        language={language}
        mode={mode}
        input={input}
        setInput={setInput}
        output={output}
        copied={copied}
        onCopyClick={copyToClipboard}
        onProcessClick={performCrypto}
      />
    </div>
  );
};

export default EncryptionBody;
