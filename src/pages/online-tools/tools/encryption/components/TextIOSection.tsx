
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Clipboard, ClipboardCheck } from 'lucide-react';
import { EncryptionMode } from '../types';

interface TextIOSectionProps {
  language: string;
  mode: EncryptionMode;
  input: string;
  setInput: (text: string) => void;
  output: string;
  copied: boolean;
  onCopyClick: () => void;
  onProcessClick: () => void;
}

const TextIOSection: React.FC<TextIOSectionProps> = ({
  language,
  mode,
  input,
  setInput,
  output,
  copied,
  onCopyClick,
  onProcessClick
}) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <label className="block text-sm font-medium mb-2">
            {mode === 'encrypt'
              ? (language === 'zh' ? '明文' : 'Plaintext')
              : (language === 'zh' ? '密文' : 'Ciphertext')}
          </label>
          <Textarea
            placeholder={
              mode === 'encrypt'
                ? (language === 'zh' ? '输入要加密的文本...' : 'Enter text to encrypt...')
                : (language === 'zh' ? '输入要解密的文本...' : 'Enter text to decrypt...')
            }
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="h-40"
          />
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between mb-2">
            <label className="block text-sm font-medium">
              {mode === 'encrypt'
                ? (language === 'zh' ? '密文' : 'Ciphertext')
                : (language === 'zh' ? '明文' : 'Plaintext')}
            </label>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onCopyClick}
              disabled={!output}
            >
              {copied 
                ? <ClipboardCheck className="h-4 w-4 mr-1" /> 
                : <Clipboard className="h-4 w-4 mr-1" />}
              {language === 'zh' ? '复制' : 'Copy'}
            </Button>
          </div>
          <Textarea
            value={output}
            readOnly
            className="h-40"
            placeholder={
              mode === 'encrypt'
                ? (language === 'zh' ? '加密结果将显示在此处...' : 'Encrypted result will appear here...')
                : (language === 'zh' ? '解密结果将显示在此处...' : 'Decrypted result will appear here...')
            }
          />
        </div>
      </div>
      
      <Button onClick={onProcessClick} className="w-full">
        {mode === 'encrypt'
          ? (language === 'zh' ? '加密' : 'Encrypt')
          : (language === 'zh' ? '解密' : 'Decrypt')}
      </Button>
    </div>
  );
};

export default TextIOSection;
