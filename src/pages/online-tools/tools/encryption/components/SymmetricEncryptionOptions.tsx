
import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SymmetricAlgorithm, AESMode, AESPadding, KeySize } from '../types';

interface SymmetricEncryptionOptionsProps {
  language: string;
  symmetricAlgorithm: SymmetricAlgorithm;
  setSymmetricAlgorithm: (algo: SymmetricAlgorithm) => void;
  aesMode: AESMode;
  setAesMode: (mode: AESMode) => void;
  aesPadding: AESPadding;
  setAesPadding: (padding: AESPadding) => void;
  keySize: KeySize;
  setKeySize: (size: KeySize) => void;
  key: string;
  setKey: (key: string) => void;
  iv: string;
  setIv: (iv: string) => void;
  showPassword: boolean;
  setShowPassword: (show: boolean) => void;
  useIv: boolean;
  setUseIv: (use: boolean) => void;
}

const SymmetricEncryptionOptions: React.FC<SymmetricEncryptionOptionsProps> = ({
  language,
  symmetricAlgorithm,
  setSymmetricAlgorithm,
  aesMode,
  setAesMode,
  aesPadding,
  setAesPadding,
  keySize,
  setKeySize,
  key,
  setKey,
  iv,
  setIv,
  showPassword,
  setShowPassword,
  useIv,
  setUseIv
}) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="symmetric-algorithm" className="mb-2 block">
            {language === 'zh' ? '算法' : 'Algorithm'}
          </Label>
          <Select value={symmetricAlgorithm} onValueChange={(value) => setSymmetricAlgorithm(value as SymmetricAlgorithm)}>
            <SelectTrigger id="symmetric-algorithm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="aes">AES</SelectItem>
              <SelectItem value="des">DES</SelectItem>
              <SelectItem value="blowfish">Blowfish</SelectItem>
              <SelectItem value="twofish">Twofish</SelectItem>
              <SelectItem value="rc4">RC4</SelectItem>
              <SelectItem value="xor">XOR</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        {symmetricAlgorithm === 'aes' && (
          <div>
            <Label htmlFor="aes-mode" className="mb-2 block">
              {language === 'zh' ? '模式' : 'Mode'}
            </Label>
            <Select value={aesMode} onValueChange={(value) => setAesMode(value as AESMode)}>
              <SelectTrigger id="aes-mode">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cbc">CBC</SelectItem>
                <SelectItem value="ecb">ECB</SelectItem>
                <SelectItem value="cfb">CFB</SelectItem>
                <SelectItem value="ofb">OFB</SelectItem>
                <SelectItem value="ctr">CTR</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
        
        {symmetricAlgorithm === 'aes' && (
          <div>
            <Label htmlFor="aes-padding" className="mb-2 block">
              {language === 'zh' ? '填充方式' : 'Padding'}
            </Label>
            <Select value={aesPadding} onValueChange={(value) => setAesPadding(value as AESPadding)}>
              <SelectTrigger id="aes-padding">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pkcs7">PKCS7</SelectItem>
                <SelectItem value="iso10126">ISO10126</SelectItem>
                <SelectItem value="ansix923">ANSI X.923</SelectItem>
                <SelectItem value="nopadding">No Padding</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
        
        {symmetricAlgorithm === 'aes' && (
          <div>
            <Label htmlFor="key-size" className="mb-2 block">
              {language === 'zh' ? '密钥大小' : 'Key Size'}
            </Label>
            <Select value={keySize} onValueChange={(value) => setKeySize(value as KeySize)}>
              <SelectTrigger id="key-size">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="128">128 bits</SelectItem>
                <SelectItem value="192">192 bits</SelectItem>
                <SelectItem value="256">256 bits</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <Label htmlFor="encrypt-key" className="mb-1">
            {language === 'zh' ? '密钥' : 'Key'}
          </Label>
          <div className="flex items-center space-x-2">
            <Switch 
              id="show-password" 
              checked={showPassword}
              onCheckedChange={setShowPassword}
              className="scale-75"
            />
            <Label htmlFor="show-password" className="text-xs">
              {language === 'zh' ? '显示' : 'Show'}
            </Label>
          </div>
        </div>
        <Input
          id="encrypt-key"
          type={showPassword ? 'text' : 'password'}
          placeholder={language === 'zh' ? '输入加密密钥...' : 'Enter encryption key...'}
          value={key}
          onChange={(e) => setKey(e.target.value)}
        />
      </div>
      
      {(symmetricAlgorithm === 'aes' || symmetricAlgorithm === 'des') && aesMode !== 'ecb' && (
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <Label htmlFor="encrypt-iv" className="mb-1">
              {language === 'zh' ? '初始化向量 (IV)' : 'Initialization Vector (IV)'}
            </Label>
            <div className="flex items-center space-x-2">
              <Switch 
                id="use-iv" 
                checked={useIv}
                onCheckedChange={setUseIv}
                className="scale-75"
              />
              <Label htmlFor="use-iv" className="text-xs">
                {language === 'zh' ? '使用' : 'Use'}
              </Label>
            </div>
          </div>
          {useIv && (
            <Input
              id="encrypt-iv"
              type={showPassword ? 'text' : 'password'}
              placeholder={language === 'zh' ? '输入初始化向量...' : 'Enter initialization vector...'}
              value={iv}
              onChange={(e) => setIv(e.target.value)}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default SymmetricEncryptionOptions;
