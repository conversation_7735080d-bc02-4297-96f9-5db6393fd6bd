
import React from 'react';
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AsymmetricAlgorithm, EncryptionMode } from '../types';
import { generateKeyPair } from '../utils';

interface AsymmetricEncryptionOptionsProps {
  language: string;
  asymmetricAlgorithm: AsymmetricAlgorithm;
  setAsymmetricAlgorithm: (algo: AsymmetricAlgorithm) => void;
  publicKey: string;
  setPublicKey: (key: string) => void;
  privateKey: string;
  setPrivateKey: (key: string) => void;
  mode: EncryptionMode;
  onGenerateKeyPair: () => void;
}

const AsymmetricEncryptionOptions: React.FC<AsymmetricEncryptionOptionsProps> = ({
  language,
  asymmetricAlgorithm,
  setAsymmetricAlgorithm,
  publicKey,
  setPublicKey,
  privateKey,
  setPrivateKey,
  mode,
  onGenerateKeyPair
}) => {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="asymmetric-algorithm" className="mb-2 block">
          {language === 'zh' ? '算法' : 'Algorithm'}
        </Label>
        <Select 
          value={asymmetricAlgorithm} 
          onValueChange={(value) => setAsymmetricAlgorithm(value as AsymmetricAlgorithm)}
        >
          <SelectTrigger id="asymmetric-algorithm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="rsa">RSA</SelectItem>
            <SelectItem value="ecc">ECC</SelectItem>
            <SelectItem value="dsa">DSA</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <h3 className="font-medium">{language === 'zh' ? '公钥' : 'Public Key'}</h3>
              <Textarea 
                placeholder={language === 'zh' ? '输入公钥...' : 'Enter public key...'}
                value={publicKey}
                onChange={(e) => setPublicKey(e.target.value)}
                className="h-24"
                disabled={mode === 'decrypt'}
              />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <h3 className="font-medium">{language === 'zh' ? '私钥' : 'Private Key'}</h3>
              <Textarea 
                placeholder={language === 'zh' ? '输入私钥...' : 'Enter private key...'}
                value={privateKey}
                onChange={(e) => setPrivateKey(e.target.value)}
                className="h-24"
                disabled={mode === 'encrypt'}
              />
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Button onClick={onGenerateKeyPair} variant="outline" className="w-full">
        {language === 'zh' ? '生成密钥对（演示）' : 'Generate Key Pair (Demo)'}
      </Button>
    </div>
  );
};

export default AsymmetricEncryptionOptions;
