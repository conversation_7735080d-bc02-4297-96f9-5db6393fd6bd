
export type EncryptionType = 'symmetric' | 'asymmetric';
export type EncryptionMode = 'encrypt' | 'decrypt';
export type SymmetricAlgorithm = 'aes' | 'des' | 'rc4' | 'xor' | 'blowfish' | 'twofish';
export type AsymmetricAlgorithm = 'rsa' | 'ecc' | 'dsa';
export type AESMode = 'cbc' | 'ecb' | 'cfb' | 'ofb' | 'ctr';
export type AESPadding = 'pkcs7' | 'iso10126' | 'ansix923' | 'nopadding';
export type KeySize = '128' | '192' | '256';
