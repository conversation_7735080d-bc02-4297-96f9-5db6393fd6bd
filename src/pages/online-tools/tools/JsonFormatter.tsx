
import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Copy, FileCode, Download, Upload, ClipboardCheck } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';

const JsonFormatter = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [inputJson, setInputJson] = useState('');
  const [outputJson, setOutputJson] = useState('');
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [indentSpaces, setIndentSpaces] = useState(2);
  const [sortKeys, setSortKeys] = useState(false);
  const [compact, setCompact] = useState(false);
  const [escapeHtml, setEscapeHtml] = useState(false);
  const [copied, setCopied] = useState(false);

  const formatJson = () => {
    try {
      if (!inputJson.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入JSON数据' : 'Please enter JSON data',
        });
        setJsonError(null);
        setOutputJson('');
        return;
      }

      // Parse JSON to validate
      const parsedJson = JSON.parse(inputJson);
      
      // Format with specified options
      const formatted = compact 
        ? JSON.stringify(parsedJson) 
        : JSON.stringify(parsedJson, sortKeys ? Object.keys(parsedJson).sort() : null, indentSpaces);
      
      setOutputJson(formatted);
      setJsonError(null);

      // Add to history
      addToHistory(
        language === 'zh' ? 'JSON格式化' : 'JSON Formatter',
        inputJson.substring(0, 100) + (inputJson.length > 100 ? '...' : ''),
        formatted.substring(0, 100) + (formatted.length > 100 ? '...' : '')
      );
    } catch (error) {
      console.error('JSON formatting error:', error);
      setJsonError(error instanceof Error ? error.message : String(error));
      toast({
        variant: "destructive",
        description: language === 'zh' ? '无效的JSON格式' : 'Invalid JSON format',
      });
      setOutputJson('');
    }
  };

  const minifyJson = () => {
    try {
      if (!inputJson.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入JSON数据' : 'Please enter JSON data',
        });
        setJsonError(null);
        setOutputJson('');
        return;
      }

      // Parse JSON to validate
      const parsedJson = JSON.parse(inputJson);
      
      // Minify
      const minified = JSON.stringify(parsedJson);
      
      setOutputJson(minified);
      setJsonError(null);

      // Add to history
      addToHistory(
        language === 'zh' ? 'JSON压缩' : 'JSON Minify',
        inputJson.substring(0, 100) + (inputJson.length > 100 ? '...' : ''),
        minified.substring(0, 100) + (minified.length > 100 ? '...' : '')
      );
    } catch (error) {
      console.error('JSON minifying error:', error);
      setJsonError(error instanceof Error ? error.message : String(error));
      toast({
        variant: "destructive",
        description: language === 'zh' ? '无效的JSON格式' : 'Invalid JSON format',
      });
      setOutputJson('');
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(outputJson);
    setCopied(true);
    toast({
      description: language === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
    });
    setTimeout(() => setCopied(false), 2000);
  };

  const downloadJson = () => {
    const blob = new Blob([outputJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'formatted.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setInputJson(content);
    };
    
    reader.readAsText(file);
    
    // Reset the input so the same file can be uploaded again
    e.target.value = '';
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <FileCode className="mr-2 h-5 w-5" />
          {language === 'zh' ? 'JSON格式化工具' : 'JSON Formatter'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '美化或压缩JSON数据，便于阅读和分析' 
            : 'Beautify or minify JSON data for better readability and analysis'}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <div className="flex justify-between mb-2">
            <label className="block text-sm font-medium">
              {language === 'zh' ? '输入JSON' : 'Input JSON'}
            </label>
            <div>
              <label htmlFor="json-file-upload" className="cursor-pointer">
                <Button variant="outline" size="sm" asChild>
                  <span>
                    <Upload className="h-4 w-4 mr-1" />
                    {language === 'zh' ? '上传' : 'Upload'}
                  </span>
                </Button>
              </label>
              <input 
                id="json-file-upload" 
                type="file" 
                accept=".json,application/json" 
                className="hidden"
                onChange={handleFileUpload}
              />
            </div>
          </div>
          
          <Textarea
            placeholder={language === 'zh' ? '在此粘贴JSON数据...' : 'Paste JSON data here...'}
            value={inputJson}
            onChange={(e) => setInputJson(e.target.value)}
            className="font-mono h-64"
          />
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between mb-2">
            <label className="block text-sm font-medium">
              {language === 'zh' ? '输出JSON' : 'Output JSON'}
            </label>
            <div className="space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={copyToClipboard}
                disabled={!outputJson}
              >
                {copied ? <ClipboardCheck className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
                {language === 'zh' ? '复制' : 'Copy'}
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={downloadJson}
                disabled={!outputJson}
              >
                <Download className="h-4 w-4 mr-1" />
                {language === 'zh' ? '下载' : 'Download'}
              </Button>
            </div>
          </div>
          
          <Textarea
            value={outputJson}
            readOnly
            className={`font-mono h-64 ${jsonError ? 'border-red-500' : ''}`}
            placeholder={jsonError || (language === 'zh' ? '格式化输出将显示在此处...' : 'Formatted output will appear here...')}
          />
        </div>
      </div>
      
      <div className="bg-muted/50 rounded-lg p-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="indent-spaces" className="text-sm">
              {language === 'zh' ? '缩进空格数' : 'Indent Spaces'}: {indentSpaces}
            </Label>
            <Slider 
              id="indent-spaces"
              min={0} 
              max={8} 
              step={1}
              value={[indentSpaces]}
              onValueChange={([value]) => setIndentSpaces(value)}
              disabled={compact}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="sort-keys" 
              checked={sortKeys} 
              onCheckedChange={(checked) => setSortKeys(checked === true)}
            />
            <Label htmlFor="sort-keys" className="text-sm">
              {language === 'zh' ? '排序键' : 'Sort Keys'}
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="compact" 
              checked={compact} 
              onCheckedChange={setCompact}
            />
            <Label htmlFor="compact" className="text-sm">
              {language === 'zh' ? '压缩模式' : 'Compact Mode'}
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="escape-html" 
              checked={escapeHtml} 
              onCheckedChange={setEscapeHtml}
            />
            <Label htmlFor="escape-html" className="text-sm">
              {language === 'zh' ? '转义HTML' : 'Escape HTML'}
            </Label>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={formatJson} className="flex-1">
            {language === 'zh' ? '格式化' : 'Format'}
          </Button>
          <Button variant="outline" onClick={minifyJson}>
            {language === 'zh' ? '压缩' : 'Minify'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default JsonFormatter;
