
import { NationalityNames } from './types';

// Sample data for name generation
export const firstNamesMale = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
export const firstNamesFemale = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
export const lastNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
export const middleNames = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', '<PERSON>', '<PERSON>', '<PERSON>', 'M', 'N', 'O', '<PERSON>', 'Q', '<PERSON>', '<PERSON>', 'T'];

export const nationalities = ['random', 'us', 'spanish', 'chinese', 'indian', 'arabic', 'japanese', 'russian', 'french', 'german'] as const;

export const nationalityNames: NationalityNames = {
  us: {
    male: firstNamesMale,
    female: firstNamesFemale,
    last: lastNames
  },
  spanish: {
    male: ['Miguel', 'Jose', 'Francisco', 'Antonio', 'Juan', 'Manuel', 'Carlos', 'Javier'],
    female: ['Maria', 'Carmen', 'Isabel', 'Ana', 'Lucia', 'Sofia', 'Elena', 'Pilar'],
    last: ['Garcia', 'Rodriguez', 'Gonzalez', 'Fernandez', 'Lopez', 'Martinez', 'Sanchez', 'Perez']
  },
  chinese: {
    male: ['Wei', 'Li', 'Ming', 'Hui', 'Jian', 'Xiang', 'Hao', 'Xing'],
    female: ['Yan', 'Mei', 'Na', 'Ying', 'Xiu', 'Zhen', 'Li', 'Juan'],
    last: ['Wang', 'Li', 'Zhang', 'Liu', 'Chen', 'Yang', 'Huang', 'Wu']
  },
  indian: {
    male: ['Raj', 'Amit', 'Vijay', 'Rahul', 'Sanjay', 'Ajay', 'Sunil', 'Rajesh'],
    female: ['Priya', 'Neha', 'Anjali', 'Pooja', 'Meera', 'Sunita', 'Anita', 'Geeta'],
    last: ['Patel', 'Sharma', 'Singh', 'Kumar', 'Gupta', 'Shah', 'Joshi', 'Mishra']
  },
  arabic: {
    male: ['Mohammed', 'Ahmad', 'Ali', 'Hassan', 'Abdullah', 'Omar', 'Yusuf', 'Khalid'],
    female: ['Fatima', 'Aisha', 'Zahra', 'Mariam', 'Leila', 'Noor', 'Sara', 'Huda'],
    last: ['Al-Sayed', 'Al-Hassan', 'Al-Farsi', 'Al-Najjar', 'Al-Qasim', 'Al-Ahmad', 'Al-Mansoor', 'Al-Zahrani']
  },
  japanese: {
    male: ['Takashi', 'Hiroshi', 'Kenji', 'Satoshi', 'Akira', 'Yuki', 'Kenta', 'Daiki'],
    female: ['Yuko', 'Haruka', 'Akiko', 'Yumi', 'Naomi', 'Ayumi', 'Sakura', 'Emi'],
    last: ['Tanaka', 'Suzuki', 'Sato', 'Watanabe', 'Takahashi', 'Yamamoto', 'Nakamura', 'Kobayashi']
  },
  russian: {
    male: ['Sergei', 'Ivan', 'Dmitri', 'Vladimir', 'Alexei', 'Mikhail', 'Nikolai', 'Andrei'],
    female: ['Olga', 'Tatiana', 'Svetlana', 'Elena', 'Natalia', 'Irina', 'Ekaterina', 'Anna'],
    last: ['Ivanov', 'Petrov', 'Smirnov', 'Kuznetsov', 'Popov', 'Sokolov', 'Lebedev', 'Kozlov']
  },
  french: {
    male: ['Jean', 'Pierre', 'Michel', 'Philippe', 'André', 'René', 'Louis', 'François'],
    female: ['Marie', 'Anne', 'Sophie', 'Isabelle', 'Catherine', 'Nicole', 'Monique', 'Jacqueline'],
    last: ['Martin', 'Bernard', 'Dubois', 'Thomas', 'Robert', 'Richard', 'Petit', 'Durand']
  },
  german: {
    male: ['Hans', 'Klaus', 'Peter', 'Michael', 'Wolfgang', 'Thomas', 'Andreas', 'Stefan'],
    female: ['Maria', 'Anna', 'Ursula', 'Elisabeth', 'Monika', 'Petra', 'Sabine', 'Claudia'],
    last: ['Müller', 'Schmidt', 'Schneider', 'Fischer', 'Weber', 'Meyer', 'Wagner', 'Becker']
  }
};
