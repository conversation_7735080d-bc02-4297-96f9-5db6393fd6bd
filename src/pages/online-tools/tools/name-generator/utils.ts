
import { NationalityCode, GenderCode, Name } from './types';
import { nationalityNames } from './nameData';

export const getRandomElement = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

export const getRandomName = (
  firstNames: string[],
  lastNames: string[],
  includeMiddle: boolean,
  middleNames?: string[]
): string => {
  const firstName = getRandomElement(firstNames);
  const lastName = getRandomElement(lastNames);
  
  if (includeMiddle && middleNames && middleNames.length > 0) {
    const middleName = getRandomElement(middleNames);
    return `${firstName} ${middleName} ${lastName}`;
  }
  
  return `${firstName} ${lastName}`;
};

export const generateUniqueNames = (
  count: number,
  firstNames: string[],
  lastNames: string[],
  includeMiddle: boolean,
  middleNames?: string[]
): string[] => {
  const result: string[] = [];
  const used = new Set<string>();
  
  // Prevent infinite loops
  const possibleCombinations = firstNames.length * lastNames.length;
  const targetCount = Math.min(count, possibleCombinations);
  
  while (result.length < targetCount) {
    const name = getRandomName(firstNames, lastNames, includeMiddle, middleNames);
    if (!used.has(name)) {
      used.add(name);
      result.push(name);
    }
  }
  
  return result;
};

// Add the generateNames function that's imported in NameGenerator.tsx
export const generateNames = (
  count: number,
  gender: GenderCode,
  nationality: NationalityCode,
  includeMiddleName: boolean,
  middleNames: string[]
): Name[] => {
  // Get names based on nationality and gender
  let firstNamesPool: string[] = [];
  let lastNamesPool: string[] = [];
  
  if (nationality === 'random') {
    // For random nationality, pick a random one from available nationalities
    const availableNationalities = Object.keys(nationalityNames);
    nationality = getRandomElement(availableNationalities) as NationalityCode;
  }
  
  const names = nationalityNames[nationality];
  
  if (gender === 'male') {
    firstNamesPool = names?.male || [];
  } else if (gender === 'female') {
    firstNamesPool = names?.female || [];
  } else {
    // For random gender, combine both pools
    const randomGender = Math.random() > 0.5 ? 'male' : 'female';
    firstNamesPool = names?.[randomGender] || [];
  }
  
  lastNamesPool = names?.last || [];
  
  // Generate unique combinations
  const result: Name[] = [];
  const used = new Set<string>();
  
  // Prevent infinite loops
  const possibleCombinations = firstNamesPool.length * lastNamesPool.length;
  const targetCount = Math.min(count, possibleCombinations);
  
  while (result.length < targetCount) {
    const firstName = getRandomElement(firstNamesPool);
    const lastName = getRandomElement(lastNamesPool);
    const fullName = `${firstName} ${lastName}`;
    
    if (!used.has(fullName)) {
      used.add(fullName);
      
      result.push({
        firstName,
        lastName,
        gender: gender === 'random' ? (Math.random() > 0.5 ? 'male' : 'female') : gender,
        nationality
      });
    }
  }
  
  return result;
};

export const getNationalityLabel = (nationality: NationalityCode, language: string): string => {
  const labels: Record<NationalityCode, { en: string; zh: string }> = {
    'random': { en: 'Random', zh: '随机' },
    'us': { en: 'American', zh: '美国' },
    'spanish': { en: 'Spanish', zh: '西班牙' },
    'chinese': { en: 'Chinese', zh: '中国' },
    'indian': { en: 'Indian', zh: '印度' },
    'arabic': { en: 'Arabic', zh: '阿拉伯' },
    'japanese': { en: 'Japanese', zh: '日本' },
    'russian': { en: 'Russian', zh: '俄罗斯' },
    'french': { en: 'French', zh: '法国' },
    'german': { en: 'German', zh: '德国' }
  };
  
  return language === 'zh' ? labels[nationality].zh : labels[nationality].en;
};

export const getGenderLabel = (gender: GenderCode, language: string): string => {
  if (gender === 'male') {
    return language === 'zh' ? '男性' : 'Male';
  } else if (gender === 'female') {
    return language === 'zh' ? '女性' : 'Female';
  } else {
    return language === 'zh' ? '随机' : 'Random';
  }
};
