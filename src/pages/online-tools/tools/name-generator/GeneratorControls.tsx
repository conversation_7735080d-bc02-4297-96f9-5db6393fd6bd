
import React from 'react';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { NationalityCode, GenderCode } from './types';
import { getNationalityLabel } from './utils';
import { nationalities } from './nameData';

interface GeneratorControlsProps {
  language: string;
  gender: GenderCode;
  setGender: (gender: GenderCode) => void;
  count: number;
  setCount: (count: number) => void;
  nationality: NationalityCode;
  setNationality: (nationality: NationalityCode) => void;
  includeMiddleName: boolean;
  setIncludeMiddleName: (include: boolean) => void;
  onGenerate: () => void;
}

const GeneratorControls: React.FC<GeneratorControlsProps> = ({
  language,
  gender,
  setGender,
  count,
  setCount,
  nationality,
  setNationality,
  includeMiddleName,
  setIncludeMiddleName,
  onGenerate,
}) => {
  return (
    <div className="bg-muted/30 rounded-lg p-4 space-y-4">
      <div>
        <Label htmlFor="name-gender" className="mb-2 block">
          {language === 'zh' ? '性别' : 'Gender'}
        </Label>
        <RadioGroup 
          id="name-gender"
          value={gender} 
          onValueChange={(value) => setGender(value as GenderCode)}
          className="flex space-x-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="male" id="male" />
            <Label htmlFor="male">
              {language === 'zh' ? '男性' : 'Male'}
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="female" id="female" />
            <Label htmlFor="female">
              {language === 'zh' ? '女性' : 'Female'}
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="random" id="random" />
            <Label htmlFor="random">
              {language === 'zh' ? '随机' : 'Random'}
            </Label>
          </div>
        </RadioGroup>
      </div>
      
      <div>
        <Label htmlFor="name-count" className="mb-2 block">
          {language === 'zh' ? '生成数量' : 'Count'}: {count}
        </Label>
        <Slider 
          id="name-count"
          min={1} 
          max={20} 
          step={1}
          value={[count]}
          onValueChange={([value]) => setCount(value)}
        />
      </div>
      
      <div>
        <Label htmlFor="name-nationality" className="mb-2 block">
          {language === 'zh' ? '国籍/文化' : 'Nationality/Culture'}
        </Label>
        <Select 
          value={nationality} 
          onValueChange={(value) => setNationality(value as NationalityCode)}
        >
          <SelectTrigger id="name-nationality">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {nationalities.map(nat => (
              <SelectItem key={nat} value={nat}>
                {getNationalityLabel(nat as NationalityCode, language)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch 
          id="include-middle-name" 
          checked={includeMiddleName} 
          onCheckedChange={setIncludeMiddleName}
        />
        <Label htmlFor="include-middle-name" className="text-sm">
          {language === 'zh' ? '包含中间名' : 'Include Middle Initial'}
        </Label>
      </div>
      
      <Button onClick={onGenerate} className="w-full">
        <RefreshCw className="mr-2 h-4 w-4" />
        {language === 'zh' ? '生成姓名' : 'Generate Names'}
      </Button>
    </div>
  );
};

export default GeneratorControls;
