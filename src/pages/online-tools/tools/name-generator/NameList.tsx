
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Name, GenderCode } from './types';
import { getGenderLabel } from './utils';

interface NameListProps {
  language: string;
  generatedNames: Name[];
}

const NameList: React.FC<NameListProps> = ({ language, generatedNames }) => {
  return (
    <Card>
      <div className="p-3 border-b">
        <h3 className="font-medium">
          {language === 'zh' ? '生成的姓名' : 'Generated Names'}
        </h3>
      </div>
      <CardContent className="p-0 max-h-80 overflow-y-auto">
        {generatedNames.length === 0 ? (
          <div className="py-8 text-center text-muted-foreground">
            {language === 'zh' ? '点击生成按钮创建姓名' : 'Click the generate button to create names'}
          </div>
        ) : (
          <ul className="divide-y">
            {generatedNames.map((name, index) => (
              <li key={index} className="px-4 py-2 flex justify-between items-center">
                <div>
                  <span className="font-medium">{name.firstName} {name.lastName}</span>
                  <span className="ml-2 text-xs text-muted-foreground">
                    ({getGenderLabel(name.gender as GenderCode, language)})
                  </span>
                </div>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
};

export default NameList;
