
export type GenderCode = 'male' | 'female' | 'random';
export type NationalityCode = 'random' | 'us' | 'spanish' | 'chinese' | 'indian' | 'arabic' | 'japanese' | 'russian' | 'french' | 'german';

export interface Name {
  firstName: string;
  lastName: string;
  gender: 'male' | 'female';
  nationality: NationalityCode;
}

export interface NationalityNames {
  [key: string]: {
    male: string[];
    female: string[];
    last: string[];
  };
}
