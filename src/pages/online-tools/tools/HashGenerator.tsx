import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Co<PERSON>, Clipboard<PERSON>heck, Hash } from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';

// MD5 哈希函数实现
function md5(input: string): string {
  // 转换字符串为UTF-8编码的字节数组
  function stringToUtf8ByteArray(str: string): number[] {
    const out: number[] = [];
    let p = 0;
    for (let i = 0; i < str.length; i++) {
      let c = str.charCodeAt(i);
      if (c < 128) {
        out[p++] = c;
      } else if (c < 2048) {
        out[p++] = (c >> 6) | 192;
        out[p++] = (c & 63) | 128;
      } else if (
        ((c & 0xFC00) === 0xD800) && (i + 1) < str.length &&
        ((str.charCodeAt(i + 1) & 0xFC00) === 0xDC00)
      ) {
        // Surrogate pair
        c = 0x10000 + ((c & 0x03FF) << 10) + (str.charCodeAt(++i) & 0x03FF);
        out[p++] = (c >> 18) | 240;
        out[p++] = ((c >> 12) & 63) | 128;
        out[p++] = ((c >> 6) & 63) | 128;
        out[p++] = (c & 63) | 128;
      } else {
        out[p++] = (c >> 12) | 224;
        out[p++] = ((c >> 6) & 63) | 128;
        out[p++] = (c & 63) | 128;
      }
    }
    return out;
  }

  // 左循环移位
  function lrot(num: number, cnt: number): number {
    return ((num << cnt) | (num >>> (32 - cnt))) >>> 0;
  }

  // 初始化MD5常量
  const k: number[] = [
    0xd76aa478, 0xe8c7b756, 0x242070db, 0xc1bdceee,
    0xf57c0faf, 0x4787c62a, 0xa8304613, 0xfd469501,
    0x698098d8, 0x8b44f7af, 0xffff5bb1, 0x895cd7be,
    0x6b901122, 0xfd987193, 0xa679438e, 0x49b40821,
    0xf61e2562, 0xc040b340, 0x265e5a51, 0xe9b6c7aa,
    0xd62f105d, 0x02441453, 0xd8a1e681, 0xe7d3fbc8,
    0x21e1cde6, 0xc33707d6, 0xf4d50d87, 0x455a14ed,
    0xa9e3e905, 0xfcefa3f8, 0x676f02d9, 0x8d2a4c8a,
    0xfffa3942, 0x8771f681, 0x6d9d6122, 0xfde5380c,
    0xa4beea44, 0x4bdecfa9, 0xf6bb4b60, 0xbebfbc70,
    0x289b7ec6, 0xeaa127fa, 0xd4ef3085, 0x04881d05,
    0xd9d4d039, 0xe6db99e5, 0x1fa27cf8, 0xc4ac5665,
    0xf4292244, 0x432aff97, 0xab9423a7, 0xfc93a039,
    0x655b59c3, 0x8f0ccc92, 0xffeff47d, 0x85845dd1,
    0x6fa87e4f, 0xfe2ce6e0, 0xa3014314, 0x4e0811a1,
    0xf7537e82, 0xbd3af235, 0x2ad7d2bb, 0xeb86d391
  ];

  // 位移量
  const r: number[] = [
    7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22,
    5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20,
    4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23,
    6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21
  ];

  // 将UTF-8编码的字符串转换为字节数组
  const bytes = stringToUtf8ByteArray(input);
  
  // 原始长度（位）
  const originalBitLength = bytes.length * 8;
  
  // 添加填充
  bytes.push(0x80); // 添加10000000
  
  // 填充到长度为56 mod 64的位置
  while (bytes.length % 64 !== 56) {
    bytes.push(0);
  }
  
  // 添加原始长度（位）作为小端64位整数
  for (let i = 0; i < 8; i++) {
    bytes.push((originalBitLength >>> (i * 8)) & 0xff);
  }
  
  // 初始哈希值
  let h0 = 0x67452301;
  let h1 = 0xEFCDAB89;
  let h2 = 0x98BADCFE;
  let h3 = 0x10325476;
  
  // 处理消息块
  for (let i = 0; i < bytes.length; i += 64) {
    // 将64字节分块转换为16个32位字
    const w: number[] = new Array(16);
    for (let j = 0; j < 16; j++) {
      w[j] = bytes[i + j*4] | (bytes[i + j*4 + 1] << 8) |
             (bytes[i + j*4 + 2] << 16) | (bytes[i + j*4 + 3] << 24);
    }
    
    // 初始化哈希值
    let a = h0;
    let b = h1;
    let c = h2;
    let d = h3;
    
    // 主循环
    for (let j = 0; j < 64; j++) {
      let f: number, g: number;
      
      if (j < 16) {
        f = (b & c) | ((~b) & d);
        g = j;
      } else if (j < 32) {
        f = (d & b) | ((~d) & c);
        g = (5 * j + 1) % 16;
      } else if (j < 48) {
        f = b ^ c ^ d;
        g = (3 * j + 5) % 16;
      } else {
        f = c ^ (b | (~d));
        g = (7 * j) % 16;
      }
      
      // 更新哈希值
      const temp = d;
      d = c;
      c = b;
      b = (b + lrot((a + f + k[j] + w[g]), r[j])) >>> 0;
      a = temp;
    }
    
    // 更新哈希状态
    h0 = (h0 + a) >>> 0;
    h1 = (h1 + b) >>> 0;
    h2 = (h2 + c) >>> 0;
    h3 = (h3 + d) >>> 0;
  }
  
  // 转换为小端字节顺序的十六进制字符串
  return [h0, h1, h2, h3].map(h => {
    return ('00000000' + h.toString(16)).slice(-8);
  }).join('');
}

const HashGenerator = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [inputText, setInputText] = useState('');
  const [algorithm, setAlgorithm] = useState('sha256');
  const [hashResult, setHashResult] = useState('');
  const [copied, setCopied] = useState(false);

  const generateHash = async () => {
    try {
      if (!inputText.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入要哈希的文本' : 'Please enter text to hash',
        });
        setHashResult('');
        return;
      }

      let hashHex = '';
      
      // MD5算法使用自定义实现
      if (algorithm === 'md5') {
        hashHex = md5(inputText);
      } else {
        // 其他算法使用Web Crypto API
        const encoder = new TextEncoder();
        const data = encoder.encode(inputText);
        
        let hashBuffer;
        switch (algorithm) {
          case 'sha1':
            hashBuffer = await crypto.subtle.digest('SHA-1', data);
            break;
          case 'sha256':
            hashBuffer = await crypto.subtle.digest('SHA-256', data);
            break;
          case 'sha384':
            hashBuffer = await crypto.subtle.digest('SHA-384', data);
            break;
          case 'sha512':
            hashBuffer = await crypto.subtle.digest('SHA-512', data);
            break;
          default:
            hashBuffer = await crypto.subtle.digest('SHA-256', data);
        }
        
        // Convert buffer to hex string
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      }
      
      setHashResult(hashHex);
      
      // Add to history
      addToHistory(
        language === 'zh' ? `${algorithm.toUpperCase()} 哈希` : `${algorithm.toUpperCase()} Hash`,
        inputText.substring(0, 50) + (inputText.length > 50 ? '...' : ''),
        hashHex
      );
    } catch (error) {
      console.error('Hash generation error:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '生成哈希时出错' : 'Error generating hash',
      });
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(hashResult);
    setCopied(true);
    toast({
      description: language === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
    });
    setTimeout(() => setCopied(false), 2000);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <Hash className="mr-2 h-5 w-5" />
          {language === 'zh' ? '哈希生成工具' : 'Hash Generator'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '使用多种算法计算文本的哈希值，包括MD5, SHA-1, SHA-256, SHA-384和SHA-512' 
            : 'Calculate hash values for text using various algorithms including MD5, SHA-1, SHA-256, SHA-384, and SHA-512'}
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="hash-algorithm" className="mb-2 block">
            {language === 'zh' ? '哈希算法' : 'Hash Algorithm'}
          </Label>
          <Select value={algorithm} onValueChange={setAlgorithm}>
            <SelectTrigger id="hash-algorithm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="md5">MD5</SelectItem>
              <SelectItem value="sha1">SHA-1</SelectItem>
              <SelectItem value="sha256">SHA-256</SelectItem>
              <SelectItem value="sha384">SHA-384</SelectItem>
              <SelectItem value="sha512">SHA-512</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="input-text" className="mb-2 block">
            {language === 'zh' ? '输入文本' : 'Input Text'}
          </Label>
          <Textarea
            id="input-text"
            placeholder={language === 'zh' ? '输入要计算哈希值的文本...' : 'Enter text to hash...'}
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="h-32"
          />
        </div>
        
        <Button onClick={generateHash}>
          {language === 'zh' ? '生成哈希' : 'Generate Hash'}
        </Button>
        
        {hashResult && (
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center mb-2">
                <Label className="font-medium">
                  {algorithm.toUpperCase()} {language === 'zh' ? '哈希结果' : 'Hash Result'}
                </Label>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={copyToClipboard}
                >
                  {copied ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <Input
                value={hashResult}
                readOnly
                className="font-mono text-sm"
              />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default HashGenerator;
