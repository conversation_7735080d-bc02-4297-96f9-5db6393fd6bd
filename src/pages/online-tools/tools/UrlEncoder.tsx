
import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Copy, ClipboardCheck, ExternalLink } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const UrlEncoder = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [mode, setMode] = useState<'encode' | 'decode'>('encode');
  const [copied, setCopied] = useState(false);
  const [encodingMode, setEncodingMode] = useState<'standard' | 'component' | 'all'>('standard');
  const [encodeSpace, setEncodeSpace] = useState(true);

  const processUrl = () => {
    try {
      if (!input.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入URL或文本' : 'Please enter a URL or text',
        });
        return;
      }

      let result = '';
      
      if (mode === 'encode') {
        switch (encodingMode) {
          case 'standard':
            result = encodeURI(input);
            // Replace spaces with + if option is selected
            if (encodeSpace) {
              result = result.replace(/%20/g, '+');
            }
            break;
          case 'component':
            result = encodeURIComponent(input);
            if (encodeSpace) {
              result = result.replace(/%20/g, '+');
            }
            break;
          case 'all':
            result = input.split('').map(char => {
              return '%' + char.charCodeAt(0).toString(16).padStart(2, '0');
            }).join('');
            break;
          default:
            result = encodeURI(input);
        }
      } else {
        // Decode mode
        // Replace + with spaces first if present
        let processedInput = input.replace(/\+/g, ' ');
        
        try {
          if (encodingMode === 'component') {
            result = decodeURIComponent(processedInput);
          } else if (encodingMode === 'all') {
            // Custom decode for 'all' mode
            result = processedInput.replace(/%[0-9A-Fa-f]{2}/g, match => {
              return String.fromCharCode(parseInt(match.substring(1), 16));
            });
          } else {
            result = decodeURI(processedInput);
          }
        } catch (error) {
          throw new Error(language === 'zh' ? 'URL 解码失败' : 'URL decoding failed');
        }
      }
      
      setOutput(result);
      
      // Add to history
      addToHistory(
        mode === 'encode'
          ? (language === 'zh' ? 'URL编码' : 'URL Encode')
          : (language === 'zh' ? 'URL解码' : 'URL Decode'),
        input.substring(0, 100) + (input.length > 100 ? '...' : ''),
        result.substring(0, 100) + (result.length > 100 ? '...' : '')
      );
    } catch (error) {
      console.error('URL processing error:', error);
      toast({
        variant: "destructive",
        description: error instanceof Error ? error.message : (language === 'zh' ? 'URL处理错误' : 'URL processing error'),
      });
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(output);
    setCopied(true);
    toast({
      description: language === 'zh' ? '已复制到剪贴板' : 'Copied to clipboard',
    });
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <ExternalLink className="mr-2 h-5 w-5" />
          {language === 'zh' ? 'URL编码工具' : 'URL Encoder/Decoder'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '编码或解码URL字符串，用于Web开发和调试' 
            : 'Encode or decode URL strings for web development and debugging'}
        </p>
      </div>

      <Tabs value={mode} onValueChange={(value) => setMode(value as 'encode' | 'decode')} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="encode">
            {language === 'zh' ? '编码' : 'Encode'}
          </TabsTrigger>
          <TabsTrigger value="decode">
            {language === 'zh' ? '解码' : 'Decode'}
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-center">
        <div>
          <Label htmlFor="encoding-mode" className="mb-2 block">
            {language === 'zh' ? '编码模式' : 'Encoding Mode'}
          </Label>
          <Select value={encodingMode} onValueChange={(value) => setEncodingMode(value as 'standard' | 'component' | 'all')}>
            <SelectTrigger id="encoding-mode">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="standard">
                {language === 'zh' ? '标准 (encodeURI)' : 'Standard (encodeURI)'}
              </SelectItem>
              <SelectItem value="component">
                {language === 'zh' ? '组件 (encodeURIComponent)' : 'Component (encodeURIComponent)'}
              </SelectItem>
              <SelectItem value="all">
                {language === 'zh' ? '编码所有字符' : 'Encode All Characters'}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {mode === 'encode' && (
          <div className="flex items-center space-x-2">
            <Switch 
              id="encode-space" 
              checked={encodeSpace}
              onCheckedChange={setEncodeSpace}
            />
            <Label htmlFor="encode-space">
              {language === 'zh' ? '将空格编码为 "+" 而不是 "%20"' : 'Encode spaces as "+" instead of "%20"'}
            </Label>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <label className="block text-sm font-medium mb-2">
            {mode === 'encode'
              ? (language === 'zh' ? '输入文本' : 'Input Text')
              : (language === 'zh' ? '输入编码URL' : 'Input Encoded URL')}
          </label>
          <Textarea
            placeholder={
              mode === 'encode'
                ? (language === 'zh' ? '输入要编码的文本...' : 'Enter text to encode...')
                : (language === 'zh' ? '输入要解码的URL...' : 'Enter URL to decode...')
            }
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="h-40"
          />
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between mb-2">
            <label className="block text-sm font-medium">
              {mode === 'encode'
                ? (language === 'zh' ? '编码输出' : 'Encoded Output')
                : (language === 'zh' ? '解码输出' : 'Decoded Output')}
            </label>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={copyToClipboard}
              disabled={!output}
            >
              {copied ? <ClipboardCheck className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
              {language === 'zh' ? '复制' : 'Copy'}
            </Button>
          </div>
          <Textarea
            value={output}
            readOnly
            className="h-40"
            placeholder={
              mode === 'encode'
                ? (language === 'zh' ? '编码结果将显示在此处...' : 'Encoded result will appear here...')
                : (language === 'zh' ? '解码结果将显示在此处...' : 'Decoded result will appear here...')
            }
          />
        </div>
      </div>
      
      <Button onClick={processUrl} className="w-full">
        {mode === 'encode'
          ? (language === 'zh' ? '编码' : 'Encode')
          : (language === 'zh' ? '解码' : 'Decode')}
      </Button>
    </div>
  );
};

export default UrlEncoder;
