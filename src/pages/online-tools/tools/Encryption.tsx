
import React from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import EncryptionContent from './encryption/EncryptionContent';

const Encryption = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  
  return <EncryptionContent language={language} addToHistory={addToHistory} />;
};

export default Encryption;
