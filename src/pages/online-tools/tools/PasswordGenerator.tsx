
import React, { useState, useEffect } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useOnlineTools } from '../OnlineToolsContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { 
  Copy, 
  ClipboardCheck, 
  Lock, 
  RefreshCw, 
  Eye, 
  EyeOff, 
  ChevronDown, 
  ChevronUp 
} from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

const PasswordGenerator = () => {
  const { language } = useAppContext();
  const { addToHistory } = useOnlineTools();
  const { toast } = useToast();
  
  const [password, setPassword] = useState('');
  const [length, setLength] = useState(16);
  const [includeUppercase, setIncludeUppercase] = useState(true);
  const [includeLowercase, setIncludeLowercase] = useState(true);
  const [includeNumbers, setIncludeNumbers] = useState(true);
  const [includeSpecialChars, setIncludeSpecialChars] = useState(true);
  const [excludeSimilarChars, setExcludeSimilarChars] = useState(false);
  const [excludeAmbiguousChars, setExcludeAmbiguousChars] = useState(false);
  const [customCharSet, setCustomCharSet] = useState('');
  const [useCustomCharSet, setUseCustomCharSet] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [showPassword, setShowPassword] = useState(true);
  const [copied, setCopied] = useState(false);
  const [copyTimeout, setCopyTimeout] = useState<number | null>(null);
  const [passwordHistory, setPasswordHistory] = useState<string[]>([]);
  
  useEffect(() => {
    generatePassword();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  const generatePassword = () => {
    try {
      if (useCustomCharSet && !customCharSet.trim()) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '请输入自定义字符集' : 'Please enter custom character set',
        });
        return;
      }
      
      // Define character sets
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const numbers = '0123456789';
      const specialChars = '!@#$%^&*()_+~`|}{[]:;?><,./-=';
      const similarChars = 'iIlL1oO0';
      const ambiguousChars = '{}[]()/\\\'"`~,;:.<>';
      
      // Build character set
      let charSet = '';
      
      if (useCustomCharSet) {
        charSet = customCharSet;
      } else {
        if (includeUppercase) charSet += uppercase;
        if (includeLowercase) charSet += lowercase;
        if (includeNumbers) charSet += numbers;
        if (includeSpecialChars) charSet += specialChars;
        
        if (excludeSimilarChars) {
          for (const char of similarChars) {
            charSet = charSet.replace(new RegExp(char, 'g'), '');
          }
        }
        
        if (excludeAmbiguousChars) {
          for (const char of ambiguousChars) {
            charSet = charSet.replace(new RegExp('\\' + char, 'g'), '');
          }
        }
      }
      
      if (charSet.length === 0) {
        toast({
          variant: "destructive",
          description: language === 'zh' ? '字符集不能为空' : 'Character set cannot be empty',
        });
        return;
      }
      
      // Generate password
      let newPassword = '';
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charSet.length);
        newPassword += charSet[randomIndex];
      }
      
      setPassword(newPassword);
      calculatePasswordStrength(newPassword);
      
      // Add to history
      if (newPassword.trim()) {
        setPasswordHistory(prev => [newPassword, ...prev.slice(0, 9)]);
      }
      
      // Add to tools history
      addToHistory(
        language === 'zh' ? '密码生成器' : 'Password Generator',
        `Length: ${length}, Uppercase: ${includeUppercase}, Lowercase: ${includeLowercase}, Numbers: ${includeNumbers}, Special: ${includeSpecialChars}`,
        `${newPassword.slice(0, 3)}***${newPassword.slice(-2)}`
      );
    } catch (error) {
      console.error('Password generation error:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '生成密码时出错' : 'Error generating password',
      });
    }
  };
  
  const calculatePasswordStrength = (pass: string) => {
    // Simple password strength calculation
    let strength = 0;
    
    // Length check
    if (pass.length >= 8) strength += 1;
    if (pass.length >= 12) strength += 1;
    if (pass.length >= 16) strength += 1;
    
    // Character variety check
    if (/[A-Z]/.test(pass)) strength += 1;
    if (/[a-z]/.test(pass)) strength += 1;
    if (/[0-9]/.test(pass)) strength += 1;
    if (/[^A-Za-z0-9]/.test(pass)) strength += 1;
    
    setPasswordStrength(Math.min(5, strength));
  };
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(password);
    
    // Clear any existing timeout
    if (copyTimeout) {
      clearTimeout(copyTimeout);
    }
    
    setCopied(true);
    
    toast({
      description: language === 'zh' ? '密码已复制到剪贴板' : 'Password copied to clipboard',
    });
    
    // Set new timeout
    const timeout = window.setTimeout(() => {
      setCopied(false);
    }, 2000);
    
    setCopyTimeout(timeout);
  };
  
  const getStrengthLabel = () => {
    const labels = [
      language === 'zh' ? '非常弱' : 'Very Weak',
      language === 'zh' ? '弱' : 'Weak',
      language === 'zh' ? '一般' : 'Medium',
      language === 'zh' ? '强' : 'Strong',
      language === 'zh' ? '非常强' : 'Very Strong',
      language === 'zh' ? '极强' : 'Extremely Strong',
    ];
    
    return labels[passwordStrength];
  };
  
  const getStrengthColor = () => {
    const colors = [
      'bg-red-500',
      'bg-orange-500',
      'bg-yellow-500',
      'bg-lime-500',
      'bg-green-500',
      'bg-emerald-500',
    ];
    
    return colors[passwordStrength];
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-3 flex items-center">
          <Lock className="mr-2 h-5 w-5" />
          {language === 'zh' ? '密码生成工具' : 'Password Generator'}
        </h2>
        <p className="text-muted-foreground">
          {language === 'zh' 
            ? '生成安全、强壮且随机的密码' 
            : 'Generate secure, strong, and random passwords'}
        </p>
      </div>

      <div className="relative">
        <Input
          type={showPassword ? "text" : "password"}
          value={password}
          readOnly
          className="font-mono text-lg pr-24"
        />
        <div className="absolute right-1 top-1 flex gap-1">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => setShowPassword(!showPassword)}
            className="h-8 w-8"
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={copyToClipboard}
            className="h-8 w-8"
          >
            {copied ? <ClipboardCheck className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
          </Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>{language === 'zh' ? '密码强度' : 'Password Strength'}: {getStrengthLabel()}</span>
        </div>
        <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
          <div 
            className={`h-full ${getStrengthColor()}`} 
            style={{ width: `${(passwordStrength + 1) * 16.67}%` }}
          ></div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-4 bg-muted/30 rounded-lg p-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label htmlFor="password-length">
                {language === 'zh' ? '密码长度' : 'Password Length'}: {length}
              </Label>
            </div>
            <Slider 
              id="password-length"
              min={4} 
              max={64} 
              step={1}
              value={[length]}
              onValueChange={([value]) => setLength(value)}
            />
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="include-uppercase" 
                checked={includeUppercase} 
                onCheckedChange={(checked) => setIncludeUppercase(checked === true)}
                disabled={useCustomCharSet}
              />
              <Label htmlFor="include-uppercase" className="text-sm cursor-pointer">
                {language === 'zh' ? '包含大写字母' : 'Include Uppercase'}
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="include-lowercase" 
                checked={includeLowercase} 
                onCheckedChange={(checked) => setIncludeLowercase(checked === true)}
                disabled={useCustomCharSet}
              />
              <Label htmlFor="include-lowercase" className="text-sm cursor-pointer">
                {language === 'zh' ? '包含小写字母' : 'Include Lowercase'}
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="include-numbers" 
                checked={includeNumbers} 
                onCheckedChange={(checked) => setIncludeNumbers(checked === true)}
                disabled={useCustomCharSet}
              />
              <Label htmlFor="include-numbers" className="text-sm cursor-pointer">
                {language === 'zh' ? '包含数字' : 'Include Numbers'}
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="include-special" 
                checked={includeSpecialChars} 
                onCheckedChange={(checked) => setIncludeSpecialChars(checked === true)}
                disabled={useCustomCharSet}
              />
              <Label htmlFor="include-special" className="text-sm cursor-pointer">
                {language === 'zh' ? '包含特殊字符' : 'Include Special Chars'}
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="exclude-similar" 
                checked={excludeSimilarChars} 
                onCheckedChange={(checked) => setExcludeSimilarChars(checked === true)}
                disabled={useCustomCharSet}
              />
              <Label htmlFor="exclude-similar" className="text-sm cursor-pointer">
                {language === 'zh' ? '排除相似字符' : 'Exclude Similar'}
                <span className="text-xs opacity-70 ml-1">(i, l, 1, o, 0, etc)</span>
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="exclude-ambiguous" 
                checked={excludeAmbiguousChars} 
                onCheckedChange={(checked) => setExcludeAmbiguousChars(checked === true)}
                disabled={useCustomCharSet}
              />
              <Label htmlFor="exclude-ambiguous" className="text-sm cursor-pointer">
                {language === 'zh' ? '排除歧义字符' : 'Exclude Ambiguous'}
                <span className="text-xs opacity-70 ml-1">({}, [], etc)</span>
              </Label>
            </div>
          </div>
          
          <Accordion type="single" collapsible>
            <AccordionItem value="custom-charset">
              <AccordionTrigger className="py-2">
                {language === 'zh' ? '自定义字符集' : 'Custom Character Set'}
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="use-custom-charset" 
                      checked={useCustomCharSet} 
                      onCheckedChange={setUseCustomCharSet}
                    />
                    <Label htmlFor="use-custom-charset" className="text-sm">
                      {language === 'zh' ? '使用自定义字符集' : 'Use Custom Character Set'}
                    </Label>
                  </div>
                  
                  <Input
                    placeholder={language === 'zh' ? '输入自定义字符集...' : 'Enter custom character set...'}
                    value={customCharSet}
                    onChange={(e) => setCustomCharSet(e.target.value)}
                    disabled={!useCustomCharSet}
                    className="font-mono"
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          
          <Button onClick={generatePassword} className="w-full">
            <RefreshCw className="mr-2 h-4 w-4" />
            {language === 'zh' ? '生成新密码' : 'Generate New Password'}
          </Button>
        </div>
        
        <div>
          <Card>
            <div className="px-4 py-2 border-b flex items-center">
              <h3 className="font-medium">
                {language === 'zh' ? '密码历史记录' : 'Password History'}
              </h3>
            </div>
            <CardContent className="p-0 max-h-80 overflow-y-auto">
              {passwordHistory.length === 0 ? (
                <div className="py-8 text-center text-muted-foreground">
                  {language === 'zh' ? '暂无密码历史记录' : 'No password history yet'}
                </div>
              ) : (
                <ul className="divide-y">
                  {passwordHistory.map((historyPassword, index) => (
                    <li key={index} className="px-4 py-2 flex justify-between items-center">
                      <code className="font-mono">{historyPassword}</code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigator.clipboard.writeText(historyPassword)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PasswordGenerator;
