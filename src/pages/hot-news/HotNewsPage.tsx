import React, { useState, useEffect, useRef } from 'react';
import { useAppContext } from '@/context/AppContext';
import { useToast } from '@/components/ui/use-toast';
import { TrendingUp, RefreshCw, AlertCircle } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { NewsPlatform } from '@/hooks/hot-news/types';
import { fetchPlatforms, fallbackPlatforms } from '@/hooks/hot-news/api';
import { HotNewsHeader } from './components/HotNewsHeader';
import { PlatformCard } from './components/PlatformCard';
import { LoadMoreTrigger } from './components/LoadMoreTrigger';
import { useHotNewsLoader } from './hooks/useHotNewsLoader';
import PageWrapper from '@/components/layout/PageWrapper';

// Cache API data
let cachedPlatforms: NewsPlatform[] = [];

const HotNewsPage: React.FC = () => {
    const { language } = useAppContext();
    const { toast } = useToast();
    const containerRef = useRef<HTMLDivElement>(null);

    // Use custom hook to manage hot news loading state and logic
    const {
        platforms,
        setPlatforms,
        loadedPlatforms,
        setLoadedPlatforms,
        newsMap,
        setNewsMap,
        isLoading,
        setIsLoading,
        loadingPlatform,
        setLoadingPlatform,
        loadedPlatformsCount,
        setLoadedPlatformsCount,
        error,
        setError,
        lastUpdateTime,
        setLastUpdateTime,
        loadingRef,
        observersRef,
        loadMoreElementRef,
        loadMoreObserverRef,
        loadPlatformNews,
        loadMorePlatforms
    } = useHotNewsLoader();

    // Initialize to get platform info, cache API results
    useEffect(() => {
        const fetchPlatformsList = async () => {
            setIsLoading(true);
            setError(null);

            try {
                // If there's cached data, use it first
                if (cachedPlatforms.length > 0) {
                    console.log('Using cached platform data:', cachedPlatforms.length, 'platforms');
                    setPlatforms(cachedPlatforms);

                    // Reset loading state
                    setLoadedPlatformsCount(0);
                    setLoadedPlatforms({});
                    loadingRef.current = {};

                    // Set last update time
                    setLastUpdateTime(new Date());

                    // Load data for the first few platforms
                    loadInitialPlatforms(cachedPlatforms.slice(0, 8));
                } else {
                    // Get all supported platforms
                    const allPlatforms = await fetchPlatforms();

                    // Cache platform data
                    cachedPlatforms = allPlatforms;

                    setPlatforms(allPlatforms);

                    // Reset loading state
                    setLoadedPlatformsCount(0);
                    setLoadedPlatforms({});
                    loadingRef.current = {};

                    // Set last update time
                    setLastUpdateTime(new Date());

                    console.log(`Successfully fetched ${allPlatforms.length} platforms`);

                    // Load data for the first few platforms
                    loadInitialPlatforms(allPlatforms.slice(0, 8));
                }
            } catch (err) {
                console.error('Failed to fetch platform list:', err);
                if (platforms.length === 0) {
                    setPlatforms(fallbackPlatforms);
                }
                setError(language === 'en' ? 'Failed to load platform list' : '加载平台列表失败');
                toast({
                    variant: "destructive",
                    title: language === 'en' ? 'Error' : '错误',
                    description: String(err),
                });
            } finally {
                setIsLoading(false);
            }
        };

        fetchPlatformsList();

        // Clean up all observers
        return () => {
            Object.values(observersRef.current).forEach(observer => {
                observer.disconnect();
            });
            observersRef.current = {};

            if (loadMoreObserverRef.current) {
                loadMoreObserverRef.current.disconnect();
                loadMoreObserverRef.current = null;
            }
        };
    }, [language]);

    // Load initial platform data
    const loadInitialPlatforms = async (initialPlatforms: NewsPlatform[]) => {
        for (const platform of initialPlatforms) {
            await loadPlatformNews(platform);
        }
    };

    // Set up bottom load more observer
    useEffect(() => {
        if (!loadMoreElementRef.current || platforms.length === 0) return;

        const loadMoreObserver = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting) {
                loadMorePlatforms();
            }
        }, { threshold: 0.1 });

        loadMoreObserver.observe(loadMoreElementRef.current);
        loadMoreObserverRef.current = loadMoreObserver;

        return () => {
            if (loadMoreObserverRef.current) {
                loadMoreObserverRef.current.disconnect();
            }
        };
    }, [platforms, loadedPlatformsCount]);

    // Refresh all data
    const handleRefreshAll = async () => {
        setIsLoading(true);
        setError(null);

        toast({
            title: language === 'en' ? 'Refreshing data' : '正在刷新数据',
            description: language === 'en' ? 'Please wait...' : '请稍候...',
        });

        try {
            // Re-fetch platform information
            const allPlatforms = await fetchPlatforms();
            // Update cache
            cachedPlatforms = allPlatforms;
            setPlatforms(allPlatforms);

            // Reset loading state
            setLoadedPlatformsCount(0);
            setLoadedPlatforms({});
            loadingRef.current = {};
            setNewsMap({});

            // Update last update time
            setLastUpdateTime(new Date());

            console.log(`Refreshed ${allPlatforms.length} platforms`);

            // Only reload visible platforms
            const visiblePlatforms = allPlatforms.slice(0, 8);

            for (const platform of visiblePlatforms) {
                await loadPlatformNews(platform);
            }

            toast({
                title: language === 'en' ? 'Refresh complete' : '刷新完成',
                description: language === 'en' ? 'Data has been updated' : '数据已更新',
            });
        } catch (err) {
            console.error('Failed to refresh data:', err);
            setError(language === 'en' ? 'Failed to refresh data' : '刷新数据失败');

            toast({
                variant: "destructive",
                title: language === 'en' ? 'Error' : '错误',
                description: String(err),
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Set up lazy loading observer
    const setupObserver = (platformPath: string, element: HTMLElement) => {
        if (observersRef.current[platformPath]) {
            observersRef.current[platformPath].disconnect();
        }

        const platform = platforms.find(p => p.path === platformPath);
        if (!platform) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !loadedPlatforms[platformPath]) {
                    // Element enters viewport, load data
                    loadPlatformNews(platform);
                    // Stop observing after loading
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,  // Lower threshold, easier to trigger loading
            rootMargin: '100px' // Preload area by 100px
        });

        observer.observe(element);
        observersRef.current[platformPath] = observer;
    };

    // Format last update time
    const formatLastUpdateTime = () => {
        if (!lastUpdateTime) return '';
        return lastUpdateTime.toLocaleString(language === 'en' ? 'en-US' : 'zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };

    // Platform total count
    const totalPlatformsCount = platforms.length;
    // Loaded platform count
    const loadedCount = Object.keys(loadedPlatforms).length;

    const headerContent = (
        <HotNewsHeader
            language={language}
            totalPlatformsCount={totalPlatformsCount}
            loadedCount={loadedCount}
            loadingPlatform={loadingPlatform}
            lastUpdateTime={lastUpdateTime}
            formatLastUpdateTime={formatLastUpdateTime}
            isLoading={isLoading}
            handleRefreshAll={handleRefreshAll}
        />
    );

    return (
        <PageWrapper headerContent={headerContent}>
            <div className="container pt-1" ref={containerRef}>
                {error && (
                <Alert variant="destructive" className="mb-6">
                    <AlertCircle className="h-4 w-4"/>
                    <AlertTitle>{language === 'en' ? 'Error' : '错误'}</AlertTitle>
                    <AlertDescription>
                        {error}
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleRefreshAll}
                            className="ml-2"
                        >
                            {language === 'en' ? 'Try Again' : '重试'}
                        </Button>
                    </AlertDescription>
                </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {platforms.map(platform => (
                    <PlatformCard
                        key={platform.path}
                        platform={platform}
                        news={newsMap[platform.path] || []}
                        isNewsLoading={!loadedPlatforms[platform.path] || loadingRef.current[platform.path]}
                        language={language}
                        setupObserver={setupObserver}
                    />
                ))}
            </div>

            <LoadMoreTrigger
                ref={loadMoreElementRef}
                loadedCount={loadedCount}
                totalPlatformsCount={totalPlatformsCount}
                language={language}
            />
            </div>
        </PageWrapper>
    );
};

export default HotNewsPage;
