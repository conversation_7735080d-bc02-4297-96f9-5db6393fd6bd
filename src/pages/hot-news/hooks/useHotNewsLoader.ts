
import { useState, useRef, useCallback } from 'react';
import { NewsPlatform, NewsItem } from '@/hooks/hot-news/types';
import { fetchNews } from '@/hooks/hot-news/api';

export const useHotNewsLoader = () => {
    // 状态管理
    const [platforms, setPlatforms] = useState<NewsPlatform[]>([]);
    const [newsMap, setNewsMap] = useState<Record<string, NewsItem[]>>({});
    const [isLoading, setIsLoading] = useState(true);
    const [loadingPlatform, setLoadingPlatform] = useState<string | null>(null);
    const [loadedPlatformsCount, setLoadedPlatformsCount] = useState(0);
    const [error, setError] = useState<string | null>(null);
    // 跟踪已经加载过的平台
    const [loadedPlatforms, setLoadedPlatforms] = useState<Record<string, boolean>>({});
    // 用于跟踪懒加载的观察器
    const observersRef = useRef<Record<string, IntersectionObserver>>({});
    // 记录正在加载中的平台
    const loadingRef = useRef<Record<string, boolean>>({});
    // 记录最后更新时间
    const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
    // 底部加载观察器
    const loadMoreObserverRef = useRef<IntersectionObserver | null>(null);
    const loadMoreElementRef = useRef<HTMLDivElement>(null);
    
    // 加载单个平台的新闻数据
    const loadPlatformNews = useCallback(async (platform: NewsPlatform) => {
        // 如果已经加载过或正在加载，跳过
        if (loadedPlatforms[platform.path] || loadingRef.current[platform.path]) {
            return;
        }
        
        // 标记为正在加载
        loadingRef.current[platform.path] = true;
        setLoadingPlatform(platform.chineseName);
        
        try {
            const data = await fetchNews(platform.path);
            
            // 更新新闻数据
            setNewsMap(prev => ({
                ...prev,
                [platform.path]: data
            }));
            
            // 标记为已加载
            setLoadedPlatforms(prev => ({
                ...prev,
                [platform.path]: true
            }));
            
            setLoadedPlatformsCount(prev => prev + 1);
            
        } catch (err) {
            console.error(`获取'${platform.chineseName}'新闻失败:`, err);
            // 即使失败也标记为已尝试加载
            setLoadedPlatforms(prev => ({
                ...prev,
                [platform.path]: true
            }));
            setLoadedPlatformsCount(prev => prev + 1);
        } finally {
            // 清除加载状态
            loadingRef.current[platform.path] = false;
            setLoadingPlatform(null);
        }
    }, [loadedPlatforms]);
    
    // 加载更多平台数据
    const loadMorePlatforms = useCallback(() => {
        // 确定还有未加载的平台
        const unloadedPlatforms = platforms.filter(p => !loadedPlatforms[p.path]);
        
        if (unloadedPlatforms.length === 0) {
            console.log('所有平台数据已加载完成');
            return;
        }
        
        // 加载下一批平台
        const nextBatch = unloadedPlatforms.slice(0, 5);
        console.log(`加载下一批${nextBatch.length}个平台...`);
        
        nextBatch.forEach(platform => {
            loadPlatformNews(platform);
        });
    }, [platforms, loadedPlatforms, loadPlatformNews]);
    
    return {
        platforms,
        setPlatforms,
        newsMap,
        setNewsMap,
        isLoading,
        setIsLoading,
        loadingPlatform,
        setLoadingPlatform,
        loadedPlatformsCount,
        setLoadedPlatformsCount,
        error,
        setError,
        loadedPlatforms,
        setLoadedPlatforms,
        lastUpdateTime,
        setLastUpdateTime,
        observersRef,
        loadingRef,
        loadMoreElementRef,
        loadMoreObserverRef,
        loadPlatformNews,
        loadMorePlatforms
    };
};
