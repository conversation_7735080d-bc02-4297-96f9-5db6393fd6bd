
import React, { forwardRef } from 'react';

interface LoadMoreTriggerProps {
    loadedCount: number;
    totalPlatformsCount: number;
    language: string;
}

export const LoadMoreTrigger = forwardRef<HTMLDivElement, LoadMoreTriggerProps>(
    ({ loadedCount, totalPlatformsCount, language }, ref) => {
        return (
            <div 
                ref={ref} 
                className="flex justify-center items-center py-8"
            >
                {loadedCount < totalPlatformsCount ? (
                    <div className="flex items-center">
                        <span className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full mr-2"></span>
                        {language === 'en' ? 'Loading more platforms...' : '加载更多平台...'}
                    </div>
                ) : (
                    <div className="text-muted-foreground text-sm">
                        {language === 'en' ? 'All platforms loaded' : '已加载全部平台'}
                    </div>
                )}
            </div>
        );
    }
);

LoadMoreTrigger.displayName = 'LoadMoreTrigger';
