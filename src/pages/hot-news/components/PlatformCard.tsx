
import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { NewsPlatform, NewsItem } from '@/hooks/hot-news/types';
import { NewsItemComponent } from './NewsItem';
import { NewsLoadingSkeleton } from './NewsLoadingSkeleton';

interface PlatformCardProps {
    platform: NewsPlatform;
    news: NewsItem[];
    isNewsLoading: boolean;
    language: string;
    setupObserver: (platformPath: string, element: HTMLElement) => void;
}

export const PlatformCard: React.FC<PlatformCardProps> = ({
    platform,
    news,
    isNewsLoading,
    language,
    setupObserver
}) => {
    return (
        <Card 
            className="w-full mb-6"
            ref={el => {
                if (el) {
                    setupObserver(platform.path, el);
                }
            }}
        >
            <CardHeader className="pb-2 border-b">
                <CardTitle className="text-lg flex items-center justify-between">
                    <span>
                        {language === 'en' ? platform.name : platform.chineseName}
                    </span>
                    <Badge variant="outline" className="text-xs font-normal">
                        {news.length} {language === 'en' ? 'items' : '条'}
                    </Badge>
                </CardTitle>
            </CardHeader>

            <CardContent className="flex-1 p-0">
                <ScrollArea className="h-[500px] w-full">
                    {isNewsLoading ? (
                        <NewsLoadingSkeleton />
                    ) : news.length === 0 ? (
                        <div className="p-4 text-center text-muted-foreground text-sm">
                            {language === 'en' ? 'No news available' : '暂无新闻'}
                        </div>
                    ) : (
                        <div className="divide-y divide-dashed">
                            {news.map((item, index) => (
                                <NewsItemComponent 
                                    key={`${item.url}-${index}`}
                                    item={item}
                                    index={index}
                                    language={language}
                                />
                            ))}
                        </div>
                    )}
                </ScrollArea>
            </CardContent>
        </Card>
    );
};
