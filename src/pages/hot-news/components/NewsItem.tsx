
import React from 'react';
import { TrendingUp, ImageOff } from 'lucide-react';
import { NewsItem } from '@/hooks/hot-news/types';

interface NewsItemProps {
    item: NewsItem;
    index: number;
    language: string;
}

export const NewsItemComponent: React.FC<NewsItemProps> = ({ item, index, language }) => {
    // Format timestamp to readable date (add year)
    const formatTimestamp = (timestamp: string): string => {
        if (!timestamp) return '';

        try {
            const date = new Date(parseInt(timestamp));
            return date.toLocaleString(language === 'en' ? 'en-US' : 'zh-CN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (err) {
            console.error('Failed to format timestamp:', err);
            return '';
        }
    };

    // Show different style badges based on rank
    const getRankBadge = () => {
        if (index === 0) {
            // Gold flame icon (first place)
            return (
                <span className="flex items-center justify-center w-5 h-5 mr-2 text-amber-500">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                        <path fillRule="evenodd"
                            d="M12.963 2.286a.75.75 0 00-1.071-.136 9.742 9.742 0 00-3.539 6.177A7.547 7.547 0 016.648 6.61a.75.75 0 00-1.152-.082A9 9 0 1015.68 4.534a7.46 7.46 0 01-2.717-2.248zM15.75 14.25a3.75 3.75 0 11-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 011.925-3.545 3.75 3.75 0 013.255 3.717z"
                            clipRule="evenodd"/>
                    </svg>
                </span>
            );
        } else if (index === 1) {
            // Silver flame icon (second place)
            return (
                <span className="flex items-center justify-center w-5 h-5 mr-2 text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                        <path fillRule="evenodd"
                            d="M12.963 2.286a.75.75 0 00-1.071-.136 9.742 9.742 0 00-3.539 6.177A7.547 7.547 0 016.648 6.61a.75.75 0 00-1.152-.082A9 9 0 1015.68 4.534a7.46 7.46 0 01-2.717-2.248zM15.75 14.25a3.75 3.75 0 11-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 011.925-3.545 3.75 3.75 0 013.255 3.717z"
                            clipRule="evenodd"/>
                    </svg>
                </span>
            );
        } else if (index === 2) {
            // Bronze flame icon (third place)
            return (
                <span className="flex items-center justify-center w-5 h-5 mr-2 text-amber-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                        <path fillRule="evenodd"
                            d="M12.963 2.286a.75.75 0 00-1.071-.136 9.742 9.742 0 00-3.539 6.177A7.547 7.547 0 016.648 6.61a.75.75 0 00-1.152-.082A9 9 0 1015.68 4.534a7.46 7.46 0 01-2.717-2.248zM15.75 14.25a3.75 3.75 0 11-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 011.925-3.545 3.75 3.75 0 013.255 3.717z"
                            clipRule="evenodd"/>
                    </svg>
                </span>
            );
        } else {
            // Number rank (fourth place and later)
            return <span
                className="flex items-center justify-center w-5 h-5 mr-2 text-muted-foreground font-bold">{index + 1}</span>;
        }
    };

    return (
        <div className="p-4 border-b last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <a
                href={item.url}
                target="_blank"
                rel="noopener noreferrer"
                className="block"
            >
                <div className="flex gap-3">
                    {getRankBadge()}
                    {item.cover && (
                        <div className="flex-shrink-0 w-16 h-16 overflow-hidden rounded">
                            <img
                                src={item.cover}
                                alt={item.title}
                                className="w-full h-full object-cover"
                                loading="eager"
                                style={{ display: 'block' }} /* Ensure image is visible */
                                onError={(e) => {
                                    const target = e.currentTarget;
                                    const container = target.parentElement;
                                    
                                    if (container) {
                                        // Create a fallback element with ImageOff icon
                                        const fallback = document.createElement('div');
                                        fallback.className = 'w-full h-full flex items-center justify-center bg-muted';
                                        
                                        // Create simple SVG for the ImageOff icon using Lucide-style markup
                                        fallback.innerHTML = `
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round" 
                                            stroke-linejoin="round" class="text-muted-foreground">
                                            <line x1="2" y1="2" x2="22" y2="22" />
                                            <path d="M10.41 10.41a2 2 0 1 1-2.83-2.83" />
                                            <line x1="13.5" y1="13.5" x2="6" y2="21" />
                                            <rect x="2" y="2" width="20" height="20" rx="5" />
                                          </svg>
                                        `;
                                        
                                        // Replace the img with fallback
                                        container.replaceChild(fallback, target);
                                    }
                                }}
                            />
                        </div>
                    )}
                    <div className="flex-1">
                        <h3 className="text-sm font-medium line-clamp-2">{item.title}</h3>
                        {item.timestamp && (
                            <div className="mt-2 text-xs text-muted-foreground">
                                {formatTimestamp(item.timestamp) || ''}
                            </div>
                        )}
                        {item.hot && (
                            <div className="mt-1 text-xs text-rose-500 flex items-center">
                                <TrendingUp className="w-3 h-3 mr-1"/>
                                热度: {item.hot}
                            </div>
                        )}
                    </div>
                </div>
            </a>
        </div>
    );
};
