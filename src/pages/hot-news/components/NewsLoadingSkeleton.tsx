
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

export const NewsLoadingSkeleton: React.FC = () => {
    return (
        <div className="p-4 space-y-4">
            {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="flex gap-3 animate-pulse">
                    <Skeleton className="w-5 h-5 rounded-full"/>
                    <Skeleton className="w-16 h-16 rounded"/>
                    <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-full"/>
                        <Skeleton className="h-4 w-3/4"/>
                        <Skeleton className="h-3 w-1/4"/>
                    </div>
                </div>
            ))}
        </div>
    );
};
