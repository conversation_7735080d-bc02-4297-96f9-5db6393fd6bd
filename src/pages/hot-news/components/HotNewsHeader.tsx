
import React from 'react';
import { TrendingUp, RefreshCw, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import UnifiedHeader from '@/components/common/UnifiedHeader';

interface HotNewsHeaderProps {
    language: string;
    totalPlatformsCount: number;
    loadedCount: number;
    loadingPlatform: string | null;
    lastUpdateTime: Date | null;
    formatLastUpdateTime: () => string;
    isLoading: boolean;
    handleRefreshAll: () => void;
}

export const HotNewsHeader: React.FC<HotNewsHeaderProps> = ({
    language,
    totalPlatformsCount,
    loadedCount,
    loadingPlatform,
    lastUpdateTime,
    formatLastUpdateTime,
    isLoading,
    handleRefreshAll
}) => {
    const badges = totalPlatformsCount > 0 ? [{
        text: `${language === 'en' ? 'Loaded' : '已加载'}: ${loadedCount}/${totalPlatformsCount}`,
        variant: 'secondary' as const
    }] : [];

    const actions = [
        {
            id: 'refresh',
            label: language === 'en' ? 'Refresh All' : '刷新全部',
            icon: RefreshCw,
            onClick: handleRefreshAll,
            disabled: isLoading,
            variant: 'outline' as const
        }
    ];

    const statusContent = loadingPlatform ? (
        <div className="flex items-center text-sm text-white/90">
            <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
            {language === 'en' 
                ? `Loading ${loadingPlatform}`
                : `正在加载 ${loadingPlatform}`
            }
        </div>
    ) : lastUpdateTime ? (
        <div className="text-sm text-white/90">
            {language === 'en' ? 'Last updated: ' : '最后更新: '}
            {formatLastUpdateTime()}
        </div>
    ) : null;

    return (
        <UnifiedHeader
            title={language === 'en' ? "Today's Hot List" : '今日热榜'}
            description={language === 'en'
                ? 'Browse trending topics from various platforms'
                : '浏览各大平台的热门话题'}
            icon={TrendingUp}
            variant="gradient"
            gradientFrom="from-red-500"
            gradientTo="to-pink-500"
            badges={badges}
            actions={actions}
            layout="default"
        >
            {statusContent}
        </UnifiedHeader>
    );
};
