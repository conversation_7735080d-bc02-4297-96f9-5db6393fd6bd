
import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { useAppContext } from '@/context/AppContext';
import { Home } from 'lucide-react';

const NotFound = () => {
  const { t } = useAppContext();
  
  return (
    <div className="container flex flex-col items-center justify-center min-h-[70vh] py-10 text-center">
      <h1 className="text-9xl font-bold text-gray-200 dark:text-gray-800">404</h1>
      <h2 className="text-3xl font-bold mt-6">Page Not Found</h2>
      <p className="text-muted-foreground mt-2 max-w-md">
        The page you are looking for doesn't exist or has been moved.
      </p>
      <Button asChild className="mt-8">
        <Link to="/">
          <Home className="mr-2 h-4 w-4" />
          {t('home')}
        </Link>
      </Button>
    </div>
  );
};

export default NotFound;
