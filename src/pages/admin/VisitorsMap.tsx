
import React, { useEffect, useState } from 'react';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import MapContainer from './visitors-map/MapContainer';

// TypeScript interface for visitor locations
interface VisitorLocation {
  latitude: number;
  longitude: number;
  country: string;
  city: string;
  count: number;
}

const VisitorsMap: React.FC = () => {
  const [locations, setLocations] = useState<VisitorLocation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [mapKey, setMapKey] = useState(Date.now()); // For forcing re-render

  useEffect(() => {
    fetchVisitorLocations();
  }, []);

  const fetchVisitorLocations = async () => {
    setIsLoading(true);
    try {
      let data: any[] = [];

      if (isUsingSupabase()) {
        const { supabase } = await import('@/integrations/supabase/client');
        const { data: supabaseData, error } = await supabase
          .from('url_visits')
          .select('latitude, longitude, country, city');

        if (error) throw error;
        data = supabaseData || [];
      } else {
        // Use Go backend API
        const config = getBackendConfig();
        const response = await fetch(`${config.goBackend?.baseUrl}/visits`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        data = await response.json();
      }

      if (data && data.length > 0) {
        // Filter out records without coordinates
        const validData = data.filter(visit =>
          visit.latitude != null &&
          visit.longitude != null
        );

        if (validData.length > 0) {
          // Group by country and city
          const locationMap = new Map<string, VisitorLocation>();

          validData.forEach((visit: any) => {
            const country = visit.country || 'Unknown';
            const city = visit.city || 'Unknown';
            const key = `${visit.latitude},${visit.longitude}`;

            if (locationMap.has(key)) {
              const existing = locationMap.get(key)!;
              existing.count += 1;
            } else {
              locationMap.set(key, {
                latitude: visit.latitude,
                longitude: visit.longitude,
                country: country,
                city: city,
                count: 1
              });
            }
          });

          setLocations(Array.from(locationMap.values()));
        } else {
          // Sample data if no valid coordinates
          setSampleLocations();
        }
      } else {
        // Sample data if no real data exists
        setSampleLocations();
      }
    } catch (error) {
      console.error('Error fetching visitor locations:', error);
      // Fallback to sample data
      setSampleLocations();
    } finally {
      setIsLoading(false);
    }
  };

  const setSampleLocations = () => {
    setLocations([
      { latitude: 40.7128, longitude: -74.0060, country: 'United States', city: 'New York', count: 35 },
      { latitude: 39.9042, longitude: 116.4074, country: 'China', city: 'Beijing', count: 28 },
      { latitude: 19.0760, longitude: 72.8777, country: 'India', city: 'Mumbai', count: 15 },
      { latitude: 51.5074, longitude: -0.1278, country: 'United Kingdom', city: 'London', count: 22 },
      { latitude: 52.5200, longitude: 13.4050, country: 'Germany', city: 'Berlin', count: 17 },
      { latitude: 35.6762, longitude: 139.6503, country: 'Japan', city: 'Tokyo', count: 12 },
      { latitude: -33.8688, longitude: 151.2093, country: 'Australia', city: 'Sydney', count: 9 },
      { latitude: 55.7558, longitude: 37.6173, country: 'Russia', city: 'Moscow', count: 11 },
      { latitude: -23.5505, longitude: -46.6333, country: 'Brazil', city: 'São Paulo', count: 14 },
      { latitude: 48.8566, longitude: 2.3522, country: 'France', city: 'Paris', count: 19 }
    ]);
  };

  const handleReloadMap = () => {
    setMapKey(Date.now()); // Force re-render
    fetchVisitorLocations();
  };

  return (
    <div className="h-full w-full" key={mapKey}>
      <MapContainer
        locations={locations}
        isLoading={isLoading}
        onReload={handleReloadMap}
      />
    </div>
  );
};

export default VisitorsMap;
