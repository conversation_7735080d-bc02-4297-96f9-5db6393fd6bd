
import React from 'react';
import { Button } from '@/components/ui/button';
import { useAppContext } from '@/context/AppContext';

interface NoDataFallbackProps {
  onReload: () => void;
}

const NoDataFallback: React.FC<NoDataFallbackProps> = ({ onReload }) => {
  const { language } = useAppContext();
  
  return (
    <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
      <div className="text-center p-6 rounded-lg bg-card shadow-lg">
        <h3 className="text-lg font-semibold mb-2">
          {language === 'en' ? 'No Location Data Available' : '暂无位置数据'}
        </h3>
        <p className="text-muted-foreground mb-4">
          {language === 'en' 
            ? 'Location data will appear here once visitors access your short links.' 
            : '当访问者访问您的短链接时，位置数据将显示在此处。'}
        </p>
        <Button 
          variant="outline" 
          onClick={onReload}
          className="text-sm"
        >
          {language === 'en' ? 'Reload Map' : '重新加载地图'}
        </Button>
      </div>
    </div>
  );
};

export default NoDataFallback;
