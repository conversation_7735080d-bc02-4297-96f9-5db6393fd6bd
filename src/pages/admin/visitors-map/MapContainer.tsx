
import React, { useState } from 'react';
import { useCloudflareMap } from '@/hooks/useCloudflareMap';
import NoDataFallback from './NoDataFallback';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

interface VisitorLocation {
  latitude: number;
  longitude: number;
  country: string;
  city: string;
  count: number;
}

interface MapContainerProps {
  locations: VisitorLocation[];
  isLoading: boolean;
  onReload: () => void;
}

const MapContainer: React.FC<MapContainerProps> = ({ locations, isLoading, onReload }) => {
  const containerId = "cloudflare-map-container";
  const { isMapLoaded } = useCloudflareMap({ 
    locations,
    containerId
  });
  const [apiError, setApiError] = useState(false);

  // Handle potential loading errors
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!isMapLoaded && locations.length > 0 && !isLoading) {
        setApiError(true);
      }
    }, 5000); // Wait 5 seconds before showing the error

    return () => clearTimeout(timeoutId);
  }, [locations, isLoading, isMapLoaded]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (apiError) {
    return (
      <div className="flex flex-col justify-center items-center h-full">
        <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
        <h3 className="text-lg font-semibold mb-2">Map Loading Error</h3>
        <p className="text-muted-foreground mb-4 text-center">
          Unable to load the visitor map. Please try again later.
        </p>
        <Button onClick={onReload}>Try Again</Button>
      </div>
    );
  }
  
  return (
    <div className="relative h-full w-full">
      <div 
        id={containerId} 
        className="h-full w-full rounded-lg border" 
      ></div>
      
      {locations.length === 0 && (
        <NoDataFallback onReload={onReload} />
      )}
    </div>
  );
};

export default MapContainer;
