
import React from 'react';
import { motion } from 'framer-motion';
import NavigationTabs from '@/components/navigation/NavigationTabs';
import { ScrollArea } from '@/components/ui/scroll-area';

const NavigationContent = () => {
  return (
    <motion.div 
      className="min-h-screen bg-gray-50 dark:bg-gray-900 py-6 px-4 pt-16"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <ScrollArea className="h-[calc(100vh-100px)]">
        <div className="max-w-7xl mx-auto">
          <NavigationTabs activeTab="browse" setActiveTab={() => {}} />
        </div>
      </ScrollArea>
    </motion.div>
  );
};

export default NavigationContent;
