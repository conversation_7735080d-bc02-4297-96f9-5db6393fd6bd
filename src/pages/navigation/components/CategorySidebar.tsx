
import React from 'react';
import { ChevronRight } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface CategorySidebarProps {
  categories: Array<{
    id: string;
    name: string;
    is_public: boolean;
    sort_order: number;
    user_id: string;
    parent_id?: string | null;
  }>;
  activeCategory: string | null;
  setActiveCategory: (id: string) => void;
  setActiveSubcategory: (id: string | null) => void;
}

const CategorySidebar = ({ 
  categories, 
  activeCategory, 
  setActiveCategory, 
  setActiveSubcategory 
}: CategorySidebarProps) => {
  const { language } = useAppContext();

  return (
    <div className="md:col-span-1 space-y-2">
      <h2 className="font-semibold mb-4">{language === 'en' ? 'Categories' : '分类'}</h2>
      <div className="space-y-1 border rounded-lg p-2">
        {categories.map(category => (
          <button
            key={category.id}
            className={`w-full text-left px-4 py-3 rounded-md transition-colors flex items-center justify-between ${
              activeCategory === category.id 
                ? 'bg-primary text-primary-foreground' 
                : 'hover:bg-muted'
            }`}
            onClick={() => {
              setActiveCategory(category.id);
              setActiveSubcategory(null);
            }}
          >
            <span>{category.name}</span>
            <ChevronRight className={`h-4 w-4 transition-transform ${
              activeCategory === category.id ? 'rotate-90' : ''
            }`} />
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategorySidebar;
