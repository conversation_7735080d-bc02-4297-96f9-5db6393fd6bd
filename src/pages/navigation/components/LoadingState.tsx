
import React from 'react';
import { useAppContext } from '@/context/AppContext';

const LoadingState = () => {
  const { language } = useAppContext();

  return (
    <div className="container py-20">
      <div className="flex justify-center items-center">
        <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full"></div>
        <span className="ml-3">
          {language === 'en' ? 'Loading...' : '加载中...'}
        </span>
      </div>
    </div>
  );
};

export default LoadingState;
