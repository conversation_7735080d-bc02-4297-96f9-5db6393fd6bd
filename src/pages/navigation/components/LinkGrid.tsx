
import React from 'react';
import { ExternalLink, LinkIcon } from 'lucide-react';
import { motion } from 'framer-motion';
import { useAppContext } from '@/context/AppContext';
import { Card, CardContent } from '@/components/ui/card';

interface LinkGridProps {
  links: Array<{
    id: string;
    name: string;
    url: string;
    icon: string | null;
    is_internal: boolean;
  }>;
}

const LinkGrid = ({ links }: LinkGridProps) => {
  const { language } = useAppContext();

  if (links.length === 0) {
    return (
      <div className="py-8 text-center border rounded-lg bg-muted/20">
        <p className="text-muted-foreground">
          {language === 'en' 
            ? 'No navigation links found in this category.' 
            : '此分类中未找到导航链接。'}
        </p>
      </div>
    );
  }

  return (
    <motion.div 
      className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {links.map((link, index) => (
        <motion.div
          key={link.id}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            duration: 0.3, 
            delay: index * 0.05,
            ease: [0.4, 0, 0.2, 1]
          }}
          whileHover={{ 
            scale: 1.03, 
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" 
          }}
        >
          <Card className="h-full overflow-hidden border border-primary/10 bg-gradient-to-b from-white to-primary/5 dark:from-background dark:to-primary/10 hover:border-primary/30 transition-all duration-300">
            <CardContent className="p-4 h-full flex flex-col">
              <a
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex flex-col items-center text-center gap-2 h-full group"
              >
                <div className="w-16 h-16 flex items-center justify-center rounded-full bg-primary/10 mb-3 mt-2 transition-all duration-300 group-hover:bg-primary/20">
                  {link.icon ? (
                    <img 
                      src={link.icon} 
                      alt={link.name} 
                      className="w-10 h-10 object-contain"
                      onError={(e) => {
                        e.currentTarget.src = '/placeholder.svg';
                      }}
                    />
                  ) : (
                    <LinkIcon className="w-8 h-8 text-primary/60" />
                  )}
                </div>
                <div className="font-medium text-foreground group-hover:text-primary transition-colors">{link.name}</div>
                <div className="text-xs text-muted-foreground flex items-center gap-1 mt-auto pt-2">
                  {link.is_internal ? (
                    <span>{language === 'en' ? 'Internal' : '内部'}</span>
                  ) : (
                    <>
                      <ExternalLink className="w-3 h-3" />
                      <span>{language === 'en' ? 'External' : '外部'}</span>
                    </>
                  )}
                </div>
              </a>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default LinkGrid;
