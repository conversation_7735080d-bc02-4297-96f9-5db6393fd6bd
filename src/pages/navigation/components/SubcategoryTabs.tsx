
import React from 'react';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tag } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface SubcategoryTabsProps {
  subcategories: Array<{
    id: string;
    name: string;
    is_public: boolean;
    sort_order: number;
    user_id: string;
    parent_id?: string | null;
  }>;
  activeSubcategory: string | null;
  setActiveSubcategory: (id: string | null) => void;
}

const SubcategoryTabs = ({ 
  subcategories, 
  activeSubcategory, 
  setActiveSubcategory 
}: SubcategoryTabsProps) => {
  const { language } = useAppContext();

  if (subcategories.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <Tabs 
        value={activeSubcategory || "all"} 
        onValueChange={(value) => setActiveSubcategory(value === "all" ? null : value)}
        className="w-full"
      >
        <TabsList className="mb-4 w-full justify-start overflow-x-auto">
          <TabsTrigger value="all" className="flex items-center gap-1">
            <Tag className="w-4 h-4" />
            {language === 'en' ? 'All' : '全部'}
          </TabsTrigger>
          {subcategories.map(subcategory => (
            <TabsTrigger key={subcategory.id} value={subcategory.id} className="flex items-center gap-1">
              <Tag className="w-4 h-4" />
              {subcategory.name}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
};

export default SubcategoryTabs;
