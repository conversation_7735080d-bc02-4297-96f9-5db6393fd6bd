
import React, { useState, useEffect } from 'react';
import { Tabs } from "@/components/ui/tabs";
import { useAppContext } from '@/context/AppContext';
import { useLocation } from 'react-router-dom';
import AboutHero from '@/components/about/AboutHero';
import AboutContent from '@/components/about/AboutContent';
import AboutFAQ from '@/components/about/AboutFAQ';
import GridPattern from '@/components/about/GridPattern';

const About = () => {
  const { t } = useAppContext();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("about"); // "about" 或 "features"
  
  // 根据URL参数设置初始标签
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get('tab');
    if (tabParam === 'features') {
      setActiveTab('features');
    } else {
      setActiveTab('about');
    }
  }, [location]);
  
  // 监听自定义事件，用于在导航栏点击时切换标签
  useEffect(() => {
    // 处理自定义的标签切换事件
    const handleSwitchTab = (e: Event) => {
      const customEvent = e as CustomEvent;
      if (customEvent.detail && customEvent.detail.tab) {
        setActiveTab(customEvent.detail.tab);
        
        // 更新URL查询参数，但不触发新的导航
        const url = new URL(window.location.href);
        if (customEvent.detail.tab === 'features') {
          url.searchParams.set('tab', 'features');
        } else {
          url.searchParams.delete('tab');
        }
        window.history.replaceState({}, '', url.toString());
      }
    };
    
    // 添加事件监听
    window.addEventListener('switchAboutTab', handleSwitchTab);
    
    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('switchAboutTab', handleSwitchTab);
    };
  }, []);
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/30">
      {/* Hero Section with Background */}
      <AboutHero activeTab={activeTab} setActiveTab={setActiveTab} />

      {/* Main Content Area */}
      <Tabs value={activeTab} className="w-full">
        <AboutContent activeTab={activeTab} />
      </Tabs>

      {/* FAQ Section */}
      <AboutFAQ />

      {/* Include the Grid Pattern CSS */}
      <GridPattern />
    </div>
  );
};

export default About;
