import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { useDashboardData } from '@/hooks/useDashboardData';
import DashboardTabs from '@/components/dashboard/DashboardTabs';
import UnifiedHeader from '@/components/common/UnifiedHeader';
import { BarChart3 } from 'lucide-react';
import PageWrapper from '@/components/layout/PageWrapper';

const Dashboard = () => {
  const { language } = useAppContext();
  const { toast } = useToast();
  const navigate = useNavigate();

  const {
    urlData,
    emailData,
    approvedDomains,
    isLoading,
    userId,
    isAuthenticated,
    handleDeleteUrl,
    handleDeleteEmail,
    handleCreateTempEmail,
    handleUrlCreated
  } = useDashboardData();

  // Redirect to login if not authenticated
  React.useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      navigate('/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      description: language === 'en' ? "Copied to clipboard" : "已复制到剪贴板",
    });
  };

  if (isLoading) {
    return (
      <div className="container py-10">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  const headerContent = (
    <UnifiedHeader
      title={language === 'en' ? 'Dashboard' : '控制台'}
      description={language === 'en' ? 'Manage your short URLs and temporary emails' : '管理您的短链接和临时邮箱'}
      icon={BarChart3}
      variant="gradient"
      gradientFrom="from-green-500"
      gradientTo="to-blue-500"
      layout="default"
    />
  );

  return (
    <PageWrapper headerContent={headerContent}>
      <div className="container pt-1">

      <DashboardTabs
        urlData={urlData}
        emailData={emailData}
        approvedDomains={approvedDomains}
        userId={userId}
        isLoading={isLoading}
        onCopy={handleCopy}
        onDeleteUrl={handleDeleteUrl}
        onDeleteEmail={handleDeleteEmail}
        onCreateEmail={handleCreateTempEmail}
        onUrlCreated={handleUrlCreated}
      />
      </div>
    </PageWrapper>
  );
};

export default Dashboard;
