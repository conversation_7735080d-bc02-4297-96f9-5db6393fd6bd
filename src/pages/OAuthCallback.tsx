import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams, useParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Loader2, UserPlus, Link as LinkIcon } from 'lucide-react';
import { authService, UserAuthResponse } from '@/services/api';

interface OAuthUserInfo {
  provider_user_id: string;
  email: string;
  username: string;
  avatar_url?: string;
  name?: string;
}

interface OAuthCallbackResponse {
  token?: string;
  user?: UserAuthResponse;
  need_account_link: boolean;
  account_link_token?: string;
  existing_user_email?: string;
  provider_user_info?: OAuthUserInfo;
}

const OAuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const { provider } = useParams<{ provider: string }>();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const { fetchUser } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [needAccountLink, setNeedAccountLink] = useState(false);
  const [accountLinkToken, setAccountLinkToken] = useState('');
  const [existingUserEmail, setExistingUserEmail] = useState('');
  const [providerUserInfo, setProviderUserInfo] = useState<OAuthUserInfo | null>(null);
  const [newUsername, setNewUsername] = useState('');
  const [linkExisting, setLinkExisting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    handleOAuthCallback();
  }, []);

  const handleOAuthCallback = async () => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const savedState = localStorage.getItem('oauth_state');

    if (!code) {
      setError('缺少授权码');
      setLoading(false);
      return;
    }

    if (!provider) {
      setError('缺少提供商信息');
      setLoading(false);
      return;
    }

    // 验证state（防止CSRF攻击）
    if (state !== savedState) {
      setError('状态验证失败，请重新登录');
      setLoading(false);
      return;
    }

    // 清除保存的state
    localStorage.removeItem('oauth_state');

    try {
      const response = await authService.handleOAuthCallback(provider, code, state || '');
      const data: OAuthCallbackResponse = response.data;

      if (data.need_account_link) {
        // 需要账户关联
        setNeedAccountLink(true);
        setAccountLinkToken(data.account_link_token || '');
        setExistingUserEmail(data.existing_user_email || '');
        setProviderUserInfo(data.provider_user_info || null);
        setLinkExisting(!!data.existing_user_email);
        
        if (data.provider_user_info?.username) {
          setNewUsername(data.provider_user_info.username);
        }
      } else {
        // 直接登录成功
        if (data.token && data.user) {
          localStorage.setItem('authToken', data.token);
          localStorage.setItem('auth_token', data.token);
          
          // 立即更新用户状态
          await fetchUser();
          
          // 触发自定义事件确保状态更新
          window.dispatchEvent(new Event('authStateChanged'));
          
          toast({
            title: '登录成功',
            description: '欢迎回来！',
          });
          navigate('/');
        } else {
          setError('登录响应格式错误');
        }
      }
    } catch (error: unknown) {
      console.error('OAuth callback error:', error);
      let errorMessage = 'OAuth登录失败';
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { error?: string } } };
        errorMessage = axiosError.response?.data?.error || errorMessage;
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleAccountLink = async () => {
    if (!accountLinkToken) {
      setError('缺少账户关联令牌');
      return;
    }

    if (!linkExisting && !newUsername.trim()) {
      setError('请输入用户名');
      return;
    }

    setLoading(true);
    try {
      const response = await authService.linkOAuthAccount({
        token: accountLinkToken,
        username: linkExisting ? undefined : newUsername,
        link_existing: linkExisting,
      });

      const data = response.data;
      if (data.token && data.user) {
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('auth_token', data.token);
        
        // 立即更新用户状态
        await fetchUser();
        
        // 触发自定义事件确保状态更新
        window.dispatchEvent(new Event('authStateChanged'));
        
        toast({
          title: '账户关联成功',
          description: linkExisting ? '已关联到现有账户' : '新账户创建成功',
        });
        navigate('/');
      } else {
        setError('账户关联响应格式错误');
      }
    } catch (error: unknown) {
      console.error('Account link error:', error);
      let errorMessage = '账户关联失败';
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { error?: string } } };
        errorMessage = axiosError.response?.data?.error || errorMessage;
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !needAccountLink) {
    return (
      <div className="container py-10 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mb-4" />
            <p className="text-muted-foreground">正在处理登录...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-10 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">登录失败</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => navigate('/login')} 
              className="w-full"
            >
              返回登录页面
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (needAccountLink) {
    return (
      <div className="container py-10 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>账户关联</CardTitle>
            <CardDescription>
              {existingUserEmail 
                ? `检测到已有账户 ${existingUserEmail}，是否要关联到现有账户？`
                : '请设置用户名以创建新账户'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {providerUserInfo && (
              <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                {providerUserInfo.avatar_url && (
                  <img 
                    src={providerUserInfo.avatar_url} 
                    alt="头像" 
                    className="w-10 h-10 rounded-full"
                  />
                )}
                <div>
                  <p className="font-medium">{providerUserInfo.name || providerUserInfo.username}</p>
                  <p className="text-sm text-muted-foreground">{providerUserInfo.email}</p>
                </div>
              </div>
            )}

            {existingUserEmail && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="link-existing"
                    name="account-action"
                    checked={linkExisting}
                    onChange={() => setLinkExisting(true)}
                  />
                  <Label htmlFor="link-existing">关联到现有账户 {existingUserEmail}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="create-new"
                    name="account-action"
                    checked={!linkExisting}
                    onChange={() => setLinkExisting(false)}
                  />
                  <Label htmlFor="create-new">创建新账户</Label>
                </div>
              </div>
            )}

            {!linkExisting && (
              <div className="space-y-2">
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  value={newUsername}
                  onChange={(e) => setNewUsername(e.target.value)}
                  placeholder="请输入用户名"
                  required
                />
              </div>
            )}

            {error && (
              <div className="text-red-600 text-sm">{error}</div>
            )}

            <div className="flex space-x-2">
              <Button
                onClick={handleAccountLink}
                disabled={loading}
                className="flex-1"
              >
                {loading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : linkExisting ? (
                  <LinkIcon className="mr-2 h-4 w-4" />
                ) : (
                  <UserPlus className="mr-2 h-4 w-4" />
                )}
                {linkExisting ? '关联账户' : '创建账户'}
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/login')}
                disabled={loading}
              >
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
};

export default OAuthCallback; 