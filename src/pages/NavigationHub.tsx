import React, { useState, useCallback, useEffect } from 'react';
// 如果没有使用Next.js或没有安装，移除此导入
// import { NextPage } from 'next';

// 定义链接类型
interface LinkItem {
  id: string;
  title: string;
  description: string;
  url: string;
  category?: string;
}

// const NavigationHub: NextPage = () => {
const NavigationHub = () => {
  const [linkStatus, setLinkStatus] = useState<{ [key: string]: boolean }>({});
  const [viewType, setViewType] = useState<'list' | 'grid'>('list');
  const [filteredLinks, setFilteredLinks] = useState<LinkItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  
  // 添加缺失的函数定义
  const handleRefresh = () => {
    // 刷新逻辑
  };

  // 检查链接是否可访问
  const checkLinkAvailability = useCallback(async (url: string) => {
    try {
      const response = await fetch(`/api/check-link?url=${encodeURIComponent(url)}`);
      const data = await response.json();
      
      setLinkStatus(prev => ({
        ...prev,
        [url]: data.isAvailable
      }));
      
      return data.isAvailable;
    } catch (error) {
      console.error('检查链接出错:', error);
      setLinkStatus(prev => ({
        ...prev,
        [url]: false
      }));
      return false;
    }
  }, []);

  useEffect(() => {
    const checkLinks = async () => {
      if (viewType === 'grid' && filteredLinks.length > 0) {
        // 仅检查当前显示的前10个链接以避免过多请求
        const linksToCheck = filteredLinks.slice(0, 10);
        for (const link of linksToCheck) {
          if (!(link.url in linkStatus)) {
            await checkLinkAvailability(link.url);
            // 添加小延迟以避免请求风暴
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        }
      }
    };
    
    checkLinks();
  }, [filteredLinks, viewType, checkLinkAvailability, linkStatus]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        
        {/* 搜索区域 - 调整大小比例 */}
        <div className="mb-6 flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
          <div className="relative flex-grow">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索资源..."
              className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2.5 pr-10 text-gray-700 focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:focus:border-blue-400"
            />
            <span className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </span>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => setViewType('list')}
              className={`flex items-center justify-center rounded-lg px-2.5 py-2 text-sm ${
                viewType === 'list'
                  ? 'bg-blue-600 text-white dark:bg-blue-500'
                  : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200'
              }`}
              aria-label="列表视图"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              <span className="ml-1.5">列表</span>
            </button>
            <button
              onClick={() => setViewType('grid')}
              className={`flex items-center justify-center rounded-lg px-2.5 py-2 text-sm ${
                viewType === 'grid'
                  ? 'bg-blue-600 text-white dark:bg-blue-500'
                  : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200'
              }`}
              aria-label="网格视图"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
              <span className="ml-1.5">网格</span>
            </button>
            <button
              onClick={handleRefresh}
              className="flex items-center justify-center rounded-lg bg-green-600 px-2.5 py-2 text-sm text-white hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600"
              aria-label="刷新"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span className="ml-1.5">刷新</span>
            </button>
          </div>
        </div>

        {/* 类别选择器 */}
        <div className="mt-4">
          {/* 类别选择器内容 */}
        </div>

        {/* 链接内容区域 */}
        <div className="mt-6">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
            </div>
          ) : error ? (
            <div className="rounded-lg bg-red-100 p-4 text-red-700 dark:bg-red-900 dark:text-red-200">
              <p>加载资源时出错，请稍后再试。</p>
            </div>
          ) : viewType === 'grid' ? (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {filteredLinks.length === 0 ? (
                <div className="col-span-full py-8 text-center text-gray-500 dark:text-gray-400">
                  <p>未找到匹配的资源。</p>
                </div>
              ) : (
                filteredLinks.map((link) => (
                  <div
                    key={link.id}
                    className="group relative overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
                  >
                    <div className="absolute right-2 top-2 z-10 flex space-x-1">
                      {linkStatus[link.url] === false && (
                        <span className="flex h-6 w-6 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-200" title="链接无法访问">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </span>
                      )}
                      {/* 其他图标 */}
                    </div>
                    <a 
                      href={link.url} 
                      target="_blank" 
                      rel="noopener noreferrer" 
                      className="block p-4"
                    >
                      <h3 className="mb-1 text-lg font-medium text-gray-900 dark:text-white">{link.title}</h3>
                      <p className="mb-3 text-sm text-gray-600 dark:text-gray-300">{link.description}</p>
                    </a>
                  </div>
                ))
              )}
            </div>
          ) : (
            <div className="divide-y divide-gray-200 rounded-lg border border-gray-200 bg-white dark:divide-gray-700 dark:border-gray-700 dark:bg-gray-800">
              {/* 列表视图内容 */}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NavigationHub; 