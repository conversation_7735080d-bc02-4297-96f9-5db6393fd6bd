
import React from 'react';
import { Link } from 'react-router-dom';
import { Link2, Mail } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useAppContext } from '@/context/AppContext';

const QuickStartSection = () => {
  const { t } = useAppContext();
  
  return (
    <section id="quick-start" className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">{t('quickStart')}</h2>
            <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              {t('quickStartDesc')}
            </p>
          </div>
        </div>

        <div className="grid gap-10 mt-12 md:grid-cols-2">
          {/* URL Shortener Quick Start */}
          <div className="flex flex-col space-y-4">
            <div className="p-6 rounded-xl bg-card border shadow-sm">
              <h3 className="text-2xl font-bold flex items-center">
                <Link2 className="mr-2 h-6 w-6 text-accent1-500" />
                {t('urlShortener')}
              </h3>
              <div className="mt-4 space-y-3">
                <div className="flex items-start">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary mr-3">1</div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {t('enterLongUrl')}
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary mr-3">2</div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {t('chooseExpiration')} {t('orPermanent')}
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary mr-3">3</div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {t('getShortUrl')}
                    </p>
                  </div>
                </div>
              </div>
              <Button asChild className="mt-6 w-full bg-gradient-to-r from-brand-500 to-accent1-500 hover:from-brand-600 hover:to-accent1-600">
                <Link to="/">
                  {t('tryItNow')}
                </Link>
              </Button>
            </div>
          </div>

          {/* Temp Email Quick Start */}
          <div className="flex flex-col space-y-4">
            <div className="p-6 rounded-xl bg-card border shadow-sm">
              <h3 className="text-2xl font-bold flex items-center">
                <Mail className="mr-2 h-6 w-6 text-brand-500" />
                {t('tempEmail')}
              </h3>
              <div className="mt-4 space-y-3">
                <div className="flex items-start">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary mr-3">1</div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {t('createAccount')}
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary mr-3">2</div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {t('generateTempEmail')} ({t('upTo30Days')})
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary mr-3">3</div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {t('receiveMessages')}
                    </p>
                  </div>
                </div>
              </div>
              <Button asChild className="mt-6 w-full bg-gradient-to-r from-brand-500 to-accent1-500 hover:from-brand-600 hover:to-accent1-600">
                <Link to="/register">
                  {t('getStarted')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default QuickStartSection;
