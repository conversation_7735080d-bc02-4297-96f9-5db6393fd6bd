import React from 'react';
import { Container } from '@/components/ui/container';
import { useAppContext } from '@/context/AppContext';
import { ArrowRight, TrendingUp, ExternalLink, BarChart3, Globe, Clock, Filter, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

const HomeHotNewsSection = () => {
  const { language } = useAppContext();
  
  // Platform colors and icons
  const platforms = [
    {
      name: '微博',
      color: 'bg-gradient-to-r from-red-500 to-red-600 text-white border-red-400',
      icon: '🔥',
      topics: ['#冬奥会#', '#明星同款#', '#热搜话题#']
    },
    {
      name: '知乎',
      color: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-400',
      icon: '💭',
      topics: ['如何看待...', '为什么会...', '如何评价...']
    },
    {
      name: '抖音',
      color: 'bg-gradient-to-r from-black to-zinc-800 text-white border-zinc-700',
      icon: '🎵',
      topics: ['#抖音挑战#', '#热门BGM#', '#创意视频#']
    },
    {
      name: '百度',
      color: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white border-blue-500',
      icon: '🔍',
      topics: ['热搜榜', '实时热点', '搜索风云榜']
    },
    {
      name: 'B站',
      color: 'bg-gradient-to-r from-pink-500 to-pink-600 text-white border-pink-400',
      icon: '📺',
      topics: ['#每周必看#', '#入站必刷#', '#热门番剧#']
    }
  ];
  
  return (
    <section className="py-20 bg-gradient-to-b from-background to-muted/20">
      <Container>
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
            {language === 'en' ? "Today's Trending Topics" : '今日热门话题'}
          </h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-3xl mx-auto">
            {language === 'en' 
              ? 'Stay updated with trending topics from various platforms all in one place. No more jumping between sites to find what\'s hot today.' 
              : '一站式了解各大平台热门话题。不必再在不同网站间跳转寻找今日热点。'}
          </p>
        </motion.div>
        
        {/* 平台卡片展示 - 采用水平滚动卡片 */}
        <div className="overflow-x-auto pb-6 -mx-4 px-4 scrollbar-hide">
          <div className="flex gap-5 min-w-max">
            {platforms.map((platform, i) => (
              <motion.div
                key={platform.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: i * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className={cn(
                  "w-64 relative overflow-hidden border-t-4", 
                  platform.color.split(' ')[0].replace('from-', 'border-')
                )}>
                  <div className="absolute -right-6 -top-6 w-16 h-16 opacity-10 flex items-center justify-center text-4xl">
                    {platform.icon}
                  </div>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-center mb-3">
                      <Badge className={cn("px-3 py-1", platform.color)}>
                        {platform.name}
                      </Badge>
                      <Zap className="h-4 w-4 text-yellow-500" />
                    </div>
                    <ul className="space-y-2 mt-4 text-sm">
                      {platform.topics.map((topic, index) => (
                        <li key={index} className="truncate hover:text-primary cursor-pointer transition-colors flex items-center">
                          <span className="inline-block w-4 text-center mr-2">{index + 1}</span>
                          <span className="truncate">{topic}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  <CardFooter className="pt-0 flex justify-end">
                    <Button variant="ghost" size="sm" className="text-xs gap-1">
                      {language === 'en' ? 'More' : '查看更多'}
                      <ArrowRight className="h-3 w-3" />
                    </Button>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* 功能特点卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Card className="bg-gradient-to-br from-background to-muted/20 hover:shadow-md transition-all border-t-2 border-primary/20 h-full">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center">
                  <div className="p-3 bg-primary/10 rounded-full mb-4">
                    <BarChart3 className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">
                    {language === 'en' ? 'Categorized Content' : '分类内容'}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {language === 'en' 
                      ? 'News grouped by social media, news sites, tech platforms, and entertainment.' 
                      : '按社交媒体、新闻网站、科技平台和娱乐分类的热点内容。'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
          
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <Card className="bg-gradient-to-br from-background to-muted/20 hover:shadow-md transition-all border-t-2 border-primary/20 h-full">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center">
                  <div className="p-3 bg-primary/10 rounded-full mb-4">
                    <Clock className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">
                    {language === 'en' ? 'Real-time Updates' : '实时更新'}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {language === 'en' 
                      ? 'Content automatically refreshes to show the latest trends as they happen.' 
                      : '内容自动刷新，实时显示最新热点。'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
          
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Card className="bg-gradient-to-br from-background to-muted/20 hover:shadow-md transition-all border-t-2 border-primary/20 h-full">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center">
                  <div className="p-3 bg-primary/10 rounded-full mb-4">
                    <Filter className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">
                    {language === 'en' ? 'Smart Filtering' : '智能筛选'}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {language === 'en' 
                      ? 'Find exactly what interests you with platform and category filtering.' 
                      : '通过平台和类别筛选，找到您感兴趣的内容。'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
        
        <div className="text-center mt-12">
          <Button asChild size="lg" className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary shadow-md hover:shadow-lg transition-all">
            <Link to="/hot-news" className="inline-flex items-center">
              {language === 'en' ? 'Explore Trending Topics' : '探索热门话题'}
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </Container>
    </section>
  );
};

export default HomeHotNewsSection;
