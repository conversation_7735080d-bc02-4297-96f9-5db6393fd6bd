
import React from 'react';
import { Link } from 'react-router-dom';
import { useAppContext } from '@/context/AppContext';
import FeaturesGrid from './FeaturesGrid';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';

const HomeFeaturesSection = () => {
  const { t } = useAppContext();
  
  return (
    <section id="features" className="w-full py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">{t('features')}</h2>
            <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              {t('featuresDesc')}
            </p>
          </div>
        </div>
        <FeaturesGrid />
        <div className="mt-12 text-center">
          <Button asChild variant="outline" size="lg">
            <Link to="/features" className="group">
              {t('learnMoreFeatures')} 
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HomeFeaturesSection;
