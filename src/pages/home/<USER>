import React from 'react';
import { Container } from '@/components/ui/container';
import { useAppContext } from '@/context/AppContext';
import { ArrowRight, Compass, Link, Globe, GanttChart, SquareCode, Settings, Star } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Link as RouterLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const HomeNavigationSection = () => {
  const { language } = useAppContext();
  
  // 示例导航类别
  const navCategories = [
    { 
      name: language === 'en' ? 'Development Tools' : '开发工具', 
      icon: <SquareCode className="h-5 w-5" />, 
      color: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white',
      count: 28
    },
    { 
      name: language === 'en' ? 'Design Resources' : '设计资源', 
      icon: <Settings className="h-5 w-5" />, 
      color: 'bg-gradient-to-r from-purple-500 to-purple-600 text-white',
      count: 36
    },
    { 
      name: language === 'en' ? 'Productivity' : '生产力工具', 
      icon: <GanttChart className="h-5 w-5" />, 
      color: 'bg-gradient-to-r from-green-500 to-green-600 text-white',
      count: 42
    },
    { 
      name: language === 'en' ? 'Learning' : '学习资源', 
      icon: <Star className="h-5 w-5" />, 
      color: 'bg-gradient-to-r from-amber-500 to-amber-600 text-white',
      count: 54
    }
  ];
  
  return (
    <section className="py-20 bg-gradient-to-b from-muted/30 to-background">
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center">
          {/* 左侧内容 */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex mb-6">
              <div className="p-3 bg-primary/10 rounded-full">
                <Compass className="h-8 w-8 text-primary" />
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70 mb-6">
              {language === 'en' ? "Navigation Directory" : '导航目录'}
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              {language === 'en' 
                ? 'Browse and discover useful tools and resources organized by category. Find everything you need in one place.' 
                : '浏览和发现按类别组织的实用工具和资源。在一处找到您所需的一切。'}
            </p>
            
            {/* 特色类别列表 */}
            <div className="space-y-3 mb-8">
              <h3 className="text-lg font-medium">{language === 'en' ? 'Featured Categories' : '精选分类'}</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {navCategories.map((category, i) => (
                  <motion.div
                    key={category.name}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: i * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="flex items-center gap-3 group p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${category.color}`}>
                        {category.icon}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium group-hover:text-primary transition-colors">
                          {category.name}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {category.count} {language === 'en' ? 'links' : '链接'}
                        </div>
                      </div>
                      <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
            
            <Button asChild size="lg" className="group shadow-md hover:shadow-lg transition-all">
              <RouterLink to="/navigation" className="inline-flex items-center">
                {language === 'en' ? 'Explore Navigation' : '探索导航'}
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </RouterLink>
            </Button>
          </motion.div>
          
          {/* 右侧卡片 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="lg:pl-10 relative"
          >
            {/* 装饰元素 */}
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-primary/5 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-primary/5 rounded-full blur-3xl"></div>
            
            {/* 导航卡片模型 */}
            <div className="relative z-10 bg-card rounded-xl border shadow-xl overflow-hidden">
              <div className="p-4 bg-muted/50 border-b flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Compass className="h-4 w-4 text-primary" />
                  <span className="font-medium">{language === 'en' ? 'Navigation' : '导航'}</span>
                </div>
                <Badge variant="outline" className="bg-primary/10 text-primary text-xs px-2 py-0 h-5">
                  {language === 'en' ? 'Premium' : '高级版'}
                </Badge>
              </div>
              <div className="p-5 space-y-4">
                {/* 搜索框示意 */}
                <div className="bg-muted/30 border rounded-md p-2 px-3 flex items-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-muted-foreground"
                  >
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.3-4.3"></path>
                  </svg>
                  <span className="text-sm text-muted-foreground">
                    {language === 'en' ? 'Search resources...' : '搜索资源...'}
                  </span>
                </div>
                
                {/* 常用网站 */}
                <div className="space-y-2">
                  <div className="text-sm font-medium">
                    {language === 'en' ? 'Frequently Used' : '常用网站'}
                  </div>
                  <div className="grid grid-cols-4 gap-2">
                    {[
                      'G', 'Y', 'B', 'F', 'A', 'T', 'M', 'C'
                    ].map((letter, i) => (
                      <div 
                        key={i}
                        className="aspect-square flex items-center justify-center rounded-md bg-muted/50 hover:bg-muted transition-colors cursor-pointer text-lg font-medium text-primary/80"
                      >
                        {letter}
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* 最近访问 */}
                <div className="space-y-2">
                  <div className="text-sm font-medium">
                    {language === 'en' ? 'Recently Visited' : '最近访问'}
                  </div>
                  <div className="space-y-1.5">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex items-center justify-between bg-muted/20 p-2 rounded-md hover:bg-muted/40 transition-colors cursor-pointer">
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-sm bg-primary/20 flex items-center justify-center text-xs text-primary">
                            {String.fromCharCode(64 + i)}
                          </div>
                          <span className="text-sm">example{i}.com</span>
                        </div>
                        <span className="text-xs text-muted-foreground">2m ago</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="p-3 bg-muted/30 border-t flex justify-center">
                <div className="text-xs text-muted-foreground flex items-center gap-1">
                  <span>
                    {language === 'en' ? 'Powered by' : '由'} URL-Stash Vault
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </Container>
    </section>
  );
};

export default HomeNavigationSection;
