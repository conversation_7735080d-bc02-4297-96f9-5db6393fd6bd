import { useState, useEffect, useRef } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAppContext } from "@/context/AppContext";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Compass, TrendingUp, ChevronLeft, ChevronRight, ArrowLeft, Link2, Mail, ListTodo, StickyNote } from "lucide-react";
import TempMailbox from "@/pages/index/TempMailbox";
import TodoMemoSection from "@/components/index/TodoMemoSection";
import AnimatedGradientTitle from '@/components/common/AnimatedGradientTitle';
import UrlShortenerWithDomains from '@/components/url-shortener/UrlShortenerWithDomains';
import AboutSection from "./index/AboutSection";
import CtaSection from "./index/CtaSection";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { fetchPlatforms, fetchNews } from '@/hooks/hot-news/api';
import { NewsItem, NewsPlatform } from '@/hooks/hot-news/types';
import { NavCategory, NavLink, getUserNavigationData } from "@/services/navigationService";
import { getApprovedDomains } from "@/services/domainWhitelistService";
import TodoMemoPreview from "@/components/index/TodoMemoPreview";
import { useHotNews } from '@/hooks/hot-news';
import { fetchFeatures } from '@/components/admin/features/hooks/useFeatureOperations';
import { FeatureConfig } from '@/components/admin/features/types/feature-types';
import { renderIcon } from '@/components/admin/features/utils/iconUtils';
import { useBannerConfig } from '@/hooks/useBannerConfig';

const Index = () => {
  const { language, user, isAuthReady, fetchUser, t } = useAppContext();
  const [approvedDomains, setApprovedDomains] = useState<string[]>([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // Banner and features state
  const [bannerFeatures, setBannerFeatures] = useState<FeatureConfig[]>([]);
  const [loadingFeatures, setLoadingFeatures] = useState(true);
  const { bannerConfig } = useBannerConfig();
  
  // 用户导航数据状态
  const [userCategories, setUserCategories] = useState<NavCategory[]>([]);
  const [userLinks, setUserLinks] = useState<NavLink[]>([]);
  const [isLoadingNavigation, setIsLoadingNavigation] = useState(false);
  
  // 热榜数据状态
  const [platforms, setPlatforms] = useState<NewsPlatform[]>([]);
  const [newsMap, setNewsMap] = useState<Record<string, NewsItem[]>>({});
  const [isLoadingHotNews, setIsLoadingHotNews] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  // 热榜自动滚动计时器和状态
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const scrollTimersRef = useRef<Record<string, NodeJS.Timeout>>({});
  const newsContainerRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // 监听认证状态变化
  useEffect(() => {
    setIsAuthenticated(isAuthReady && user !== null);
    
    // 当用户登录时获取导航数据
    if (isAuthReady && user) {
      fetchUserNavigation(user.id);
    }
    
    // 当用户登出时清除导航数据
    if (isAuthReady && !user) {
      setUserCategories([]);
      setUserLinks([]);
    }
  }, [isAuthReady, user]);

  // Fetch banner features
  useEffect(() => {
    const fetchBannerFeatures = async () => {
      try {
        setLoadingFeatures(true);
        const features = await fetchFeatures();
        setBannerFeatures(features.filter(f => f.visible).sort((a, b) => a.order - b.order));
      } catch (error) {
        console.error('Error fetching banner features:', error);
        // Use fallback features if fetch fails
        setBannerFeatures([]);
      } finally {
        setLoadingFeatures(false);
      }
    };
    
    fetchBannerFeatures();
  }, []);

  useEffect(() => {
    // 获取热榜数据
    const fetchHotNewsData = async () => {
      setIsLoadingHotNews(true);
      try {
        const allPlatforms = await fetchPlatforms();
        if (allPlatforms && allPlatforms.length > 0) {
          setPlatforms(allPlatforms.slice(0, 6)); // 只显示前6个平台
          
          // 为每个平台获取热榜数据
          const newsData: Record<string, NewsItem[]> = {};
          
          const results = await Promise.allSettled(
            allPlatforms.slice(0, 6).map(async (platform) => {
              try {
                const data = await fetchNews(platform.path);
                return { platform, data };
              } catch (err) {
                console.error(`获取'${platform.chineseName}'的新闻失败:`, err);
                return { platform, data: [] };
              }
            })
          );
          
          results.forEach(result => {
            if (result.status === 'fulfilled') {
              const { platform, data } = result.value;
              newsData[platform.path] = data;
            }
          });
          
          setNewsMap(newsData);
        }
      } catch (error) {
        console.error('获取热榜数据失败:', error);
      } finally {
        setIsLoadingHotNews(false);
      }
    };
    
    // 获取批准的域名
    const fetchApprovedDomainsData = async () => {
      try {
        const domains = await getApprovedDomains();
        setApprovedDomains(domains);
      } catch (error) {
        console.error('Error fetching approved domains:', error);
        setApprovedDomains([]);
      }
    };
    
    fetchApprovedDomainsData();
    fetchHotNewsData();
  }, []);

  // 获取用户个人导航数据
  const fetchUserNavigation = async (userId: string | number) => {
    setIsLoadingNavigation(true);
    try {
      const navData = await getUserNavigationData();
      setUserCategories(navData.categories);
      setUserLinks(navData.links);
    } catch (error) {
      console.error('获取用户导航数据失败:', error);
      setUserCategories([]);
      setUserLinks([]);
    } finally {
      setIsLoadingNavigation(false);
    }
  };
  
  // 格式化时间戳
  const formatTimestamp = (timestamp: string): string => {
    if (!timestamp) return '';
    
    try {
      const date = new Date(parseInt(timestamp));
      return date.toLocaleString(language === 'en' ? 'en-US' : 'zh-CN', {
        year: 'numeric', 
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (err) {
      console.error('格式化时间戳失败:', err);
      return '';
    }
  };
  
  // 渲染新闻项
  const renderNewsItem = (item: NewsItem, index: number) => {
    // 根据排名显示不同样式的标记
    const getRankBadge = () => {
      if (index === 0) {
        return <span className="flex items-center justify-center w-5 h-5 mr-2 text-amber-500"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5"><path fillRule="evenodd" d="M12.963 2.286a.75.75 0 00-1.071-.136 9.742 9.742 0 00-3.539 6.177A7.547 7.547 0 016.648 6.61a.75.75 0 00-1.152-.082A9 9 0 1015.68 4.534a7.46 7.46 0 01-2.717-2.248zM15.75 14.25a3.75 3.75 0 11-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 011.925-3.545 3.75 3.75 0 013.255 3.717z" clipRule="evenodd" /></svg></span>;
      } else if (index === 1) {
        return <span className="flex items-center justify-center w-5 h-5 mr-2 text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5"><path fillRule="evenodd" d="M12.963 2.286a.75.75 0 00-1.071-.136 9.742 9.742 0 00-3.539 6.177A7.547 7.547 0 016.648 6.61a.75.75 0 00-1.152-.082A9 9 0 1015.68 4.534a7.46 7.46 0 01-2.717-2.248zM15.75 14.25a3.75 3.75 0 11-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 711.925-3.545 3.75 3.75 0 013.255 3.717z" clipRule="evenodd" /></svg></span>;
      } else if (index === 2) {
        return <span className="flex items-center justify-center w-5 h-5 mr-2 text-amber-700"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5"><path fillRule="evenodd" d="M12.963 2.286a.75.75 0 00-1.071-.136 9.742 9.742 0 00-3.539 6.177A7.547 7.547 0 716.648 6.61a.75.75 0 00-1.152-.082A9 9 0 1015.68 4.534a7.46 7.46 0 01-2.717-2.248zM15.75 14.25a3.75 3.75 0 11-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 011.925-3.545 3.75 3.75 0 013.255 3.717z" clipRule="evenodd" /></svg></span>;
      } else {
        return <span className="flex items-center justify-center w-5 h-5 mr-2 text-muted-foreground font-bold">{index + 1}</span>;
      }
    };

    return (
      <div 
        key={`${item.url}-${index}`}
        className="p-3 border-b last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
      >
        <a 
          href={item.url} 
          target="_blank" 
          rel="noopener noreferrer"
          className="block"
        >
          <div className="flex gap-2">
            {getRankBadge()}
            {item.cover && (
              <div className="flex-shrink-0 w-12 h-12 overflow-hidden rounded">
                <img 
                  src={item.cover} 
                  alt={item.title} 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // 使用内联SVG替代外部占位图
                    const target = e.currentTarget;
                    target.style.display = 'none';
                    
                    const svgContainer = document.createElement('div');
                    svgContainer.className = 'w-full h-full flex items-center justify-center bg-muted';
                    svgContainer.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground"><line x1="2" y1="2" x2="22" y2="22"></line><path d="M10.41 10.41a2 2 0 1 1-2.83-2.83"></path><line x1="13.5" y1="13.5" x2="6" y2="21"></line><path d="M8.21 8.21a6 6 0 0 0 7.58 7.58"></path><path d="M21 12c0 2.34-.84 4.5-2.25 6.18"></path><path d="M12 3c5.523 0 10 4.477 10 10"></path><path d="M3 12c0-5.523 4.477-10 10-10"></path><path d="M14.121 3.051c-.8.092-1.695.144-2.605.144-3.921 0-7.35-.865-9.365-2.195M11.879 20.95c.8-.092 1.695-.144 2.605-.144 3.921 0 7.35.865 9.365 2.195"></path></svg>`;
                    
                    target.parentNode?.appendChild(svgContainer);
                  }}
                />
              </div>
            )}
            <div className="flex-1">
              <h3 className="text-sm font-medium line-clamp-2">{item.title}</h3>
              {item.timestamp && (
                <div className="mt-1 text-xs text-muted-foreground">
                  {formatTimestamp(item.timestamp) || ''}
                </div>
              )}
              {item.hot && (
                <div className="mt-1 text-xs text-rose-500 flex items-center">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  热度: {item.hot}
                </div>
              )}
            </div>
          </div>
        </a>
      </div>
    );
  };
  
  // 热榜模块的左右滚动
  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const { scrollLeft, clientWidth } = scrollRef.current;
      const scrollTo = direction === 'left' 
        ? scrollLeft - clientWidth * 0.8 
        : scrollLeft + clientWidth * 0.8;
      
      scrollRef.current.scrollTo({
        left: scrollTo,
        behavior: 'smooth'
      });
    }
  };
  
  // 自动滚动热榜新闻
  useEffect(() => {
    // 清除所有现有的滚动计时器
    const clearAllScrollTimers = () => {
      Object.values(scrollTimersRef.current).forEach(timer => clearTimeout(timer));
      scrollTimersRef.current = {};
    };
    
    // 如果热榜已加载并且自动滚动已启用，为每个平台设置滚动
    if (!isLoadingHotNews && autoScrollEnabled && platforms.length > 0) {
      platforms.forEach(platform => {
        const news = newsMap[platform.path] || [];
        if (news.length > 5) { // 只有当新闻数量超过显示数量时才需要滚动
          const scrollNewsForPlatform = () => {
            const container = newsContainerRefs.current[platform.path];
            if (container) {
              const scrollHeight = container.scrollHeight;
              const clientHeight = container.clientHeight;
              const scrollTop = container.scrollTop;
              
              // 如果已经滚动到底部，重置到顶部
              if (scrollTop + clientHeight >= scrollHeight - 10) {
                container.scrollTo({
                  top: 0,
                  behavior: 'smooth'
                });
              } else {
                // 否则继续向下滚动一个新闻项的高度
                container.scrollTo({
                  top: scrollTop + 80, // 大约是一个新闻项的高度
                  behavior: 'smooth'
                });
              }
              
              // 设置下一次滚动
              scrollTimersRef.current[platform.path] = setTimeout(scrollNewsForPlatform, 4000);
            }
          };
          
          // 初始延迟，每个平台错开开始时间以避免同时滚动
          const initialDelay = 2000 + (platforms.indexOf(platform) * 1000);
          scrollTimersRef.current[platform.path] = setTimeout(scrollNewsForPlatform, initialDelay);
        }
      });
    }
    
    return clearAllScrollTimers;
  }, [isLoadingHotNews, autoScrollEnabled, platforms, newsMap]);
  
  // 切换自动滚动
  const toggleAutoScroll = () => {
    setAutoScrollEnabled(prev => !prev);
  };

  // Convert FeatureConfig to FeatureCard format for AnimatedGradientTitle
  const convertToFeatureCards = (features: FeatureConfig[]) => {
    return features.map(feature => ({
      id: feature.id,
      icon: renderIcon(feature.icon, 32),
      title: feature.title[language === 'en' ? 'en' : 'zh'],
      subtitle: feature.description[language === 'en' ? 'en' : 'zh'],
      gradient: feature.iconBgGradient,
      badge: feature.badge ? feature.badge[language === 'en' ? 'en' : 'zh'] : undefined
    }));
  };

  return (
    <div className="container px-4 py-12 md:py-16">
      <div className="mx-auto max-w-5xl text-center">
        <AnimatedGradientTitle 
          title={{
            main: language === 'en' ? 'All-in-One Productivity Platform' : '一站式生产力平台',
            subtitle: language === 'en' 
              ? 'Streamline your workflow with powerful tools designed for modern productivity'
              : '使用为现代生产力设计的强大工具简化您的工作流程'
          }}
          features={convertToFeatureCards(bannerFeatures)}
        />
        <p className="mt-6 text-lg text-muted-foreground">
          {language === 'en' 
            ? 'Your all-in-one productivity hub: shorten URLs, manage temporary emails, explore curated resources, and stay updated with trending topics.'
            : '您的一站式生产力中心：缩短网址、管理临时邮箱、探索精选资源、了解热门话题。'}
        </p>
        
        <div className="mt-8 flex flex-col gap-8">
          {/* Render components based on banner features configuration */}
          {bannerFeatures.map((feature, index) => {
            // Only show visible features that correspond to actual components
            if (!feature.visible) return null;
            
            switch (feature.id) {
              case 'url-shortener':
                return (
                  <div key={feature.id}>
                    <UrlShortenerWithDomains approvedDomains={approvedDomains} />
                  </div>
                );
              
              case 'email':
                return (
                  <div key={feature.id}>
                    <TempMailbox />
                  </div>
                );
              
              case 'navigation':
                return (
                  <div key={feature.id}>
                    {/* 导航特性描述 - 根据用户登录状态显示不同内容 */}
                    {isAuthenticated ? (
                      <Card className="border">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-xl flex items-center">
                              <Compass className="mr-2 h-5 w-5 text-primary" />
                              {language === 'en' ? 'My Navigation' : '我的导航'}
                            </CardTitle>
                            <Button asChild variant="outline" size="sm">
                              <Link to="/dashboard">
                                {language === 'en' ? 'Manage' : '管理'}
                              </Link>
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="pb-0">
                          {isLoadingNavigation ? (
                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                              {Array.from({ length: 8 }).map((_, index) => (
                                <div key={index} className="flex flex-col items-center">
                                  <Skeleton className="h-12 w-12 rounded-full" />
                                  <Skeleton className="h-3 w-20 mt-2" />
                                </div>
                              ))}
                            </div>
                          ) : userLinks.length === 0 ? (
                            <div className="text-center py-6">
                              <p className="text-muted-foreground">
                                {language === 'en' 
                                  ? 'No navigation links found. Add some in your dashboard!' 
                                  : '没有发现导航链接。在控制面板中添加一些！'}
                              </p>
                              <Button asChild variant="outline" className="mt-4">
                                <Link to="/dashboard">
                                  {language === 'en' ? 'Add Links' : '添加链接'}
                                  <ArrowRight className="ml-2 h-4 w-4" />
                                </Link>
                              </Button>
                            </div>
                          ) : (
                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                              {userLinks.slice(0, 10).map((link) => (
                                <a 
                                  key={link.id}
                                  href={link.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex flex-col items-center p-3 rounded-lg hover:bg-muted/50 transition-colors"
                                >
                                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                                    {link.icon ? (
                                      <img 
                                        src={link.icon} 
                                        alt={link.title} 
                                        className="w-6 h-6"
                                        onError={(e) => {
                                          e.currentTarget.src = 'https://via.placeholder.com/24';
                                        }}
                                      />
                                    ) : (
                                      <Compass className="h-6 w-6 text-primary" />
                                    )}
                                  </div>
                                  <span className="text-sm font-medium text-center line-clamp-1">{link.title}</span>
                                </a>
                              ))}
                            </div>
                          )}
                        </CardContent>
                        {userLinks.length > 0 && (
                          <CardFooter className="pt-2 pb-4 flex justify-center">
                            <Button asChild variant="link" size="sm">
                              <Link to="/navigation">
                                {language === 'en' ? 'View All Navigation' : '查看全部导航'}
                                <ArrowRight className="ml-1 h-4 w-4" />
                              </Link>
                            </Button>
                          </CardFooter>
                        )}
                      </Card>
                    ) : (
                      <div className="bg-muted/30 p-6 rounded-lg border text-center">
                        <div className="flex justify-center mb-4">
                          <div className="p-3 bg-primary/10 rounded-full">
                            <Compass className="h-8 w-8 text-primary" />
                          </div>
                        </div>
                        <h2 className="text-2xl font-bold mb-4">
                          {language === 'en' ? 'Explore Our Navigation Directory' : '探索我们的导航目录'}
                        </h2>
                        <p className="text-muted-foreground mb-4 max-w-3xl mx-auto">
                          {language === 'en' 
                            ? 'Discover a curated collection of useful links and resources. Our navigation directory helps you find valuable websites, tools, and resources across various categories to boost your productivity and learning.'
                            : '发现精心策划的有用链接和资源集合。我们的导航目录帮助您在不同类别中找到有价值的网站、工具和资源，提升您的生产力和学习效率。'}
                        </p>
                        <Button asChild variant="outline" className="mt-4">
                          <Link to="/navigation" className="group">
                            {language === 'en' ? 'View Navigation Directory' : '查看导航目录'}
                            <Compass className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                );
              
              case 'hot-news':
                return (
                  <div key={feature.id}>
                    {/* 热榜特性描述 - 根据用户登录状态显示不同内容 */}
                    {isAuthenticated ? (
                      <Card className="border relative">
                        <Badge className="absolute top-4 right-4 bg-accent1-500 text-white">
                          {language === 'en' ? 'HOT' : '热门'}
                        </Badge>
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-xl flex items-center">
                              <TrendingUp className="mr-2 h-5 w-5 text-primary" />
                              {language === 'en' ? 'Today\'s Hot Topics' : '今日热榜'}
                            </CardTitle>
                            <div className="flex gap-2">
                              <Button 
                                variant={autoScrollEnabled ? "default" : "outline"} 
                                size="icon" 
                                onClick={toggleAutoScroll}
                                className="h-8 w-8 rounded-full"
                                title={autoScrollEnabled ? (language === 'en' ? 'Auto-scroll On' : '自动滚动已开启') : (language === 'en' ? 'Auto-scroll Off' : '自动滚动已关闭')}
                      >
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`${autoScrollEnabled ? 'animate-pulse' : ''}`}>
                                  <path d="M17 17H8a5 5 0 0 1-5-5V5"></path>
                                  <path d="M12 17v5"></path>
                                  <path d="M16 17l3.536 3.536a2 2 0 0 0 2.828 0L22.728 20.2a2 2 0 0 0 0-2.828L19.192 13.8"></path>
                                  <path d="M19.192 17l-4.95-4.95"></path>
                                </svg>
                              </Button>
                              <Button 
                                variant="outline" 
                                size="icon" 
                                onClick={() => scroll('left')}
                                className="h-8 w-8 rounded-full"
                              >
                                <ChevronLeft className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="outline" 
                                size="icon" 
                                onClick={() => scroll('right')}
                                className="h-8 w-8 rounded-full"
                              >
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pb-0 overflow-hidden">
                          <div className="relative">
                            <div 
                              ref={scrollRef}
                              className="flex overflow-x-auto pb-4 gap-4 snap-x"
                              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                            >
                              {isLoadingHotNews ? (
                                // 加载骨架屏
                                Array.from({ length: 3 }).map((_, index) => (
                                  <div 
                                    key={index} 
                                    className="flex-shrink-0 w-[300px] snap-start"
                                  >
                                    <div className="space-y-2">
                                      <Skeleton className="h-6 w-32" />
                                      <div className="space-y-3">
                                        {Array.from({ length: 5 }).map((_, i) => (
                                          <div key={i} className="flex gap-2 animate-pulse">
                                            <Skeleton className="w-5 h-5 rounded-full" />
                                            <Skeleton className="w-12 h-12 rounded" />
                                            <div className="flex-1 space-y-1">
                                              <Skeleton className="h-3 w-full" />
                                              <Skeleton className="h-3 w-3/4" />
                                              <Skeleton className="h-2 w-1/4" />
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                ))
                              ) : (
                                // 热榜平台列表
                                platforms.map((platform, platformIndex) => {
                                  const news = newsMap[platform.path] || [];
                                  return (
                                    <div 
                                      key={platform.path} 
                                      className={`flex-shrink-0 w-[300px] snap-start ${platformIndex !== 0 ? 'border-l border-dashed pl-4' : ''}`}
                                    >
                                      <div className="border-b pb-2 mb-2 flex justify-between items-center">
                                        <h3 className="font-semibold">
                                          {language === 'en' ? platform.name : platform.chineseName}
                                        </h3>
                                        <Badge variant="outline" className="text-xs">
                                          {news.length}
                                        </Badge>
                                      </div>
                                      <div 
                                        ref={el => newsContainerRefs.current[platform.path] = el}
                                        className="h-[320px] overflow-y-hidden"
                                      >
                                        {news.length === 0 ? (
                                          <div className="py-4 text-center text-muted-foreground text-sm">
                                            {language === 'en' ? 'No news available' : '暂无新闻'}
                                          </div>
                                        ) : (
                                          <div className="divide-y divide-dashed">
                                            {news.map((item, index) => renderNewsItem(item, index))}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  );
                                })
                              )}
                            </div>
                          </div>
                        </CardContent>
                        <CardFooter className="pt-2 pb-4 flex justify-center">
                          <Button asChild variant="link" size="sm">
                            <Link to="/hot-news">
                              {language === 'en' ? 'View All Hot Topics' : '查看全部热榜'}
                              <ArrowRight className="ml-1 h-4 w-4" />
                            </Link>
                          </Button>
                        </CardFooter>
                      </Card>
                    ) : (
                      <div className="bg-muted/30 p-6 rounded-lg border text-center relative">
                        <Badge className="absolute top-4 right-4 bg-accent1-500 text-white">
                          {language === 'en' ? 'NEW' : '新功能'}
                        </Badge>
                        <div className="flex justify-center mb-4">
                          <div className="p-3 bg-primary/10 rounded-full">
                            <TrendingUp className="h-8 w-8 text-primary" />
                          </div>
                        </div>
                        <h2 className="text-2xl font-bold mb-4">
                          {language === 'en' ? "Today's Hot Lists" : '今日热榜'}
                        </h2>
                        <p className="text-muted-foreground mb-4 max-w-3xl mx-auto">
                          {language === 'en' 
                            ? 'Stay updated with trending topics from various platforms all in one place. Our hot lists aggregate popular content from major websites like Weibo, Zhihu, Douyin, Baidu, and Bilibili, saving you time and keeping you informed.'
                            : '在一处了解各大平台的热门话题。我们的热榜聚合了微博、知乎、抖音、百度和B站等主要网站的热门内容，为您节省时间，让您随时了解最新热点。'}
                        </p>
                        <Button asChild variant="outline" className="mt-4">
                          <Link to="/hot-news" className="group">
                            {language === 'en' ? 'View Hot Lists' : '查看热榜'}
                            <TrendingUp className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                );
              
              case 'todo-memo':
                return (
                  <div key={feature.id}>
                    {/* 待办和备忘录 - 根据用户登录状态显示不同内容 */}
                    {isAuthenticated && user ? (
                      <TodoMemoPreview userId={user.id} />
                    ) : (
                      <TodoMemoSection />
                    )}
                  </div>
                );
              
              default:
                // For custom features that don't correspond to specific components
                return null;
            }
          })}
          
          {/* Show components for unconfigured features or when no features are configured */}
          {(!bannerFeatures.length || loadingFeatures) && (
            <>
              <UrlShortenerWithDomains approvedDomains={approvedDomains} />
              <TempMailbox />
              {/* Navigation and other sections... */}
            </>
          )}
          
          {/* About Section - 仅在用户未登录时显示 */}
          {!isAuthenticated && (
            <AboutSection />
          )}

          {/* CTA Section - 仅在用户未登录时显示 */}
          {!isAuthenticated && (
            <CtaSection />
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
