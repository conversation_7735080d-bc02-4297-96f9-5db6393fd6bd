import React, { useState, useEffect, useRef } from 'react';
import { useAppContext } from '@/context/AppContext';
import { OnlineToolsProvider } from './online-tools/OnlineToolsContext';
import OnlineToolsHeader from './online-tools/OnlineToolsHeader';
import UnifiedHeader from '@/components/common/UnifiedHeader';
import PageWrapper from '@/components/layout/PageWrapper';
import {
  Sidebar,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarProvider,
  SidebarFooter,
  SidebarTrigger,
} from "@/components/ui/sidebar";

// Import tool components
import JwtDecoder from './online-tools/tools/JwtDecoder';
import Encryption from './online-tools/tools/Encryption';
import UuidGenerator from './online-tools/tools/UuidGenerator';
import PasswordGenerator from './online-tools/tools/PasswordGenerator';
import TextUtils from './online-tools/tools/TextUtils';
import JsonFormatter from './online-tools/tools/JsonFormatter';
import NameGenerator from './online-tools/tools/NameGenerator';
import ColorConverter from './online-tools/tools/ColorConverter';
import Base64Converter from './online-tools/tools/Base64Converter';
import UrlEncoder from './online-tools/tools/UrlEncoder';
import HashGenerator from './online-tools/tools/HashGenerator';

// Import tool icons
import {
  Key,
  Lock,
  FileKey,
  KeyRound,
  Type,
  Code,
  User,
  Palette,
  FileCode,
  Link,
  Hash,
  ChevronLeft,
  ChevronRight,
  Wrench,
  X,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";

const OnlineTools = () => {
  const { language } = useAppContext();
  const [activeTab, setActiveTab] = useState('jwt');
  const isMobile = useIsMobile();
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [showScroll, setShowScroll] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // 添加弹窗状态
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState(false); // 是否以弹窗模式显示工具

  // Define tools with their icons and labels
  const tools = [
    { id: 'jwt', label: language === 'zh' ? 'JWT解码' : 'JWT Decoder', icon: Key, component: JwtDecoder },
    { id: 'encryption', label: language === 'zh' ? '加密/解密' : 'Encryption', icon: Lock, component: Encryption },
    { id: 'uuid', label: language === 'zh' ? 'UUID生成' : 'UUID Generator', icon: FileKey, component: UuidGenerator },
    { id: 'password', label: language === 'zh' ? '密码生成' : 'Password Generator', icon: KeyRound, component: PasswordGenerator },
    { id: 'text', label: language === 'zh' ? '文本工具' : 'Text Utils', icon: Type, component: TextUtils },
    { id: 'json', label: language === 'zh' ? 'JSON格式化' : 'JSON Formatter', icon: Code, component: JsonFormatter },
    { id: 'name', label: language === 'zh' ? '姓名生成' : 'Name Generator', icon: User, component: NameGenerator },
    { id: 'color', label: language === 'zh' ? '颜色转换' : 'Color Converter', icon: Palette, component: ColorConverter },
    { id: 'base64', label: language === 'zh' ? 'Base64转换' : 'Base64 Converter', icon: FileCode, component: Base64Converter },
    { id: 'url', label: language === 'zh' ? 'URL编码' : 'URL Encoder', icon: Link, component: UrlEncoder },
    { id: 'hash', label: language === 'zh' ? '哈希生成' : 'Hash Generator', icon: Hash, component: HashGenerator },
  ];

  // 切换工具的处理函数
  const handleToolSelect = (toolId: string) => {
    setActiveTab(toolId);
    if (isMobile) setSidebarOpen(false);
  };

  // 切换弹窗模式
  const toggleDialogMode = () => {
    if (dialogMode) {
      // 从弹窗模式切换回正常模式
      setDialogMode(false);
      setIsDialogOpen(false);
      // 保持当前选中的工具
      // 恢复正常滚动
      document.body.style.overflow = 'auto';
    } else {
      // 从正常模式切换到弹窗模式
      setDialogMode(true);
      setIsDialogOpen(true);
      // 防止滚动穿透
      document.body.style.overflow = 'hidden';
    }

    // 存储用户偏好
    try {
      localStorage.setItem('online-tools-dialog-mode', dialogMode ? 'false' : 'true');
    } catch (e) {
      // 忽略存储错误
    }
  };

  // 处理弹窗关闭
  const handleDialogClose = (open: boolean) => {
    setIsDialogOpen(open);
    // 如果用户关闭弹窗，但处于弹窗模式，则切换回普通模式
    if (!open && dialogMode) {
      setDialogMode(false);
      // 恢复正常滚动
      document.body.style.overflow = 'auto';
    }
  };

  // 初始化时检查用户偏好的显示模式
  useEffect(() => {
    try {
      const savedMode = localStorage.getItem('online-tools-dialog-mode');
      if (savedMode === 'true' && !isMobile) {
        // 仅在非移动设备上默认使用弹窗模式
        setDialogMode(true);
      }
    } catch (e) {
      // 忽略读取错误
    }
  }, []);

  // Find the current active tool component
  const ActiveComponent = tools.find(tool => tool.id === activeTab)?.component || tools[0].component;
  const activeToolLabel = tools.find(tool => tool.id === activeTab)?.label || '';

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // 检测内容区域是否需要滚动条
  useEffect(() => {
    const checkOverflow = () => {
      if (contentRef.current) {
        const { scrollHeight, clientHeight } = contentRef.current;
        setShowScroll(scrollHeight > clientHeight);
      }
    };

    // 初始检查
    checkOverflow();

    // 窗口大小改变时重新检查
    window.addEventListener('resize', checkOverflow);

    // 清理函数
    return () => window.removeEventListener('resize', checkOverflow);
  }, [activeTab]);

  const headerContent = (
    <UnifiedHeader
      title={language === 'zh' ? '在线工具箱' : 'Online Tools'}
      description={language === 'zh' ? '实用的在线工具集合' : 'Useful online utilities'}
      icon={Wrench}
      variant="gradient"
      gradientFrom="from-blue-500"
      gradientTo="to-purple-600"
      layout="default"
    >
      <OnlineToolsHeader />
    </UnifiedHeader>
  );

  return (
    <OnlineToolsProvider>
      <PageWrapper headerContent={headerContent}>
        {/* Reduced top margin */}
        <div className="flex-1 container mx-auto mt-1 mb-1 flex">
          <SidebarProvider defaultOpen={!isMobile}>
            <div className="flex h-full w-full rounded-lg border overflow-hidden shadow-sm bg-card">
              {/* 左侧菜单 */}
              <div className={`border-r bg-card transition-all duration-300 ${sidebarOpen ? 'w-64' : 'w-0 md:w-14'} flex flex-col`}>
                <div className={`p-3 flex items-center justify-between border-b ${sidebarOpen ? 'opacity-100' : 'opacity-0 md:opacity-100'}`}>
                  <span className={`font-medium transition-opacity duration-300 ${sidebarOpen ? 'opacity-100' : 'opacity-0 md:hidden'}`}>
                    {language === 'zh' ? '工具列表' : 'Tools List'}
                  </span>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={toggleSidebar}
                    className="md:hidden"
                  >
                    {sidebarOpen ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </Button>
                </div>

                <ScrollArea className="flex-1">
                  <div className="px-2 py-2">
                    {tools.map(tool => (
                      <Button
                        key={tool.id}
                        variant={activeTab === tool.id ? "secondary" : "ghost"}
                        onClick={() => {
                          handleToolSelect(tool.id);
                          if (dialogMode) {
                            setIsDialogOpen(true);
                          }
                        }}
                        className={`w-full justify-start my-1 ${!sidebarOpen && 'md:justify-center'}`}
                        size="sm"
                      >
                        <tool.icon className={`${sidebarOpen ? 'mr-2' : ''} h-5 w-5 transition-colors ${
                          activeTab === tool.id ? 'text-primary' : 'group-hover:text-primary'
                        }`} />
                        <span className={`transition-all duration-300 truncate ${
                          sidebarOpen ? 'opacity-100' : 'opacity-0 md:hidden w-0'
                        } ${activeTab === tool.id ? 'font-medium' : ''}`}>
                          {tool.label}
                        </span>
                      </Button>
                    ))}
                  </div>
                </ScrollArea>

                <div className="p-3 border-t flex justify-center">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={toggleSidebar}
                    className="hidden md:flex"
                  >
                    {sidebarOpen ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* 右侧功能区 - 根据dialogMode显示或隐藏 */}
              {!dialogMode && (
                <div className="flex-1 flex flex-col overflow-hidden min-w-0">
                  <div className="p-3 border-b flex items-center justify-between bg-muted/10">
                    <div className="flex items-center">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={toggleSidebar}
                        className="mr-2 md:hidden"
                      >
                        {sidebarOpen ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                      </Button>
                      <h2 className="font-medium flex items-center truncate">
                        <Wrench className="mr-2 h-5 w-5" />
                        {activeToolLabel}
                      </h2>
                    </div>

                    {/* 添加弹窗模式切换按钮 */}
                    <div className="flex items-center">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleDialogMode}
                        title={language === 'zh' ? '弹窗模式' : 'Dialog Mode'}
                      >
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div
                    ref={contentRef}
                    className="flex-1 p-4 overflow-auto"
                  >
                    <ActiveComponent />
                  </div>
                </div>
              )}
            </div>
          </SidebarProvider>
        </div>

        {/* 弹窗模式 */}
        <Dialog open={isDialogOpen && dialogMode} onOpenChange={handleDialogClose}>
          <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-hidden flex flex-col">
            <DialogHeader className="flex items-center justify-between flex-row border-b pb-2">
              <DialogTitle className="flex items-center">
                {tools.find(tool => tool.id === activeTab)?.icon &&
                  React.createElement(tools.find(tool => tool.id === activeTab)?.icon as React.ElementType, { className: "mr-2 h-5 w-5 text-primary" })}
                {activeToolLabel}
              </DialogTitle>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleDialogMode}
                  title={language === 'zh' ? '常规模式' : 'Normal Mode'}
                >
                  <Minimize2 className="h-4 w-4" />
                </Button>
              </div>
            </DialogHeader>

            {/* 弹窗中的工具选择器 */}
            <div className="border-b p-2 overflow-x-auto">
              <div className="flex space-x-1">
                {tools.map(tool => (
                  <Button
                    key={tool.id}
                    variant={activeTab === tool.id ? "secondary" : "ghost"}
                    size="sm"
                    onClick={() => handleToolSelect(tool.id)}
                    className="flex items-center whitespace-nowrap"
                  >
                    {React.createElement(tool.icon, {
                      className: `h-4 w-4 ${activeTab === tool.id ? 'text-primary mr-1' : 'mr-1'}`
                    })}
                    <span className="text-xs truncate">{tool.label}</span>
                  </Button>
                ))}
              </div>
            </div>

            <ScrollArea className="flex-1 p-4 overflow-auto">
              <ActiveComponent />
            </ScrollArea>
          </DialogContent>
        </Dialog>

        {/* 底部区域 */}
        <div className="p-4 border-t bg-card">
          <div className="container mx-auto">
            <div className="text-sm text-muted-foreground text-center">
              {language === 'zh'
                ? '所有工具均在本地处理，不会将您的数据发送到服务器'
                : 'All tools process data locally without sending your information to any server'}
            </div>
          </div>
        </div>

        {/* 悬浮弹窗模式按钮 - 仅在手机视图和常规模式下显示 */}
        {!dialogMode && isMobile && (
          <div className="fixed bottom-20 right-4 z-50">
            <Button
              size="icon"
              className="rounded-full h-12 w-12 shadow-lg bg-primary hover:bg-primary/90 transition-all"
              onClick={toggleDialogMode}
              title={language === 'zh' ? '弹窗模式' : 'Dialog Mode'}
            >
              <Maximize2 className="h-6 w-6" />
            </Button>
                      </div>
          )}
      </PageWrapper>
    </OnlineToolsProvider>
  );
};

export default OnlineTools;
