import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, LogIn, Home, UserPlus } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { AuthModal } from '@/components/auth/AuthModal';

interface ForbiddenProps {
  onAuthSuccess?: () => void;
}

const Forbidden: React.FC<ForbiddenProps> = ({ onAuthSuccess }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { language, user, isAuthReady } = useAppContext();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  const handleLogin = () => {
    setAuthMode('login');
    setIsAuthModalOpen(true);
  };

  const handleRegister = () => {
    setAuthMode('register');
    setIsAuthModalOpen(true);
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const handleAuthModalClose = () => {
    setIsAuthModalOpen(false);
  };

  // 监听认证状态变化，登录成功后自动跳转或刷新页面
  useEffect(() => {
    if (isAuthReady && user) {
      console.log('登录成功，准备处理页面跳转或刷新');

      // 延迟一下再处理，让用户看到登录成功的反馈
      setTimeout(() => {
        if (onAuthSuccess) {
          // 如果有 onAuthSuccess 回调，说明是在受保护页面中，直接刷新页面
          onAuthSuccess();
        } else {
          // 否则进行正常的页面跳转逻辑
          let from = location.state?.from?.pathname;

          if (!from) {
            // 如果没有 from 信息，使用当前路径
            const currentPath = location.pathname;

            // 如果当前路径是 /forbidden，说明是通过路由直接访问的，跳转到 dashboard
            // 否则说明是通过 ProtectedRoute 渲染的，当前路径就是用户想要访问的页面
            from = currentPath === '/forbidden' ? '/dashboard' : currentPath;
          }

          navigate(from, { replace: true });
        }
      }, 500);
    }
  }, [isAuthReady, user, navigate, location, onAuthSuccess]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
            <Shield className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            {language === 'en' ? 'Access Forbidden' : '访问被禁止'}
          </CardTitle>
          <CardDescription className="text-gray-600">
            {language === 'en'
              ? 'You need to be logged in to access this page.'
              : '您需要登录才能访问此页面。'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center text-sm text-gray-500">
            <p className="mb-4">
              {language === 'en'
                ? 'This page requires authentication. Please log in to continue.'
                : '此页面需要身份验证。请登录以继续。'
              }
            </p>
          </div>

          <div className="space-y-3">
            <Button
              onClick={handleLogin}
              className="w-full"
              size="lg"
            >
              <LogIn className="h-4 w-4 mr-2" />
              {language === 'en' ? 'Login' : '登录'}
            </Button>

            <Button
              onClick={handleGoHome}
              variant="outline"
              className="w-full"
              size="lg"
            >
              <Home className="h-4 w-4 mr-2" />
              {language === 'en' ? 'Go to Home' : '返回首页'}
            </Button>
          </div>

          {/* 注册按钮 */}
          <div className="pt-2">
            <Button
              onClick={handleRegister}
              variant="ghost"
              className="w-full"
              size="sm"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              {language === 'en' ? 'Create Account' : '创建账户'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 认证弹框 */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={handleAuthModalClose}
        defaultMode={authMode}
      />
    </div>
  );
};

export default Forbidden;
