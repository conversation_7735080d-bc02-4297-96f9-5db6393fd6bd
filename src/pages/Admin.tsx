import React, { useEffect, useState } from 'react';
import { Tabs } from '@/components/ui/tabs';
import { useAppContext } from '@/context/AppContext';
import { useToast } from '@/hooks/use-toast';
import { useAdminData } from '@/hooks/useAdminData';
import { getBackendConfig } from '@/config/backend';

import AdminLoader from '@/components/admin/AdminLoader';
import AdminHeader from '@/components/admin/AdminHeader';
import AdminTabsNavigation from '@/components/admin/navigation/AdminTabsNavigation';
import AdminTabsContent from '@/components/admin/content/AdminTabsContent';
import PageWrapper from '@/components/layout/PageWrapper';

const Admin = () => {
  const { toast } = useToast();
  const { users, statistics, isLoading, visitStats, approvedDomains, fetchData } = useAdminData();
  const [pendingDomainsCount, setPendingDomainsCount] = useState(0);
  const [pendingLinksCount, setPendingLinksCount] = useState(0);
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await fetchData();
        setIsDataLoaded(true);
      } catch (error) {
        console.error('Failed to load admin data:', error);
        toast({
          variant: "destructive",
          title: "Error loading data",
          description: "Please try refreshing the page."
        });
      }
    };

    loadInitialData();

    const fetchPendingCounts = async () => {
      try {
        // 使用Go后端获取待审核数量
        const config = getBackendConfig();
        const baseURL = config.goBackend?.baseUrl;
        const token = localStorage.getItem('authToken');

        if (!token) {
          console.error('No auth token found');
          return;
        }

        // 暂时设置为0，等后端实现相应的API接口后再调用
        setPendingDomainsCount(0);
        setPendingLinksCount(0);

        // TODO: 实现后端API调用
        // const domainsResponse = await fetch(`${baseURL}/admin/pending-domains-count`, {
        //   headers: { 'Authorization': `Bearer ${token}` }
        // });
        // const linksResponse = await fetch(`${baseURL}/admin/pending-links-count`, {
        //   headers: { 'Authorization': `Bearer ${token}` }
        // });

      } catch (error) {
        console.error('Error fetching pending counts:', error);
      }
    };

    fetchPendingCounts();
  }, []);

  if (isLoading && !isDataLoaded) {
    return <AdminLoader />;
  }

  const handleRefresh = async () => {
    toast({
      description: "Refreshing data...",
      duration: 2000,
    });
    await fetchData();
  };

  const headerContent = (
    <AdminHeader
      pendingDomainsCount={pendingDomainsCount}
      pendingLinksCount={pendingLinksCount}
      onRefresh={handleRefresh}
    />
  );

  return (
    <PageWrapper headerContent={headerContent}>
      <div className="container pt-1">

      <Tabs defaultValue="stats" className="space-y-2">
        <AdminTabsNavigation
          pendingDomainsCount={pendingDomainsCount}
          pendingLinksCount={pendingLinksCount}
        />

        <AdminTabsContent
          users={users}
          statistics={statistics}
          visitStats={visitStats}
          approvedDomains={approvedDomains}
        />
      </Tabs>
      </div>
    </PageWrapper>
  );
};

export default Admin;
