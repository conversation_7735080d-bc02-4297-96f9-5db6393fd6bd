import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import apiClient from '@/services/api';

const VerifyEmail = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const verifyEmail = async () => {
      const token = searchParams.get('token');
      
      if (!token) {
        setStatus('error');
        setMessage('验证链接无效');
        return;
      }

      try {
        const response = await apiClient.post('/auth/confirm-registration', { token });
        
        if (response.data.success) {
          setStatus('success');
          setMessage('邮箱验证成功！您的账户已创建。');
          
          // 如果返回了token，自动登录
          if (response.data.data?.token) {
            localStorage.setItem('authToken', response.data.data.token);
            localStorage.setItem('auth_token', response.data.data.token);
            
            toast({
              title: '验证成功',
              description: '您已成功登录！',
            });
            
            setTimeout(() => {
              navigate('/');
            }, 2000);
          } else {
            setTimeout(() => {
              navigate('/login');
            }, 3000);
          }
        } else {
          setStatus('error');
          setMessage(response.data.error || '验证失败，请重试');
        }
      } catch (error: unknown) {
        setStatus('error');
        const errorMessage = error instanceof Error ? error.message : '验证失败，链接可能已过期';
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: { error?: string } } };
          setMessage(axiosError.response?.data?.error || errorMessage);
        } else {
          setMessage(errorMessage);
        }
      }
    };

    verifyEmail();
  }, [searchParams, navigate, toast]);

  return (
    <div className="container py-10 flex items-center justify-center min-h-[60vh]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">邮箱验证</CardTitle>
          <CardDescription>
            {status === 'loading' && '正在验证您的邮箱...'}
            {status === 'success' && '验证成功'}
            {status === 'error' && '验证失败'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-center">
            {status === 'loading' && (
              <Loader2 className="h-16 w-16 animate-spin text-primary" />
            )}
            {status === 'success' && (
              <CheckCircle className="h-16 w-16 text-green-500" />
            )}
            {status === 'error' && (
              <XCircle className="h-16 w-16 text-red-500" />
            )}
          </div>
          
          <p className="text-center text-muted-foreground">
            {message}
          </p>
          
          {status === 'error' && (
            <div className="flex justify-center">
              <Button onClick={() => navigate('/register')}>
                返回注册页面
              </Button>
            </div>
          )}
          
          {status === 'success' && (
            <p className="text-center text-sm text-muted-foreground">
              正在跳转...
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default VerifyEmail; 