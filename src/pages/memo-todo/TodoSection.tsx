import React, { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar as CalendarIcon, Plus, Flag } from "lucide-react";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuSeparator, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { zhCN, enUS } from "date-fns/locale";
import { format, parseISO, isValid } from "date-fns"; // Added parseISO, isValid
import { Todo } from "@/hooks/useTodos"; // Import Todo type
import { Card, CardContent } from "@/components/ui/card"; // Import Card components
import { motion, AnimatePresence } from "framer-motion"; // Import motion components
import { Edit2, Trash2 } from "lucide-react"; // Import icons
import { Badge } from "@/components/ui/badge"; // Import Badge component

interface TodoSectionProps {
  language: string;
  newTodo: string;
  setNewTodo: (t: string) => void;
  todoInputRef: React.RefObject<HTMLInputElement>;
  selectedDueDate: Date | undefined;
  setSelectedDueDate: (d: Date | undefined) => void;
  selectedPriority: "low" | "medium" | "high" | undefined;
  setSelectedPriority: (p: "low" | "medium" | "high" | undefined) => void;
  handleAddTodo: (e: React.FormEvent) => void;
  // New props for displaying and interacting with the todo list
  todos: Todo[];
  toggleTodo: (id: string) => void;
  handleOpenTodoEdit: (todo: Todo) => void;
  deleteTodo: (id: string) => void;
  handleViewTodoDetails: (todo: Todo) => void; // To navigate to detail view
  todosLoading: boolean;
}

// Helper function to determine badge variant based on due date (can be moved to utils if used elsewhere)
const getDueDateBadgeVariant = (dueDate: string, completed: boolean): "default" | "destructive" | "outline" | "secondary" => {
  if (completed) return "secondary";
  const date = parseISO(dueDate);
  if (!isValid(date)) return "outline";
  if (format(date, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')) return "default"; // Or some other variant for today
  if (date < new Date()) return "destructive";
  return "outline";
};

// Helper function to determine badge variant for priority (can be moved to utils)
const priorityMap = {
  low: { en: 'Low', zh: '低' },
  medium: { en: 'Medium', zh: '中' },
  high: { en: 'High', zh: '高' },
};
const getPriorityBadgeVariant = (priority: "low" | "medium" | "high"): "default" | "destructive" | "outline" | "secondary" => {
  if (priority === 'high') return 'destructive';
  if (priority === 'medium') return 'default'; // Example: orange-500 might need custom styling or a different variant
  return 'secondary'; // Example: green-500 for low
};

const TodoSection: React.FC<TodoSectionProps> = ({
  language,
  newTodo,
  setNewTodo,
  todoInputRef,
  selectedDueDate,
  setSelectedDueDate,
  selectedPriority,
  setSelectedPriority,
  handleAddTodo,
  // Destructure new props
  todos,
  toggleTodo,
  handleOpenTodoEdit,
  deleteTodo,
  handleViewTodoDetails,
  todosLoading,
}) => {
  // 使用自定义的日历下拉菜单状态
  const [calendarOpen, setCalendarOpen] = useState(false);
  const calendarRef = useRef<HTMLDivElement>(null);

  // 处理点击外部关闭日历
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 切换日历显示
  const toggleCalendar = () => {
    setCalendarOpen(!calendarOpen);
  };

  return (
    <div className="space-y-4">
      <form onSubmit={handleAddTodo} className="flex gap-2">
        <div className="flex-1 flex gap-2">
          <Input
            ref={todoInputRef}
            value={newTodo}
            onChange={(e) => setNewTodo(e.target.value)}
            placeholder={language === "zh" ? "添加新的待办事项..." : "Add a new todo..."}
            className="flex-1"
          />
          <div className="relative" ref={calendarRef}>
            <Button
              type="button"
              variant="outline"
              size="icon"
              className={cn(selectedDueDate && "text-primary")}
              onClick={toggleCalendar}
            >
              <CalendarIcon className="h-4 w-4" />
            </Button>
            {calendarOpen && (
              <div className="absolute right-0 mt-2 z-50 bg-popover rounded-md shadow-md border">
                <div className="p-0">
                  <CalendarComponent
                    mode="single"
                    selected={selectedDueDate}
                    onSelect={(date) => {
                      setSelectedDueDate(date);
                      setCalendarOpen(false);
                    }}
                    initialFocus
                    className="rounded-md"
                    locale={language === "zh" ? zhCN : enUS}
                  />
                  {selectedDueDate && (
                    <div className="p-3 border-t border-border flex justify-between items-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedDueDate(undefined);
                          setCalendarOpen(false);
                        }}
                      >
                        {language === "zh" ? "清除" : "Clear"}
                      </Button>
                      <div className="text-sm">
                        {format(selectedDueDate, "PPP", { locale: language === "zh" ? zhCN : enUS })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                type="button"
                variant="outline"
                size="icon"
                className={cn(
                  selectedPriority === "high" && "text-destructive",
                  selectedPriority === "medium" && "text-orange-500", // Consider custom class for orange
                  selectedPriority === "low" && "text-green-500" // Consider custom class for green
                )}
              >
                <Flag className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuRadioGroup value={selectedPriority} onValueChange={(value) => setSelectedPriority(value as "low" | "medium" | "high" | undefined)}>
                <DropdownMenuRadioItem value="high" className="text-destructive">
                  <Flag className="mr-2 h-4 w-4" />
                  {language === "zh" ? "高优先级" : "High Priority"}
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="medium" className="text-orange-500">
                  <Flag className="mr-2 h-4 w-4" />
                  {language === "zh" ? "中优先级" : "Medium Priority"}
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="low" className="text-green-500">
                  <Flag className="mr-2 h-4 w-4" />
                  {language === "zh" ? "低优先级" : "Low Priority"}
                </DropdownMenuRadioItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setSelectedPriority(undefined)}>
                  <Flag className="mr-2 h-4 w-4 text-muted-foreground" />
                  {language === "zh" ? "清除优先级" : "Clear Priority"}
                </DropdownMenuItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <Button type="submit" disabled={!newTodo.trim()}>
          <Plus className="h-4 w-4 mr-2" />
          {language === "zh" ? "添加" : "Add"}
        </Button>
      </form>

      {/* Todos List moved here from MemoTodo.tsx */}
      <AnimatePresence>
        {todos.map(todo => (
          <motion.div 
            key={todo.id} 
            layout
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-2 cursor-pointer" // Added cursor-pointer
            onClick={() => handleViewTodoDetails(todo)} // Added onClick to view details
          >
            <Card 
              className={cn(
                "hover:shadow-md transition-shadow",
                todo.completed && "bg-muted/50 line-through text-muted-foreground"
              )}
            >
              <CardContent className="p-3 flex items-center justify-between">
                <div className="flex items-center flex-1 min-w-0">
                  <input 
                    type="checkbox" 
                    checked={todo.completed}
                    onChange={(e) => {
                      e.stopPropagation(); // Prevent card click from triggering
                      toggleTodo(todo.id);
                    }}
                    className="mr-3 h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary cursor-pointer"
                  />
                  <span 
                    className={cn(
                      "flex-1 truncate", 
                      todo.completed && "text-muted-foreground"
                    )}
                  >
                    {todo.title}
                  </span>
                </div>
                <div className="flex items-center space-x-1 sm:space-x-2 ml-2">
                  {todo.due_date && isValid(parseISO(todo.due_date)) && (
                    <Badge 
                      variant={getDueDateBadgeVariant(todo.due_date, todo.completed)}
                      className="text-xs whitespace-nowrap px-1.5 py-0.5 sm:px-2 sm:py-1"
                    >
                      {format(parseISO(todo.due_date), 'MMM d', { locale: language === 'zh' ? zhCN : enUS })}
                    </Badge>
                  )}
                  {todo.priority && (
                    <Badge 
                      variant={getPriorityBadgeVariant(todo.priority as 'low' | 'medium' | 'high')}
                      className="text-xs px-1.5 py-0.5 sm:px-2 sm:py-1"
                    >
                      {language === 'zh' ? priorityMap[todo.priority as 'low' | 'medium' | 'high'].zh : priorityMap[todo.priority as 'low' | 'medium' | 'high'].en}
                    </Badge>
                  )}
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-7 w-7"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenTodoEdit(todo);
                    }}
                  >
                    <Edit2 className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-7 w-7 text-destructive hover:text-destructive hover:bg-destructive/10"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card click from triggering
                      deleteTodo(todo.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </AnimatePresence>
      {todos.length === 0 && !todosLoading && (
        <div className="text-center py-10">
          <p className="text-muted-foreground">
            {language === 'zh' ? '没有待办事项。' : 'No todos yet.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default TodoSection;
