import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarIcon, Flag } from "lucide-react";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { zhCN, enUS } from "date-fns/locale";
import { format } from "date-fns";

interface TodoEditDialogProps {
  language: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  newTodo: string;
  setNewTodo: (t: string) => void;
  selectedDueDate: Date | undefined;
  setSelectedDueDate: (d: Date | undefined) => void;
  selectedPriority: "low" | "medium" | "high" | undefined;
  setSelectedPriority: (p: "low" | "medium" | "high" | undefined) => void;
  handleEditTodo: () => void;
}

const TodoEditDialog: React.FC<TodoEditDialogProps> = ({
  language,
  open,
  onOpenChange,
  newTodo,
  setNewTodo,
  selectedDueDate,
  setSelectedDueDate,
  selectedPriority,
  setSelectedPriority,
  handleEditTodo,
}) => {
  // 使用自定义的日历下拉菜单状态
  const [calendarOpen, setCalendarOpen] = useState(false);
  const calendarRef = useRef<HTMLDivElement>(null);

  // 处理点击外部关闭日历
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 切换日历显示
  const toggleCalendar = () => {
    setCalendarOpen(!calendarOpen);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{language === "zh" ? "编辑待办事项" : "Edit Todo"}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Input
            value={newTodo}
            onChange={(e) => setNewTodo(e.target.value)}
            placeholder={language === "zh" ? "待办事项标题" : "Todo title"}
            className="flex-1"
            autoFocus
          />

          <div className="space-y-2">
            <label className="text-sm font-medium">{language === "zh" ? "截止日期" : "Due Date"}</label>
            
            {/* 自定义日历下拉菜单 */}
            <div className="relative" ref={calendarRef}>
              <Button
                type="button"
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !selectedDueDate && "text-muted-foreground"
                )}
                onClick={toggleCalendar}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDueDate ? (
                  format(selectedDueDate, "PPP", { locale: language === "zh" ? zhCN : enUS })
                ) : (
                  <span>{language === "zh" ? "选择日期" : "Pick a date"}</span>
                )}
              </Button>

              {calendarOpen && (
                <div className="absolute top-full left-0 mt-2 z-50 bg-popover rounded-md shadow-md border w-auto">
                  <div className="p-0">
                    <CalendarComponent 
                      mode="single" 
                      selected={selectedDueDate} 
                      onSelect={(date) => {
                        setSelectedDueDate(date);
                        setCalendarOpen(false);
                      }} 
                      initialFocus 
                      className="rounded-md"
                      locale={language === "zh" ? zhCN : enUS}
                    />
                    {selectedDueDate && (
                      <div className="p-3 border-t border-border flex justify-between">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => {
                            setSelectedDueDate(undefined);
                            setCalendarOpen(false);
                          }}
                        >
                          {language === "zh" ? "清除" : "Clear"}
                        </Button>
                        <div className="text-sm">
                          {format(selectedDueDate, "PPP", { locale: language === "zh" ? zhCN : enUS })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{language === "zh" ? "优先级" : "Priority"}</label>
            <div className="flex gap-2">
              <Button
                type="button"
                variant={selectedPriority === "low" ? "default" : "outline"}
                className={cn(
                  "flex-1",
                  selectedPriority === "low" && "bg-green-500 hover:bg-green-600"
                )}
                onClick={() => setSelectedPriority(selectedPriority === "low" ? undefined : "low")}
              >
                <Flag className="h-4 w-4 mr-2" />
                {language === "zh" ? "低" : "Low"}
              </Button>
              <Button
                type="button"
                variant={selectedPriority === "medium" ? "default" : "outline"}
                className={cn(
                  "flex-1",
                  selectedPriority === "medium" && "bg-orange-500 hover:bg-orange-600"
                )}
                onClick={() => setSelectedPriority(selectedPriority === "medium" ? undefined : "medium")}
              >
                <Flag className="h-4 w-4 mr-2" />
                {language === "zh" ? "中" : "Medium"}
              </Button>
              <Button
                type="button"
                variant={selectedPriority === "high" ? "default" : "outline"}
                className={cn(
                  "flex-1",
                  selectedPriority === "high" && "bg-destructive hover:bg-destructive/90"
                )}
                onClick={() => setSelectedPriority(selectedPriority === "high" ? undefined : "high")}
              >
                <Flag className="h-4 w-4 mr-2" />
                {language === "zh" ? "高" : "High"}
              </Button>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            {language === "zh" ? "取消" : "Cancel"}
          </Button>
          <Button type="button" onClick={handleEditTodo} disabled={!newTodo.trim()}>
            {language === "zh" ? "更新" : "Update"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TodoEditDialog;
