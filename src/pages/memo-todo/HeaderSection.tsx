
import React from "react";
import { ListTodo, StickyNote } from 'lucide-react';
import UnifiedHeader from '@/components/common/UnifiedHeader';

interface HeaderSectionProps {
  language: string;
}

const HeaderSection: React.FC<HeaderSectionProps> = ({ language }) => (
  <UnifiedHeader
    title={language === "zh" ? "备忘录与待办事项" : "Memos & Todos"}
    description={language === "zh"
      ? "管理您的想法和任务，提高生产力"
      : "Manage your thoughts and tasks to boost productivity"}
    icon={ListTodo}
    variant="gradient"
    gradientFrom="from-indigo-500"
    gradientTo="to-purple-500"
    layout="default"
  />
);

export default HeaderSection;
