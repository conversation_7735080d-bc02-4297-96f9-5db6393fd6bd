import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Calendar, Plus, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { zhCN, enUS } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";

interface MemoDialogProps {
  language: string;
  memoDialogOpen: boolean;
  setMemoDialogOpen: (open: boolean) => void;
  isEditMode: boolean;
  newMemo: string;
  setNewMemo: (memo: string) => void;
  selectedReminderDate: Date | undefined;
  setSelectedReminderDate: (date: Date | undefined) => void;
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  newTag: string;
  setNewTag: (tag: string) => void;
  handleAddMemo: (e: React.FormEvent) => void;
  addTag: () => void;
  removeTag: (tagToRemove: string) => void;
}

const MemoDialog: React.FC<MemoDialogProps> = ({
  language,
  memoDialogOpen,
  setMemoDialogOpen,
  isEditMode,
  newMemo,
  setNewMemo,
  selectedReminderDate,
  setSelectedReminderDate,
  selectedTags,
  setSelectedTags,
  newTag,
  setNewTag,
  handleAddMemo,
  addTag,
  removeTag,
}) => {
  // 使用自定义的日历下拉菜单状态
  const [calendarOpen, setCalendarOpen] = useState(false);
  const calendarRef = useRef<HTMLDivElement>(null);

  // 处理点击外部关闭日历
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 切换日历显示
  const toggleCalendar = () => {
    setCalendarOpen(!calendarOpen);
  };

  return (
    <Dialog open={memoDialogOpen} onOpenChange={setMemoDialogOpen}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? (language === "zh" ? "编辑备忘录" : "Edit Memo") : (language === "zh" ? "新备忘录" : "New Memo")}
          </DialogTitle>
          <DialogDescription>
            {language === "zh" ? "记录您的想法和灵感" : "Capture your thoughts and ideas"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleAddMemo}>
          <div className="grid gap-4 py-4">
            <Textarea
              placeholder={language === "zh" ? "在这里写下您的备忘录..." : "Write your memo here..."}
              value={newMemo}
              onChange={(e) => setNewMemo(e.target.value)}
              className="min-h-[200px]"
              autoFocus
            />

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">{language === "zh" ? "标签" : "Tags"}</label>
              </div>
              <div className="flex flex-wrap gap-2 mb-2">
                {selectedTags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="cursor-pointer hover:bg-secondary/80">
                    {tag}
                    <X className="h-3 w-3 ml-1" onClick={() => removeTag(tag)} />
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder={language === "zh" ? "添加标签..." : "Add tag..."}
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addTag();
                    }
                  }}
                />
                <Button type="button" variant="outline" onClick={addTag} disabled={!newTag.trim()}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">{language === "zh" ? "提醒" : "Reminder"}</label>
              </div>
              
              {/* 自定义日历下拉菜单 */}
              <div className="relative" ref={calendarRef}>
                <Button
                  type="button"
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !selectedReminderDate && "text-muted-foreground"
                  )}
                  onClick={toggleCalendar}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {selectedReminderDate ? (
                    format(selectedReminderDate, "PPP", { locale: language === "zh" ? zhCN : enUS })
                  ) : (
                    <span>{language === "zh" ? "选择日期" : "Pick a date"}</span>
                  )}
                </Button>

                {calendarOpen && (
                  <div className="absolute top-full left-0 mt-2 z-50 bg-popover rounded-md shadow-md border">
                    <div className="p-0">
                      <CalendarComponent
                        mode="single"
                        selected={selectedReminderDate}
                        onSelect={(date) => {
                          setSelectedReminderDate(date);
                          setCalendarOpen(false);
                        }}
                        initialFocus
                        className="rounded-md"
                        locale={language === "zh" ? zhCN : enUS}
                      />
                      {selectedReminderDate && (
                        <div className="p-3 border-t border-border flex justify-between">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedReminderDate(undefined);
                              setCalendarOpen(false);
                            }}
                          >
                            {language === "zh" ? "清除" : "Clear"}
                          </Button>
                          <div className="text-sm">
                            {format(selectedReminderDate, "PPP", { locale: language === "zh" ? zhCN : enUS })}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setMemoDialogOpen(false)}>
              {language === "zh" ? "取消" : "Cancel"}
            </Button>
            <Button type="submit" disabled={!newMemo.trim()}>
              {isEditMode ? (language === "zh" ? "更新" : "Update") : (language === "zh" ? "保存" : "Save")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default MemoDialog;
