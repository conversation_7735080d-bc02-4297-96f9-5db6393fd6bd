import React, { useState } from 'react'; // Added useState
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trash2, Calendar, Clock, Edit2, StickyNote, Plus, ChevronLeft, ChevronRight } from "lucide-react"; // Added ChevronLeft, ChevronRight
import { Button } from "@/components/ui/button";
import { Memo } from "@/hooks/useMemos";
import { motion, AnimatePresence } from "framer-motion";
import { format, isAfter, parseISO, isValid } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { MarkdownMemo } from '@/components/MarkdownMemo';

interface MemoSectionProps {
  language: string;
  searchText: string;
  handleViewMemoDetails: (memo: Memo) => void;
  handleEditMemo: (memo: Memo) => void;
  deleteMemo: (id: string) => void;
  memos: Memo[];
  setMemoDialogOpen: (open: boolean) => void;
  setIsEditMode: (editMode: boolean) => void;
  setNewMemo: (memo: string) => void;
  setSelectedReminderDate: (date: Date | undefined) => void;
  setSelectedTags: (tags: string[]) => void;
  currentPage: number; // Added for pagination
  itemsPerPage: number; // Added for pagination
  onPageChange: (page: number) => void; // Added for pagination
}

const MemoSection: React.FC<MemoSectionProps> = ({
  language,
  searchText,
  handleViewMemoDetails,
  handleEditMemo,
  deleteMemo,
  memos,
  setMemoDialogOpen,
  setIsEditMode,
  setNewMemo,
  setSelectedReminderDate,
  setSelectedTags,
  currentPage,      // Added for pagination
  itemsPerPage,     // Added for pagination
  onPageChange      // Added for pagination
}) => {
  const getFormattedDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'PPP', { locale: language === 'zh' ? zhCN : enUS });
  };

  const getRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return language === 'zh' ? '刚刚' : 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return language === 'zh'
        ? `${minutes}分钟前`
        : `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return language === 'zh'
        ? `${hours}小时前`
        : `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return language === 'zh'
        ? `${days}天前`
        : `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return getFormattedDate(dateString);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(memos.length / itemsPerPage);
  const paginatedMemos = memos.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          {language === 'zh' ? '备忘录' : 'Memos'}
        </h2>
        <Button 
          onClick={() => {
            setMemoDialogOpen(true);
            setIsEditMode(false);
            setNewMemo('');
            setSelectedReminderDate(undefined);
            setSelectedTags([]);
          }}
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          {language === 'zh' ? '新备忘录' : 'New Memo'}
        </Button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence>
          {paginatedMemos.length === 0 && memos.length > 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="sm:col-span-2 lg:col-span-3 text-center py-16"
            >
              <p className="text-muted-foreground">
                {language === 'zh' ? '当前页没有备忘录。' : 'No memos on this page.'}
              </p>
            </motion.div>
          )}
          {memos.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="sm:col-span-2 lg:col-span-3 text-center py-16"
            >
              <div className="w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <StickyNote className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-1">
                {language === 'zh' ? '没有备忘录' : 'No memos yet'}
              </h3>
              <p className="text-muted-foreground max-w-xs mx-auto">
                {language === 'zh'
                  ? '点击"新备忘录"按钮创建您的第一个备忘录'
                  : 'Click the "New Memo" button to create your first memo'}
              </p>
              <Button
                className="mt-4"
                onClick={() => {
                  setMemoDialogOpen(true);
                  setIsEditMode(false);
                  setNewMemo('');
                  setSelectedReminderDate(undefined);
                  setSelectedTags([]);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                {language === 'zh' ? '创建备忘录' : 'Create Memo'}
              </Button>
            </motion.div>
          ) : (
            paginatedMemos.map((memo) => (
              <motion.div
                key={memo.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
                onClick={() => handleViewMemoDetails(memo)}
                className="cursor-pointer group"
              >
                <Card className={cn(
                  "h-full hover:shadow-md transition-shadow overflow-hidden border",
                  memo.reminder_date &&
                  isValid(parseISO(memo.reminder_date)) &&
                  isAfter(parseISO(memo.reminder_date), new Date()) &&
                  "border-l-4 border-l-amber-500"
                )}>
                  <CardContent className="p-4 flex flex-col h-full">
                    <div className="flex-1 overflow-hidden">
                      {memo.tags && memo.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {memo.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                      <div className="line-clamp-6 prose prose-sm max-w-none dark:prose-invert">
                        <MarkdownMemo content={memo.content} />
                      </div>
                    </div>
                    <div className="flex justify-between items-center pt-4 text-xs text-muted-foreground mt-auto">
                      <div className="flex flex-wrap items-center gap-2">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {getRelativeTime(memo.created_at)}
                        </div>

                        {memo.reminder_date && (
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {getFormattedDate(memo.reminder_date)}
                          </div>
                        )}
                      </div>

                      <div className="flex opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditMemo(memo);
                          }}
                          className="h-6 w-6"
                        >
                          <Edit2 className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteMemo(memo.id);
                          }}
                          className="h-6 w-6"
                        >
                          <Trash2 className="h-3 w-3 text-destructive" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </AnimatePresence>
      </div>
      <PaginationControls 
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        language={language}
      />
    </div>
  );
};

export default MemoSection;

// Pagination UI (conditionally rendered)
const PaginationControls: React.FC<{ 
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  language: string;
}> = ({ currentPage, totalPages, onPageChange, language }) => {
  if (totalPages <= 1) return null;

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <div className="flex justify-center items-center space-x-2 mt-6">
      <Button 
        variant="outline" 
        size="sm" 
        onClick={handlePreviousPage} 
        disabled={currentPage === 1}
      >
        <ChevronLeft className="h-4 w-4 mr-1" />
        {language === 'zh' ? '上一页' : 'Previous'}
      </Button>
      <span className="text-sm">
        {language === 'zh' ? `第 ${currentPage} 页 / 共 ${totalPages} 页` : `Page ${currentPage} of ${totalPages}`}
      </span>
      <Button 
        variant="outline" 
        size="sm" 
        onClick={handleNextPage} 
        disabled={currentPage === totalPages}
      >
        {language === 'zh' ? '下一页' : 'Next'}
        <ChevronRight className="h-4 w-4 ml-1" />
      </Button>
    </div>
  );
};
