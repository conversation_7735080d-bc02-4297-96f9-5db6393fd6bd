import React from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, CheckCircle, Circle, Clock, Flag, Tag } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Todo } from "@/hooks/useTodos";
import { isToday, isTomorrow, isAfter, isBefore } from "date-fns";

interface SidebarNavProps {
  language: string;
  todos: Todo[];
  filteredTodos: Todo[];
  activeCategory: string;
  setActiveCategory: (category: string) => void;
  completedVisibility: string;
  setCompletedVisibility: (visibility: string) => void;
  dueDateFilter: string;
  setDueDateFilter: (filter: string) => void;
  availableTags: string[];
}

const SidebarNav: React.FC<SidebarNavProps> = ({
  language,
  todos,
  filteredTodos,
  activeCategory,
  setActiveCategory,
  completedVisibility,
  setCompletedVisibility,
  dueDateFilter,
  setDueDateFilter,
  availableTags,
}) => {
  // Count for different states
  const activeTodosCount = todos.filter(todo => !todo.completed).length;
  const completedTodosCount = todos.filter(todo => todo.completed).length;
  
  // Count for different dates
  const todayTodosCount = todos.filter(todo => {
    try {
      return todo.due_date && isToday(new Date(todo.due_date));
    } catch (e) {
      return false;
    }
  }).length;
  
  const tomorrowTodosCount = todos.filter(todo => {
    try {
      return todo.due_date && isTomorrow(new Date(todo.due_date));
    } catch (e) {
      return false;
    }
  }).length;
  
  const upcomingTodosCount = todos.filter(todo => {
    try {
      if (!todo.due_date) return false;
      const dueDate = new Date(todo.due_date);
      return isAfter(dueDate, new Date()) && !isToday(dueDate) && !isTomorrow(dueDate);
    } catch (e) {
      return false;
    }
  }).length;
  
  const overdueTodosCount = todos.filter(todo => {
    try {
      if (!todo.due_date || todo.completed) return false;
      const dueDate = new Date(todo.due_date);
      return isBefore(dueDate, new Date()) && !isToday(dueDate);
    } catch (e) {
      return false;
    }
  }).length;
  
  const noDateTodosCount = todos.filter(todo => !todo.due_date).length;

  return (
    <div className="space-y-4">
      <Card className="p-3">
        <h3 className="text-sm font-medium mb-2">
          {language === "zh" ? "完成状态" : "Status"}
        </h3>
        <div className="space-y-1">
          <Button
            variant={completedVisibility === "all" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", completedVisibility === "all" && "bg-secondary")}
            onClick={() => setCompletedVisibility("all")}
          >
            <Flag className="h-4 w-4 mr-2" />
            {language === "zh" ? "全部" : "All"}
            <Badge variant="outline" className="ml-auto">
              {todos.length}
            </Badge>
          </Button>
          <Button
            variant={completedVisibility === "active" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", completedVisibility === "active" && "bg-secondary")}
            onClick={() => setCompletedVisibility("active")}
          >
            <Circle className="h-4 w-4 mr-2" />
            {language === "zh" ? "进行中" : "Active"}
            <Badge variant="outline" className="ml-auto">
              {activeTodosCount}
            </Badge>
          </Button>
          <Button
            variant={completedVisibility === "completed" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", completedVisibility === "completed" && "bg-secondary")}
            onClick={() => setCompletedVisibility("completed")}
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            {language === "zh" ? "已完成" : "Completed"}
            <Badge variant="outline" className="ml-auto">
              {completedTodosCount}
            </Badge>
          </Button>
        </div>
      </Card>

      <Card className="p-3">
        <h3 className="text-sm font-medium mb-2">
          {language === "zh" ? "日期筛选" : "Due Date"}
        </h3>
        <div className="space-y-1">
          <Button
            variant={dueDateFilter === "all" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", dueDateFilter === "all" && "bg-secondary")}
            onClick={() => setDueDateFilter("all")}
          >
            <Calendar className="h-4 w-4 mr-2" />
            {language === "zh" ? "全部日期" : "All Dates"}
          </Button>
          <Button
            variant={dueDateFilter === "today" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", dueDateFilter === "today" && "bg-secondary")}
            onClick={() => setDueDateFilter("today")}
          >
            <Clock className="h-4 w-4 mr-2 text-orange-500" />
            {language === "zh" ? "今天" : "Today"}
            {todayTodosCount > 0 && (
              <Badge variant="outline" className="ml-auto">
                {todayTodosCount}
              </Badge>
            )}
          </Button>
          <Button
            variant={dueDateFilter === "tomorrow" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", dueDateFilter === "tomorrow" && "bg-secondary")}
            onClick={() => setDueDateFilter("tomorrow")}
          >
            <Clock className="h-4 w-4 mr-2 text-primary" />
            {language === "zh" ? "明天" : "Tomorrow"}
            {tomorrowTodosCount > 0 && (
              <Badge variant="outline" className="ml-auto">
                {tomorrowTodosCount}
              </Badge>
            )}
          </Button>
          <Button
            variant={dueDateFilter === "upcoming" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", dueDateFilter === "upcoming" && "bg-secondary")}
            onClick={() => setDueDateFilter("upcoming")}
          >
            <Calendar className="h-4 w-4 mr-2" />
            {language === "zh" ? "即将到来" : "Upcoming"}
            {upcomingTodosCount > 0 && (
              <Badge variant="outline" className="ml-auto">
                {upcomingTodosCount}
              </Badge>
            )}
          </Button>
          <Button
            variant={dueDateFilter === "overdue" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", dueDateFilter === "overdue" && "bg-secondary")}
            onClick={() => setDueDateFilter("overdue")}
          >
            <Clock className="h-4 w-4 mr-2 text-destructive" />
            {language === "zh" ? "已逾期" : "Overdue"}
            {overdueTodosCount > 0 && (
              <Badge variant="outline" className="ml-auto text-destructive">
                {overdueTodosCount}
              </Badge>
            )}
          </Button>
          <Button
            variant={dueDateFilter === "no-date" ? "secondary" : "ghost"}
            className={cn("w-full justify-start text-sm", dueDateFilter === "no-date" && "bg-secondary")}
            onClick={() => setDueDateFilter("no-date")}
          >
            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
            {language === "zh" ? "无日期" : "No Date"}
            {noDateTodosCount > 0 && (
              <Badge variant="outline" className="ml-auto">
                {noDateTodosCount}
              </Badge>
            )}
          </Button>
        </div>
      </Card>

      {/* 标签筛选 */}
      {availableTags.length > 0 && (
        <Card className="p-3">
          <h3 className="text-sm font-medium mb-2">
            {language === "zh" ? "标签筛选" : "Tags"}
          </h3>
          <div className="space-y-1">
            <Button
              variant={activeCategory === "all" ? "secondary" : "ghost"}
              className={cn("w-full justify-start text-sm", activeCategory === "all" && "bg-secondary")}
              onClick={() => setActiveCategory("all")}
            >
              <Tag className="h-4 w-4 mr-2" />
              {language === "zh" ? "全部标签" : "All Tags"}
            </Button>
            
            {availableTags.map((tag) => (
              <Button
                key={tag}
                variant={activeCategory === tag ? "secondary" : "ghost"}
                className={cn("w-full justify-start text-sm", activeCategory === tag && "bg-secondary")}
                onClick={() => setActiveCategory(tag)}
              >
                <Tag className="h-4 w-4 mr-2" />
                {tag}
              </Button>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default SidebarNav;
