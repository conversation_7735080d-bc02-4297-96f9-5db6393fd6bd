import React from "react";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Calendar, Trash2, Edit2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { format, parseISO, isValid } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { Memo } from "@/hooks/useMemos";
import { useAppContext } from "@/context/AppContext";
import { MarkdownMemo } from "@/components/MarkdownMemo";

interface MemoDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  memo: Memo | null;
  deleteMemo: (id: string) => void;
  handleEditMemo: (memo: Memo) => void;
}

const MemoDetailDialog: React.FC<MemoDetailDialogProps> = ({
  open,
  onOpenChange,
  memo,
  deleteMemo,
  handleEditMemo,
}) => {
  const { language } = useAppContext();
  const { toast } = useToast();

  const getFormattedDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'PPP', { locale: language === 'zh' ? zhCN : enUS });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>
            {language === 'zh' ? '备忘录详情' : 'Memo Details'}
          </DialogTitle>
          <DialogDescription>
            {memo?.created_at && getFormattedDate(memo.created_at)}
            {memo?.updated_at && memo.updated_at !== memo.created_at && (
              <span className="ml-2 text-muted-foreground">
                ({language === 'zh' ? '已编辑' : 'edited'})
              </span>
            )}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          {memo?.tags && memo.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {memo.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          <div className="prose prose-sm dark:prose-invert max-w-full">
            {memo && <MarkdownMemo content={memo.content} />}
          </div>

          {memo?.reminder_date && (
            <div className="flex items-center text-sm text-amber-500">
              <Clock className="h-4 w-4 mr-2" />
              <span>
                {language === 'zh' ? '提醒: ' : 'Reminder: '}
                {getFormattedDate(memo.reminder_date)}
              </span>
            </div>
          )}
        </div>
        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              if (memo) {
                deleteMemo(memo.id);
                onOpenChange(false);
              }
            }}
            className="mr-auto"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {language === 'zh' ? '删除' : 'Delete'}
          </Button>

          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            {language === 'zh' ? '关闭' : 'Close'}
          </Button>
          <Button
            type="button"
            onClick={() => {
              if (memo) {
                handleEditMemo(memo);
                onOpenChange(false);
              }
            }}
          >
            <Edit2 className="h-4 w-4 mr-2" />
            {language === 'zh' ? '编辑' : 'Edit'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default MemoDetailDialog;
