
import React from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface SearchBarProps {
  language: string;
  searchText: string;
  setSearchText: (searchText: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ language, searchText, setSearchText }) => (
  <div className="relative">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
    <Input
      type="search"
      placeholder={language === "zh" ? "搜索待办事项和备忘录..." : "Search todos and memos..."}
      value={searchText}
      onChange={(e) => setSearchText(e.target.value)}
      className="pl-10"
    />
  </div>
);

export default SearchBar;
