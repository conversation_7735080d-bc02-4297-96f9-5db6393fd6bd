import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';

// 定义位置数据的接口
interface LocationData {
  ip?: string;
  country_name?: string;
  city?: string;
  latitude?: number;
  longitude?: number;
}

const ShortUrlRedirect = () => {
  const { t, language } = useAppContext();
  const { shortCode } = useParams<{ shortCode: string }>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 创建一个函数用于记录访问统计数据（不阻塞主流程）
  const trackVisit = async (shortUrl: ShortUrl) => {
    try {
      // 获取地理位置数据 - 在后台进行，不阻塞重定向
      fetch('https://ipapi.co/json/')
        .then(response => {
          if (response.ok) return response.json();
          return {};
        })
        .then(async (locationData: LocationData) => {
          // 并行执行数据统计操作
          await Promise.all([
            // 增加点击计数
            supabase
              .from('short_urls')
              .update({
                clicks: (shortUrl.clicks || 0) + 1
              })
              .eq('id', shortUrl.id),

            // 存储访问信息和地理位置数据
            locationData && Object.keys(locationData).length > 0 ?
              supabase.from('url_visits').insert({
                short_url_id: shortUrl.id,
                ip_address: locationData.ip || null,
                country: locationData.country_name || null,
                city: locationData.city || null,
                latitude: locationData.latitude || null,
                longitude: locationData.longitude || null
              } as UrlVisitInsert) : Promise.resolve()
          ]);
        })
        .catch(err => {
          console.error('Error in background tracking:', err);
        });
    } catch (err) {
      // 这里的错误不会阻止重定向，只记录日志
      console.error('Error tracking visit (non-blocking):', err);
    }
  };

  useEffect(() => {
    const redirectToOriginalUrl = async () => {
      if (!shortCode) {
        setError(language === 'en' ? 'Invalid short URL' : '无效的短URL');
        setLoading(false);
        return;
      }

      try {
        // 尽快获取原始URL并立即重定向
        const { data, error: fetchError } = await supabase
          .from('short_urls')
          .select('id, original_url, expires_at, clicks')
          .eq('short_code', shortCode)
          .single();

        if (fetchError || !data) {
          console.error('Error fetching URL:', fetchError);
          setError(language === 'en' ? 'Short URL not found' : '找不到短URL');
          setLoading(false);
          return;
        }

        const shortUrl = data as ShortUrl;

        // 检查链接是否过期
        if (shortUrl.expires_at && new Date(shortUrl.expires_at) < new Date()) {
          setError(language === 'en' ? 'This URL has expired' : '此URL已过期');
          setLoading(false);
          return;
        }

        // 在后台记录访问统计，不阻塞重定向
        trackVisit(shortUrl);

        // 立即重定向到原始URL
        window.location.replace(shortUrl.original_url);
      } catch (err) {
        console.error('Error in redirect:', err);
        setError(language === 'en' ? 'An error occurred' : '发生错误');
        setLoading(false);
      }
    };

    // 立即执行重定向
    redirectToOriginalUrl();
  }, [shortCode, language]);

  // 只在有错误时才显示错误界面
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh] max-w-md mx-auto text-center">
        <div className="bg-destructive/10 p-4 rounded-full mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-12 w-12 text-destructive"
          >
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
        </div>
        <h1 className="text-2xl font-bold">{error}</h1>
        <p className="text-muted-foreground mt-2 mb-6">
          {language === 'en'
            ? 'The short link you clicked is not valid or has expired.'
            : '您点击的短链接无效或已过期。'}
        </p>
        <a
          href="/"
          className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
        >
          {language === 'en' ? 'Go to Homepage' : '返回首页'}
        </a>
      </div>
    );
  }

  // 加载中的UI显示
  return (
    <div className="flex flex-col items-center justify-center h-[70vh]">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <h1 className="text-2xl font-bold">{language === 'en' ? 'Redirecting...' : '重定向中...'}</h1>
    </div>
  );
};

export default ShortUrlRedirect;
