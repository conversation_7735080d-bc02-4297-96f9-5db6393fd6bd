// 后端类型枚举
export enum BackendType {
  SUPABASE = 'supabase',
  GO_BACKEND = 'go_backend'
}

// 后端配置接口
export interface BackendConfig {
  type: BackendType;
  supabase?: {
    url: string;
    anonKey: string;
  };
  goBackend?: {
    baseUrl: string;
    timeout?: number;
  };
}

// 默认配置
const DEFAULT_CONFIG: BackendConfig = {
  type: BackendType.GO_BACKEND, // 默认使用 Go 后端
  supabase: {
    url: import.meta.env.VITE_SUPABASE_URL || '',
    anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
  },
  goBackend: {
    baseUrl: import.meta.env.VITE_GO_BACKEND_URL || 'http://localhost:8080/api/v1',
    timeout: 10000,
  },
};

// 获取当前后端配置
export function getBackendConfig(): BackendConfig {
  // 从环境变量读取后端类型
  const backendType = import.meta.env.VITE_BACKEND_TYPE as BackendType;

  // 验证配置
  if (backendType === BackendType.SUPABASE) {
    if (!DEFAULT_CONFIG.supabase?.url || !DEFAULT_CONFIG.supabase?.anonKey) {
      console.warn('Supabase 配置不完整，回退到 Go 后端');
      return { ...DEFAULT_CONFIG, type: BackendType.GO_BACKEND };
    }
  }

  return {
    ...DEFAULT_CONFIG,
    type: backendType || DEFAULT_CONFIG.type,
  };
}

// 检查当前是否使用 Supabase
export function isUsingSupabase(): boolean {
  return getBackendConfig().type === BackendType.SUPABASE;
}

// 检查当前是否使用 Go 后端
export function isUsingGoBackend(): boolean {
  return getBackendConfig().type === BackendType.GO_BACKEND;
}

// 检查Supabase环境变量是否配置
export function hasSupabaseConfig(): boolean {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

  return !!(supabaseUrl && supabaseAnonKey &&
    supabaseUrl !== '' && supabaseAnonKey !== '' &&
    supabaseUrl !== 'undefined' && supabaseAnonKey !== 'undefined' &&
    !supabaseUrl.startsWith('${') && !supabaseAnonKey.startsWith('${'));
}

// 获取当前后端显示名称
export function getCurrentBackendName(): string {
  const config = getBackendConfig();
  switch (config.type) {
    case BackendType.SUPABASE:
      return 'Supabase';
    case BackendType.GO_BACKEND:
      return 'Go 后端';
    default:
      return '未知后端';
  }
}

// 验证后端连接
export async function validateBackendConnection(): Promise<boolean> {
  const config = getBackendConfig();

  try {
    if (config.type === BackendType.SUPABASE) {
      // 验证 Supabase 连接
      const response = await fetch(`${config.supabase?.url}/rest/v1/`, {
        headers: {
          'apikey': config.supabase?.anonKey || '',
          'Authorization': `Bearer ${config.supabase?.anonKey}`,
        },
      });
      return response.ok;
    } else {
      // 验证 Go 后端连接
      const response = await fetch(`${config.goBackend?.baseUrl}/health`);
      return response.ok;
    }
  } catch (error) {
    console.error('后端连接验证失败:', error);
    return false;
  }
}