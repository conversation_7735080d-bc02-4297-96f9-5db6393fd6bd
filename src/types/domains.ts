
export interface DomainData {
  id: string;
  domain: string;
  approved: boolean;
  created_at: string;
  user_id?: string | null;
}

export interface DomainHookReturn {
  domains: DomainData[];
  pendingDomains: DomainData[];
  isLoading: boolean;
  submitDomain: (newDomain: string) => Promise<boolean>;
  approveDomain: (id: string) => Promise<boolean>;
  rejectDomain: (id: string) => Promise<boolean>;
  toggleDomainApproval: (id: string, currentStatus: boolean) => Promise<boolean>;
  deleteDomain: (id: string) => Promise<boolean>;
  fetchDomains: () => Promise<void>;
}
