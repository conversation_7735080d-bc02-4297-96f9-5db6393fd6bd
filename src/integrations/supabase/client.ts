import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from './types';
import { isUsingSupabase, hasSupabaseConfig } from '@/config/backend';

let supabaseInstance: SupabaseClient<Database> | null = null;

// 延迟初始化Supabase客户端
function initializeSupabase(): SupabaseClient<Database> {
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // 检查是否配置了Supabase
  if (!hasSupabaseConfig()) {
    throw new Error('Supabase 配置缺失：VITE_SUPABASE_URL 和 VITE_SUPABASE_ANON_KEY 必须设置');
  }

  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

  // 验证URL格式
  try {
    new URL(supabaseUrl);
  } catch (error) {
    throw new Error(`无效的 Supabase URL: ${supabaseUrl}`);
  }

  supabaseInstance = createClient<Database>(supabaseUrl, supabaseAnonKey);
  return supabaseInstance;
}

// 获取Supabase客户端（仅在使用Supabase时）
export function getSupabaseClient(): SupabaseClient<Database> {
  if (!isUsingSupabase()) {
    throw new Error('当前配置使用Go后端，不应该调用Supabase客户端');
  }

  return initializeSupabase();
}

// 为了向后兼容，保留supabase导出，但添加检查
export const supabase = new Proxy({} as SupabaseClient<Database>, {
  get(target, prop) {
    if (!isUsingSupabase()) {
      console.warn('尝试访问Supabase客户端，但当前配置使用Go后端');
      throw new Error('当前配置使用Go后端，请使用Go后端API');
    }

    const client = initializeSupabase();
    return (client as any)[prop];
  }
});