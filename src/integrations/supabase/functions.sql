
-- Create the SQL functions for email domain management

-- Function to get all email domains
CREATE OR REPLACE FUNCTION public.get_email_domains()
RETURNS SETOF public.email_domains
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT * FROM public.email_domains ORDER BY domain ASC;
$$;

-- Function to add a new email domain
CREATE OR REPLACE FUNCTION public.add_email_domain(domain_value TEXT)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_id uuid;
BEGIN
    INSERT INTO public.email_domains (domain)
    VALUES (domain_value)
    RETURNING id INTO new_id;
    
    RETURN new_id;
END;
$$;

-- Function to delete an email domain
CREATE OR REPLACE FUNCTION public.delete_email_domain(domain_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    DELETE FROM public.email_domains
    WHERE id = domain_id;
    
    RETURN FOUND;
END;
$$;
