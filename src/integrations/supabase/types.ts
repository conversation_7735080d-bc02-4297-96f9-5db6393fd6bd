export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      account_link_requests: {
        Row: {
          created_at: string
          email: string
          expires_at: string
          id: string
          provider_data: Json | null
          provider_name: string
          provider_user_id: string
          token: string
          used: boolean
        }
        Insert: {
          created_at?: string
          email: string
          expires_at: string
          id?: string
          provider_data?: Json | null
          provider_name: string
          provider_user_id: string
          token: string
          used?: boolean
        }
        Update: {
          created_at?: string
          email?: string
          expires_at?: string
          id?: string
          provider_data?: Json | null
          provider_name?: string
          provider_user_id?: string
          token?: string
          used?: boolean
        }
        Relationships: []
      }
      banner_config: {
        Row: {
          animation_speed: number | null
          created_at: string
          custom_class: string | null
          display_style: string | null
          first_gradient: string
          first_text: string
          fourth_gradient: string | null
          fourth_text: string | null
          height: number | null
          id: string
          lines: Json | null
          main_description: <PERSON><PERSON> | null
          main_title: Json | null
          second_gradient: string
          second_text: string
          spacing: number | null
          third_gradient: string | null
          third_text: string | null
          updated_at: string
          use_third_line: boolean | null
        }
        Insert: {
          animation_speed?: number | null
          created_at?: string
          custom_class?: string | null
          display_style?: string | null
          first_gradient: string
          first_text: string
          fourth_gradient?: string | null
          fourth_text?: string | null
          height?: number | null
          id?: string
          lines?: Json | null
          main_description?: Json | null
          main_title?: Json | null
          second_gradient: string
          second_text: string
          spacing?: number | null
          third_gradient?: string | null
          third_text?: string | null
          updated_at?: string
          use_third_line?: boolean | null
        }
        Update: {
          animation_speed?: number | null
          created_at?: string
          custom_class?: string | null
          display_style?: string | null
          first_gradient?: string
          first_text?: string
          fourth_gradient?: string | null
          fourth_text?: string | null
          height?: number | null
          id?: string
          lines?: Json | null
          main_description?: Json | null
          main_title?: Json | null
          second_gradient?: string
          second_text?: string
          spacing?: number | null
          third_gradient?: string | null
          third_text?: string | null
          updated_at?: string
          use_third_line?: boolean | null
        }
        Relationships: []
      }
      domain_whitelist: {
        Row: {
          approved: boolean | null
          created_at: string
          domain: string
          id: string
          user_id: string | null
        }
        Insert: {
          approved?: boolean | null
          created_at?: string
          domain: string
          id?: string
          user_id?: string | null
        }
        Update: {
          approved?: boolean | null
          created_at?: string
          domain?: string
          id?: string
          user_id?: string | null
        }
        Relationships: []
      }
      email_domains: {
        Row: {
          created_at: string
          domain: string
          id: string
        }
        Insert: {
          created_at?: string
          domain: string
          id?: string
        }
        Update: {
          created_at?: string
          domain?: string
          id?: string
        }
        Relationships: []
      }
      feature_ordering: {
        Row: {
          component_type: string
          components_config: Json
          created_at: string | null
          id: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          component_type: string
          components_config?: Json
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          component_type?: string
          components_config?: Json
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      memos: {
        Row: {
          content: string
          created_at: string
          id: string
          reminder_date: string | null
          tags: string[] | null
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          reminder_date?: string | null
          tags?: string[] | null
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          reminder_date?: string | null
          tags?: string[] | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      nav_categories: {
        Row: {
          created_at: string
          description: string | null
          id: string
          is_admin_default: boolean | null
          is_public: boolean | null
          name: string
          parent_id: string | null
          sort_order: number | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          is_admin_default?: boolean | null
          is_public?: boolean | null
          name: string
          parent_id?: string | null
          sort_order?: number | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          is_admin_default?: boolean | null
          is_public?: boolean | null
          name?: string
          parent_id?: string | null
          sort_order?: number | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "nav_categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "nav_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      nav_links: {
        Row: {
          category_id: string
          clicks: number | null
          created_at: string
          icon: string | null
          id: string
          is_internal: boolean | null
          is_public: boolean | null
          name: string
          popularity: number | null
          sort_order: number | null
          submission_status: string | null
          submitted_by: string | null
          url: string
          user_id: string | null
        }
        Insert: {
          category_id: string
          clicks?: number | null
          created_at?: string
          icon?: string | null
          id?: string
          is_internal?: boolean | null
          is_public?: boolean | null
          name: string
          popularity?: number | null
          sort_order?: number | null
          submission_status?: string | null
          submitted_by?: string | null
          url: string
          user_id?: string | null
        }
        Update: {
          category_id?: string
          clicks?: number | null
          created_at?: string
          icon?: string | null
          id?: string
          is_internal?: boolean | null
          is_public?: boolean | null
          name?: string
          popularity?: number | null
          sort_order?: number | null
          submission_status?: string | null
          submitted_by?: string | null
          url?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "nav_links_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "nav_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      oauth_providers: {
        Row: {
          client_id: string
          client_secret: string
          created_at: string
          enabled: boolean
          id: string
          provider_name: string
          redirect_url: string | null
          scopes: string[] | null
          updated_at: string
        }
        Insert: {
          client_id: string
          client_secret: string
          created_at?: string
          enabled?: boolean
          id?: string
          provider_name: string
          redirect_url?: string | null
          scopes?: string[] | null
          updated_at?: string
        }
        Update: {
          client_id?: string
          client_secret?: string
          created_at?: string
          enabled?: boolean
          id?: string
          provider_name?: string
          redirect_url?: string | null
          scopes?: string[] | null
          updated_at?: string
        }
        Relationships: []
      }
      received_emails: {
        Row: {
          body: string | null
          from_address: string
          id: string
          received_at: string
          subject: string | null
          temp_email_id: string
        }
        Insert: {
          body?: string | null
          from_address: string
          id?: string
          received_at?: string
          subject?: string | null
          temp_email_id: string
        }
        Update: {
          body?: string | null
          from_address?: string
          id?: string
          received_at?: string
          subject?: string | null
          temp_email_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "received_emails_temp_email_id_fkey"
            columns: ["temp_email_id"]
            isOneToOne: false
            referencedRelation: "temp_emails"
            referencedColumns: ["id"]
          },
        ]
      }
      short_urls: {
        Row: {
          clicks: number | null
          created_at: string
          expires_at: string | null
          id: string
          original_url: string
          short_code: string
          user_id: string | null
        }
        Insert: {
          clicks?: number | null
          created_at?: string
          expires_at?: string | null
          id?: string
          original_url: string
          short_code: string
          user_id?: string | null
        }
        Update: {
          clicks?: number | null
          created_at?: string
          expires_at?: string | null
          id?: string
          original_url?: string
          short_code?: string
          user_id?: string | null
        }
        Relationships: []
      }
      temp_emails: {
        Row: {
          created_at: string
          email_address: string
          expires_at: string
          id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          email_address: string
          expires_at: string
          id?: string
          user_id: string
        }
        Update: {
          created_at?: string
          email_address?: string
          expires_at?: string
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      todos: {
        Row: {
          completed: boolean | null
          created_at: string
          due_date: string | null
          id: string
          priority: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          completed?: boolean | null
          created_at?: string
          due_date?: string | null
          id?: string
          priority?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          completed?: boolean | null
          created_at?: string
          due_date?: string | null
          id?: string
          priority?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      url_visits: {
        Row: {
          city: string | null
          country: string | null
          created_at: string
          id: string
          ip_address: string | null
          latitude: number | null
          longitude: number | null
          short_url_id: string | null
        }
        Insert: {
          city?: string | null
          country?: string | null
          created_at?: string
          id?: string
          ip_address?: string | null
          latitude?: number | null
          longitude?: number | null
          short_url_id?: string | null
        }
        Update: {
          city?: string | null
          country?: string | null
          created_at?: string
          id?: string
          ip_address?: string | null
          latitude?: number | null
          longitude?: number | null
          short_url_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "url_visits_short_url_id_fkey"
            columns: ["short_url_id"]
            isOneToOne: false
            referencedRelation: "short_urls"
            referencedColumns: ["id"]
          },
        ]
      }
      user_oauth_accounts: {
        Row: {
          created_at: string
          id: string
          provider_data: Json | null
          provider_email: string | null
          provider_name: string
          provider_user_id: string
          provider_username: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          provider_data?: Json | null
          provider_email?: string | null
          provider_name: string
          provider_user_id: string
          provider_username?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          provider_data?: Json | null
          provider_email?: string | null
          provider_name?: string
          provider_user_id?: string
          provider_username?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_email_domain: {
        Args: { domain_value: string }
        Returns: {
          created_at: string
          domain: string
          id: string
        }
      }
      check_account_link: {
        Args: {
          provider_email: string
          provider_name: string
          provider_user_id: string
          provider_data: Json
        }
        Returns: Json
      }
      confirm_account_link: {
        Args: { link_token: string; user_password: string }
        Returns: Json
      }
      delete_email_domain: {
        Args: { domain_id: string }
        Returns: undefined
      }
      extract_domain: {
        Args: { url: string }
        Returns: string
      }
      get_email_domains: {
        Args: Record<PropertyKey, never>
        Returns: {
          created_at: string
          domain: string
          id: string
        }[]
      }
      increment_link_clicks: {
        Args: { link_id: string }
        Returns: undefined
      }
      is_super_admin: {
        Args: { user_id: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
