import { Database } from './types';

// Define specific table row types
export type ShortUrl = Database["public"]["Tables"]["short_urls"]["Row"];
export type DomainWhitelist = Database["public"]["Tables"]["domain_whitelist"]["Row"];
export type TempEmail = Database["public"]["Tables"]["temp_emails"]["Row"];
export type ReceivedEmail = Database["public"]["Tables"]["received_emails"]["Row"];
export type UserRole = Database["public"]["Tables"]["user_roles"]["Row"];
// Manually define EmailDomain type since it might not be in auto-generated types yet
export type EmailDomain = {
  id: string;
  domain: string;
  created_at: string;
};

// Define url_visits type manually since it's not in the auto-generated types yet
export type UrlVisit = {
  id: string;
  short_url_id: string | null;
  ip_address: string | null;
  country: string | null;
  city: string | null;
  latitude: number | null;
  longitude: number | null;
  created_at: string;
};

// Define insert types
export type ShortUrlInsert = Database["public"]["Tables"]["short_urls"]["Insert"];
export type DomainWhitelistInsert = Database["public"]["Tables"]["domain_whitelist"]["Insert"];
export type TempEmailInsert = Database["public"]["Tables"]["temp_emails"]["Insert"];
export type ReceivedEmailInsert = Database["public"]["Tables"]["received_emails"]["Insert"];
export type UserRoleInsert = Database["public"]["Tables"]["user_roles"]["Insert"];
// Manually define EmailDomainInsert type
export type EmailDomainInsert = {
  domain: string;
  created_at?: string;
};

// Define url_visits insert type manually
export type UrlVisitInsert = {
  short_url_id?: string | null;
  ip_address?: string | null;
  country?: string | null;
  city?: string | null;
  latitude?: number | null;
  longitude?: number | null;
};

// Define update types
export type ShortUrlUpdate = Database["public"]["Tables"]["short_urls"]["Update"];
export type DomainWhitelistUpdate = Database["public"]["Tables"]["domain_whitelist"]["Update"];
export type TempEmailUpdate = Database["public"]["Tables"]["temp_emails"]["Update"];
export type ReceivedEmailUpdate = Database["public"]["Tables"]["received_emails"]["Update"];
export type UserRoleUpdate = Database["public"]["Tables"]["user_roles"]["Update"];
// Manually define EmailDomainUpdate type
export type EmailDomainUpdate = {
  domain?: string;
  created_at?: string;
};

// Define url_visits update type manually
export type UrlVisitUpdate = {
  short_url_id?: string | null;
  ip_address?: string | null;
  country?: string | null;
  city?: string | null;
  latitude?: number | null;
  longitude?: number | null;
};

// Define nav_links type with the submission fields and clicks
export type NavLink = Database["public"]["Tables"]["nav_links"]["Row"] & {
  submission_status?: 'pending' | 'approved' | 'rejected' | null;
  submitted_by?: string | null;
  clicks?: number;
};

export type NavLinkInsert = Database["public"]["Tables"]["nav_links"]["Insert"] & {
  submission_status?: 'pending' | 'approved' | 'rejected' | null;
  submitted_by?: string | null;
  clicks?: number;
};

export type NavLinkUpdate = Database["public"]["Tables"]["nav_links"]["Update"] & {
  submission_status?: 'pending' | 'approved' | 'rejected' | null;
  submitted_by?: string | null;
  clicks?: number;
};

// Helper function for calculating future dates
export const getFutureDate = (days: number): string => {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString();
};

// Helper function for generating a random short code
export const generateShortCode = (length: number = 6): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Helper function to extract domain from URL
export const extractDomain = (url: string): string => {
  try {
    // Add protocol if missing to make URL parsing work
    if (!url.match(/^[a-zA-Z]+:\/\//)) {
      url = 'https://' + url;
    }
    
    const domain = new URL(url).hostname;
    return domain.startsWith('www.') ? domain.substring(4) : domain;
  } catch (error) {
    console.error('Error extracting domain:', error);
    return '';
  }
};

// Helper function to check if URL is valid
export const isValidUrl = (url: string): boolean => {
  try {
    // Add protocol if missing to make URL parsing work
    if (!url.match(/^[a-zA-Z]+:\/\//)) {
      url = 'https://' + url;
    }
    
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

// Helper function to format date for display
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Helper function to check if a date is in the past
export const isExpired = (dateString: string | null): boolean => {
  if (!dateString) return false;
  
  const expiryDate = new Date(dateString);
  const now = new Date();
  
  return expiryDate < now;
};

// Express expiration options for display
export const urlExpirationOptions = [
  { value: "1", label: "1 day" },
  { value: "7", label: "7 days" },
  { value: "30", label: "30 days" },
  { value: "365", label: "1 year" },
  { value: "permanent", label: "Permanent (registered users only)" }
];

export const emailExpirationOptions = [
  { value: "1", label: "1 day" },
  { value: "7", label: "7 days" },
  { value: "14", label: "14 days" },
  { value: "30", label: "30 days (maximum)" }
];
