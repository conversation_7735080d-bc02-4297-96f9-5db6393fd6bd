
import { useState, useEffect, useCallback } from 'react';
import { fetchPlatforms, fetchNews, fallbackPlatforms } from './api';
import { NewsPlatform, NewsItem, HotNewsHookReturn } from './types';

export const useHotNews = (): HotNewsHookReturn => {
  const [platforms, setPlatforms] = useState<NewsPlatform[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoadingPlatforms, setIsLoadingPlatforms] = useState(false);
  const [isLoadingNews, setIsLoadingNews] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available platforms
  const refetchPlatforms = useCallback(async () => {
    setIsLoadingPlatforms(true);
    setError(null);
    
    try {
      const data = await fetchPlatforms();
      setPlatforms(data);
      
      // Set the first platform as selected by default
      if (data.length > 0 && !selectedPlatform) {
        setSelectedPlatform(data[0].path);
      }
    } catch (err) {
      console.error('Error in refetchPlatforms:', err);
      setPlatforms(fallbackPlatforms);
      if (!selectedPlatform) {
        setSelectedPlatform(fallbackPlatforms[0].path);
      }
      setError('Failed to fetch platforms. Using local fallback data.');
    } finally {
      setIsLoadingPlatforms(false);
    }
  }, [selectedPlatform]);

  // Fetch news for the selected platform
  const refetchNews = useCallback(async () => {
    if (!selectedPlatform) return;
    
    setIsLoadingNews(true);
    setError(null);
    
    try {
      const data = await fetchNews(selectedPlatform);
      setNews(data);
    } catch (err) {
      console.error('Error in refetchNews:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch news. Please try again later.');
      setNews([]);
    } finally {
      setIsLoadingNews(false);
    }
  }, [selectedPlatform]);

  // Fetch platforms on mount
  useEffect(() => {
    refetchPlatforms();
  }, [refetchPlatforms]);

  // Fetch news when selected platform changes
  useEffect(() => {
    if (selectedPlatform) {
      refetchNews();
    }
  }, [selectedPlatform, refetchNews]);

  return {
    platforms,
    selectedPlatform,
    setSelectedPlatform,
    news,
    isLoadingPlatforms,
    isLoadingNews,
    error,
    refetchPlatforms,
    refetchNews
  };
};
