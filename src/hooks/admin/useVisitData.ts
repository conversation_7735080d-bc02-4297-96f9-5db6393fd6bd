
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useAppContext } from '@/context/AppContext';
import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import apiClient from '@/services/api';

export interface VisitStat {
  name: string;
  value: number;
}

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }

  try {
    const { supabase } = await import('@/integrations/supabase/client');
    return supabase;
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

export function useVisitData() {
  const { language } = useAppContext();
  const { toast } = useToast();
  const [visitStats, setVisitStats] = useState<VisitStat[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchVisitData = async () => {
    setIsLoading(true);
    try {
      if (isUsingSupabase()) {
        // 使用 Supabase
        const supabase = await getSupabaseClient();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          toast({
            variant: "destructive",
            description: language === 'en' ? "You must be logged in to access the admin panel" : "您必须登录才能访问管理员面板",
          });
          return;
        }

        const { data: visitsData } = await supabase
          .from('url_visits')
          .select('country')
          .not('country', 'is', null);

        if (visitsData && visitsData.length > 0) {
          const countryStats: Record<string, number> = {};
          visitsData.forEach(visit => {
            const country = visit.country || 'Unknown';
            countryStats[country] = (countryStats[country] || 0) + 1;
          });

          const chartData = Object.entries(countryStats)
            .map(([name, value]) => ({ name, value }))
            .sort((a, b) => b.value - a.value);

          setVisitStats(chartData);
        } else {
          setVisitStats([
            { name: 'United States', value: 35 },
            { name: 'China', value: 30 },
            { name: 'India', value: 15 },
            { name: 'United Kingdom', value: 10 },
            { name: 'Germany', value: 5 }
          ]);
        }
      } else {
        // 使用 Go 后端
        try {
          const response = await apiClient.get('/visits/stats');
          const visitsData = response.data;

          if (visitsData && visitsData.length > 0) {
            const countryStats: Record<string, number> = {};
            visitsData.forEach((visit: any) => {
              const country = visit.country || 'Unknown';
              countryStats[country] = (countryStats[country] || 0) + 1;
            });

            const chartData = Object.entries(countryStats)
              .map(([name, value]) => ({ name, value }))
              .sort((a, b) => b.value - a.value);

            setVisitStats(chartData);
          } else {
            // 使用默认数据
            setVisitStats([
              { name: 'United States', value: 35 },
              { name: 'China', value: 30 },
              { name: 'India', value: 15 },
              { name: 'United Kingdom', value: 10 },
              { name: 'Germany', value: 5 }
            ]);
          }
        } catch (error) {
          console.error('Error fetching visit data from Go backend:', error);
          // 使用默认数据
          setVisitStats([
            { name: 'United States', value: 35 },
            { name: 'China', value: 30 },
            { name: 'India', value: 15 },
            { name: 'United Kingdom', value: 10 },
            { name: 'Germany', value: 5 }
          ]);
        }
      }
    } catch (error) {
      console.error('Error fetching visit data:', error);
      // Set fallback data
      setVisitStats([
        { name: 'United States', value: 35 },
        { name: 'China', value: 30 },
        { name: 'India', value: 15 },
        { name: 'United Kingdom', value: 10 },
        { name: 'Germany', value: 5 }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchVisitData();
  }, []);

  return {
    visitStats,
    isLoading,
    fetchVisitData
  };
}
