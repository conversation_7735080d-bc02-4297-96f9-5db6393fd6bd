
import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { Category, NavLink } from '@/components/dashboard/navigation/types';
import { getBackendConfig } from '@/config/backend';

export const useNavigationManager = (userId: string) => {
  const { toast } = useToast();
  const { language } = useAppContext();

  const [categories, setCategories] = useState<Category[]>([]);
  const [links, setLinks] = useState<NavLink[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const config = getBackendConfig();
        const baseURL = config.goBackend?.baseUrl;
        const token = localStorage.getItem('authToken');

        if (!token) {
          console.error('No auth token found');
          return;
        }

        const response = await fetch(`${baseURL}/navigation/categories`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const userCategories = data.filter((cat: any) => cat.user_id !== null);

        setCategories(userCategories);
        if (userCategories.length > 0 && !selectedCategory) {
          setSelectedCategory(userCategories[0]);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, [userId, selectedCategory]);

  // Fetch links for selected category
  useEffect(() => {
    const fetchLinks = async () => {
      if (!selectedCategory) return;

      try {
        const config = getBackendConfig();
        const baseURL = config.goBackend?.baseUrl;
        const token = localStorage.getItem('authToken');

        if (!token) {
          console.error('No auth token found');
          return;
        }

        const response = await fetch(`${baseURL}/navigation/links?category_id=${selectedCategory.id}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setLinks(data || []);
      } catch (error) {
        console.error('Error fetching links:', error);
      }
    };

    fetchLinks();
  }, [selectedCategory]);

  // Handle category creation
  const handleCreateCategory = async (name: string, isPublic: boolean, parentId: string | null) => {
    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        console.error('No auth token found');
        return;
      }

      const response = await fetch(`${baseURL}/navigation/categories`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name,
          is_public: isPublic,
          parent_id: parentId,
          sort_order: categories.length
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      toast({
        description: language === 'en' ? "Category created successfully." : "分类创建成功。",
      });
      setCategories([...categories, data]);
      setSelectedCategory(data);
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to create category." : "创建分类失败。",
      });
    }
  };

  // Handle category deletion
  const handleDeleteCategory = async (category: Category) => {
    const hasSubcategories = categories.some(cat => cat.parent_id === category.id);

    if (hasSubcategories) {
      toast({
        variant: "destructive",
        description: language === 'en'
          ? "Cannot delete category with subcategories. Delete subcategories first."
          : "无法删除有子分类的分类。请先删除子分类。",
      });
      return;
    }

    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        console.error('No auth token found');
        return;
      }

      // 删除分类（后端会自动处理关联的链接）
      const response = await fetch(`${baseURL}/navigation/categories/${category.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      toast({
        description: language === 'en' ? "Category deleted successfully." : "分类删除成功。",
      });

      const updatedCategories = categories.filter(c => c.id !== category.id);
      setCategories(updatedCategories);

      if (selectedCategory?.id === category.id) {
        setSelectedCategory(updatedCategories.length > 0 ? updatedCategories[0] : null);
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to delete category." : "删除分类失败。",
      });
    }
  };

  // Handle link creation
  const handleCreateLink = async (
    name: string,
    url: string,
    icon: string,
    isInternal: boolean,
    categoryId: string
  ) => {
    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        console.error('No auth token found');
        return;
      }

      const response = await fetch(`${baseURL}/navigation/links`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name,
          url: url,
          icon: icon || null,
          is_internal: isInternal,
          category_id: categoryId,
          sort_order: links.length
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      toast({
        description: language === 'en' ? "Link created successfully." : "链接创建成功。",
      });

      if (selectedCategory?.id === categoryId) {
        setLinks([...links, data]);
      }
    } catch (error) {
      console.error('Error creating link:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to create link." : "创建链接失败。",
      });
    }
  };

  // Handle link editing
  const handleEditLink = async (updatedLink: NavLink) => {
    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        console.error('No auth token found');
        return;
      }

      const response = await fetch(`${baseURL}/navigation/links/${updatedLink.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: updatedLink.name,
          url: updatedLink.url,
          icon: updatedLink.icon || null,
          is_internal: updatedLink.is_internal
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      toast({
        description: language === 'en' ? "Link updated successfully." : "链接更新成功。",
      });

      setLinks(links.map(link =>
        link.id === updatedLink.id ? updatedLink : link
      ));
    } catch (error) {
      console.error('Error updating link:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to update link." : "更新链接失败。",
      });
    }
  };

  // Handle link deletion
  const handleDeleteLink = async (linkId: string) => {
    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        console.error('No auth token found');
        return;
      }

      const response = await fetch(`${baseURL}/navigation/links/${linkId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      toast({
        description: language === 'en' ? "Link deleted successfully." : "链接删除成功。",
      });

      setLinks(links.filter(link => link.id !== linkId));
    } catch (error) {
      console.error('Error deleting link:', error);
      toast({
        variant: "destructive",
        description: language === 'en' ? "Failed to delete link." : "删除链接失败。",
      });
    }
  };

  // Handle submit link to public directory
  const handleSubmitLinkToPublic = async (linkId: string, publicCategoryId: string) => {
    const linkToSubmit = links.find(link => link.id === linkId);
    if (!linkToSubmit) {
      throw new Error('Link not found');
    }

    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      if (!token) {
        throw new Error('No auth token found');
      }

      const response = await fetch(`${baseURL}/navigation/links`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: linkToSubmit.name,
          url: linkToSubmit.url,
          icon: linkToSubmit.icon,
          is_internal: linkToSubmit.is_internal,
          category_id: publicCategoryId,
          status: 'pending',
          is_public: true
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error submitting link to public:', error);
      throw error;
    }
  };

  return {
    categories,
    links,
    selectedCategory,
    setSelectedCategory,
    handleCreateCategory,
    handleDeleteCategory,
    handleCreateLink,
    handleEditLink,
    handleDeleteLink,
    handleSubmitLinkToPublic
  };
};
