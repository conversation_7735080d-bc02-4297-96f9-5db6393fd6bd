import React, { createContext, useContext, useState, useEffect } from "react";
import { getCurrentUser, User as AuthUser } from "@/services/authService";

type Language = "en" | "zh";
type Theme = "light" | "dark";

// 使用 authService 中的 User 类型
type User = AuthUser | null;

interface AppContextType {
  language: Language;
  theme: Theme;
  toggleLanguage: () => void;
  toggleTheme: () => void;
  translations: Record<string, Record<Language, string>>;
  t: (key: string) => string;
  user: User;
  setUser: (user: User) => void;
  isAuthReady: boolean;
  fetchUser: () => Promise<void>; // 添加一个手动获取用户的方法
}

const translationsData = {
  dashboard: {
    en: "Dashboard",
    zh: "控制台",
  },
  urls: {
    en: "URLs",
    zh: "短网址",
  },
  emails: {
    en: "Emails",
    zh: "临时邮箱",
  },
  settings: {
    en: "Settings",
    zh: "设置",
  },
  admin: {
    en: "Admin Panel",
    zh: "管理员面板",
  },
  login: {
    en: "Login",
    zh: "登录",
  },
  register: {
    en: "Register",
    zh: "注册",
  },
  logout: {
    en: "Logout",
    zh: "登出",
  },
  createUrl: {
    en: "Create Short URL",
    zh: "创建短网址",
  },
  createEmail: {
    en: "Create Temp Email",
    zh: "创建临时邮箱",
  },
  urlStats: {
    en: "URL Statistics",
    zh: "网址统计",
  },
  originalUrl: {
    en: "Original URL",
    zh: "原始网址",
  },
  shortUrl: {
    en: "Short URL",
    zh: "短网址",
  },
  copy: {
    en: "Copy",
    zh: "复制",
  },
  copied: {
    en: "Copied!",
    zh: "已复制!",
  },
  clicks: {
    en: "Clicks",
    zh: "点击次数",
  },
  createdAt: {
    en: "Created At",
    zh: "创建时间",
  },
  actions: {
    en: "Actions",
    zh: "操作",
  },
  delete: {
    en: "Delete",
    zh: "删除",
  },
  edit: {
    en: "Edit",
    zh: "编辑",
  },
  emailAddress: {
    en: "Email Address",
    zh: "邮箱地址",
  },
  messages: {
    en: "Messages",
    zh: "邮件",
  },
  subject: {
    en: "Subject",
    zh: "主题",
  },
  sender: {
    en: "Sender",
    zh: "发件人",
  },
  receivedAt: {
    en: "Received At",
    zh: "接收时间",
  },
  view: {
    en: "View",
    zh: "查看",
  },
  home: {
    en: "Home",
    zh: "首页",
  },
  features: {
    en: "Features",
    zh: "功能",
  },
  pricing: {
    en: "Pricing",
    zh: "价格",
  },
  about: {
    en: "About",
    zh: "关于",
  },
  contact: {
    en: "Contact",
    zh: "联系我们",
  },
  urlShortener: {
    en: "URL Shortener",
    zh: "短网址服务",
  },
  tempEmail: {
    en: "Temporary Email",
    zh: "临时邮箱",
  },
  submit: {
    en: "Submit",
    zh: "提交",
  },
  cancel: {
    en: "Cancel",
    zh: "取消",
  },
  save: {
    en: "Save",
    zh: "保存",
  },
  welcome: {
    en: "Welcome to g2.al",
    zh: "欢迎使用 g2.al",
  },
  welcomeDesc: {
    en: "The simplest way to shorten URLs and create temporary email addresses",
    zh: "最简单的短网址和临时邮箱服务",
  },
  getStarted: {
    en: "Get Started",
    zh: "开始使用",
  },
  learnMore: {
    en: "Learn More",
    zh: "了解更多",
  },
  noAccount: {
    en: "Don't have an account?",
    zh: "还没有账号？",
  },
  haveAccount: {
    en: "Already have an account?",
    zh: "已有账号？",
  },
  urlFeature: {
    en: "Shorten and track your URLs",
    zh: "缩短并跟踪您的网址",
  },
  emailFeature: {
    en: "Create disposable email addresses",
    zh: "创建一次性邮箱地址",
  },
  allInOne: {
    en: "All-in-One Toolbox",
    zh: "一站式工具集",
  },
  boostProductivity: {
    en: "Boost Your Productivity",
    zh: "提高您的工作效率",
  },
  simplifyYourLife: {
    en: "Simplify Your Digital Life",
    zh: "简化您的数字生活",
  },
  secureReliable: {
    en: "Secure & Reliable",
    zh: "安全可靠",
  },
  fastEfficient: {
    en: "Fast & Efficient",
    zh: "快速高效",
  },
  userFriendly: {
    en: "User-Friendly Interface",
    zh: "用户友好的界面",
  },
  terms: {
    en: "Terms of Service",
    zh: "服务条款",
  },
  privacy: {
    en: "Privacy Policy",
    zh: "隐私政策",
  },
  copyright: {
    en: "© 2024 URL Stash Vault. All rights reserved.",
    zh: "© 2024 URL Stash Vault. 版权所有。",
  },
  username: {
    en: "Username",
    zh: "用户名",
  },
  email: {
    en: "Email",
    zh: "邮箱",
  },
  password: {
    en: "Password",
    zh: "密码",
  },
  confirmPassword: {
    en: "Confirm Password",
    zh: "确认密码",
  },
  forgotPassword: {
    en: "Forgot Password?",
    zh: "忘记密码？",
  },
  resetPassword: {
    en: "Reset Password",
    zh: "重置密码",
  },
  sendResetLink: {
    en: "Send Reset Link",
    zh: "发送重置链接",
  },
  newPassword: {
    en: "New Password",
    zh: "新密码",
  },
  rememberMe: {
    en: "Remember me",
    zh: "记住我",
  },
  dashboardOverview: {
    en: "Dashboard Overview",
    zh: "仪表盘概览",
  },
  manageUrls: {
    en: "Manage URLs",
    zh: "管理短网址",
  },
  manageEmails: {
    en: "Manage Temp Emails",
    zh: "管理临时邮箱",
  },
  accountSettings: {
    en: "Account Settings",
    zh: "账户设置",
  },
  profile: {
    en: "Profile",
    zh: "个人资料",
  },
  security: {
    en: "Security",
    zh: "安全",
  },
  preferences: {
    en: "Preferences",
    zh: "偏好设置",
  },
  adminDashboard: {
    en: "Admin Dashboard",
    zh: "管理后台",
  },
  userManagement: {
    en: "User Management",
    zh: "用户管理",
  },
  systemSettings: {
    en: "System Settings",
    zh: "系统设置",
  },
  siteAnalytics: {
    en: "Site Analytics",
    zh: "网站分析",
  },
  featureManagement: {
    en: "Feature Management",
    zh: "功能管理",
  },
  emailDomainManagement: {
    en: "Email Domain Management",
    zh: "邮箱域名管理",
  },
  linkModeration: {
    en: "Link Moderation",
    zh: "链接审核",
  },
  navigationManagement: {
    en: "Navigation Management",
    zh: "导航管理",
  },
  addDomain: {
    en: "Add Domain",
    zh: "添加域名",
  },
  domain: {
    en: "Domain",
    zh: "域名",
  },
  status: {
    en: "Status",
    zh: "状态",
  },
  approved: {
    en: "Approved",
    zh: "已批准",
  },
  pending: {
    en: "Pending",
    zh: "待审核",
  },
  manage: {
    en: "Manage",
    zh: "管理",
  },
  approve: {
    en: "Approve",
    zh: "批准",
  },
  reject: {
    en: "Reject",
    zh: "拒绝",
  },
  totalUsers: {
    en: "Total Users",
    zh: "总用户数",
  },
  totalUrls: {
    en: "Total URLs",
    zh: "总短网址数",
  },
  totalEmails: {
    en: "Total Temp Emails",
    zh: "总临时邮箱数",
  },
  totalClicks: {
    en: "Total Clicks",
    zh: "总点击量",
  },
  last7Days: {
    en: "Last 7 Days",
    zh: "最近7天",
  },
  users: {
    en: "Users",
    zh: "用户",
  },
  id: {
    en: "ID",
    zh: "ID",
  },
  roles: {
    en: "Roles",
    zh: "角色",
  },
  isSuperAdmin: {
    en: "Super Admin",
    zh: "超级管理员",
  },
  joinedAt: {
    en: "Joined At",
    zh: "加入时间",
  },
  editUser: {
    en: "Edit User",
    zh: "编辑用户",
  },
  deleteUser: {
    en: "Delete User",
    zh: "删除用户",
  },
  yes: {
    en: "Yes",
    zh: "是",
  },
  no: {
    en: "No",
    zh: "否",
  },
  saveChanges: {
    en: "Save Changes",
    zh: "保存更改",
  },
  confirmDeleteUser: {
    en: "Are you sure you want to delete this user?",
    zh: "您确定要删除此用户吗？",
  },
  thisActionCannotBeUndone: {
    en: "This action cannot be undone.",
    zh: "此操作无法撤销。",
  },
  tempEmailInbox: {
    en: "Temporary Email Inbox",
    zh: "临时邮箱收件箱",
  },
  generateNewEmail: {
    en: "Generate New Email",
    zh: "生成新邮箱",
  },
  yourTempEmail: {
    en: "Your Temporary Email:",
    zh: "您的临时邮箱：",
  },
  noMessagesYet: {
    en: "No messages yet. Refresh to check for new emails.",
    zh: "暂无邮件。刷新以检查新邮件。",
  },
  refreshInbox: {
    en: "Refresh Inbox",
    zh: "刷新收件箱",
  },
  urlShortenerTitle: {
    en: "URL Shortener",
    zh: "短网址生成器",
  },
  enterLongUrl: {
    en: "Enter your long URL here",
    zh: "在此输入您的长网址",
  },
  shorten: {
    en: "Shorten",
    zh: "缩短",
  },
  yourShortUrl: {
    en: "Your Short URL:",
    zh: "您的短网址：",
  },
  customShortCodeOptional: {
    en: "Custom Short Code (Optional)",
    zh: "自定义短码（可选）",
  },
  myUrls: {
    en: "My URLs",
    zh: "我的短网址",
  },
  myEmails: {
    en: "My Temp Emails",
    zh: "我的临时邮箱",
  },
  upTo1Hour: {
    en: "up to 1 hour",
    zh: "最多1小时",
  },
  upTo24Hours: {
    en: "up to 24 hours",
    zh: "最多24小时",
  },
  upTo7Days: {
    en: "up to 7 days",
    zh: "最多7天",
  },
  upTo30Days: {
    en: "up to 30 days",
    zh: "最多30天",
  },
  receiveMessages: {
    en: "Receive and view messages in your inbox",
    zh: "在收件箱中接收和查看邮件",
  },
  tryItNow: {
    en: "Try It Now",
    zh: "立即尝试",
  },
  expirationTime: {
    en: "Expiration Time",
    zh: "过期时间",
  },
  selectExpiration: {
    en: "Select expiration time",
    zh: "选择过期时间",
  },
  hour: {
    en: "1 Hour",
    zh: "1小时",
  },
  day: {
    en: "1 Day",
    zh: "1天",
  },
  week: {
    en: "1 Week",
    zh: "1周",
  },
  month: {
    en: "1 Month",
    zh: "1个月",
  },
  permanent: {
    en: "Permanent",
    zh: "永久",
  },
  permanentRequiresLogin: {
    en: "Permanent (requires login)",
    zh: "永久（需要登录）",
  },
  shortenUrl: {
    en: "Shorten URL",
    zh: "缩短网址",
  },
  qrCode: {
    en: "QR Code",
    zh: "二维码",
  },
  downloadQRCode: {
    en: "Download QR Code",
    zh: "下载二维码",
  },
  scanToVisit: {
    en: "Scan to visit",
    zh: "扫描访问",
  },
  // 首页相关翻译
  heroDescription: {
    en: "The simplest way to manage your digital tools and protect your privacy",
    zh: "最简单的数字工具管理和隐私保护方式",
  },
  featuresDesc: {
    en: "Discover powerful tools designed to simplify your digital life",
    zh: "探索旨在简化您数字生活的强大工具",
  },
  // URL功能翻译
  urlFeature1Title: {
    en: "Custom Short Codes",
    zh: "自定义短码",
  },
  urlFeature1Desc: {
    en: "Create memorable short links with custom codes",
    zh: "使用自定义代码创建易记的短链接",
  },
  urlFeature2Title: {
    en: "Click Analytics",
    zh: "点击分析",
  },
  urlFeature2Desc: {
    en: "Track clicks, locations, and device information",
    zh: "跟踪点击量、位置和设备信息",
  },
  urlFeature3Title: {
    en: "QR Code Generation",
    zh: "二维码生成",
  },
  urlFeature3Desc: {
    en: "Automatically generate QR codes for your links",
    zh: "自动为您的链接生成二维码",
  },
  urlFeature4Title: {
    en: "Link Expiration",
    zh: "链接过期",
  },
  urlFeature4Desc: {
    en: "Set expiration dates for your short links",
    zh: "为您的短链接设置过期日期",
  },
  urlFeature5Title: {
    en: "Batch Operations",
    zh: "批量操作",
  },
  urlFeature5Desc: {
    en: "Create and manage multiple links at once",
    zh: "一次创建和管理多个链接",
  },
  urlFeature6Title: {
    en: "Domain Whitelist",
    zh: "域名白名单",
  },
  urlFeature6Desc: {
    en: "Secure link creation with approved domains",
    zh: "使用已批准域名进行安全链接创建",
  },
  urlExpiration: {
    en: "Link Expiration Options",
    zh: "链接过期选项",
  },
  urlExpirationDesc: {
    en: "Choose how long your links stay active",
    zh: "选择您的链接保持活跃的时间",
  },
  unregisteredUsers: {
    en: "Unregistered Users",
    zh: "未注册用户",
  },
  registeredUsers: {
    en: "Registered Users",
    zh: "注册用户",
  },
  urlExpirationOption1: {
    en: "• 1 Hour",
    zh: "• 1小时",
  },
  urlExpirationOption2: {
    en: "• 1 Day",
    zh: "• 1天",
  },
  urlExpirationOption3: {
    en: "• 1 Week",
    zh: "• 1周",
  },
  urlExpirationOption4: {
    en: "• 1 Month",
    zh: "• 1个月",
  },
  urlExpirationOption5: {
    en: "• Permanent (Login Required)",
    zh: "• 永久（需要登录）",
  },
  // 索引页面翻译
  "index.features.title": {
    en: "Powerful Features",
    zh: "强大功能",
  },
  "index.features.subtitle": {
    en: "Everything you need to manage your digital life",
    zh: "管理您数字生活所需的一切",
  },
  "index.features.new": {
    en: "NEW",
    zh: "新功能",
  },
  "index.features.comingSoon": {
    en: "Coming Soon",
    zh: "即将推出",
  },
  "index.banner.defaultMainTitle": {
    en: "Digital Tools Hub",
    zh: "数字工具中心",
  },
  "index.banner.defaultSubtitle": {
    en: "Your all-in-one solution for digital productivity",
    zh: "您的数字生产力一站式解决方案",
  },
  // 导航相关翻译
  darkMode: {
    en: "Dark Mode",
    zh: "暗色模式",
  },
  lightMode: {
    en: "Light Mode",
    zh: "亮色模式",
  },
  toggleLanguage: {
    en: "切换语言",
    zh: "Switch Language",
  },
  aboutUs: {
    en: "About Us",
    zh: "关于我们",
  },
  learnMoreFeatures: {
    en: "Learn More About Features",
    zh: "了解更多功能",
  },
  // About Us section translations
  aboutUsDesc: {
    en: "We are dedicated to providing simple, efficient, and privacy-focused digital tools that help you manage your online activities while keeping your data secure and private.",
    zh: "我们致力于提供简单、高效且注重隐私的数字工具，帮助您管理在线活动，同时确保您的数据安全和隐私。",
  },
  ourMissionDesc: {
    en: "To empower users with tools that simplify their digital experience while maintaining the highest standards of privacy and security. We believe technology should work for you, not against you.",
    zh: "为用户提供简化数字体验的工具，同时保持最高的隐私和安全标准。我们相信技术应该为您服务，而不是与您作对。",
  },
  privacyFocus: {
    en: "Privacy First",
    zh: "隐私优先",
  },
  privacyFocusDesc: {
    en: "Your privacy is our top priority. We don't track, store, or sell your personal data. All tools are designed with privacy-by-design principles to ensure your information stays yours.",
    zh: "您的隐私是我们的首要任务。我们不跟踪、存储或出售您的个人数据。所有工具都采用隐私设计原则，确保您的信息属于您自己。",
  },
  joinUs: {
    en: "Join Our Community",
    zh: "加入我们的社区",
  },
  joinUsDesc: {
    en: "Start using our tools today and experience the difference privacy-focused technology can make in your digital life.",
    zh: "立即开始使用我们的工具，体验注重隐私的技术在您数字生活中带来的不同。",
  },
  signUp: {
    en: "Sign Up Free",
    zh: "免费注册",
  },
  // Ready to Start section translations
  readyToStart: {
    en: "Ready to Get Started?",
    zh: "准备开始了吗？",
  },
  joinThousands: {
    en: "Join thousands of users who trust our platform for their digital productivity needs. Start your journey today with our comprehensive suite of tools.",
    zh: "加入数千名信任我们平台满足数字生产力需求的用户。立即开始您的旅程，使用我们全面的工具套件。",
  },
  exploreFeatures: {
    en: "Explore Features",
    zh: "探索功能",
  },
  // Forgot password translations
  forgotPasswordTitle: {
    en: "Forgot Password",
    zh: "忘记密码",
  },
  forgotPasswordDesc: {
    en: "Enter your email address and we'll send you a link to reset your password.",
    zh: "输入您的邮箱地址，我们将向您发送重置密码的链接。",
  },
  sendResetEmail: {
    en: "Send Reset Email",
    zh: "发送重置邮件",
  },
  backToLogin: {
    en: "Back to Login",
    zh: "返回登录",
  },
  resetEmailSent: {
    en: "Reset email sent",
    zh: "重置邮件已发送",
  },
  resetEmailSentDesc: {
    en: "If your email exists in our system, you will receive a password reset link.",
    zh: "如果您的邮箱存在于我们的系统中，您将收到密码重置链接。",
  },
  failedToSendResetEmail: {
    en: "Failed to send reset email",
    zh: "发送重置邮件失败",
  },
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>(() => {
    const savedLanguage = localStorage.getItem("language");
    return (savedLanguage as Language) || (navigator.language.startsWith("zh") ? "zh" : "en");
  });

  const [theme, setTheme] = useState<Theme>(() => {
    const savedTheme = localStorage.getItem("theme");
    return (savedTheme as Theme) || (window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light");
  });

  const [user, setUser] = useState<User>(null);
  const [isAuthReady, setIsAuthReady] = useState(false);

  const fetchUser = async () => {
    try {
      console.log('Attempting to fetch current user via authService...');
      const currentUser = await getCurrentUser();
      console.log('Fetched user:', currentUser);
      setUser(currentUser);
    } catch (e) {
      console.error('Error fetching user in AppContext:', e);
      setUser(null);
    } finally {
      setIsAuthReady(true);
    }
  };

  useEffect(() => {
    fetchUser();

    // Listen to custom auth events if implemented in authService, or use localStorage events
    // For simplicity, we'll rely on components to call fetchUser after login/logout actions for now.
    // Or, we can listen to localStorage changes for 'auth_token' or 'authToken'
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'auth_token' || event.key === 'authToken') {
        console.log('Auth token changed, re-fetching user...');
        fetchUser();
      }
    };

    // Custom event listener for auth state changes
    const handleAuthStateChange = () => {
      console.log('Auth state change event received, re-fetching user...');
      fetchUser();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('authStateChanged', handleAuthStateChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('authStateChanged', handleAuthStateChange);
    };
  }, []);

  useEffect(() => {
    localStorage.setItem("language", language);
  }, [language]);

  useEffect(() => {
    localStorage.setItem("theme", theme);
    document.documentElement.classList.toggle("dark", theme === "dark");
  }, [theme]);

  const toggleLanguage = () => {
    setLanguage((prev) => (prev === "en" ? "zh" : "en"));
  };

  const toggleTheme = () => {
    setTheme((prev) => (prev === "light" ? "dark" : "light"));
  };

  const t = (key: string) => {
    return translationsData[key]?.[language] || key;
  };

  return (
    <AppContext.Provider
      value={{
        language,
        theme,
        toggleLanguage,
        toggleTheme,
        translations: translationsData,
        t,
        user,
        setUser,
        isAuthReady,
        fetchUser
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};
