import { isUsingSupabase, isUsingGoBackend, getBackendConfig } from '@/config/backend';
import { authService as goAuthService, memoService as goMemoService, shortUrlService as goShortUrlService } from './api';

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }
  
  try {
    const { supabase } = await import('@/integrations/supabase/client');
    return supabase;
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

// 统一的用户接口
export interface UnifiedUser {
  id: string;
  email: string;
  created_at: string;
  updated_at?: string;
}

// 统一的认证响应接口
export interface UnifiedAuthResponse {
  user: UnifiedUser;
  token?: string; // Go 后端返回 JWT token，Supabase 使用 session
  session?: object; // Supabase session 对象
}

// 统一的 Memo 接口
export interface UnifiedMemo {
  id: string;
  user_id: string;
  content: string;
  tags?: string[] | null;
  reminder_date?: string | null;
  created_at: string;
  updated_at: string;
}

// 统一的 ShortURL 接口
export interface UnifiedShortURL {
  id: string;
  user_id?: string | null;
  original_url: string;
  short_code: string;
  full_short_url?: string;
  clicks: number;
  expires_at?: string | null;
  created_at: string;
}

// 统一认证服务
export class UnifiedAuthService {
  async register(email: string, password: string): Promise<UnifiedAuthResponse> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      
      if (error) throw error;
      if (!data.user) throw new Error('注册失败');
      
      return {
        user: {
          id: data.user.id,
          email: data.user.email!,
          created_at: data.user.created_at,
        },
        session: data.session,
      };
    } else {
      const response = await goAuthService.register({ email, password });
      const { token, user } = response.data;
      
      // 存储 JWT token
      localStorage.setItem('authToken', token);
      
      return {
        user: {
          id: user.id.toString(),
          email: user.email,
          created_at: user.created_at,
          updated_at: user.updated_at,
        },
        token,
      };
    }
  }

  async login(email: string, password: string): Promise<UnifiedAuthResponse> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) throw error;
      if (!data.user) throw new Error('登录失败');
      
      return {
        user: {
          id: data.user.id,
          email: data.user.email!,
          created_at: data.user.created_at,
        },
        session: data.session,
      };
    } else {
      const response = await goAuthService.login({ email, password });
      const { token, user } = response.data;
      
      // 存储 JWT token
      localStorage.setItem('authToken', token);
      
      return {
        user: {
          id: user.id.toString(),
          email: user.email,
          created_at: user.created_at,
          updated_at: user.updated_at,
        },
        token,
      };
    }
  }

  async logout(): Promise<void> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } else {
      // 清除 JWT token
      localStorage.removeItem('authToken');
    }
  }

  async getCurrentUser(): Promise<UnifiedUser | null> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) return null;
      
      return {
        id: user.id,
        email: user.email!,
        created_at: user.created_at,
      };
    } else {
      try {
        const response = await goAuthService.getMe();
        const user = response.data;
        
        return {
          id: user.id.toString(),
          email: user.email,
          created_at: user.created_at,
          updated_at: user.updated_at,
        };
      } catch (error) {
        console.error('获取当前用户失败:', error);
        return null;
      }
    }
  }
}

// 统一 Memo 服务
export class UnifiedMemoService {
  async createMemo(content: string, tags?: string[], reminderDate?: string): Promise<UnifiedMemo> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase
        .from('memos')
        .insert({
          content,
          tags,
          reminder_date: reminderDate,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      return {
        id: data.id,
        user_id: data.user_id,
        content: data.content,
        tags: data.tags,
        reminder_date: data.reminder_date,
        created_at: data.created_at,
        updated_at: data.updated_at,
      };
    } else {
      const response = await goMemoService.createMemo({
        content,
        tags,
        reminder_date: reminderDate,
      });
      
      const memo = response.data;
      return {
        id: memo.id,
        user_id: memo.user_id.toString(),
        content: memo.content,
        tags: memo.tags,
        reminder_date: memo.reminder_date,
        created_at: memo.created_at,
        updated_at: memo.updated_at,
      };
    }
  }

  async listMemos(): Promise<UnifiedMemo[]> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase
        .from('memos')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return data.map(memo => ({
        id: memo.id,
        user_id: memo.user_id,
        content: memo.content,
        tags: memo.tags,
        reminder_date: memo.reminder_date,
        created_at: memo.created_at,
        updated_at: memo.updated_at,
      }));
    } else {
      const response = await goMemoService.listMemos();
      
      return response.data.map(memo => ({
        id: memo.id,
        user_id: memo.user_id.toString(),
        content: memo.content,
        tags: memo.tags,
        reminder_date: memo.reminder_date,
        created_at: memo.created_at,
        updated_at: memo.updated_at,
      }));
    }
  }

  async updateMemo(id: string, updates: Partial<Pick<UnifiedMemo, 'content' | 'tags' | 'reminder_date'>>): Promise<UnifiedMemo> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase
        .from('memos')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      
      return {
        id: data.id,
        user_id: data.user_id,
        content: data.content,
        tags: data.tags,
        reminder_date: data.reminder_date,
        created_at: data.created_at,
        updated_at: data.updated_at,
      };
    } else {
      const response = await goMemoService.updateMemo(id, updates);
      
      const memo = response.data;
      return {
        id: memo.id,
        user_id: memo.user_id.toString(),
        content: memo.content,
        tags: memo.tags,
        reminder_date: memo.reminder_date,
        created_at: memo.created_at,
        updated_at: memo.updated_at,
      };
    }
  }

  async deleteMemo(id: string): Promise<void> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase
        .from('memos')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    } else {
      await goMemoService.deleteMemo(id);
    }
  }
}

// 统一 ShortURL 服务
export class UnifiedShortUrlService {
  async createShortUrl(originalUrl: string, shortCode?: string, expiresAt?: string): Promise<UnifiedShortURL> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase
        .from('short_urls')
        .insert({
          original_url: originalUrl,
          short_code: shortCode,
          expires_at: expiresAt,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      return {
        id: data.id,
        user_id: data.user_id,
        original_url: data.original_url,
        short_code: data.short_code,
        full_short_url: data.full_short_url,
        clicks: data.clicks,
        expires_at: data.expires_at,
        created_at: data.created_at,
      };
    } else {
      const response = await goShortUrlService.createShortUrl({
        original_url: originalUrl,
        short_code: shortCode,
        expires_at: expiresAt,
      });
      
      const shortUrl = response.data;
      return {
        id: shortUrl.id,
        user_id: shortUrl.user_id?.toString() || null,
        original_url: shortUrl.original_url,
        short_code: shortUrl.short_code,
        full_short_url: shortUrl.full_short_url,
        clicks: shortUrl.clicks,
        expires_at: shortUrl.expires_at,
        created_at: shortUrl.created_at,
      };
    }
  }

  async listShortUrls(): Promise<UnifiedShortURL[]> {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase
        .from('short_urls')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return data.map(shortUrl => ({
        id: shortUrl.id,
        user_id: shortUrl.user_id,
        original_url: shortUrl.original_url,
        short_code: shortUrl.short_code,
        full_short_url: shortUrl.full_short_url,
        clicks: shortUrl.clicks,
        expires_at: shortUrl.expires_at,
        created_at: shortUrl.created_at,
      }));
    } else {
      const response = await goShortUrlService.listShortUrls();
      
      return response.data.map(shortUrl => ({
        id: shortUrl.id,
        user_id: shortUrl.user_id?.toString() || null,
        original_url: shortUrl.original_url,
        short_code: shortUrl.short_code,
        full_short_url: shortUrl.full_short_url,
        clicks: shortUrl.clicks,
        expires_at: shortUrl.expires_at,
        created_at: shortUrl.created_at,
      }));
    }
  }
}

// 导出统一服务实例
export const unifiedAuthService = new UnifiedAuthService();
export const unifiedMemoService = new UnifiedMemoService();
export const unifiedShortUrlService = new UnifiedShortUrlService(); 