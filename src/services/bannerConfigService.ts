import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import { BannerConfig, DisplayStyle } from '@/hooks/useBannerConfig';

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }
  
  try {
    const { supabase } = await import('@/integrations/supabase/client');
    return supabase;
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

// Go 后端 API 客户端获取函数
async function getGoApiClient() {
  const config = getBackendConfig();
  const baseURL = config.goBackend?.baseUrl;
  
  return {
    async get(path: string) {
      const response = await fetch(`${baseURL}${path}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    },
    
    async post(path: string, data: unknown) {
      const response = await fetch(`${baseURL}${path}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    },
    
    async put(path: string, data: unknown) {
      const response = await fetch(`${baseURL}${path}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    }
  };
}

export const fetchBannerConfig = async (): Promise<BannerConfig | null> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase
        .from('banner_config')
        .select('*')
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching banner config:', error);
        throw error;
      }

      return data as BannerConfig;
    } else {
      // Go 后端实现 - 返回默认配置，因为Go后端可能没有这个功能
      return {
        id: 'default',
        first_text: 'URL Stash Vault',
        first_gradient: 'from-primary to-brand-500',
        second_text: '一站式工具集',
        second_gradient: 'from-accent1-400 to-brand-500',
        height: 250,
        spacing: 2,
        animation_speed: 0.8,
        display_style: 'default' as DisplayStyle,
        lines: [],
      };
    }
  } catch (err) {
    console.error('Error in fetchBannerConfig:', err);
    throw err;
  }
};

export const saveBannerConfig = async (values: BannerConfig): Promise<BannerConfig | null> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      
      // 准备保存的数据，移除 lines 字段中的不兼容类型
      const configToSave = {
        ...values,
        lines: values.lines ? JSON.stringify(values.lines) : null,
      };
      
      let response;
      
      if (values.id) {
        // Update existing config
        response = await supabase
          .from('banner_config')
          .update(configToSave)
          .eq('id', values.id)
          .select();
      } else {
        // Insert new config
        response = await supabase
          .from('banner_config')
          .insert(configToSave)
          .select();
      }
      
      const { data, error } = response;
      
      if (error) {
        console.error('Error saving banner config:', error);
        throw error;
      }
      
      if (data && data.length > 0) {
        return data[0] as BannerConfig;
      }
      
      return null;
    } else {
      // Go 后端实现 - 目前返回传入的值，因为Go后端可能没有这个功能
      console.log('Banner config saved (Go backend):', values);
      return values;
    }
  } catch (err) {
    console.error('Error in saveBannerConfig:', err);
    throw err;
  }
}; 