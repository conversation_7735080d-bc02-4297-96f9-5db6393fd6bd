import { isUsingSupabase, getBackendConfig } from '@/config/backend';

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }

  try {
    const { getSupabaseClient } = await import('@/integrations/supabase/client');
    return getSupabaseClient();
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

// Go 后端 API 客户端获取函数
async function getGoApiClient() {
  const config = getBackendConfig();
  const baseURL = config.goBackend?.baseUrl;

  return {
    async get(path: string, options: { headers?: Record<string, string> } = {}) {
      const token = localStorage.getItem('auth_token');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      };

      const response = await fetch(`${baseURL}${path}`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    },

    async post(path: string, data?: unknown, options: { headers?: Record<string, string> } = {}) {
      const token = localStorage.getItem('auth_token');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      };

      const response = await fetch(`${baseURL}${path}`, {
        method: 'POST',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    }
  };
}

// 用户信息接口
export interface User {
  id: string | number;
  username?: string;
  email: string;
  email_verified?: boolean;
  avatar_url?: string;
  is_super_admin?: boolean;
  roles?: string[];
  created_at?: string;
  updated_at?: string;
}

// 认证响应接口
export interface AuthResponse {
  user: User;
  token?: string;
  session?: unknown;
}

// 登录请求
export interface LoginRequest {
  email: string;
  password: string;
}

// 注册请求
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

// OAuth提供商
export interface OAuthProvider {
  id: string;
  provider_name: string;
  client_id: string;
  redirect_url: string;
  scopes: string[];
  enabled: boolean;
}

// 登录
export const login = async (credentials: LoginRequest): Promise<AuthResponse> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.signInWithPassword(credentials);

      if (error) throw error;

      return {
        user: {
          id: data.user?.id || '',
          email: data.user?.email || '',
        },
        session: data.session,
      };
    } else {
      const apiClient = await getGoApiClient();
      const response = await apiClient.post('/auth/login', credentials);

      // 保存token到localStorage
      if (response.token) {
        localStorage.setItem('auth_token', response.token);
      }

      return {
        user: response.user,
        token: response.token,
      };
    }
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

// 注册
export const register = async (userData: RegisterRequest): Promise<AuthResponse> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            username: userData.username,
          },
        },
      });

      if (error) throw error;

      return {
        user: {
          id: data.user?.id || '',
          email: data.user?.email || '',
          username: userData.username,
        },
        session: data.session,
      };
    } else {
      const apiClient = await getGoApiClient();
      const response = await apiClient.post('/auth/register', userData);

      // 保存token到localStorage
      if (response.token) {
        localStorage.setItem('auth_token', response.token);
      }

      return {
        user: response.user,
        token: response.token,
      };
    }
  } catch (error) {
    console.error('Register error:', error);
    throw error;
  }
};

// 登出
export const logout = async (): Promise<void> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } else {
      // 调用Go后端登出API
      const apiClient = await getGoApiClient();
      try {
        await apiClient.post('/auth/logout');
      } catch (error) {
        console.error('Backend logout API failed:', error);
        // 即使后端API失败，也要清除本地token
      }
      localStorage.removeItem('auth_token');
      localStorage.removeItem('authToken'); // 也清除authToken
    }
  } catch (error) {
    console.error('Logout error:', error);
    // 即使出错也要清除本地token
    localStorage.removeItem('auth_token');
    localStorage.removeItem('authToken'); // 也清除authToken
    throw error;
  }
};

// 获取当前用户
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error) throw error;

      return user ? {
        id: user.id,
        email: user.email || '',
        username: user.user_metadata?.username,
      } : null;
    } else {
      const token = localStorage.getItem('auth_token');
      if (!token) return null;

      const apiClient = await getGoApiClient();
      const response = await apiClient.get('/auth/me');

      return response;
    }
  } catch (error) {
    console.error('Get current user error:', error);
    // 如果token无效，清除它
    if (!isUsingSupabase()) {
      localStorage.removeItem('auth_token');
    }
    return null;
  }
};

// 获取OAuth提供商
export const getOAuthProviders = async (): Promise<OAuthProvider[]> => {
  try {
    if (isUsingSupabase()) {
      // Supabase的OAuth提供商是固定的
      return [
        {
          id: 'github',
          provider_name: 'github',
          client_id: '',
          redirect_url: '',
          scopes: [],
          enabled: true,
        },
        {
          id: 'google',
          provider_name: 'google',
          client_id: '',
          redirect_url: '',
          scopes: [],
          enabled: true,
        },
      ];
    } else {
      const apiClient = await getGoApiClient();
      const response = await apiClient.get('/auth/oauth/providers');
      return response.providers || [];
    }
  } catch (error) {
    console.error('Get OAuth providers error:', error);
    return [];
  }
};

// 发送验证邮件
export const sendVerificationEmail = async (): Promise<void> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: '', // 需要传入邮箱
      });
      if (error) throw error;
    } else {
      const apiClient = await getGoApiClient();
      await apiClient.post('/auth/send-verification');
    }
  } catch (error) {
    console.error('Send verification email error:', error);
    throw error;
  }
};

// 验证邮箱
export const verifyEmail = async (token: string): Promise<void> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: 'email',
      });
      if (error) throw error;
    } else {
      const apiClient = await getGoApiClient();
      await apiClient.post('/auth/verify-email', { token });
    }
  } catch (error) {
    console.error('Verify email error:', error);
    throw error;
  }
};

// 忘记密码
export const forgotPassword = async (email: string): Promise<void> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      if (error) throw error;
    } else {
      const apiClient = await getGoApiClient();
      await apiClient.post('/auth/forgot-password', { email });
    }
  } catch (error) {
    console.error('Forgot password error:', error);
    throw error;
  }
};

// 重置密码
export const resetPassword = async (token: string, newPassword: string): Promise<void> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });
      if (error) throw error;
    } else {
      const apiClient = await getGoApiClient();
      await apiClient.post('/auth/reset-password', { token, new_password: newPassword });
    }
  } catch (error) {
    console.error('Reset password error:', error);
    throw error;
  }
};