import { isUsingSupabase, isUsingGoBackend } from '@/config/backend';
import { DomainData } from '@/types/domains';
import { extractDomain } from '@/utils/urlUtils';

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }
  
  try {
    const supabaseModule = await import('../integrations/supabase/client');
    return supabaseModule.supabase;
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

// Go 后端 API 客户端
async function getGoApiClient() {
  if (!isUsingGoBackend()) {
    throw new Error('当前未配置使用 Go 后端');
  }
  
  try {
    const apiModule = await import('./api');
    return apiModule.default; // 使用默认导出的 apiClient
  } catch (error) {
    console.error('Go API 客户端导入失败:', error);
    throw error;
  }
}

export async function fetchAllDomains(): Promise<DomainData[]> {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    const { data, error } = await supabase
      .from('domain_whitelist')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data as DomainData[];
  } else {
    // Go 后端实现
    const apiClient = await getGoApiClient();
    const response = await apiClient.get('/domains');
    return response.data;
  }
}

export async function submitDomainForApproval(domain: string, userId?: string): Promise<DomainData> {
  let processedDomain = domain.trim();
  
  // Extract domain if full URL was provided
  if (processedDomain.includes('://') || processedDomain.includes('www.')) {
    processedDomain = extractDomain(processedDomain) || '';
  }
  
  if (!processedDomain) {
    throw new Error('Invalid domain format');
  }

  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    const { data, error } = await supabase
      .from('domain_whitelist')
      .insert({ 
        domain: processedDomain, 
        approved: false,
        user_id: userId
      })
      .select()
      .single();
    
    if (error) throw error;
    return data as DomainData;
  } else {
    // Go 后端实现
    const apiClient = await getGoApiClient();
    const response = await apiClient.post('/domains', {
      domain: processedDomain,
      approved: false,
      user_id: userId
    });
    return response.data;
  }
}

export async function updateDomainApproval(id: string, approved: boolean): Promise<boolean> {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    const { error } = await supabase
      .from('domain_whitelist')
      .update({ approved })
      .eq('id', id);
    
    if (error) throw error;
    return true;
  } else {
    // Go 后端实现
    const apiClient = await getGoApiClient();
    await apiClient.put(`/domains/${id}`, { approved });
    return true;
  }
}

export async function deleteDomainById(id: string): Promise<boolean> {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    const { error } = await supabase
      .from('domain_whitelist')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
    return true;
  } else {
    // Go 后端实现
    const apiClient = await getGoApiClient();
    await apiClient.delete(`/domains/${id}`);
    return true;
  }
}

export async function seedDefaultDomains(): Promise<void> {
  const defaultDomains = [
    'google.com', 'github.com', 'microsoft.com', 
    'apple.com', 'amazon.com', 'facebook.com', 
    'twitter.com', 'linkedin.com', 'youtube.com'
  ];
  
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    await Promise.all(
      defaultDomains.map(domain => 
        supabase
          .from('domain_whitelist')
          .insert({ domain, approved: true })
      )
    );
  } else {
    // Go 后端实现
    const apiClient = await getGoApiClient();
    await Promise.all(
      defaultDomains.map(domain =>
        apiClient.post('/domains', { domain, approved: true })
      )
    );
  }
}
