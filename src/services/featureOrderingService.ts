import { isUsingSupabase, getBackendConfig } from '@/config/backend';

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }

  try {
    const { supabase } = await import('@/integrations/supabase/client');
    return supabase;
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

export interface FeatureConfig {
  id: string;
  name: { en: string; zh: string };
  icon: string;
  visible: boolean;
}

export const fetchFeatureOrdering = async (userId: string | null, componentType: string): Promise<FeatureConfig[]> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();

      let query = supabase
        .from('feature_ordering')
        .select('*')
        .eq('component_type', componentType);

      if (userId) {
        query = query.eq('user_id', userId);
      } else {
        query = query.filter('user_id', 'is', null);
      }

      const { data, error } = await query.maybeSingle();

      if (error) {
        console.error(`Error fetching ${componentType} features:`, error);
        return [];
      }

      if (data && data.components_config) {
        return data.components_config as FeatureConfig[];
      }

      return [];
    } else {
      // Go 后端实现
      try {
        const config = getBackendConfig();
        const baseURL = config.goBackend?.baseUrl;
        const token = localStorage.getItem('authToken');

        if (!token) {
          console.warn('No auth token found, returning default features');
          return getDefaultFeatures(componentType);
        }

        const params = new URLSearchParams();
        params.append('component_type', componentType);
        if (userId) {
          params.append('user_id', userId);
        }

        const response = await fetch(`${baseURL}/features/ordering?${params.toString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          console.warn(`Failed to fetch feature ordering: ${response.status}`);
          return getDefaultFeatures(componentType);
        }

        const data = await response.json();
        return data.components_config || getDefaultFeatures(componentType);
      } catch (error) {
        console.error('Error fetching feature ordering from Go backend:', error);
        return getDefaultFeatures(componentType);
      }
    }
  } catch (err) {
    console.error('Error in fetchFeatureOrdering:', err);
    return getDefaultFeatures(componentType);
  }
};

export const saveFeatureOrdering = async (
  userId: string | null,
  componentType: string,
  features: FeatureConfig[]
): Promise<void> => {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();

      let query = supabase
        .from('feature_ordering')
        .select('id')
        .eq('component_type', componentType);

      if (userId) {
        query = query.eq('user_id', userId);
      } else {
        query = query.filter('user_id', 'is', null);
      }

      const { data: existingConfig, error: fetchError } = await query.maybeSingle();

      if (fetchError) {
        console.error('Fetch error:', fetchError);
        throw fetchError;
      }

      if (existingConfig) {
        const { error: updateError } = await supabase
          .from('feature_ordering')
          .update({
            components_config: features as unknown,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingConfig.id);

        if (updateError) {
          console.error('Update error:', updateError);
          throw updateError;
        }
      } else {
        const { error: insertError } = await supabase
          .from('feature_ordering')
          .insert({
            user_id: userId,
            component_type: componentType,
            components_config: features as unknown,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Insert error:', insertError);
          throw insertError;
        }
      }
    } else {
      // Go 后端实现
      try {
        const config = getBackendConfig();
        const baseURL = config.goBackend?.baseUrl;
        const token = localStorage.getItem('authToken');

        if (!token) {
          console.warn('No auth token found, cannot save feature ordering');
          return;
        }

        const response = await fetch(`${baseURL}/features/ordering`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: userId,
            component_type: componentType,
            components_config: features
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to save feature ordering: ${response.status}`);
        }

        console.log(`Successfully saved ${componentType} features for user ${userId}`);
      } catch (error) {
        console.error('Error saving feature ordering to Go backend:', error);
        throw error;
      }
    }
  } catch (err) {
    console.error('Error in saveFeatureOrdering:', err);
    throw err;
  }
};

function getDefaultFeatures(componentType: string): FeatureConfig[] {
  if (componentType === 'dashboard') {
    return [
      {
        id: 'url-shortener',
        name: { en: 'URL Shortener', zh: '缩短网址' },
        icon: 'link',
        visible: true
      },
      {
        id: 'temp-email',
        name: { en: 'Temporary Email', zh: '临时邮箱' },
        icon: 'mail',
        visible: true
      },
      {
        id: 'navigation',
        name: { en: 'My Navigation', zh: '我的导航' },
        icon: 'compass',
        visible: true
      },
      {
        id: 'features-order',
        name: { en: 'Dashboard Layout', zh: '控制台布局' },
        icon: 'layout',
        visible: true
      }
    ];
  }

  if (componentType === 'banner_features') {
    return [
      {
        id: 'short-url',
        name: { en: 'Short URL', zh: '短网址' },
        icon: 'link',
        visible: true
      },
      {
        id: 'temp-email',
        name: { en: 'Temp Email', zh: '临时邮箱' },
        icon: 'mail',
        visible: true
      }
    ];
  }

  return [];
}