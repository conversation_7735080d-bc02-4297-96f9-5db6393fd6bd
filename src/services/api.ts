import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_GO_BACKEND_URL || 'http://localhost:8080/api/v1'; // 从环境变量或默认值获取后端URL

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器，用于在每个请求中附加 JWT token
apiClient.interceptors.request.use(
  (config) => {
    // 假设 token 存储在 localStorage 中
    const token = localStorage.getItem('authToken'); 
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 (可选，用于统一处理错误或 token 失效等)
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      // 例如：token 失效，清除 token 并重定向到登录页
      localStorage.removeItem('authToken');
      // window.location.href = '/login'; // 或者使用 Vue Router 等导航
      console.error('Unauthorized, redirecting to login...');
    }
    return Promise.reject(error);
  }
);

// --- Auth Types (matching Go backend DTOs) ---
export interface RegisterUserPayload {
  username: string;
  email: string;
  password: string;
}

export interface LoginUserPayload {
  email: string;
  password: string;
}

export interface UserAuthResponse {
  id: number;
  email: string;
  is_super_admin: boolean;
  roles: string[];
  created_at: string;
  updated_at: string;
}

export interface LoginSuccessResponse {
  token: string;
  user: UserAuthResponse;
}
// --- End Auth Types ---

// 用户认证服务
export const authService = {
  register: (data: RegisterUserPayload) => apiClient.post('/auth/register', data),
  login: (data: LoginUserPayload) => apiClient.post<LoginSuccessResponse>('/auth/login', data),
  logout: () => apiClient.post('/auth/logout'),
  getMe: () => apiClient.get<UserAuthResponse>('/auth/me'),
  
  // OAuth methods
  getOAuthProviders: () => apiClient.get('/auth/oauth/providers'),
  getOAuthAuthURL: (provider: string, state: string) => 
    apiClient.get(`/auth/oauth/${provider}/auth?state=${state}`),
  handleOAuthCallback: (provider: string, code: string, state: string) =>
    apiClient.get(`/auth/oauth/${provider}/callback?code=${code}&state=${state}`),
  linkOAuthAccount: (data: { token: string; username?: string; link_existing: boolean }) =>
    apiClient.post('/auth/oauth/link', data),
  getUserOAuthAccounts: () => apiClient.get('/auth/oauth/accounts'),
  unlinkOAuthAccount: (provider: string) => apiClient.delete(`/auth/oauth/${provider}`),
};

// Memo 服务示例
export interface Memo {
  id: string;
  user_id: number;
  content: string;
  tags?: string[] | null; // Assuming tags are stored as an array of strings or null
  reminder_date?: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateMemoPayload {
  content: string;
  tags?: string[];
  reminder_date?: string | null; // RFC3339 format
}

export interface UpdateMemoPayload {
  content?: string;
  tags?: string[];
  reminder_date?: string | null; // RFC3339 format, or empty string to clear
}

export const memoService = {
  createMemo: (data: CreateMemoPayload) => apiClient.post<Memo>('/memos', data),
  listMemos: () => apiClient.get<Memo[]>('/memos'),
  getMemo: (id: string) => apiClient.get<Memo>(`/memos/${id}`),
  updateMemo: (id: string, data: UpdateMemoPayload) => apiClient.put<Memo>(`/memos/${id}`, data),
  deleteMemo: (id: string) => apiClient.delete(`/memos/${id}`),
};

// ShortURL 服务示例 (可以根据需要扩展)
export interface ShortURLResponseFromAPI { // Renamed to avoid conflict if you have another ShortURL type
    id: string;
    user_id?: number | null;
    original_url: string;
    short_code: string;
    full_short_url: string;
    clicks: number;
    expires_at?: string | null;
    created_at: string;
}

export const shortUrlService = {
  createShortUrl: (data: { original_url: string; short_code?: string, expires_at?: string }) => 
    apiClient.post<ShortURLResponseFromAPI>('/shorturls', data),
  listShortUrls: () => apiClient.get<ShortURLResponseFromAPI[]>('/shorturls'),
  getShortUrlDetails: (shortCode: string) => apiClient.get<ShortURLResponseFromAPI>(`/shorturls/${shortCode}`),
  // Redirect is handled by direct navigation: GET /s/:shortCode (not an API call returning JSON)
};

// Admin API methods
export const adminService = {
  // 获取用户列表
  async getUsers(page: number = 1, limit: number = 10, search?: string) {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    if (search) {
      params.append('search', search);
    }
    
    return apiClient.get(`/admin/users?${params.toString()}`);
  },

  // 更新用户信息
  async updateUser(userId: number, updates: {
    username?: string;
    email?: string;
    email_verified?: boolean;
    is_super_admin?: boolean;
  }) {
    return apiClient.put(`/admin/users/${userId}`, updates);
  },

  // 删除用户
  async deleteUser(userId: number) {
    return apiClient.delete(`/admin/users/${userId}`);
  },

  // 获取用户统计
  async getUserStats() {
    return apiClient.get('/admin/users/stats');
  },
};

export default apiClient; 