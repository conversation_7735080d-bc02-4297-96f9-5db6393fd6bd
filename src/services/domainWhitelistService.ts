import { isUsingSupabase, getBackendConfig } from '@/config/backend';
import { DomainData } from '@/types/domains';

// Supabase 客户端获取函数
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    throw new Error('当前未配置使用 Supabase');
  }

  try {
    const { supabase } = await import('@/integrations/supabase/client');
    return supabase;
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

// Go 后端 API 客户端获取函数
async function getGoApiClient() {
  const config = getBackendConfig();
  const baseURL = config.goBackend?.baseUrl;

  return {
    async get(path: string, options: { headers?: Record<string, string> } = {}) {
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      };

      const response = await fetch(`${baseURL}${path}`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    },

    async post(path: string, data?: unknown, options: { headers?: Record<string, string> } = {}) {
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      };

      const response = await fetch(`${baseURL}${path}`, {
        method: 'POST',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    },

    async put(path: string, data?: unknown, options: { headers?: Record<string, string> } = {}) {
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      };

      const response = await fetch(`${baseURL}${path}`, {
        method: 'PUT',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    },

    async delete(path: string, options: { headers?: Record<string, string> } = {}) {
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      };

      const response = await fetch(`${baseURL}${path}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    }
  };
}

/**
 * Extracts the main domain from a subdomain
 */
function extractMainDomain(domain: string): string {
  // Remove protocol if present
  let cleanDomain = domain.replace(/^https?:\/\//, '');

  // Remove path, query string, and fragment
  cleanDomain = cleanDomain.split('/')[0];

  // Remove www. if present
  cleanDomain = cleanDomain.replace(/^www\./, '');

  // Extract the main domain (last two parts)
  const parts = cleanDomain.split('.');
  if (parts.length > 2) {
    // Check for country codes or known TLDs with subdomains
    const lastPart = parts[parts.length - 1];
    const secondLastPart = parts[parts.length - 2];

    // Common country codes or TLDs with subdomains (e.g., .co.uk, .com.au)
    const specialTLDs = ['uk', 'au', 'br', 'jp', 'cn'];

    if (lastPart.length === 2 && specialTLDs.includes(lastPart)) {
      // For domains like example.co.uk, return co.uk
      return parts.slice(parts.length - 3).join('.');
    } else {
      // For normal domains like sub.example.com, return example.com
      return parts.slice(parts.length - 2).join('.');
    }
  }

  return cleanDomain;
}

/**
 * Fetches all domains from the database or a filtered list.
 * For Go backend, assumes an endpoint like /api/v1/domains/whitelist
 * which can optionally be filtered by query parameters (e.g., ?approved=true)
 */
export async function fetchDomainWhitelist(filters?: { approved?: boolean }): Promise<DomainData[]> {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      let query = supabase.from('domain_whitelist').select('*');
      if (filters?.approved !== undefined) {
        query = query.eq('approved', filters.approved);
      }
      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data as DomainData[];
    } else {
      const apiClient = await getGoApiClient();
      // Construct query parameters for Go backend
      const params: Record<string, string> = {};
      if (filters?.approved !== undefined) {
        params.approved = String(filters.approved);
      }
      // Assuming the Go backend endpoint is /api/v1/domains/whitelist
      // Adjust the path and expected response structure as necessary.
      const response = await apiClient.get('/domains/whitelist', params as any);
      return (response.domains || response.data || []) as DomainData[];
    }
  } catch (error) {
    console.error('Error in fetchDomainWhitelist:', error);
    throw error;
  }
}

/**
 * Fetches only approved domains.
 */
export async function getApprovedDomains(): Promise<string[]> {
    try {
        const domainsData = await fetchDomainWhitelist({ approved: true });
        return domainsData.map(item => item.domain);
    } catch (error) {
        console.error('Error fetching approved domains:', error);
        // Return empty array or re-throw, depending on desired error handling
        return [];
    }
}

/**
 * Submits a new domain for approval
 */
export async function submitDomainForApproval(domain: string, userId?: string): Promise<DomainData> {
  try {
    // Extract the main domain
    const mainDomain = extractMainDomain(domain);

    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();

      // Check if domain already exists
      const { data: existingDomain, error: checkError } = await supabase
        .from('domain_whitelist')
        .select('*')
        .eq('domain', mainDomain)
        .maybeSingle();

      if (checkError) throw checkError;

      // If domain already exists, return it
      if (existingDomain) {
        return existingDomain as DomainData;
      }

      // Otherwise, add the new domain
      const { data, error } = await supabase
        .from('domain_whitelist')
        .insert({
          domain: mainDomain,
          approved: false,
          user_id: userId
        })
        .select()
        .single();

      if (error) throw error;
      return data as DomainData;
    } else {
      const apiClient = await getGoApiClient();

      // Check if domain already exists
      const existingDomains = await apiClient.get('/domains/whitelist');
      const domains = existingDomains.domains || [];
      const existingDomain = domains.find((d: any) => d.domain === mainDomain);

      if (existingDomain) {
        return existingDomain as DomainData;
      }

      // Create new domain
      const response = await apiClient.post('/domains/whitelist', {
        domain: mainDomain,
        approved: false
      });

      return response as DomainData;
    }
  } catch (error) {
    console.error('Error in submitDomainForApproval:', error);
    throw error;
  }
}

/**
 * Updates the approval status of a domain
 */
export async function updateDomainApproval(id: string, approved: boolean): Promise<boolean> {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase
        .from('domain_whitelist')
        .update({ approved })
        .eq('id', id);

      if (error) throw error;
      return true;
    } else {
      const apiClient = await getGoApiClient();
      await apiClient.put(`/domains/whitelist/${id}`, { approved });
      return true;
    }
  } catch (error) {
    console.error('Error in updateDomainApproval:', error);
    throw error;
  }
}

/**
 * Deletes a domain by ID
 */
export async function deleteDomainById(id: string): Promise<boolean> {
  try {
    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      const { error } = await supabase
        .from('domain_whitelist')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } else {
      const apiClient = await getGoApiClient();
      await apiClient.delete(`/domains/whitelist/${id}`);
      return true;
    }
  } catch (error) {
    console.error('Error in deleteDomainById:', error);
    throw error;
  }
}

/**
 * Seeds default domains if no domains exist
 */
export async function seedDefaultDomains(): Promise<boolean> {
  try {
    const defaultDomains = [
      'google.com', 'github.com', 'microsoft.com',
      'apple.com', 'amazon.com', 'facebook.com',
      'twitter.com', 'linkedin.com', 'youtube.com'
    ];

    if (isUsingSupabase()) {
      const supabase = await getSupabaseClient();
      await Promise.all(
        defaultDomains.map(domain =>
          supabase
            .from('domain_whitelist')
            .insert({ domain, approved: true })
        )
      );
    } else {
      const apiClient = await getGoApiClient();
      await apiClient.post('/domains/whitelist/seed');
    }

    return true;
  } catch (error) {
    console.error('Error in seedDefaultDomains:', error);
    throw error;
  }
}
