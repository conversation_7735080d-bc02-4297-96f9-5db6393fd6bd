import { getBackendConfig, isUsingSupabase } from '@/config/backend';

// Go 后端 API 客户端获取函数 (与 authService.ts 中的类似)
async function getGoApiClient() {
  const config = getBackendConfig();
  const baseURL = config.goBackend?.baseUrl;
  if (!baseURL) {
    throw new Error('Go backend base URL is not configured.');
  }

  return {
    async get(path: string, params?: Record<string, string | string[]>) {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      let url = `${baseURL}${path}`;
      if (params) {
        const queryParams = new URLSearchParams();
        for (const key in params) {
          const value = params[key];
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v));
          } else {
            queryParams.append(key, value);
          }
        }
        if (queryParams.toString()) {
          url += `?${queryParams.toString()}`;
        }
      }

      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        try {
          const errorData = await response.json();
          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        } catch (e) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      }
      return response.json();
    },
  };
}

// Supabase 客户端 (如果需要回退或混合模式)
async function getSupabaseClient() {
  if (!isUsingSupabase()) {
    // This function should ideally not be called if not using Supabase for navigation
    console.warn('Attempted to get Supabase client for navigation when not configured.');
    return null;
  }
  try {
    const { getSupabaseClient } = await import('@/integrations/supabase/client');
    return getSupabaseClient();
  } catch (error) {
    console.error('Supabase 客户端导入失败:', error);
    throw error;
  }
}

export interface NavCategory {
  id: string | number;
  user_id: string;
  name: string;
  sort_order: number;
  created_at?: string;
  updated_at?: string;
  // Add any other fields that come from your backend
}

export interface NavLink {
  id: string | number;
  category_id: string | number;
  user_id: string; // Assuming links are also directly associated with a user
  title: string;
  url: string;
  icon?: string; // URL or identifier for an icon
  sort_order: number;
  description?: string;
  created_at?: string;
  updated_at?: string;
  // Add any other fields
}

// 获取用户导航分类
export const getUserCategories = async (): Promise<NavCategory[]> => {
  if (isUsingSupabase()) {
    // Fallback or specific Supabase logic if needed for navigation categories
    // For now, assuming primary path is Go backend.
    // This part would need to be implemented if Supabase is still a source for this.
    console.warn('getUserCategories: Supabase fallback not fully implemented. Attempting Go backend.');
  }

  // Always try Go backend if VITE_BACKEND_TYPE is go_backend or if Supabase isn't configured for this
  const apiClient = await getGoApiClient();
  try {
    const data = await apiClient.get('/navigation/categories');
    return data.categories || []; // Adjust based on actual backend response structure
  } catch (error) {
    console.error('Error fetching user categories:', error);
    throw error;
  }
};

// 获取特定分类下的导航链接 (或者所有用户链接，然后前端筛选)
// Let's assume an endpoint that takes category IDs, or fetches all for the user.
// If fetching all links for the user:
export const getUserLinks = async (categoryIds?: (string | number)[]): Promise<NavLink[]> => {
  if (isUsingSupabase()) {
    // Supabase fallback for links
    console.warn('getUserLinks: Supabase fallback not fully implemented. Attempting Go backend.');
  }

  const apiClient = await getGoApiClient();
  try {
    // Example: /navigation/links?category_ids=1,2,3 or /navigation/links if it fetches all user links
    const params: Record<string, string | string[]> = {};
    if (categoryIds && categoryIds.length > 0) {
      params['category_ids'] = categoryIds.map(id => String(id));
    }
    // If your backend returns all links for the logged-in user without needing category_ids:
    // const data = await apiClient.get('/navigation/links');
    // If it specifically needs category_ids or another filter:
    const data = await apiClient.get('/navigation/links', params);
    return data.links || []; // Adjust based on actual backend response structure
  } catch (error) {
    console.error('Error fetching user links:', error);
    throw error;
  }
};

// Potentially a combined function if your backend supports it
export interface UserNavigationData {
  categories: NavCategory[];
  links: NavLink[];
}

export const getUserNavigationData = async (): Promise<UserNavigationData> => {
    // This is a placeholder if you have a single endpoint.
    // For now, we'll use the separate functions.
    // If using separate functions:
    try {
        const categories = await getUserCategories();
        let links: NavLink[] = [];
        if (categories.length > 0) {
            const categoryIds = categories.map(c => c.id);
            links = await getUserLinks(categoryIds);
        }
        return { categories, links };
    } catch (error) {
        console.error('Error fetching user navigation data:', error);
        throw { categories: [], links: [] }; // Return empty on error
    }
};

// Navigation link moderation functions
export async function fetchNavigationLinksWithStatus(status?: string) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    let query = supabase
      .from('nav_links')
      .select(`
        *,
        nav_categories (name)
      `)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('submission_status', status);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  } else {
    const apiClient = await getGoApiClient();
    const params: Record<string, string> = {};
    if (status) params.status = status;

    return await apiClient.get('/navigation/links', params);
  }
}

export async function updateNavigationLinkStatus(linkId: string, status: string) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    const { error } = await supabase
      .from('nav_links')
      .update({ submission_status: status })
      .eq('id', linkId);

    if (error) throw error;
  } else {
    const apiClient = await getGoApiClient();
    await apiClient.get(`/navigation/links/${linkId}`, { status });
  }
}

export async function getNavigationLinkCount(status: string) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    const { count, error } = await supabase
      .from('nav_links')
      .select('*', { count: 'exact', head: true })
      .eq('submission_status', status);

    if (error) throw error;
    return count || 0;
  } else {
    const apiClient = await getGoApiClient();
    const response = await apiClient.get('/navigation/links', { status, count_only: 'true' });
    return response.count || 0;
  }
}

// Additional navigation functions
export async function fetchNavigationCategories() {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    const { data, error } = await supabase
      .from('nav_categories')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  } else {
    const apiClient = await getGoApiClient();
    return await apiClient.get('/navigation/categories');
  }
}

export async function createNavigationLink(link: any) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    const { data, error } = await supabase
      .from('nav_links')
      .insert(link)
      .select()
      .single();

    if (error) throw error;
    return data;
  } else {
    const apiClient = await getGoApiClient();
    return await apiClient.post('/navigation/links', link);
  }
}

export async function updateNavigationLink(linkId: string, updates: any) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    const { data, error } = await supabase
      .from('nav_links')
      .update(updates)
      .eq('id', linkId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } else {
    const apiClient = await getGoApiClient();
    return await apiClient.put(`/navigation/links/${linkId}`, updates);
  }
}

export async function deleteNavigationLink(linkId: string) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    const { error } = await supabase
      .from('nav_links')
      .delete()
      .eq('id', linkId);

    if (error) throw error;
  } else {
    const apiClient = await getGoApiClient();
    await apiClient.delete(`/navigation/links/${linkId}`);
  }
}

export async function createNavigationCategory(category: any) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    const { data, error } = await supabase
      .from('nav_categories')
      .insert(category)
      .select()
      .single();

    if (error) throw error;
    return data;
  } else {
    const apiClient = await getGoApiClient();
    return await apiClient.post('/navigation/categories', category);
  }
}

export async function updateNavigationCategory(categoryId: string, updates: any) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    const { data, error } = await supabase
      .from('nav_categories')
      .update(updates)
      .eq('id', categoryId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } else {
    const apiClient = await getGoApiClient();
    return await apiClient.put(`/navigation/categories/${categoryId}`, updates);
  }
}

export async function deleteNavigationCategory(categoryId: string) {
  if (isUsingSupabase()) {
    const supabase = await getSupabaseClient();
    if (!supabase) throw new Error('Supabase client not available');

    // First delete all links in this category
    await supabase
      .from('nav_links')
      .delete()
      .eq('category_id', categoryId);

    // Then delete the category
    const { error } = await supabase
      .from('nav_categories')
      .delete()
      .eq('id', categoryId);

    if (error) throw error;
  } else {
    const apiClient = await getGoApiClient();

    // For Go backend, delete links first
    const links = await apiClient.get('/navigation/links', { category_id: categoryId });
    for (const link of (links || [])) {
      await apiClient.delete(`/navigation/links/${link.id}`);
    }

    // Then delete the category
    await apiClient.delete(`/navigation/categories/${categoryId}`);
  }
}