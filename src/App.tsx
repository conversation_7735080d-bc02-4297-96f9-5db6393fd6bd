import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AppProvider } from "./context/AppContext";
import { TooltipProvider } from "./components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import Layout from "./components/Layout";
import Index from "./pages/Index";
import Home from "./pages/Home";
import VerifyEmail from "./pages/VerifyEmail";
import Dashboard from "./pages/Dashboard";
import Admin from "./pages/Admin";
import Profile from "./pages/Profile";
import NavigationPage from "./pages/NavigationPage";
import HotNews from "./pages/HotNews";
import OnlineTools from "./pages/OnlineTools";
import MemoTodo from "./pages/MemoTodo";
import NotFound from "./pages/NotFound";
import About from "./pages/About";
import ShortUrlRedirect from "./pages/ShortUrlRedirect";
import UpdatePassword from "./components/UpdatePassword";
import PopoverTest from "./pages/PopoverTest";
import DraggableTest from "./pages/DraggableTest";
import OAuthCallback from '@/pages/OAuthCallback';
import OAuthTest from '@/pages/OAuthTest';
import ProtectedRoute from "./components/ProtectedRoute";
import Forbidden from "./pages/Forbidden";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 30000, // 30 seconds
    },
  },
});

const App = () => (
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <AppProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <Router>
            <Layout>
              <Routes>
                {/* 公开页面 */}
                <Route path="/" element={<Index />} />
                <Route path="/home" element={<Home />} />
                <Route path="/verify-email" element={<VerifyEmail />} />
                <Route path="/oauth/callback/:provider" element={<OAuthCallback />} />
                <Route path="/oauth/test" element={<OAuthTest />} />
                <Route path="/about" element={<About />} />
                <Route path="/features" element={<About />} />
                <Route path="/navigation" element={<NavigationPage />} />
                <Route path="/hot-news" element={<HotNews />} />
                <Route path="/update-password" element={<UpdatePassword />} />
                <Route path="/s/:shortCode" element={<ShortUrlRedirect />} />
                <Route path="/:shortCode" element={<ShortUrlRedirect />} />
                <Route path="/online-tools" element={<OnlineTools />} />
                <Route path="/popover-test" element={<PopoverTest />} />
                <Route path="/draggable-test" element={<DraggableTest />} />
                <Route path="/forbidden" element={<Forbidden />} />

                {/* 需要登录的页面 */}
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <Profile />
                  </ProtectedRoute>
                } />
                <Route path="/memo-todo" element={
                  <ProtectedRoute>
                    <MemoTodo />
                  </ProtectedRoute>
                } />

                {/* 需要管理员权限的页面 */}
                <Route path="/admin" element={
                  <ProtectedRoute requireAdmin={true}>
                    <Admin />
                  </ProtectedRoute>
                } />

                {/* 404页面 */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Layout>
          </Router>
        </TooltipProvider>
      </AppProvider>
    </QueryClientProvider>
  </React.StrictMode>
);

export default App;
